using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Blog post entity for astrology articles and insights
    /// </summary>
    public class BlogPost : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Excerpt { get; set; } = string.Empty;

        public string? Content { get; set; }

        [Required]
        public DateTime PublishedAt { get; set; }

        public int ReadCount { get; set; } = 0;

        [Required]
        [MaxLength(150)]
        public string Slug { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? FeaturedImageUrl { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual ICollection<BlogPostTag> BlogPostTags { get; set; } = new List<BlogPostTag>();
    }
}
