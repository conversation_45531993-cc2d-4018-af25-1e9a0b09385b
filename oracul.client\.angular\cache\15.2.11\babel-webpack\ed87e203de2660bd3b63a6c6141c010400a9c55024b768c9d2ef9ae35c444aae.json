{"ast": null, "code": "import { map, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkAuth(state.url);\n  }\n  canActivateChild(route, state) {\n    return this.checkAuth(state.url);\n  }\n  checkAuth(url) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      if (isAuthenticated) {\n        return true;\n      } else {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class RoleGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const requiredRoles = route.data['roles'];\n    if (!requiredRoles || requiredRoles.length === 0) {\n      return true;\n    }\n    return this.authService.currentUser$.pipe(take(1), map(user => {\n      if (!user) {\n        this.router.navigate(['/login']);\n        return false;\n      }\n      const hasRole = requiredRoles.some(role => user.roles.includes(role));\n      if (!hasRole) {\n        this.router.navigate(['/unauthorized']);\n        return false;\n      }\n      return true;\n    }));\n  }\n  static {\n    this.ɵfac = function RoleGuard_Factory(t) {\n      return new (t || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleGuard,\n      factory: RoleGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;;;;AAM1C,OAAM,MAAOC,SAAS;EACpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,gBAAW,GAAXD,WAAW;IACX,WAAM,GAANC,MAAM;EACb;EAEHC,WAAW,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACD,KAAK,CAACE,GAAG,CAAC;EAClC;EAEAC,gBAAgB,CACdJ,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACD,KAAK,CAACE,GAAG,CAAC;EAClC;EAEQD,SAAS,CAACC,GAAW;IAC3B,OAAO,IAAI,CAACN,WAAW,CAACQ,gBAAgB,CAACC,IAAI,CAC3CZ,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACc,eAAe,IAAG;MACpB,IAAIA,eAAe,EAAE;QACnB,OAAO,IAAI;OACZ,MAAM;QACL,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAAEC,WAAW,EAAE;YAAEC,SAAS,EAAEP;UAAG;QAAE,CAAE,CAAC;QACrE,OAAO,KAAK;;IAEhB,CAAC,CAAC,CACH;EACH;;;uBAhCWR,SAAS;IAAA;EAAA;;;aAATA,SAAS;MAAAgB,SAAThB,SAAS;MAAAiB,YAFR;IAAM;EAAA;;AAwCpB,OAAM,MAAOC,SAAS;EACpBjB,YACUC,WAAwB,EACxBC,MAAc;IADd,gBAAW,GAAXD,WAAW;IACX,WAAM,GAANC,MAAM;EACb;EAEHC,WAAW,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,MAAMa,aAAa,GAAGd,KAAK,CAACe,IAAI,CAAC,OAAO,CAAa;IAErD,IAAI,CAACD,aAAa,IAAIA,aAAa,CAACE,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,IAAI;;IAGb,OAAO,IAAI,CAACnB,WAAW,CAACoB,YAAY,CAACX,IAAI,CACvCZ,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACyB,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE;QACT,IAAI,CAACpB,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAChC,OAAO,KAAK;;MAGd,MAAMW,OAAO,GAAGL,aAAa,CAACM,IAAI,CAACC,IAAI,IAAIH,IAAI,CAACI,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,CAAC;MACrE,IAAI,CAACF,OAAO,EAAE;QACZ,IAAI,CAACrB,MAAM,CAACU,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;;;uBAjCWK,SAAS;IAAA;EAAA;;;aAATA,SAAS;MAAAF,SAATE,SAAS;MAAAD,YAFR;IAAM;EAAA", "names": ["map", "take", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "checkAuth", "url", "canActivateChild", "isAuthenticated$", "pipe", "isAuthenticated", "navigate", "queryParams", "returnUrl", "factory", "providedIn", "<PERSON><PERSON><PERSON>", "requiredRoles", "data", "length", "currentUser$", "user", "hasRole", "some", "role", "roles", "includes"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { map, take } from 'rxjs/operators';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate, CanActivateChild {\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Observable<boolean> | Promise<boolean> | boolean {\r\n    return this.checkAuth(state.url);\r\n  }\r\n\r\n  canActivateChild(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Observable<boolean> | Promise<boolean> | boolean {\r\n    return this.checkAuth(state.url);\r\n  }\r\n\r\n  private checkAuth(url: string): Observable<boolean> {\r\n    return this.authService.isAuthenticated$.pipe(\r\n      take(1),\r\n      map(isAuthenticated => {\r\n        if (isAuthenticated) {\r\n          return true;\r\n        } else {\r\n          this.router.navigate(['/login'], { queryParams: { returnUrl: url } });\r\n          return false;\r\n        }\r\n      })\r\n    );\r\n  }\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RoleGuard implements CanActivate {\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Observable<boolean> | Promise<boolean> | boolean {\r\n    const requiredRoles = route.data['roles'] as string[];\r\n    \r\n    if (!requiredRoles || requiredRoles.length === 0) {\r\n      return true;\r\n    }\r\n\r\n    return this.authService.currentUser$.pipe(\r\n      take(1),\r\n      map(user => {\r\n        if (!user) {\r\n          this.router.navigate(['/login']);\r\n          return false;\r\n        }\r\n\r\n        const hasRole = requiredRoles.some(role => user.roles.includes(role));\r\n        if (!hasRole) {\r\n          this.router.navigate(['/unauthorized']);\r\n          return false;\r\n        }\r\n\r\n        return true;\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}