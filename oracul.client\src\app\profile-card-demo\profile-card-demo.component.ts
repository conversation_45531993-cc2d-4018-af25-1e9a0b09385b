import { Component, OnInit } from '@angular/core';
import { UserProfile } from '../profile/models/profile.models';

@Component({
  selector: 'app-profile-card-demo',
  templateUrl: './profile-card-demo.component.html',
  styleUrls: ['./profile-card-demo.component.css']
})
export class ProfileCardDemoComponent implements OnInit {
  sampleProfiles: UserProfile[] = [];

  ngOnInit(): void {
    this.loadSampleProfiles();
  }

  private loadSampleProfiles(): void {
    this.sampleProfiles = [
      {
        id: 1,
        userId: 1,
        username: 'luna-starweaver',
        slug: 'luna-starweaver',
        isPublic: true,
        profileCompletionPercentage: 95,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
        firstName: 'Luna',
        lastName: 'Starweaver',
        professionalTitle: 'Professional Astrologer & Cosmic Guide',
        headline: 'Illuminating your path through the wisdom of the stars',
        location: {
          city: 'Sedona',
          state: 'Arizona',
          country: 'USA',
          displayLocation: 'Sedona, Arizona'
        },
        contactInfo: {
          email: '<EMAIL>',
          isEmailPublic: true,
          website: 'https://starweaver.com',
          phoneNumbers: [
            {
              number: '+****************',
              type: 'business',
              isPublic: true,
              isPrimary: true
            }
          ]
        },
        summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling. My approach combines traditional astrological wisdom with modern psychological insights.',
        skills: [
          { name: 'Natal Chart Reading', endorsements: 45, proficiencyLevel: 'expert' },
          { name: 'Tarot Reading', endorsements: 38, proficiencyLevel: 'expert' },
          { name: 'Spiritual Counseling', endorsements: 32, proficiencyLevel: 'advanced' },
          { name: 'Crystal Healing', endorsements: 28, proficiencyLevel: 'advanced' },
          { name: 'Meditation Guidance', endorsements: 25, proficiencyLevel: 'intermediate' }
        ],
        experiences: [
          {
            company: 'Cosmic Wisdom Center',
            position: 'Senior Astrologer',
            startDate: new Date('2018-01-01'),
            isCurrent: true,
            description: 'Leading astrologer providing personalized readings and workshops',
            location: 'Sedona, Arizona'
          },
          {
            company: 'Mystic Moon Studio',
            position: 'Astrology Consultant',
            startDate: new Date('2015-01-01'),
            endDate: new Date('2017-12-31'),
            isCurrent: false,
            description: 'Provided astrological consultations and spiritual guidance',
            location: 'Santa Fe, New Mexico'
          }
        ],
        portfolioItems: [
          {
            title: 'Cosmic Birth Chart Analysis',
            description: 'Comprehensive natal chart reading service',
            imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],
            technologies: ['Astrology', 'Spiritual Guidance'],
            completedAt: new Date('2023-01-01'),
            category: 'Astrology Services'
          }
        ],
        blogPosts: [],
        achievements: [],
        certifications: [],
        socialLinks: [],
        profileViews: 1247,
        createdAt: new Date('2020-01-01'),
        updatedAt: new Date('2023-12-01')
      },
      {
        id: 2,
        userId: 2,
        username: 'marcus-celestial',
        slug: 'marcus-celestial',
        isPublic: true,
        profileCompletionPercentage: 88,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
        firstName: 'Marcus',
        lastName: 'Celestial',
        professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',
        headline: 'Unlocking the secrets of your cosmic blueprint',
        location: {
          city: 'Los Angeles',
          state: 'California',
          country: 'USA',
          displayLocation: 'Los Angeles, California'
        },
        contactInfo: {
          email: '<EMAIL>',
          isEmailPublic: true,
          website: 'https://celestialreadings.com',
          phoneNumbers: [
            {
              number: '+****************',
              type: 'business',
              isPublic: true,
              isPrimary: true
            }
          ]
        },
        summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges through astrological insights.',
        skills: [
          { name: 'Birth Chart Analysis', endorsements: 52, proficiencyLevel: 'expert' },
          { name: 'Compatibility Reading', endorsements: 41, proficiencyLevel: 'expert' },
          { name: 'Life Transitions', endorsements: 35, proficiencyLevel: 'advanced' },
          { name: 'Career Guidance', endorsements: 29, proficiencyLevel: 'advanced' }
        ],
        experiences: [
          {
            company: 'Celestial Insights',
            position: 'Lead Astrologer',
            startDate: new Date('2019-01-01'),
            isCurrent: true,
            description: 'Providing comprehensive astrological services and mentoring junior astrologers',
            location: 'Los Angeles, California'
          }
        ],
        portfolioItems: [],
        blogPosts: [],
        achievements: [],
        certifications: [],
        socialLinks: [],
        profileViews: 892,
        createdAt: new Date('2021-01-01'),
        updatedAt: new Date('2023-11-15')
      }
    ];
  }

  onContactProfile(profile: UserProfile): void {
    console.log('Contact profile:', profile.firstName, profile.lastName);
    if (profile.contactInfo?.email) {
      window.location.href = `mailto:${profile.contactInfo.email}`;
    }
  }

  onProfileClicked(profile: UserProfile): void {
    console.log('Profile clicked:', profile.firstName, profile.lastName);
    // In a real app, this would navigate to the profile
  }
}
