# Oracul Project - Quick Start Guide

## 🚀 Project Overview

The Oracul project consists of:
- **Oracul.Data** - Entity Framework Core data layer
- **Oracul.Server** - ASP.NET Core Web API backend
- **oracul.client** - Angular frontend with Material Design

## 📋 Prerequisites

1. **.NET 9.0 SDK** - [Download here](https://dotnet.microsoft.com/download)
2. **Node.js 18+** - [Download here](https://nodejs.org/)
3. **SQL Server** - LocalDB, Express, or full version
4. **Entity Framework Tools**:
   ```bash
   dotnet tool install --global dotnet-ef
   ```

## 🗄️ Database Setup

### First Time Setup

1. **Update connection string** in `Oracul.Server/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=.;Database=OraculDb;User Id=sa;Password=yourpassword;TrustServerCertificate=True;"
     }
   }
   ```

2. **Apply migrations** to create database:
   ```powershell
   .\migrate-clean.ps1
   ```

### Adding New Migrations

1. **Make changes** to your entity models in `Oracul.Data/Models/`

2. **Create migration**:
   ```powershell
   .\migrate-clean.ps1 -Action add -MigrationName "YourMigrationName"
   ```

3. **Apply migration**:
   ```powershell
   .\migrate-clean.ps1
   ```

### Migration Commands Reference

```powershell
# Apply all pending migrations (most common)
.\migrate-clean.ps1

# Create new migration
.\migrate-clean.ps1 -Action add -MigrationName "AddNewFeature"

# List all migrations
.\migrate-clean.ps1 -Action list

# Generate SQL script
.\migrate-clean.ps1 -Action script

# Remove last migration (if not applied to production)
.\migrate-clean.ps1 -Action remove

# Show help
.\migrate-clean.ps1 -Action help
```

## 🖥️ Running the Application

### Backend (API)

```bash
# Navigate to server project
cd Oracul.Server

# Run the API
dotnet run
```

The API will be available at: `https://localhost:7154` or `http://localhost:5154`

### Frontend (Angular)

```bash
# Navigate to client project
cd oracul.client

# Install dependencies (first time only)
npm install

# Start development server
ng serve
```

The Angular app will be available at: `http://localhost:4200`

### Both Together

```bash
# From solution root, run both projects
dotnet run --project Oracul.Server
# In another terminal:
cd oracul.client && ng serve
```

## 🎨 Angular Material

The frontend includes Angular Material with:
- **Deep Purple/Amber theme**
- **Roboto typography**
- **Material Icons**
- **Responsive components**

### Available Components
- Toolbar, Buttons, Cards, Tables
- Form fields, Inputs, Selects
- Progress spinners, Snackbars
- Dialogs, Checkboxes, Menus

See `ANGULAR-MATERIAL-GUIDE.md` for detailed usage.

## 🏗️ Project Structure

```
Oracul/
├── Oracul.Data/                 # Data layer
│   ├── Models/                  # Entity models
│   ├── Data/                    # DbContext
│   ├── Repositories/            # Repository pattern
│   ├── Interfaces/              # Contracts
│   └── Migrations/              # EF migrations
├── Oracul.Server/               # Web API
│   ├── Controllers/             # API controllers
│   ├── Program.cs               # Startup configuration
│   └── appsettings.json         # Configuration
├── oracul.client/               # Angular frontend
│   ├── src/app/                 # Angular components
│   ├── angular.json             # Angular configuration
│   └── package.json             # NPM dependencies
├── migrate-clean.ps1            # Migration helper script
├── migrate.bat                  # Batch migration script
└── MIGRATION-README.md          # Detailed migration guide
```

## 🔧 Development Workflow

### Adding New Features

1. **Create entity models** in `Oracul.Data/Models/`
2. **Update DbContext** if needed
3. **Create migration**: `.\migrate-clean.ps1 -Action add -MigrationName "AddFeature"`
4. **Apply migration**: `.\migrate-clean.ps1`
5. **Create repositories** if needed
6. **Add API controllers** in `Oracul.Server/Controllers/`
7. **Create Angular components** in `oracul.client/src/app/`

### Testing Changes

1. **Build solution**: `dotnet build`
2. **Run backend**: `dotnet run --project Oracul.Server`
3. **Run frontend**: `cd oracul.client && ng serve`
4. **Test API endpoints** at `https://localhost:7154/swagger`
5. **Test UI** at `http://localhost:4200`

## 📚 Documentation

- **`MIGRATION-README.md`** - Comprehensive migration guide
- **`ANGULAR-MATERIAL-GUIDE.md`** - Angular Material usage
- **`Oracul.Data/README.md`** - Data layer documentation

## 🆘 Troubleshooting

### Database Issues
- Verify SQL Server is running
- Check connection string in `appsettings.json`
- Run `.\migrate-clean.ps1 -Action list` to check migration status

### Build Issues
- Run `dotnet clean && dotnet build`
- Check for missing packages: `dotnet restore`

### Angular Issues
- Delete `node_modules` and run `npm install`
- Check Angular CLI version: `ng version`

## 🎯 Quick Commands

```bash
# Build everything
dotnet build

# Run migrations
.\migrate-clean.ps1

# Start backend
dotnet run --project Oracul.Server

# Start frontend
cd oracul.client && ng serve

# Create new migration
.\migrate-clean.ps1 -Action add -MigrationName "YourFeature"

# View migration status
.\migrate-clean.ps1 -Action list
```

## 🚀 You're Ready!

Your Oracul application is now set up with:
- ✅ Separated data layer with Entity Framework Core
- ✅ Working migration system
- ✅ Angular frontend with Material Design
- ✅ Complete development workflow

Happy coding! 🎉
