# 🔧 Static Ports Development Setup

## ✅ **Configuration Complete**

The Oracul application is now configured with **static ports** for consistent development experience and to eliminate CORS issues.

## 🌐 **Port Configuration**

### **Frontend (Angular)**
- **Port**: `4200` (Standard Angular port)
- **URL**: http://localhost:4200
- **Protocol**: HTTP (simplified for development)

### **Backend (ASP.NET Core)**
- **Port**: `5144` (Static backend port)
- **URL**: http://localhost:5144
- **Protocol**: HTTP

## 📁 **Configuration Files Updated**

### **1. Angular Configuration**
**File**: `oracul.client/angular.json`
```json
"serve": {
  "builder": "@angular-devkit/build-angular:dev-server",
  "options": {
    "proxyConfig": "src/proxy.conf.js",
    "port": 4200,
    "host": "localhost"
  }
}
```

### **2. Proxy Configuration**
**File**: `oracul.client/src/proxy.conf.js`
```javascript
// Static backend URL for consistent development
const target = 'http://localhost:5144';

const PROXY_CONFIG = [
  {
    context: ["/weatherforecast", "/api"],
    target,
    secure: false,
    changeOrigin: true,
    logLevel: "debug"
  }
]
```

### **3. Package.json Scripts**
**File**: `oracul.client/package.json`
```json
"scripts": {
  "start": "ng serve",
  "start:dev": "ng serve --port 4200 --host localhost"
}
```

### **4. Backend CORS Configuration**
**File**: `Oracul.Server/Program.cs`
```csharp
// Configure CORS - Static development ports
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularApp", policy =>
    {
        policy.WithOrigins(
                "http://localhost:4200",   // Angular dev server
                "https://localhost:4200"   // Angular dev server with SSL
              )
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
```

## 🚀 **Starting the Application**

### **Option 1: Automated Script**
```bash
# Run the batch script (Windows)
start-dev.bat
```

### **Option 2: Manual Start**
```bash
# Terminal 1: Start Backend
cd Oracul.Server
dotnet run

# Terminal 2: Start Frontend
cd oracul.client
npm start
```

### **Option 3: Individual Commands**
```bash
# Frontend only
cd oracul.client
npm start

# Backend only
cd Oracul.Server
dotnet run
```

## 🔍 **Verification**

After starting both services, verify they're running:

1. **Frontend**: Open http://localhost:4200
2. **Backend**: Check http://localhost:5144/api (should show API endpoints)
3. **CORS**: Frontend should successfully communicate with backend

## 🛠 **Benefits of Static Ports**

### **✅ Advantages**
- **No CORS Issues**: Consistent origin for CORS configuration
- **Predictable URLs**: Always know where services are running
- **Simplified Development**: No need to check dynamic ports
- **Easy Bookmarking**: Consistent URLs for development
- **Team Consistency**: All developers use same ports

### **🔧 Technical Improvements**
- **Removed SSL Complexity**: Simplified to HTTP for development
- **Eliminated Dynamic Port Assignment**: No more port conflicts
- **Streamlined Proxy Configuration**: Direct backend targeting
- **Cleaner npm Scripts**: Removed complex SSL certificate handling

## 📋 **Port Usage Summary**

| Service | Port | URL | Purpose |
|---------|------|-----|---------|
| Angular Frontend | 4200 | http://localhost:4200 | Main application UI |
| ASP.NET Backend | 5144 | http://localhost:5144 | API and data services |

## 🔒 **Security Notes**

- **Development Only**: This configuration is optimized for development
- **Production**: Use HTTPS and proper SSL certificates in production
- **CORS**: Production should have restrictive CORS policies
- **Ports**: Production should use standard ports (80/443)

## 🐛 **Troubleshooting**

### **Port Already in Use**
```bash
# Check what's using the port
netstat -ano | findstr :4200
netstat -ano | findstr :5144

# Kill process if needed
taskkill /PID <process_id> /F
```

### **CORS Errors**
- Verify backend is running on port 5144
- Check CORS configuration in `Program.cs`
- Ensure frontend is accessing from localhost:4200

### **Proxy Issues**
- Check `proxy.conf.js` configuration
- Verify backend URL is correct
- Restart Angular dev server after proxy changes

## 🎯 **Next Steps**

The static port configuration is now complete and ready for development. The scroll-based navigation highlighting feature is also fully functional and can be tested at http://localhost:4200.

---

**Happy Coding!** 🌟
