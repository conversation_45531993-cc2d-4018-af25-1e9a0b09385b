.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Header Card */
.header-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 4px solid #673ab7;
}

.header-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #673ab7;
}

.header-card code {
  background-color: #673ab7;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Grid Demo */
.grid-demo-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2196f3;
}

.profiles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 16px;
}

/* List Demo */
.list-demo-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4caf50;
}

.profiles-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

/* Features Card */
.features-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff9800;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #673ab7;
}

.feature-item mat-icon {
  color: #673ab7;
  margin-top: 2px;
}

.feature-content h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.feature-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Technical Card */
.technical-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #9c27b0;
}

.tech-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 16px;
}

.tech-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 500;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 4px;
}

.tech-section ul {
  margin: 0;
  padding-left: 20px;
}

.tech-section li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.tech-section li strong {
  color: #673ab7;
}

.tech-section pre {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 0;
}

.tech-section code {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #333;
}

/* Card Headers */
mat-card-header {
  margin-bottom: 16px;
}

mat-card-title {
  font-size: 1.25rem;
  font-weight: 500;
}

mat-card-subtitle {
  color: #666;
  font-size: 0.95rem;
  margin-top: 4px;
}

/* Card Actions */
mat-card-actions {
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

mat-card-actions button {
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profiles-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
    gap: 20px;
  }
  
  .profiles-grid {
    gap: 16px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .feature-item {
    padding: 12px;
  }
  
  .tech-section pre {
    padding: 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .demo-container {
    padding: 12px;
  }
  
  .feature-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .feature-item mat-icon {
    margin-top: 0;
  }
  
  mat-card-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  mat-card-actions button {
    width: 100%;
    margin-right: 0;
  }
}
