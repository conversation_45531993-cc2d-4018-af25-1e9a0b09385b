using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Business address entity for contact information
    /// </summary>
    public class BusinessAddress : BaseEntity
    {
        [Required]
        public int ContactInformationId { get; set; }

        [MaxLength(200)]
        public string? Street { get; set; }

        [MaxLength(100)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? State { get; set; }

        [MaxLength(20)]
        public string? PostalCode { get; set; }

        [MaxLength(100)]
        public string? Country { get; set; }

        public bool IsPublic { get; set; } = false;

        // Navigation properties
        public virtual ContactInformation ContactInformation { get; set; } = null!;
    }
}
