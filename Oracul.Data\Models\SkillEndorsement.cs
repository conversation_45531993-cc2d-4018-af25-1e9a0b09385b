using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Skill endorsement entity for tracking user endorsements
    /// </summary>
    public class SkillEndorsement : BaseEntity
    {
        [Required]
        public int ProfileSkillId { get; set; }

        [Required]
        public int EndorserUserId { get; set; }

        // Navigation properties
        public virtual ProfileSkill ProfileSkill { get; set; } = null!;
        public virtual User EndorserUser { get; set; } = null!;
    }
}
