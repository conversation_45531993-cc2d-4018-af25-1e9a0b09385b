@echo off
REM Simple Windows batch script for database reset

echo.
echo 🔮 Oracul Database Reset Script
echo.

REM Change to project root directory
cd /d "%~dp0\.."

echo ⚠️ WARNING: This will completely reset the database with fresh oracle data!
set /p confirm="Type 'YES' to confirm: "

if not "%confirm%"=="YES" (
    echo ❌ Operation cancelled
    pause
    exit /b 1
)

echo.
echo 🗑️ Dropping Database...
dotnet ef database drop --project Oracul.Data --startup-project Oracul.Server --force

echo.
echo 🔄 Running Database Migrations...
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server

echo.
echo 🌱 Seeding Database with Oracle Profiles...
dotnet run --project Oracul.Server --seed-only

echo.
echo 🎉 Database reset complete!
echo The database now contains 5 fresh oracle profiles:
echo   • Luna Starweaver (Astrologer)
echo   • Sage Moonchild (Crystal Healer)
echo   • River Palmistry (Palm Reader)
echo   • Aurora Wisdom (Spiritual Counselor)
echo   • Cosmic Dawn (Numerologist)
echo.

pause
