using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Portfolio image entity for portfolio item images
    /// </summary>
    public class PortfolioImage : BaseEntity
    {
        [Required]
        public int PortfolioItemId { get; set; }

        [Required]
        [MaxLength(500)]
        public string ImageUrl { get; set; } = string.Empty;

        public int DisplayOrder { get; set; } = 0;

        // Navigation properties
        public virtual PortfolioItem PortfolioItem { get; set; } = null!;
    }
}
