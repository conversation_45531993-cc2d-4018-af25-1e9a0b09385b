{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, EventEmitter, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i6 from '@angular/material/core';\nimport { mixinDisabled, mixinInitialized } from '@angular/material/core';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/material/form-field';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_div_2_mat_form_field_3_mat_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r6, \" \");\n  }\n}\nfunction MatPaginator_div_2_mat_form_field_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 16)(1, \"mat-select\", 17);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_div_2_mat_form_field_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7._changePageSize($event.value));\n    });\n    i0.ɵɵtemplate(2, MatPaginator_div_2_mat_form_field_3_mat_option_2_Template, 2, 2, \"mat-option\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r3._formFieldAppearance)(\"color\", ctx_r3.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r3.pageSize)(\"disabled\", ctx_r3.disabled)(\"aria-labelledby\", ctx_r3._pageSizeLabelId)(\"panelClass\", ctx_r3.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r3.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.pageSize);\n  }\n}\nfunction MatPaginator_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_div_2_mat_form_field_3_Template, 3, 8, \"mat-form-field\", 14);\n    i0.ɵɵtemplate(4, MatPaginator_div_2_div_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r0._pageSizeLabelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length <= 1);\n  }\n}\nfunction MatPaginator_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r2._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r2._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r2._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2._intl.lastPageLabel);\n  }\n}\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    this.changes = new Subject();\n    /** A label for the page size selector. */\n    this.itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    this.nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    this.previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    this.firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    this.lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    this.getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n  }\n}\nMatPaginatorIntl.ɵfac = function MatPaginatorIntl_Factory(t) {\n  return new (t || MatPaginatorIntl)();\n};\nMatPaginatorIntl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatPaginatorIntl,\n  factory: MatPaginatorIntl.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = mixinDisabled(mixinInitialized(class {}));\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\nclass _MatPaginatorBase extends _MatPaginatorMixinBase {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = coerceNumberProperty(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(coerceNumberProperty(value), 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** Whether to hide the page size selection UI from the user. */\n  get hidePageSize() {\n    return this._hidePageSize;\n  }\n  set hidePageSize(value) {\n    this._hidePageSize = coerceBooleanProperty(value);\n  }\n  /** Whether to show the first/last buttons UI to the user. */\n  get showFirstLastButtons() {\n    return this._showFirstLastButtons;\n  }\n  set showFirstLastButtons(value) {\n    this._showFirstLastButtons = coerceBooleanProperty(value);\n  }\n  constructor(_intl, _changeDetectorRef, defaults) {\n    super();\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._pageIndex = 0;\n    this._length = 0;\n    this._pageSizeOptions = [];\n    this._hidePageSize = false;\n    this._showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    this.selectConfig = {};\n    /** Event emitted when the paginator changes the page size or page index. */\n    this.page = new EventEmitter();\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this._hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this._showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n  }\n  ngOnInit() {\n    this._initialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._markInitialized();\n  }\n  ngOnDestroy() {\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._initialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n}\n_MatPaginatorBase.ɵfac = function _MatPaginatorBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n_MatPaginatorBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatPaginatorBase,\n  inputs: {\n    color: \"color\",\n    pageIndex: \"pageIndex\",\n    length: \"length\",\n    pageSize: \"pageSize\",\n    pageSizeOptions: \"pageSizeOptions\",\n    hidePageSize: \"hidePageSize\",\n    showFirstLastButtons: \"showFirstLastButtons\",\n    selectConfig: \"selectConfig\"\n  },\n  outputs: {\n    page: \"page\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatPaginatorBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: MatPaginatorIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }];\n  }, {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    length: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input\n    }],\n    showFirstLastButtons: [{\n      type: Input\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator extends _MatPaginatorBase {\n  constructor(intl, changeDetectorRef, defaults) {\n    super(intl, changeDetectorRef, defaults);\n    /** ID for the DOM node containing the paginator's items per page label. */\n    this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n}\nMatPaginator.ɵfac = function MatPaginator_Factory(t) {\n  return new (t || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n};\nMatPaginator.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatPaginator,\n  selectors: [[\"mat-paginator\"]],\n  hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n  inputs: {\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matPaginator\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 14,\n  vars: 14,\n  consts: [[1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [\"class\", \"mat-mdc-paginator-page-size\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-page-size-label\", 3, \"id\"], [\"class\", \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\", 4, \"ngIf\"], [\"class\", \"mat-mdc-paginator-page-size-value\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n  template: function MatPaginator_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, MatPaginator_div_2_Template, 5, 4, \"div\", 2);\n      i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(6, MatPaginator_button_6_Template, 3, 5, \"button\", 5);\n      i0.ɵɵelementStart(7, \"button\", 6);\n      i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n        return ctx.previousPage();\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(8, \"svg\", 7);\n      i0.ɵɵelement(9, \"path\", 8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(10, \"button\", 9);\n      i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n        return ctx.nextPage();\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(11, \"svg\", 7);\n      i0.ɵɵelement(12, \"path\", 10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(13, MatPaginator_button_13_Template, 3, 5, \"button\", 11);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.hidePageSize);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n      i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n      i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i3.MatIconButton, i4.MatFormField, i5.MatSelect, i6.MatOption, i7.MatTooltip],\n  styles: [\".mat-mdc-paginator{display:block}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      inputs: ['disabled'],\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatPaginatorIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatPaginatorModule {}\nMatPaginatorModule.ɵfac = function MatPaginatorModule_Factory(t) {\n  return new (t || MatPaginatorModule)();\n};\nMatPaginatorModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatPaginatorModule\n});\nMatPaginatorModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_PAGINATOR_INTL_PROVIDER],\n  imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule],\n      exports: [MatPaginator],\n      declarations: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent, _MatPaginatorBase };", "map": {"version": 3, "names": ["i2", "CommonModule", "i0", "Injectable", "Optional", "SkipSelf", "InjectionToken", "EventEmitter", "Directive", "Input", "Output", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "NgModule", "Subject", "i3", "MatButtonModule", "i5", "MatSelectModule", "i7", "MatTooltipModule", "i6", "mixinDisabled", "mixinInitialized", "coerceNumberProperty", "coerceBooleanProperty", "i4", "MatPaginatorIntl", "constructor", "changes", "itemsPerPageLabel", "nextPageLabel", "previousPageLabel", "firstPageLabel", "lastPageLabel", "getRangeLabel", "page", "pageSize", "length", "Math", "max", "startIndex", "endIndex", "min", "ɵfac", "ɵprov", "type", "args", "providedIn", "MAT_PAGINATOR_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_PAGINATOR_INTL_PROVIDER", "provide", "deps", "useFactory", "DEFAULT_PAGE_SIZE", "PageEvent", "MAT_PAGINATOR_DEFAULT_OPTIONS", "_MatPaginatorMixinBase", "_MatPaginatorBase", "pageIndex", "_pageIndex", "value", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_length", "_pageSize", "_updateDisplayedPageSizeOptions", "pageSizeOptions", "_pageSizeOptions", "map", "p", "hidePageSize", "_hidePageSize", "showFirstLastButtons", "_showFirstLastButtons", "_intl", "defaults", "selectConfig", "_intlChanges", "subscribe", "ngOnInit", "_initialized", "_markInitialized", "ngOnDestroy", "unsubscribe", "nextPage", "hasNextPage", "previousPageIndex", "_emitPageEvent", "previousPage", "hasPreviousPage", "firstPage", "lastPage", "getNumberOfPages", "maxPageIndex", "ceil", "_changePageSize", "floor", "_nextButtonsDisabled", "disabled", "_previousButtonsDisabled", "_displayedPageSizeOptions", "slice", "indexOf", "push", "sort", "a", "b", "emit", "ɵdir", "ChangeDetectorRef", "undefined", "color", "nextUniqueId", "MatPaginator", "intl", "changeDetectorRef", "_pageSizeLabelId", "_formFieldAppearance", "formFieldAppearance", "ɵcmp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "MatIconButton", "MatFormField", "MatSelect", "MatOption", "MatTooltip", "selector", "exportAs", "inputs", "host", "changeDetection", "OnPush", "encapsulation", "None", "template", "styles", "decorators", "MatPaginatorModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/paginator.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, EventEmitter, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i6 from '@angular/material/core';\nimport { mixinDisabled, mixinInitialized } from '@angular/material/core';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/material/form-field';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n    constructor() {\n        /**\n         * Stream to emit from when labels are changed. Use this to notify components when the labels have\n         * changed after initialization.\n         */\n        this.changes = new Subject();\n        /** A label for the page size selector. */\n        this.itemsPerPageLabel = 'Items per page:';\n        /** A label for the button that increments the current page. */\n        this.nextPageLabel = 'Next page';\n        /** A label for the button that decrements the current page. */\n        this.previousPageLabel = 'Previous page';\n        /** A label for the button that moves to the first page. */\n        this.firstPageLabel = 'First page';\n        /** A label for the button that moves to the last page. */\n        this.lastPageLabel = 'Last page';\n        /** A label for the range of items within the current page and the length of the whole list. */\n        this.getRangeLabel = (page, pageSize, length) => {\n            if (length == 0 || pageSize == 0) {\n                return `0 of ${length}`;\n            }\n            length = Math.max(length, 0);\n            const startIndex = page * pageSize;\n            // If the start index exceeds the list length, do not try and fix the end index to the end.\n            const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n            return `${startIndex + 1} – ${endIndex} of ${length}`;\n        };\n    }\n}\nMatPaginatorIntl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nMatPaginatorIntl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorIntl, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n    // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n    provide: MatPaginatorIntl,\n    deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n    useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = mixinDisabled(mixinInitialized(class {\n}));\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\nclass _MatPaginatorBase extends _MatPaginatorMixinBase {\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n        return this._pageIndex;\n    }\n    set pageIndex(value) {\n        this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n        return this._length;\n    }\n    set length(value) {\n        this._length = coerceNumberProperty(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n        return this._pageSize;\n    }\n    set pageSize(value) {\n        this._pageSize = Math.max(coerceNumberProperty(value), 0);\n        this._updateDisplayedPageSizeOptions();\n    }\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n        return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n        this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n        this._updateDisplayedPageSizeOptions();\n    }\n    /** Whether to hide the page size selection UI from the user. */\n    get hidePageSize() {\n        return this._hidePageSize;\n    }\n    set hidePageSize(value) {\n        this._hidePageSize = coerceBooleanProperty(value);\n    }\n    /** Whether to show the first/last buttons UI to the user. */\n    get showFirstLastButtons() {\n        return this._showFirstLastButtons;\n    }\n    set showFirstLastButtons(value) {\n        this._showFirstLastButtons = coerceBooleanProperty(value);\n    }\n    constructor(_intl, _changeDetectorRef, defaults) {\n        super();\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._pageIndex = 0;\n        this._length = 0;\n        this._pageSizeOptions = [];\n        this._hidePageSize = false;\n        this._showFirstLastButtons = false;\n        /** Used to configure the underlying `MatSelect` inside the paginator. */\n        this.selectConfig = {};\n        /** Event emitted when the paginator changes the page size or page index. */\n        this.page = new EventEmitter();\n        this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n        if (defaults) {\n            const { pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons } = defaults;\n            if (pageSize != null) {\n                this._pageSize = pageSize;\n            }\n            if (pageSizeOptions != null) {\n                this._pageSizeOptions = pageSizeOptions;\n            }\n            if (hidePageSize != null) {\n                this._hidePageSize = hidePageSize;\n            }\n            if (showFirstLastButtons != null) {\n                this._showFirstLastButtons = showFirstLastButtons;\n            }\n        }\n    }\n    ngOnInit() {\n        this._initialized = true;\n        this._updateDisplayedPageSizeOptions();\n        this._markInitialized();\n    }\n    ngOnDestroy() {\n        this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex + 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n        // hasPreviousPage being false implies at the start\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = 0;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n        // hasNextPage being false implies at the end\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.getNumberOfPages() - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n        return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n        const maxPageIndex = this.getNumberOfPages() - 1;\n        return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n        if (!this.pageSize) {\n            return 0;\n        }\n        return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n        // Current page needs to be updated to reflect the new page size. Navigate to the page\n        // containing the previous page's first item.\n        const startIndex = this.pageIndex * this.pageSize;\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n        this.pageSize = pageSize;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n        return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n        return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n        if (!this._initialized) {\n            return;\n        }\n        // If no page size is provided, use the first page size option or the default page size.\n        if (!this.pageSize) {\n            this._pageSize =\n                this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n        }\n        this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n        if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n            this._displayedPageSizeOptions.push(this.pageSize);\n        }\n        // Sort the numbers using a number-specific sort function.\n        this._displayedPageSizeOptions.sort((a, b) => a - b);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n        this.page.emit({\n            previousPageIndex,\n            pageIndex: this.pageIndex,\n            pageSize: this.pageSize,\n            length: this.length,\n        });\n    }\n}\n_MatPaginatorBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatPaginatorBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatPaginatorBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatPaginatorBase, inputs: { color: \"color\", pageIndex: \"pageIndex\", length: \"length\", pageSize: \"pageSize\", pageSizeOptions: \"pageSizeOptions\", hidePageSize: \"hidePageSize\", showFirstLastButtons: \"showFirstLastButtons\", selectConfig: \"selectConfig\" }, outputs: { page: \"page\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatPaginatorBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: MatPaginatorIntl }, { type: i0.ChangeDetectorRef }, { type: undefined }]; }, propDecorators: { color: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input\n            }], length: [{\n                type: Input\n            }], pageSize: [{\n                type: Input\n            }], pageSizeOptions: [{\n                type: Input\n            }], hidePageSize: [{\n                type: Input\n            }], showFirstLastButtons: [{\n                type: Input\n            }], selectConfig: [{\n                type: Input\n            }], page: [{\n                type: Output\n            }] } });\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator extends _MatPaginatorBase {\n    constructor(intl, changeDetectorRef, defaults) {\n        super(intl, changeDetectorRef, defaults);\n        /** ID for the DOM node containing the paginator's items per page label. */\n        this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n        this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n}\nMatPaginator.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginator, deps: [{ token: MatPaginatorIntl }, { token: i0.ChangeDetectorRef }, { token: MAT_PAGINATOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatPaginator.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatPaginator, selector: \"mat-paginator\", inputs: { disabled: \"disabled\" }, host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-paginator\" }, exportAs: [\"matPaginator\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i3.MatIconButton, selector: \"button[mat-icon-button]\", inputs: [\"disabled\", \"disableRipple\", \"color\"], exportAs: [\"matButton\"] }, { kind: \"component\", type: i4.MatFormField, selector: \"mat-form-field\", inputs: [\"hideRequiredMarker\", \"color\", \"floatLabel\", \"appearance\", \"subscriptSizing\", \"hintLabel\"], exportAs: [\"matFormField\"] }, { kind: \"component\", type: i5.MatSelect, selector: \"mat-select\", inputs: [\"disabled\", \"disableRipple\", \"tabIndex\", \"hideSingleSelectionIndicator\"], exportAs: [\"matSelect\"] }, { kind: \"component\", type: i6.MatOption, selector: \"mat-option\", exportAs: [\"matOption\"] }, { kind: \"directive\", type: i7.MatTooltip, selector: \"[matTooltip]\", exportAs: [\"matTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginator, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-paginator', exportAs: 'matPaginator', inputs: ['disabled'], host: {\n                        'class': 'mat-mdc-paginator',\n                        'role': 'group',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\" viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"] }]\n        }], ctorParameters: function () { return [{ type: MatPaginatorIntl }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatPaginatorModule {\n}\nMatPaginatorModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatPaginatorModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorModule, declarations: [MatPaginator], imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule], exports: [MatPaginator] });\nMatPaginatorModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorModule, providers: [MAT_PAGINATOR_INTL_PROVIDER], imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule],\n                    exports: [MatPaginator],\n                    declarations: [MatPaginator],\n                    providers: [MAT_PAGINATOR_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent, _MatPaginatorBase };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC/L,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,wBAAwB;AACxE,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,EAAE,MAAM,8BAA8B;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;EAAA;IAkCwG1B,EAAE,oCAoRysC;IApR3sCA,EAAE,UAoRqvC;IApRvvCA,EAAE,eAoRkwC;EAAA;EAAA;IAAA;IApRpwCA,EAAE,uCAoRwsC;IApR1sCA,EAAE,aAoRqvC;IApRvvCA,EAAE,gDAoRqvC;EAAA;AAAA;AAAA;EAAA;IAAA,YApRvvCA,EAAE;IAAFA,EAAE,wCAoRmuB;IApRruBA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAoRmhC,oCAA6B;IAAA,EAAE;IApRpjCA,EAAE,iGAoRkwC;IApRpwCA,EAAE,eAoRyxC;EAAA;EAAA;IAAA,eApR3xCA,EAAE;IAAFA,EAAE,sDAoRipB;IApRnpBA,EAAE,aAoRwxB;IApR1xBA,EAAE,qCAoRwxB;IApR1xBA,EAAE,aAoR2qC;IApR7qCA,EAAE,wDAoR2qC;EAAA;AAAA;AAAA;EAAA;IApR7qCA,EAAE,6BAoR+6C;IApRj7CA,EAAE,UAoR27C;IApR77CA,EAAE,eAoRi8C;EAAA;EAAA;IAAA,eApRn8CA,EAAE;IAAFA,EAAE,aAoR27C;IApR77CA,EAAE,mCAoR27C;EAAA;AAAA;AAAA;EAAA;IApR77CA,EAAE,6BAoRwY;IApR1YA,EAAE,UAoR0gB;IApR5gBA,EAAE,eAoRghB;IApRlhBA,EAAE,wFAoRkzC;IApRpzCA,EAAE,kEAoRi8C;IApRn8CA,EAAE,eAoR68C;EAAA;EAAA;IAAA,eApR/8CA,EAAE;IAAFA,EAAE,aAoR4d;IApR9dA,EAAE,qDAoR4d;IApR9dA,EAAE,aAoR0gB;IApR5gBA,EAAE,6DAoR0gB;IApR5gBA,EAAE,aAoR+lB;IApRjmBA,EAAE,gEAoR+lB;IApRjmBA,EAAE,aAoR46C;IApR96CA,EAAE,iEAoR46C;EAAA;AAAA;AAAA;EAAA;IAAA,aApR96CA,EAAE;IAAFA,EAAE,gCAoRwnE;IApR1nEA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAoRiyD,kBAAW;IAAA,EAAE;IApRhzDA,EAAE,iBAoRktE;IApRptEA,EAAE,4BAoRktE;IApRptEA,EAAE,yBAoRsyE;IApRxyEA,EAAE,eAoRszE;EAAA;EAAA;IAAA,eApRxzEA,EAAE;IAAFA,EAAE,sDAoR65D;IApR/5DA,EAAE,uDAoRw2D;EAAA;AAAA;AAAA;EAAA;IAAA,aApR12DA,EAAE;IAAFA,EAAE;IAAFA,EAAE,kBAoRw/H;IApR1/HA,EAAE,gCAoRw/H;IApR1/HA,EAAE;MAAFA,EAAE;MAAA,gBAAFA,EAAE;MAAA,OAAFA,EAAE,aAoR4qH,kBAAU;IAAA,EAAE;IApR1rHA,EAAE,iBAoRklI;IApRplIA,EAAE,4BAoRklI;IApRplIA,EAAE,yBAoRsqI;IApRxqIA,EAAE,eAoRsrI;EAAA;EAAA;IAAA,eApRxrIA,EAAE;IAAFA,EAAE,qDAoRqyH;IApRvyHA,EAAE,sDAoRivH;EAAA;AAAA;AAlT31H,MAAM2B,gBAAgB,CAAC;EACnBC,WAAW,GAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAIf,OAAO,EAAE;IAC5B;IACA,IAAI,CAACgB,iBAAiB,GAAG,iBAAiB;IAC1C;IACA,IAAI,CAACC,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACC,iBAAiB,GAAG,eAAe;IACxC;IACA,IAAI,CAACC,cAAc,GAAG,YAAY;IAClC;IACA,IAAI,CAACC,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,KAAK;MAC7C,IAAIA,MAAM,IAAI,CAAC,IAAID,QAAQ,IAAI,CAAC,EAAE;QAC9B,OAAQ,QAAOC,MAAO,EAAC;MAC3B;MACAA,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACF,MAAM,EAAE,CAAC,CAAC;MAC5B,MAAMG,UAAU,GAAGL,IAAI,GAAGC,QAAQ;MAClC;MACA,MAAMK,QAAQ,GAAGD,UAAU,GAAGH,MAAM,GAAGC,IAAI,CAACI,GAAG,CAACF,UAAU,GAAGJ,QAAQ,EAAEC,MAAM,CAAC,GAAGG,UAAU,GAAGJ,QAAQ;MACtG,OAAQ,GAAEI,UAAU,GAAG,CAAE,MAAKC,QAAS,OAAMJ,MAAO,EAAC;IACzD,CAAC;EACL;AACJ;AACAX,gBAAgB,CAACiB,IAAI;EAAA,iBAA6FjB,gBAAgB;AAAA,CAAoD;AACtLA,gBAAgB,CAACkB,KAAK,kBADkF7C,EAAE;EAAA,OACY2B,gBAAgB;EAAA,SAAhBA,gBAAgB;EAAA,YAAc;AAAM,EAAG;AAC7J;EAAA,mDAFwG3B,EAAE,mBAEV2B,gBAAgB,EAAc,CAAC;IACnHmB,IAAI,EAAE7C,UAAU;IAChB8C,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASC,mCAAmC,CAACC,UAAU,EAAE;EACrD,OAAOA,UAAU,IAAI,IAAIvB,gBAAgB,EAAE;AAC/C;AACA;AACA,MAAMwB,2BAA2B,GAAG;EAChC;EACAC,OAAO,EAAEzB,gBAAgB;EACzB0B,IAAI,EAAE,CAAC,CAAC,IAAInD,QAAQ,EAAE,EAAE,IAAIC,QAAQ,EAAE,EAAEwB,gBAAgB,CAAC,CAAC;EAC1D2B,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;AAEhB;AACA,MAAMC,6BAA6B,GAAG,IAAIrD,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA,MAAMsD,sBAAsB,GAAGpC,aAAa,CAACC,gBAAgB,CAAC,MAAM,EACnE,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA,MAAMoC,iBAAiB,SAASD,sBAAsB,CAAC;EACnD;EACA,IAAIE,SAAS,GAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAAS,CAACE,KAAK,EAAE;IACjB,IAAI,CAACD,UAAU,GAAGtB,IAAI,CAACC,GAAG,CAAChB,oBAAoB,CAACsC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACC,kBAAkB,CAACC,YAAY,EAAE;EAC1C;EACA;EACA,IAAI1B,MAAM,GAAG;IACT,OAAO,IAAI,CAAC2B,OAAO;EACvB;EACA,IAAI3B,MAAM,CAACwB,KAAK,EAAE;IACd,IAAI,CAACG,OAAO,GAAGzC,oBAAoB,CAACsC,KAAK,CAAC;IAC1C,IAAI,CAACC,kBAAkB,CAACC,YAAY,EAAE;EAC1C;EACA;EACA,IAAI3B,QAAQ,GAAG;IACX,OAAO,IAAI,CAAC6B,SAAS;EACzB;EACA,IAAI7B,QAAQ,CAACyB,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAG3B,IAAI,CAACC,GAAG,CAAChB,oBAAoB,CAACsC,KAAK,CAAC,EAAE,CAAC,CAAC;IACzD,IAAI,CAACK,+BAA+B,EAAE;EAC1C;EACA;EACA,IAAIC,eAAe,GAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAe,CAACN,KAAK,EAAE;IACvB,IAAI,CAACO,gBAAgB,GAAG,CAACP,KAAK,IAAI,EAAE,EAAEQ,GAAG,CAACC,CAAC,IAAI/C,oBAAoB,CAAC+C,CAAC,CAAC,CAAC;IACvE,IAAI,CAACJ,+BAA+B,EAAE;EAC1C;EACA;EACA,IAAIK,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAACV,KAAK,EAAE;IACpB,IAAI,CAACW,aAAa,GAAGhD,qBAAqB,CAACqC,KAAK,CAAC;EACrD;EACA;EACA,IAAIY,oBAAoB,GAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoB,CAACZ,KAAK,EAAE;IAC5B,IAAI,CAACa,qBAAqB,GAAGlD,qBAAqB,CAACqC,KAAK,CAAC;EAC7D;EACAlC,WAAW,CAACgD,KAAK,EAAEb,kBAAkB,EAAEc,QAAQ,EAAE;IAC7C,KAAK,EAAE;IACP,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACb,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACF,UAAU,GAAG,CAAC;IACnB,IAAI,CAACI,OAAO,GAAG,CAAC;IAChB,IAAI,CAACI,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACG,YAAY,GAAG,CAAC,CAAC;IACtB;IACA,IAAI,CAAC1C,IAAI,GAAG,IAAI/B,YAAY,EAAE;IAC9B,IAAI,CAAC0E,YAAY,GAAGH,KAAK,CAAC/C,OAAO,CAACmD,SAAS,CAAC,MAAM,IAAI,CAACjB,kBAAkB,CAACC,YAAY,EAAE,CAAC;IACzF,IAAIa,QAAQ,EAAE;MACV,MAAM;QAAExC,QAAQ;QAAE+B,eAAe;QAAEI,YAAY;QAAEE;MAAqB,CAAC,GAAGG,QAAQ;MAClF,IAAIxC,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAAC6B,SAAS,GAAG7B,QAAQ;MAC7B;MACA,IAAI+B,eAAe,IAAI,IAAI,EAAE;QACzB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;MAC3C;MACA,IAAII,YAAY,IAAI,IAAI,EAAE;QACtB,IAAI,CAACC,aAAa,GAAGD,YAAY;MACrC;MACA,IAAIE,oBAAoB,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACC,qBAAqB,GAAGD,oBAAoB;MACrD;IACJ;EACJ;EACAO,QAAQ,GAAG;IACP,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACf,+BAA+B,EAAE;IACtC,IAAI,CAACgB,gBAAgB,EAAE;EAC3B;EACAC,WAAW,GAAG;IACV,IAAI,CAACL,YAAY,CAACM,WAAW,EAAE;EACnC;EACA;EACAC,QAAQ,GAAG;IACP,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC5B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC6B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAE,YAAY,GAAG;IACX,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAAC5B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC6B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAI,SAAS,GAAG;IACR;IACA,IAAI,CAAC,IAAI,CAACD,eAAe,EAAE,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAAC5B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC6B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAK,QAAQ,GAAG;IACP;IACA,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC5B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACkC,gBAAgB,EAAE,GAAG,CAAC;IAC5C,IAAI,CAACL,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAG,eAAe,GAAG;IACd,OAAO,IAAI,CAAC/B,SAAS,IAAI,CAAC,IAAI,IAAI,CAACvB,QAAQ,IAAI,CAAC;EACpD;EACA;EACAkD,WAAW,GAAG;IACV,MAAMQ,YAAY,GAAG,IAAI,CAACD,gBAAgB,EAAE,GAAG,CAAC;IAChD,OAAO,IAAI,CAAClC,SAAS,GAAGmC,YAAY,IAAI,IAAI,CAAC1D,QAAQ,IAAI,CAAC;EAC9D;EACA;EACAyD,gBAAgB,GAAG;IACf,IAAI,CAAC,IAAI,CAACzD,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAOE,IAAI,CAACyD,IAAI,CAAC,IAAI,CAAC1D,MAAM,GAAG,IAAI,CAACD,QAAQ,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4D,eAAe,CAAC5D,QAAQ,EAAE;IACtB;IACA;IACA,MAAMI,UAAU,GAAG,IAAI,CAACmB,SAAS,GAAG,IAAI,CAACvB,QAAQ;IACjD,MAAMmD,iBAAiB,GAAG,IAAI,CAAC5B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAGrB,IAAI,CAAC2D,KAAK,CAACzD,UAAU,GAAGJ,QAAQ,CAAC,IAAI,CAAC;IACvD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACoD,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAW,oBAAoB,GAAG;IACnB,OAAO,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACb,WAAW,EAAE;EAC/C;EACA;EACAc,wBAAwB,GAAG;IACvB,OAAO,IAAI,CAACD,QAAQ,IAAI,CAAC,IAAI,CAACT,eAAe,EAAE;EACnD;EACA;AACJ;AACA;AACA;EACIxB,+BAA+B,GAAG;IAC9B,IAAI,CAAC,IAAI,CAACe,YAAY,EAAE;MACpB;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAAC7C,QAAQ,EAAE;MAChB,IAAI,CAAC6B,SAAS,GACV,IAAI,CAACE,eAAe,CAAC9B,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC8B,eAAe,CAAC,CAAC,CAAC,GAAGb,iBAAiB;IACtF;IACA,IAAI,CAAC+C,yBAAyB,GAAG,IAAI,CAAClC,eAAe,CAACmC,KAAK,EAAE;IAC7D,IAAI,IAAI,CAACD,yBAAyB,CAACE,OAAO,CAAC,IAAI,CAACnE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACiE,yBAAyB,CAACG,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC;IACtD;IACA;IACA,IAAI,CAACiE,yBAAyB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACpD,IAAI,CAAC7C,kBAAkB,CAACC,YAAY,EAAE;EAC1C;EACA;EACAyB,cAAc,CAACD,iBAAiB,EAAE;IAC9B,IAAI,CAACpD,IAAI,CAACyE,IAAI,CAAC;MACXrB,iBAAiB;MACjB5B,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBvB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;AACJ;AACAqB,iBAAiB,CAACf,IAAI;EA9OkF5C,EAAE;AAAA,CA8OoF;AAC9L2D,iBAAiB,CAACmD,IAAI,kBA/OkF9G,EAAE;EAAA,MA+OH2D,iBAAiB;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WA/OhB3D,EAAE;AAAA,EA+O2T;AACra;EAAA,mDAhPwGA,EAAE,mBAgPV2D,iBAAiB,EAAc,CAAC;IACpHb,IAAI,EAAExC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEwC,IAAI,EAAEnB;IAAiB,CAAC,EAAE;MAAEmB,IAAI,EAAE9C,EAAE,CAAC+G;IAAkB,CAAC,EAAE;MAAEjE,IAAI,EAAEkE;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEC,KAAK,EAAE,CAAC;MACjJnE,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAEqD,SAAS,EAAE,CAAC;MACZd,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAE+B,MAAM,EAAE,CAAC;MACTQ,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAE8B,QAAQ,EAAE,CAAC;MACXS,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAE6D,eAAe,EAAE,CAAC;MAClBtB,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAEiE,YAAY,EAAE,CAAC;MACf1B,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAEmE,oBAAoB,EAAE,CAAC;MACvB5B,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAEuE,YAAY,EAAE,CAAC;MACfhC,IAAI,EAAEvC;IACV,CAAC,CAAC;IAAE6B,IAAI,EAAE,CAAC;MACPU,IAAI,EAAEtC;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,IAAI0G,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASxD,iBAAiB,CAAC;EACzC/B,WAAW,CAACwF,IAAI,EAAEC,iBAAiB,EAAExC,QAAQ,EAAE;IAC3C,KAAK,CAACuC,IAAI,EAAEC,iBAAiB,EAAExC,QAAQ,CAAC;IACxC;IACA,IAAI,CAACyC,gBAAgB,GAAI,iCAAgCJ,YAAY,EAAG,EAAC;IACzE,IAAI,CAACK,oBAAoB,GAAG1C,QAAQ,EAAE2C,mBAAmB,IAAI,SAAS;EAC1E;AACJ;AACAL,YAAY,CAACvE,IAAI;EAAA,iBAA6FuE,YAAY,EAnRlBnH,EAAE,mBAmRkC2B,gBAAgB,GAnRpD3B,EAAE,mBAmR+DA,EAAE,CAAC+G,iBAAiB,GAnRrF/G,EAAE,mBAmRgGyD,6BAA6B;AAAA,CAA4D;AACnS0D,YAAY,CAACM,IAAI,kBApRuFzH,EAAE;EAAA,MAoRRmH,YAAY;EAAA;EAAA,oBAA6F,OAAO;EAAA;IAAA;EAAA;EAAA;EAAA,WApR1GnH,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,4BAoRgR;MApRlRA,EAAE,2DAoR68C;MApR/8CA,EAAE,4BAoRogD;MApRtgDA,EAAE,UAoRopD;MApRtpDA,EAAE,eAoR0pD;MApR5pDA,EAAE,iEAoRu0E;MApRz0EA,EAAE,+BAoRiwF;MApRnwFA,EAAE;QAAA,OAoR+8E,kBAAc;MAAA,EAAE;MApRj+EA,EAAE,iBAoR21F;MApR71FA,EAAE,4BAoR21F;MApR71FA,EAAE,wBAoRk6F;MApRp6FA,EAAE,eAoRk7F;MApRp7FA,EAAE,kBAoRq2G;MApRv2GA,EAAE,gCAoRq2G;MApRv2GA,EAAE;QAAA,OAoRukG,cAAU;MAAA,EAAE;MApRrlGA,EAAE,iBAoR+7G;MApRj8GA,EAAE,6BAoR+7G;MApRj8GA,EAAE,0BAoRugH;MApRzgHA,EAAE,eAoRuhH;MApRzhHA,EAAE,oEAoRusI;MApRzsIA,EAAE,eAoRmtI;IAAA;IAAA;MApRrtIA,EAAE,aAoRqY;MApRvYA,EAAE,sCAoRqY;MApRvYA,EAAE,aAoRopD;MApRtpDA,EAAE,+FAoRopD;MApRtpDA,EAAE,aAoRqnE;MApRvnEA,EAAE,6CAoRqnE;MApRvnEA,EAAE,aAoRolF;MApRtlFA,EAAE,sDAoRolF;MApRtlFA,EAAE,uDAoR4hF;MApR9hFA,EAAE,aAoRgsG;MApRlsGA,EAAE,kDAoRgsG;MApRlsGA,EAAE,mDAoR4oG;MApR9oGA,EAAE,aAoRq/H;MApRv/HA,EAAE,6CAoRq/H;IAAA;EAAA;EAAA,eAA82CF,EAAE,CAAC4H,OAAO,EAAmH5H,EAAE,CAAC6H,IAAI,EAA6F5G,EAAE,CAAC6G,aAAa,EAA6IlG,EAAE,CAACmG,YAAY,EAA4L5G,EAAE,CAAC6G,SAAS,EAAmKzG,EAAE,CAAC0G,SAAS,EAAgF5G,EAAE,CAAC6G,UAAU;EAAA;EAAA;EAAA;AAAA,EAAwJ;AACt8M;EAAA,mDArRwGhI,EAAE,mBAqRVmH,YAAY,EAAc,CAAC;IAC/GrE,IAAI,EAAErC,SAAS;IACfsC,IAAI,EAAE,CAAC;MAAEkF,QAAQ,EAAE,eAAe;MAAEC,QAAQ,EAAE,cAAc;MAAEC,MAAM,EAAE,CAAC,UAAU,CAAC;MAAEC,IAAI,EAAE;QAC9E,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE;MACZ,CAAC;MAAEC,eAAe,EAAE3H,uBAAuB,CAAC4H,MAAM;MAAEC,aAAa,EAAE5H,iBAAiB,CAAC6H,IAAI;MAAEC,QAAQ,EAAE,0gIAA0gI;MAAEC,MAAM,EAAE,CAAC,mkCAAmkC;IAAE,CAAC;EAC5sK,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE5F,IAAI,EAAEnB;IAAiB,CAAC,EAAE;MAAEmB,IAAI,EAAE9C,EAAE,CAAC+G;IAAkB,CAAC,EAAE;MAAEjE,IAAI,EAAEkE,SAAS;MAAE2B,UAAU,EAAE,CAAC;QAC1H7F,IAAI,EAAE5C;MACV,CAAC,EAAE;QACC4C,IAAI,EAAElC,MAAM;QACZmC,IAAI,EAAE,CAACU,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmF,kBAAkB,CAAC;AAEzBA,kBAAkB,CAAChG,IAAI;EAAA,iBAA6FgG,kBAAkB;AAAA,CAAkD;AACxLA,kBAAkB,CAACC,IAAI,kBA5SiF7I,EAAE;EAAA,MA4SW4I;AAAkB,EAAuI;AAC9QA,kBAAkB,CAACE,IAAI,kBA7SiF9I,EAAE;EAAA,WA6S0C,CAACmD,2BAA2B,CAAC;EAAA,UAAYpD,YAAY,EAAEiB,eAAe,EAAEE,eAAe,EAAEE,gBAAgB;AAAA,EAAI;AACjQ;EAAA,mDA9SwGpB,EAAE,mBA8SV4I,kBAAkB,EAAc,CAAC;IACrH9F,IAAI,EAAEjC,QAAQ;IACdkC,IAAI,EAAE,CAAC;MACCgG,OAAO,EAAE,CAAChJ,YAAY,EAAEiB,eAAe,EAAEE,eAAe,EAAEE,gBAAgB,CAAC;MAC3E4H,OAAO,EAAE,CAAC7B,YAAY,CAAC;MACvB8B,YAAY,EAAE,CAAC9B,YAAY,CAAC;MAC5B+B,SAAS,EAAE,CAAC/F,2BAA2B;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASM,6BAA6B,EAAEN,2BAA2B,EAAEF,mCAAmC,EAAEkE,YAAY,EAAExF,gBAAgB,EAAEiH,kBAAkB,EAAEpF,SAAS,EAAEG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}