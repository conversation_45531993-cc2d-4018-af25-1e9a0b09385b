<div class="theme-selector">
  <h3>
    <mat-icon>palette</mat-icon>
    {{ t.themes.title }}
  </h3>

  <div class="theme-grid">
    <div
      *ngFor="let theme of availableThemes"
      class="theme-option"
      [class.selected]="theme.name === currentTheme.name"
      (click)="selectTheme(theme)">

      <div class="theme-preview">
        <div class="color-bar primary" [style.background-color]="theme.colors.primary"></div>
        <div class="color-bar accent" [style.background-color]="theme.colors.accent"></div>
        <div class="color-bar surface" [style.background-color]="theme.colors.surface"></div>
      </div>

      <div class="theme-info">
        <span class="theme-name">{{ getThemeDisplayName(theme.name) }}</span>
        <mat-icon *ngIf="theme.name === currentTheme.name" class="selected-icon">check_circle</mat-icon>
      </div>
    </div>
  </div>

  <mat-divider></mat-divider>

  <div class="theme-actions">
    <button mat-stroked-button (click)="exportTheme()">
      <mat-icon>download</mat-icon>
      Експортирай текущата тема
    </button>

    <input
      type="file"
      accept=".json"
      (change)="onFileSelected($event)"
      #fileInput
      style="display: none;">

    <button mat-stroked-button (click)="fileInput.click()">
      <mat-icon>upload</mat-icon>
      Импортирай тема
    </button>
  </div>
</div>
