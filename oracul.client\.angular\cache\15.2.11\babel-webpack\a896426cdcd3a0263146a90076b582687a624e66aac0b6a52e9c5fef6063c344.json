{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  constructor(config) {\n    this.appearance = config?.appearance || 'raised';\n  }\n}\nMatCard.ɵfac = function MatCard_Factory(t) {\n  return new (t || MatCard)(i0.ɵɵdirectiveInject(MAT_CARD_CONFIG, 8));\n};\nMatCard.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCard,\n  selectors: [[\"mat-card\"]],\n  hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n  hostVars: 4,\n  hostBindings: function MatCard_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n    }\n  },\n  inputs: {\n    appearance: \"appearance\"\n  },\n  exportAs: [\"matCard\"],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatCard_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mat-mdc-card{position:relative;border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px));background-color:var(--mdc-elevated-card-container-color, transparent);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color, transparent)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px))}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width, 1px);border-style:solid;border-color:var(--mdc-outlined-card-outline-color, transparent)}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mat-mdc-card{position:relative;border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px));background-color:var(--mdc-elevated-card-container-color, transparent);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color, transparent)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px))}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width, 1px);border-style:solid;border-color:var(--mdc-outlined-card-outline-color, transparent)}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_CARD_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {}\nMatCardTitle.ɵfac = function MatCardTitle_Factory(t) {\n  return new (t || MatCardTitle)();\n};\nMatCardTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardTitle,\n  selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-title\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {}\nMatCardTitleGroup.ɵfac = function MatCardTitleGroup_Factory(t) {\n  return new (t || MatCardTitleGroup)();\n};\nMatCardTitleGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCardTitleGroup,\n  selectors: [[\"mat-card-title-group\"]],\n  hostAttrs: [1, \"mat-mdc-card-title-group\"],\n  ngContentSelectors: _c2,\n  decls: 4,\n  vars: 0,\n  template: function MatCardTitleGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵelementStart(0, \"div\");\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {}\nMatCardContent.ɵfac = function MatCardContent_Factory(t) {\n  return new (t || MatCardContent)();\n};\nMatCardContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardContent,\n  selectors: [[\"mat-card-content\"]],\n  hostAttrs: [1, \"mat-mdc-card-content\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {}\nMatCardSubtitle.ɵfac = function MatCardSubtitle_Factory(t) {\n  return new (t || MatCardSubtitle)();\n};\nMatCardSubtitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardSubtitle,\n  selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-subtitle\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  constructor() {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    this.align = 'start';\n  }\n}\nMatCardActions.ɵfac = function MatCardActions_Factory(t) {\n  return new (t || MatCardActions)();\n};\nMatCardActions.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardActions,\n  selectors: [[\"mat-card-actions\"]],\n  hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n  hostVars: 2,\n  hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\"\n  },\n  exportAs: [\"matCardActions\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {}\nMatCardHeader.ɵfac = function MatCardHeader_Factory(t) {\n  return new (t || MatCardHeader)();\n};\nMatCardHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCardHeader,\n  selectors: [[\"mat-card-header\"]],\n  hostAttrs: [1, \"mat-mdc-card-header\"],\n  ngContentSelectors: _c4,\n  decls: 4,\n  vars: 0,\n  consts: [[1, \"mat-mdc-card-header-text\"]],\n  template: function MatCardHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {}\nMatCardFooter.ɵfac = function MatCardFooter_Factory(t) {\n  return new (t || MatCardFooter)();\n};\nMatCardFooter.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardFooter,\n  selectors: [[\"mat-card-footer\"]],\n  hostAttrs: [1, \"mat-mdc-card-footer\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      }\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {}\nMatCardImage.ɵfac = function MatCardImage_Factory(t) {\n  return new (t || MatCardImage)();\n};\nMatCardImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardImage,\n  selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {}\nMatCardSmImage.ɵfac = function MatCardSmImage_Factory(t) {\n  return new (t || MatCardSmImage)();\n};\nMatCardSmImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardSmImage,\n  selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {}\nMatCardMdImage.ɵfac = function MatCardMdImage_Factory(t) {\n  return new (t || MatCardMdImage)();\n};\nMatCardMdImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardMdImage,\n  selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {}\nMatCardLgImage.ɵfac = function MatCardLgImage_Factory(t) {\n  return new (t || MatCardLgImage)();\n};\nMatCardLgImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardLgImage,\n  selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {}\nMatCardXlImage.ɵfac = function MatCardXlImage_Factory(t) {\n  return new (t || MatCardXlImage)();\n};\nMatCardXlImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardXlImage,\n  selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {}\nMatCardAvatar.ɵfac = function MatCardAvatar_Factory(t) {\n  return new (t || MatCardAvatar)();\n};\nMatCardAvatar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardAvatar,\n  selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-avatar\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {}\nMatCardModule.ɵfac = function MatCardModule_Factory(t) {\n  return new (t || MatCardModule)();\n};\nMatCardModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatCardModule\n});\nMatCardModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, CommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule],\n      exports: [CARD_DIRECTIVES, MatCommonModule],\n      declarations: CARD_DIRECTIVES\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Optional", "Input", "Directive", "NgModule", "CommonModule", "MatCommonModule", "MAT_CARD_CONFIG", "MatCard", "constructor", "config", "appearance", "ɵfac", "ɵcmp", "type", "args", "selector", "host", "exportAs", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "undefined", "decorators", "MatCardTitle", "ɵdir", "MatCardTitleGroup", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardSubtitle", "MatCardActions", "align", "MatCardHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCardImage", "MatCardSmImage", "MatCardMdImage", "MatCardLgImage", "MatCardXlImage", "MatCardAvatar", "CARD_DIRECTIVES", "MatCardModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n    constructor(config) {\n        this.appearance = config?.appearance || 'raised';\n    }\n}\nMatCard.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCard, deps: [{ token: MAT_CARD_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatCard.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCard, selector: \"mat-card\", inputs: { appearance: \"appearance\" }, host: { properties: { \"class.mat-mdc-card-outlined\": \"appearance === \\\"outlined\\\"\", \"class.mdc-card--outlined\": \"appearance === \\\"outlined\\\"\" }, classAttribute: \"mat-mdc-card mdc-card\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mat-mdc-card{position:relative;border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px));background-color:var(--mdc-elevated-card-container-color, transparent);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color, transparent)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px))}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width, 1px);border-style:solid;border-color:var(--mdc-outlined-card-outline-color, transparent)}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', host: {\n                        'class': 'mat-mdc-card mdc-card',\n                        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n                        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n                    }, exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mat-mdc-card{position:relative;border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px));background-color:var(--mdc-elevated-card-container-color, transparent);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color, transparent)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape, var(--mdc-shape-medium, 4px))}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width, 1px);border-style:solid;border-color:var(--mdc-outlined-card-outline-color, transparent)}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_CARD_CONFIG]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { appearance: [{\n                type: Input\n            }] } });\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n}\nMatCardTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardTitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardTitle, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-mdc-card-title\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: { 'class': 'mat-mdc-card-title' },\n                }]\n        }] });\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n}\nMatCardTitleGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatCardTitleGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardTitleGroup, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-mdc-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-title-group' }, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n}\nMatCardContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardContent, selector: \"mat-card-content\", host: { classAttribute: \"mat-mdc-card-content\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content',\n                    host: { 'class': 'mat-mdc-card-content' },\n                }]\n        }] });\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n}\nMatCardSubtitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardSubtitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardSubtitle, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-mdc-card-subtitle\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: { 'class': 'mat-mdc-card-subtitle' },\n                }]\n        }] });\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n    constructor() {\n        // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n        // as to not conflict with the native `align` attribute.\n        /** Position of the actions inside the card. */\n        this.align = 'start';\n    }\n}\nMatCardActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardActions.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardActions, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-card-actions mdc-card__actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-mdc-card-actions mdc-card__actions',\n                        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n}\nMatCardHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatCardHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardHeader, selector: \"mat-card-header\", host: { classAttribute: \"mat-mdc-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-header' }, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n}\nMatCardFooter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardFooter.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardFooter, selector: \"mat-card-footer\", host: { classAttribute: \"mat-mdc-card-footer\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-mdc-card-footer' },\n                }]\n        }] });\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n}\nMatCardImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardImage, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-mdc-card-image mdc-card__media\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-mdc-card-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n}\nMatCardSmImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardSmImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardSmImage, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-mdc-card-sm-image mdc-card__media\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-mdc-card-sm-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n}\nMatCardMdImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardMdImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardMdImage, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-mdc-card-md-image mdc-card__media\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-mdc-card-md-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n}\nMatCardLgImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardLgImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardLgImage, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-mdc-card-lg-image mdc-card__media\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-mdc-card-lg-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n}\nMatCardXlImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardXlImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardXlImage, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-mdc-card-xl-image mdc-card__media\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-mdc-card-xl-image mdc-card__media' },\n                }]\n        }] });\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n}\nMatCardAvatar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardAvatar.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCardAvatar, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-mdc-card-avatar\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-mdc-card-avatar' },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CARD_DIRECTIVES = [\n    MatCard,\n    MatCardActions,\n    MatCardAvatar,\n    MatCardContent,\n    MatCardFooter,\n    MatCardHeader,\n    MatCardImage,\n    MatCardLgImage,\n    MatCardMdImage,\n    MatCardSmImage,\n    MatCardSubtitle,\n    MatCardTitle,\n    MatCardTitleGroup,\n    MatCardXlImage,\n];\nclass MatCardModule {\n}\nMatCardModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatCardModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardModule, declarations: [MatCard,\n        MatCardActions,\n        MatCardAvatar,\n        MatCardContent,\n        MatCardFooter,\n        MatCardHeader,\n        MatCardImage,\n        MatCardLgImage,\n        MatCardMdImage,\n        MatCardSmImage,\n        MatCardSubtitle,\n        MatCardTitle,\n        MatCardTitleGroup,\n        MatCardXlImage], imports: [MatCommonModule, CommonModule], exports: [MatCard,\n        MatCardActions,\n        MatCardAvatar,\n        MatCardContent,\n        MatCardFooter,\n        MatCardHeader,\n        MatCardImage,\n        MatCardLgImage,\n        MatCardMdImage,\n        MatCardSmImage,\n        MatCardSubtitle,\n        MatCardTitle,\n        MatCardTitleGroup,\n        MatCardXlImage, MatCommonModule] });\nMatCardModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, CommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule],\n                    exports: [CARD_DIRECTIVES, MatCommonModule],\n                    declarations: CARD_DIRECTIVES,\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnJ,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,MAAMC,eAAe,GAAG,IAAIX,cAAc,CAAC,iBAAiB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,OAAO,CAAC;EACVC,WAAW,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,UAAU,GAAGD,MAAM,EAAEC,UAAU,IAAI,QAAQ;EACpD;AACJ;AACAH,OAAO,CAACI,IAAI;EAAA,iBAA6FJ,OAAO,EAAjBb,EAAE,mBAAiCY,eAAe;AAAA,CAA4D;AAC7MC,OAAO,CAACK,IAAI,kBADmFlB,EAAE;EAAA,MACJa,OAAO;EAAA;EAAA;EAAA;EAAA;IAAA;MADLb,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBACsU;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EAAq5J;AAC5zK;EAAA,mDAF+FA,EAAE,mBAEDa,OAAO,EAAc,CAAC;IAC1GM,IAAI,EAAEjB,SAAS;IACfkB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEC,IAAI,EAAE;QACzB,OAAO,EAAE,uBAAuB;QAChC,+BAA+B,EAAE,2BAA2B;QAC5D,4BAA4B,EAAE;MAClC,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEC,aAAa,EAAErB,iBAAiB,CAACsB,IAAI;MAAEC,eAAe,EAAEtB,uBAAuB,CAACuB,MAAM;MAAEC,QAAQ,EAAE,6BAA6B;MAAEC,MAAM,EAAE,CAAC,qyJAAqyJ;IAAE,CAAC;EACt9J,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DZ,IAAI,EAAEd,MAAM;QACZe,IAAI,EAAE,CAACR,eAAe;MAC1B,CAAC,EAAE;QACCO,IAAI,EAAEb;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEU,UAAU,EAAE,CAAC;MACzCG,IAAI,EAAEZ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,YAAY,CAAC;AAEnBA,YAAY,CAACf,IAAI;EAAA,iBAA6Fe,YAAY;AAAA,CAAmD;AAC7KA,YAAY,CAACC,IAAI,kBA5B8EjC,EAAE;EAAA,MA4BCgC,YAAY;EAAA;EAAA;AAAA,EAA+H;AAC7O;EAAA,mDA7B+FhC,EAAE,mBA6BDgC,YAAY,EAAc,CAAC;IAC/Gb,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,kDAAiD;MAC5DC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqB;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMY,iBAAiB,CAAC;AAExBA,iBAAiB,CAACjB,IAAI;EAAA,iBAA6FiB,iBAAiB;AAAA,CAAmD;AACvLA,iBAAiB,CAAChB,IAAI,kBA5CyElB,EAAE;EAAA,MA4CMkC,iBAAiB;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA5CzBlC,EAAE;MAAFA,EAAE,yBA4C+I;MA5CjJA,EAAE,gBA4CwT;MA5C1TA,EAAE,eA4CgU;MA5ClUA,EAAE,mBA4CqoB;MA5CvoBA,EAAE,mBA4CgqB;IAAA;EAAA;EAAA;EAAA;AAAA,EAAoG;AACr2B;EAAA,mDA7C+FA,EAAE,mBA6CDkC,iBAAiB,EAAc,CAAC;IACpHf,IAAI,EAAEjB,SAAS;IACfkB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEG,aAAa,EAAErB,iBAAiB,CAACsB,IAAI;MAAEC,eAAe,EAAEtB,uBAAuB,CAACuB,MAAM;MAAEL,IAAI,EAAE;QAAE,OAAO,EAAE;MAA2B,CAAC;MAAEM,QAAQ,EAAE;IAA2hB,CAAC;EAC5tB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,cAAc,CAAC;AAErBA,cAAc,CAAClB,IAAI;EAAA,iBAA6FkB,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACF,IAAI,kBA3D4EjC,EAAE;EAAA,MA2DGmC,cAAc;EAAA;EAAA;AAAA,EAAiG;AACnN;EAAA,mDA5D+FnC,EAAE,mBA4DDmC,cAAc,EAAc,CAAC;IACjHhB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,eAAe,CAAC;AAEtBA,eAAe,CAACnB,IAAI;EAAA,iBAA6FmB,eAAe;AAAA,CAAmD;AACnLA,eAAe,CAACH,IAAI,kBA7E2EjC,EAAE;EAAA,MA6EIoC,eAAe;EAAA;EAAA;AAAA,EAA2I;AAC/P;EAAA,mDA9E+FpC,EAAE,mBA8EDoC,eAAe,EAAc,CAAC;IAClHjB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,2DAA0D;MACrEC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwB;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,cAAc,CAAC;EACjBvB,WAAW,GAAG;IACV;IACA;IACA;IACA,IAAI,CAACwB,KAAK,GAAG,OAAO;EACxB;AACJ;AACAD,cAAc,CAACpB,IAAI;EAAA,iBAA6FoB,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACJ,IAAI,kBArG4EjC,EAAE;EAAA,MAqGGqC,cAAc;EAAA;EAAA;EAAA;EAAA;IAAA;MArGnBrC,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA,EAqG2Q;AAC5W;EAAA,mDAtG+FA,EAAE,mBAsGDqC,cAAc,EAAc,CAAC;IACjHlB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BE,QAAQ,EAAE,gBAAgB;MAC1BD,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wCAAwC,EAAE;MAC9C;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEgB,KAAK,EAAE,CAAC;MACtBnB,IAAI,EAAEZ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgC,aAAa,CAAC;AAEpBA,aAAa,CAACtB,IAAI;EAAA,iBAA6FsB,aAAa;AAAA,CAAmD;AAC/KA,aAAa,CAACrB,IAAI,kBA9H6ElB,EAAE;EAAA,MA8HEuC,aAAa;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA9HjBvC,EAAE;MAAFA,EAAE,gBA8H+L;MA9HjMA,EAAE,4BA8HyO;MA9H3OA,EAAE,mBA8HkZ;MA9HpZA,EAAE,eA8H0Z;MA9H5ZA,EAAE,mBA8Hqb;IAAA;EAAA;EAAA;EAAA;AAAA,EAAoG;AAC1nB;EAAA,mDA/H+FA,EAAE,mBA+HDuC,aAAa,EAAc,CAAC;IAChHpB,IAAI,EAAEjB,SAAS;IACfkB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEG,aAAa,EAAErB,iBAAiB,CAACsB,IAAI;MAAEC,eAAe,EAAEtB,uBAAuB,CAACuB,MAAM;MAAEL,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MAAEM,QAAQ,EAAE;IAAkU,CAAC;EACzf,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,aAAa,CAAC;AAEpBA,aAAa,CAACvB,IAAI;EAAA,iBAA6FuB,aAAa;AAAA,CAAmD;AAC/KA,aAAa,CAACP,IAAI,kBA7I6EjC,EAAE;EAAA,MA6IEwC,aAAa;EAAA;EAAA;AAAA,EAA+F;AAC/M;EAAA,mDA9I+FxC,EAAE,mBA8IDwC,aAAa,EAAc,CAAC;IAChHrB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,YAAY,CAAC;AAEnBA,YAAY,CAACxB,IAAI;EAAA,iBAA6FwB,YAAY;AAAA,CAAmD;AAC7KA,YAAY,CAACR,IAAI,kBAnK8EjC,EAAE;EAAA,MAmKCyC,YAAY;EAAA;EAAA;AAAA,EAA+H;AAC7O;EAAA,mDApK+FzC,EAAE,mBAoKDyC,YAAY,EAAc,CAAC;IAC/GtB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqC;IAC1D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMoB,cAAc,CAAC;AAErBA,cAAc,CAACzB,IAAI;EAAA,iBAA6FyB,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACT,IAAI,kBA/K4EjC,EAAE;EAAA,MA+KG0C,cAAc;EAAA;EAAA;AAAA,EAA0I;AAC5P;EAAA,mDAhL+F1C,EAAE,mBAgLD0C,cAAc,EAAc,CAAC;IACjHvB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMqB,cAAc,CAAC;AAErBA,cAAc,CAAC1B,IAAI;EAAA,iBAA6F0B,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACV,IAAI,kBA3L4EjC,EAAE;EAAA,MA2LG2C,cAAc;EAAA;EAAA;AAAA,EAA2I;AAC7P;EAAA,mDA5L+F3C,EAAE,mBA4LD2C,cAAc,EAAc,CAAC;IACjHxB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMsB,cAAc,CAAC;AAErBA,cAAc,CAAC3B,IAAI;EAAA,iBAA6F2B,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACX,IAAI,kBAvM4EjC,EAAE;EAAA,MAuMG4C,cAAc;EAAA;EAAA;AAAA,EAA0I;AAC5P;EAAA,mDAxM+F5C,EAAE,mBAwMD4C,cAAc,EAAc,CAAC;IACjHzB,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMuB,cAAc,CAAC;AAErBA,cAAc,CAAC5B,IAAI;EAAA,iBAA6F4B,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACZ,IAAI,kBAnN4EjC,EAAE;EAAA,MAmNG6C,cAAc;EAAA;EAAA;AAAA,EAA2I;AAC7P;EAAA,mDApN+F7C,EAAE,mBAoND6C,cAAc,EAAc,CAAC;IACjH1B,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,aAAa,CAAC;AAEpBA,aAAa,CAAC7B,IAAI;EAAA,iBAA6F6B,aAAa;AAAA,CAAmD;AAC/KA,aAAa,CAACb,IAAI,kBAvO6EjC,EAAE;EAAA,MAuOE8C,aAAa;EAAA;EAAA;AAAA,EAAkH;AAClO;EAAA,mDAxO+F9C,EAAE,mBAwOD8C,aAAa,EAAc,CAAC;IAChH3B,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,eAAe,GAAG,CACpBlC,OAAO,EACPwB,cAAc,EACdS,aAAa,EACbX,cAAc,EACdK,aAAa,EACbD,aAAa,EACbE,YAAY,EACZG,cAAc,EACdD,cAAc,EACdD,cAAc,EACdN,eAAe,EACfJ,YAAY,EACZE,iBAAiB,EACjBW,cAAc,CACjB;AACD,MAAMG,aAAa,CAAC;AAEpBA,aAAa,CAAC/B,IAAI;EAAA,iBAA6F+B,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBA1Q6EjD,EAAE;EAAA,MA0QegD;AAAa,EA0BlF;AAC3CA,aAAa,CAACE,IAAI,kBArS6ElD,EAAE;EAAA,UAqSwCW,eAAe,EAAED,YAAY,EAAEC,eAAe;AAAA,EAAI;AAC3L;EAAA,mDAtS+FX,EAAE,mBAsSDgD,aAAa,EAAc,CAAC;IAChH7B,IAAI,EAAEV,QAAQ;IACdW,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CAACxC,eAAe,EAAED,YAAY,CAAC;MACxC0C,OAAO,EAAE,CAACL,eAAe,EAAEpC,eAAe,CAAC;MAC3C0C,YAAY,EAAEN;IAClB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASnC,eAAe,EAAEC,OAAO,EAAEwB,cAAc,EAAES,aAAa,EAAEX,cAAc,EAAEK,aAAa,EAAED,aAAa,EAAEE,YAAY,EAAEG,cAAc,EAAED,cAAc,EAAEK,aAAa,EAAEN,cAAc,EAAEN,eAAe,EAAEJ,YAAY,EAAEE,iBAAiB,EAAEW,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}