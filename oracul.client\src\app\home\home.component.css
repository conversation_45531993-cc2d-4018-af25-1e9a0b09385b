/* Hero Section */
.hero-section {
  background: var(--theme-gradient-primary);
  color: white;
  padding: 80px 20px; /* Removed extra top padding since it's handled globally */
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  max-width: 500px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-button, .secondary-button {
  padding: 12px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-img {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Section Containers */
.featured-section, .articles-section, .horoscope-section {
  padding: 80px 20px;
  background: var(--theme-background);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--theme-text-secondary);
  margin-bottom: 30px;
}

.view-all-btn {
  margin-top: 20px;
}

/* Profiles Grid */
.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.profile-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
}

.profile-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--theme-accent-light);
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--theme-accent);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--theme-text-primary);
}

.profile-title {
  font-size: 1rem;
  color: var(--theme-text-secondary);
  margin: 0 0 8px 0;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
}

.profile-content {
  padding: 20px;
}

.profile-headline {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.profile-skills {
  margin-bottom: 16px;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-chip {
  background: var(--theme-accent);
  color: var(--theme-text-primary);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

.profile-stats {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
}

.stat mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Articles Grid */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.article-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.article-image {
  height: 200px;
  object-fit: cover;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.85rem;
}

.author {
  font-weight: 600;
  color: var(--theme-primary);
}

.date, .read-time {
  color: var(--theme-text-secondary);
}

.article-excerpt {
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category-chip {
  background: var(--theme-primary);
  color: white;
  font-size: 0.8rem;
}

/* Horoscope Grid */
.horoscope-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.horoscope-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px;
  border: 2px solid var(--theme-accent-light);
}

.horoscope-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  border-color: var(--theme-accent);
}

.sign-avatar {
  background: var(--theme-gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.sign-symbol {
  font-size: 1.5rem;
  font-weight: bold;
}

.prediction {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--theme-text-primary);
  margin-bottom: 16px;
}

.sign-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.detail {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: var(--theme-text-secondary);
}

.detail mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* CTA Section */
.cta-section {
  background: var(--theme-gradient-secondary);
  color: white;
  padding: 80px 20px;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-subtitle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-primary, .cta-secondary {
  padding: 12px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 60px 20px;
  color: var(--theme-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Navigation Mobile Styles */
  .nav-links {
    display: none;
  }

  .nav-actions {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .nav-container {
    padding: 0 16px;
  }

  .hero-section {
    padding: 120px 20px 80px; /* Adjusted for mobile nav */
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .profiles-grid {
    grid-template-columns: 1fr;
  }

  .articles-grid {
    grid-template-columns: 1fr;
  }

  .horoscope-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .hero-actions, .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}
