{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"../profile-card/profile-card.component\";\nfunction ProfileSearchComponent_div_26_app_profile_card_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-profile-card\", 18);\n    i0.ɵɵlistener(\"contactClicked\", function ProfileSearchComponent_div_26_app_profile_card_13_Template_app_profile_card_contactClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onContactProfile($event));\n    })(\"profileClicked\", function ProfileSearchComponent_div_26_app_profile_card_13_Template_app_profile_card_profileClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onProfileClicked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const profile_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"profile\", profile_r3)(\"compact\", ctx_r1.viewMode === \"grid\");\n  }\n}\nconst _c0 = function () {\n  return [10, 20, 50];\n};\nfunction ProfileSearchComponent_div_26_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-paginator\", 20);\n    i0.ɵɵlistener(\"page\", function ProfileSearchComponent_div_26_div_14_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r2.searchResults.totalCount)(\"pageSize\", ctx_r2.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r2.currentPage - 1);\n  }\n}\nfunction ProfileSearchComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11)(5, \"mat-button-toggle-group\", 12);\n    i0.ɵɵlistener(\"valueChange\", function ProfileSearchComponent_div_26_Template_mat_button_toggle_group_valueChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.viewMode = $event);\n    });\n    i0.ɵɵelementStart(6, \"mat-button-toggle\", 13)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"grid_view\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-button-toggle\", 14)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"view_list\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"div\", 15);\n    i0.ɵɵtemplate(13, ProfileSearchComponent_div_26_app_profile_card_13_Template, 1, 2, \"app-profile-card\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ProfileSearchComponent_div_26_div_14_Template, 2, 5, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.searchResults.totalCount, \" professional\", ctx_r0.searchResults.totalCount !== 1 ? \"s\" : \"\", \" found\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r0.viewMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"grid-view\", ctx_r0.viewMode === \"grid\")(\"list-view\", ctx_r0.viewMode === \"list\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.searchResults.profiles);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchResults.totalPages > 1);\n  }\n}\nexport class ProfileSearchComponent {\n  constructor(formBuilder, profileService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.profileService = profileService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.searchResults = null;\n    this.isLoading = false;\n    this.viewMode = 'grid';\n    this.currentPage = 1;\n    this.pageSize = 20;\n    this.searchForm = this.formBuilder.group({\n      location: [''],\n      skills: [''],\n      experience: [''],\n      sortBy: ['relevance']\n    });\n  }\n  ngOnInit() {}\n  onSearch() {\n    this.isLoading = true;\n    const formValue = this.searchForm.value;\n    const filters = {\n      location: formValue.location || undefined,\n      skills: formValue.skills ? formValue.skills.split(',').map(s => s.trim()) : undefined,\n      experience: formValue.experience || undefined,\n      sortBy: formValue.sortBy\n    };\n    this.profileService.searchProfiles(filters).subscribe({\n      next: results => {\n        this.searchResults = results;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Search error:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ProfileSearchComponent_Factory(t) {\n      return new (t || ProfileSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileSearchComponent,\n      selectors: [[\"app-profile-search\"]],\n      decls: 27,\n      vars: 2,\n      consts: [[1, \"search-container\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"search-fields\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"location\", \"placeholder\", \"City, State, Country\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"skills\", \"placeholder\", \"JavaScript, Angular, etc.\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\"], [\"class\", \"search-results\", 4, \"ngIf\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"view-options\"], [1, \"view-toggle\", 3, \"value\", \"valueChange\"], [\"value\", \"grid\"], [\"value\", \"list\"], [1, \"profiles-container\"], [3, \"profile\", \"compact\", \"contactClicked\", \"profileClicked\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [3, \"profile\", \"compact\", \"contactClicked\", \"profileClicked\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n      template: function ProfileSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Find Professionals \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ProfileSearchComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(9, \"div\", 2)(10, \"mat-form-field\", 3)(11, \"mat-label\");\n          i0.ɵɵtext(12, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 4);\n          i0.ɵɵelementStart(14, \"mat-icon\", 5);\n          i0.ɵɵtext(15, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"mat-form-field\", 3)(17, \"mat-label\");\n          i0.ɵɵtext(18, \"Skills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 6);\n          i0.ɵɵelementStart(20, \"mat-icon\", 5);\n          i0.ɵɵtext(21, \"psychology\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 7)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25, \" Search \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(26, ProfileSearchComponent_div_26_Template, 15, 9, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchResults);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatButton, i8.MatIcon, i9.MatFormField, i9.MatLabel, i9.MatSuffix, i10.MatInput, i11.ProfileCardComponent],\n      styles: [\".search-container[_ngcontent-%COMP%] { padding: 20px; }\\n    .search-fields[_ngcontent-%COMP%] { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }\\n    .profiles-grid[_ngcontent-%COMP%] { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px; margin-top: 20px; }\\n    .profile-card[_ngcontent-%COMP%] { padding: 16px; }\\n    .profile-preview[_ngcontent-%COMP%] { display: flex; flex-direction: column; align-items: center; text-align: center; gap: 8px; }\\n    .profile-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] { width: 80px; height: 80px; border-radius: 50%; object-fit: cover; }\\n    .location[_ngcontent-%COMP%] { color: var(--theme-text-secondary); font-size: 0.9rem; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHJvZmlsZS9jb21wb25lbnRzL3Byb2ZpbGUtc2VhcmNoL3Byb2ZpbGUtc2VhcmNoLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0ksb0JBQW9CLGFBQWEsRUFBRTtJQUNuQyxpQkFBaUIsYUFBYSxFQUFFLFNBQVMsRUFBRSxtQkFBbUIsRUFBRSxlQUFlLEVBQUU7SUFDakYsaUJBQWlCLGFBQWEsRUFBRSw0REFBNEQsRUFBRSxTQUFTLEVBQUUsZ0JBQWdCLEVBQUU7SUFDM0gsZ0JBQWdCLGFBQWEsRUFBRTtJQUMvQixtQkFBbUIsYUFBYSxFQUFFLHNCQUFzQixFQUFFLG1CQUFtQixFQUFFLGtCQUFrQixFQUFFLFFBQVEsRUFBRTtJQUM3Ryx1QkFBdUIsV0FBVyxFQUFFLFlBQVksRUFBRSxrQkFBa0IsRUFBRSxpQkFBaUIsRUFBRTtJQUN6RixZQUFZLGtDQUFrQyxFQUFFLGlCQUFpQixFQUFFIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnNlYXJjaC1jb250YWluZXIgeyBwYWRkaW5nOiAyMHB4OyB9XG4gICAgLnNlYXJjaC1maWVsZHMgeyBkaXNwbGF5OiBmbGV4OyBnYXA6IDE2cHg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGZsZXgtd3JhcDogd3JhcDsgfVxuICAgIC5wcm9maWxlcy1ncmlkIHsgZGlzcGxheTogZ3JpZDsgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maWxsLCBtaW5tYXgoMzAwcHgsIDFmcikpOyBnYXA6IDE2cHg7IG1hcmdpbi10b3A6IDIwcHg7IH1cbiAgICAucHJvZmlsZS1jYXJkIHsgcGFkZGluZzogMTZweDsgfVxuICAgIC5wcm9maWxlLXByZXZpZXcgeyBkaXNwbGF5OiBmbGV4OyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyBhbGlnbi1pdGVtczogY2VudGVyOyB0ZXh0LWFsaWduOiBjZW50ZXI7IGdhcDogOHB4OyB9XG4gICAgLnByb2ZpbGUtcHJldmlldyBpbWcgeyB3aWR0aDogODBweDsgaGVpZ2h0OiA4MHB4OyBib3JkZXItcmFkaXVzOiA1MCU7IG9iamVjdC1maXQ6IGNvdmVyOyB9XG4gICAgLmxvY2F0aW9uIHsgY29sb3I6IHZhcigtLXRoZW1lLXRleHQtc2Vjb25kYXJ5KTsgZm9udC1zaXplOiAwLjlyZW07IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;IAyDcA,4CAK8C;IAD5CA;MAAAA;MAAA;MAAA,OAAkBA,8CAAwB;IAAA,EAAC;MAAAA;MAAA;MAAA,OACzBA,8CAAwB;IAAA,EADC;IAE7CA,iBAAmB;;;;;IAJjBA,oCAAmB;;;;;;;;;IAQvBA,+BAAuE;IAMnEA;MAAAA;MAAA;MAAA,OAAQA,0CAAoB;IAAA,EAAC;IAE/BA,iBAAgB;;;;IANdA,eAAmC;IAAnCA,wDAAmC;;;;;;IA5BzCA,8BAAkD;IAE1CA,YAAgG;IAAAA,iBAAK;IACzGA,+BAA0B;IACCA;MAAAA;MAAA;MAAA;IAAA,EAAoB;IAC3CA,6CAAgC;IACpBA,yBAAS;IAAAA,iBAAW;IAEhCA,6CAAgC;IACpBA,0BAAS;IAAAA,iBAAW;IAMtCA,gCAAgH;IAC9GA,2GAMmB;IACrBA,iBAAM;IAGNA,iFASM;IACRA,iBAAM;;;;IAlCEA,eAAgG;IAAhGA,uIAAgG;IAEzEA,eAAoB;IAApBA,uCAAoB;IAWjBA,eAAuC;IAAvCA,uDAAuC;IAE/CA,eAAyB;IAAzBA,uDAAyB;IASdA,eAAkC;IAAlCA,0DAAkC;;;AAyBjF,OAAM,MAAOC,sBAAsB;EAQjCC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,MAAc,EACdC,QAAqB;IAHrB,gBAAW,GAAXH,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAVlB,kBAAa,GAA+B,IAAI;IAChD,cAAS,GAAG,KAAK;IACjB,aAAQ,GAAoB,MAAM;IAClC,gBAAW,GAAG,CAAC;IACf,aAAQ,GAAG,EAAE;IAQX,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC;MACvCC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,WAAW;KACrB,CAAC;EACJ;EAEAC,QAAQ,IAAU;EAElBC,QAAQ;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACT,UAAU,CAACU,KAAK;IAEvC,MAAMC,OAAO,GAAyB;MACpCT,QAAQ,EAAEO,SAAS,CAACP,QAAQ,IAAIU,SAAS;MACzCT,MAAM,EAAEM,SAAS,CAACN,MAAM,GAAGM,SAAS,CAACN,MAAM,CAACU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAS,IAAKA,CAAC,CAACC,IAAI,EAAE,CAAC,GAAGJ,SAAS;MAC/FR,UAAU,EAAEK,SAAS,CAACL,UAAU,IAAIQ,SAAS;MAC7CP,MAAM,EAAEI,SAAS,CAACJ;KACnB;IAED,IAAI,CAACR,cAAc,CAACoB,cAAc,CAACN,OAAO,CAAC,CAACO,SAAS,CAAC;MACpDC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACC,aAAa,GAAGD,OAAO;QAC5B,IAAI,CAACZ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC,IAAI,CAACd,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;;;uBA7CWd,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAA8B;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAjF/BnC,8BAA8B;UAIZA,sBAAM;UAAAA,iBAAW;UAC3BA,oCACF;UAAAA,iBAAiB;UAEnBA,wCAAkB;UACeA;YAAA,OAAYoC,cAAU;UAAA,EAAC;UACpDpC,8BAA2B;UAEZA,yBAAQ;UAAAA,iBAAY;UAC/BA,4BAA8E;UAC9EA,oCAAoB;UAAAA,4BAAW;UAAAA,iBAAW;UAG5CA,0CAAqC;UACxBA,uBAAM;UAAAA,iBAAY;UAC7BA,4BAAiF;UACjFA,oCAAoB;UAAAA,2BAAU;UAAAA,iBAAW;UAG3CA,kCAAwD;UAC5CA,uBAAM;UAAAA,iBAAW;UAC3BA,yBACF;UAAAA,iBAAS;UAIbA,0EAoCM;UACRA,iBAAmB;;;UA1DXA,eAAwB;UAAxBA,0CAAwB;UAqBDA,gBAAmB;UAAnBA,wCAAmB", "names": ["i0", "ProfileSearchComponent", "constructor", "formBuilder", "profileService", "router", "snackBar", "searchForm", "group", "location", "skills", "experience", "sortBy", "ngOnInit", "onSearch", "isLoading", "formValue", "value", "filters", "undefined", "split", "map", "s", "trim", "searchProfiles", "subscribe", "next", "results", "searchResults", "error", "console", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-search\\profile-search.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { PageEvent } from '@angular/material/paginator';\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { UserProfile, ProfileSearchFilters, ProfileSearchResult, UserProfile } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-search',\r\n  template: `\r\n    <div class=\"search-container\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>search</mat-icon>\r\n            Find Professionals\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\">\r\n            <div class=\"search-fields\">\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Location</mat-label>\r\n                <input matInput formControlName=\"location\" placeholder=\"City, State, Country\">\r\n                <mat-icon matSuffix>location_on</mat-icon>\r\n              </mat-form-field>\r\n              \r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Skills</mat-label>\r\n                <input matInput formControlName=\"skills\" placeholder=\"JavaScript, Angular, etc.\">\r\n                <mat-icon matSuffix>psychology</mat-icon>\r\n              </mat-form-field>\r\n              \r\n              <button mat-raised-button color=\"primary\" type=\"submit\">\r\n                <mat-icon>search</mat-icon>\r\n                Search\r\n              </button>\r\n            </div>\r\n          </form>\r\n          \r\n          <div class=\"search-results\" *ngIf=\"searchResults\">\r\n            <div class=\"results-header\">\r\n              <h3>{{ searchResults.totalCount }} professional{{ searchResults.totalCount !== 1 ? 's' : '' }} found</h3>\r\n              <div class=\"view-options\">\r\n                <mat-button-toggle-group [(value)]=\"viewMode\" class=\"view-toggle\">\r\n                  <mat-button-toggle value=\"grid\">\r\n                    <mat-icon>grid_view</mat-icon>\r\n                  </mat-button-toggle>\r\n                  <mat-button-toggle value=\"list\">\r\n                    <mat-icon>view_list</mat-icon>\r\n                  </mat-button-toggle>\r\n                </mat-button-toggle-group>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profiles-container\" [class.grid-view]=\"viewMode === 'grid'\" [class.list-view]=\"viewMode === 'list'\">\r\n              <app-profile-card\r\n                *ngFor=\"let profile of searchResults.profiles\"\r\n                [profile]=\"profile\"\r\n                [compact]=\"viewMode === 'grid'\"\r\n                (contactClicked)=\"onContactProfile($event)\"\r\n                (profileClicked)=\"onProfileClicked($event)\">\r\n              </app-profile-card>\r\n            </div>\r\n\r\n            <!-- Pagination -->\r\n            <div class=\"pagination-container\" *ngIf=\"searchResults.totalPages > 1\">\r\n              <mat-paginator\r\n                [length]=\"searchResults.totalCount\"\r\n                [pageSize]=\"pageSize\"\r\n                [pageSizeOptions]=\"[10, 20, 50]\"\r\n                [pageIndex]=\"currentPage - 1\"\r\n                (page)=\"onPageChange($event)\"\r\n                showFirstLastButtons>\r\n              </mat-paginator>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .search-container { padding: 20px; }\r\n    .search-fields { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }\r\n    .profiles-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px; margin-top: 20px; }\r\n    .profile-card { padding: 16px; }\r\n    .profile-preview { display: flex; flex-direction: column; align-items: center; text-align: center; gap: 8px; }\r\n    .profile-preview img { width: 80px; height: 80px; border-radius: 50%; object-fit: cover; }\r\n    .location { color: var(--theme-text-secondary); font-size: 0.9rem; }\r\n  `]\r\n})\r\nexport class ProfileSearchComponent implements OnInit {\r\n  searchForm: FormGroup;\r\n  searchResults: ProfileSearchResult | null = null;\r\n  isLoading = false;\r\n  viewMode: 'grid' | 'list' = 'grid';\r\n  currentPage = 1;\r\n  pageSize = 20;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.searchForm = this.formBuilder.group({\r\n      location: [''],\r\n      skills: [''],\r\n      experience: [''],\r\n      sortBy: ['relevance']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  onSearch(): void {\r\n    this.isLoading = true;\r\n    const formValue = this.searchForm.value;\r\n    \r\n    const filters: ProfileSearchFilters = {\r\n      location: formValue.location || undefined,\r\n      skills: formValue.skills ? formValue.skills.split(',').map((s: string) => s.trim()) : undefined,\r\n      experience: formValue.experience || undefined,\r\n      sortBy: formValue.sortBy\r\n    };\r\n\r\n    this.profileService.searchProfiles(filters).subscribe({\r\n      next: (results) => {\r\n        this.searchResults = results;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Search error:', error);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}