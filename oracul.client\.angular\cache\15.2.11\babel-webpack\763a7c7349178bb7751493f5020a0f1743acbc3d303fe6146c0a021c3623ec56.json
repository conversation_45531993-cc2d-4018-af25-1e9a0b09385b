{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (++seen <= count) {\n        subscriber.next(value);\n        if (count <= seen) {\n          subscriber.complete();\n        }\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "take", "count", "source", "subscriber", "seen", "subscribe", "value", "next", "complete"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/rxjs/dist/esm/internal/operators/take.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n    return count <= 0\n        ?\n            () => EMPTY\n        : operate((source, subscriber) => {\n            let seen = 0;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (++seen <= count) {\n                    subscriber.next(value);\n                    if (count <= seen) {\n                        subscriber.complete();\n                    }\n                }\n            }));\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,IAAI,CAACC,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAI,CAAC,GAET,MAAMJ,KAAK,GACbC,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,IAAI,GAAG,CAAC;IACZF,MAAM,CAACG,SAAS,CAACN,wBAAwB,CAACI,UAAU,EAAGG,KAAK,IAAK;MAC7D,IAAI,EAAEF,IAAI,IAAIH,KAAK,EAAE;QACjBE,UAAU,CAACI,IAAI,CAACD,KAAK,CAAC;QACtB,IAAIL,KAAK,IAAIG,IAAI,EAAE;UACfD,UAAU,CAACK,QAAQ,EAAE;QACzB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}