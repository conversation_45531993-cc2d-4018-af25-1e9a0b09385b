/* Theme CSS Custom Properties */
:root {
  /* Mystical Purple theme colors - will be overridden by ThemeService */
  --theme-primary: #67455c; /* тъмен пурпурен */
  --theme-primary-light: #a07ba0; /* розово-лилаво */
  --theme-primary-dark: #3f2f4e; /* дълбоко мистично лилаво */
  --theme-accent: #d2a6d0; /* нежен лилав */
  --theme-accent-light: #e6dbec; /* много светло лилаво */
  --theme-accent-dark: #a07ba0; /* розово-лилаво */
  --theme-warn: #d2869d;
  --theme-success: #8fbc8f;
  --theme-error: #d2869d;
  --theme-background: #e6dbec; /* много светло лилаво */
  --theme-surface: #ffffff;

  /* Text colors */
  --theme-text-primary: #3f2f4e; /* дълбоко мистично лилаво */
  --theme-text-secondary: #67455c; /* тъмен пурпурен */
  --theme-text-disabled: rgba(63, 47, 78, 0.38);
  --theme-text-hint: rgba(63, 47, 78, 0.38);

  /* Gradients */
  --theme-gradient-primary: linear-gradient(135deg, #d2a6d0 0%, #67455c 100%);
  --theme-gradient-secondary: linear-gradient(135deg, #a07ba0 0%, #3f2f4e 100%);
  --theme-gradient-auth: linear-gradient(135deg, #d2a6d0 0%, #a07ba0 100%);

  /* OAuth colors */
  --theme-google-bg: #ffffff;
  --theme-google-border: #d2a6d0; /* нежен лилав */
  --theme-google-text: #67455c; /* тъмен пурпурен */
  --theme-google-hover: #e6dbec; /* много светло лилаво */

  --theme-facebook-bg: #ffffff;
  --theme-facebook-border: #67455c; /* тъмен пурпурен */
  --theme-facebook-text: #67455c; /* тъмен пурпурен */
  --theme-facebook-hover: #e6dbec; /* много светло лилаво */
}

/* Global theme classes */
.theme-primary {
  color: var(--theme-primary) !important;
}

.theme-primary-bg {
  background-color: var(--theme-primary) !important;
}

.theme-accent {
  color: var(--theme-accent) !important;
}

.theme-accent-bg {
  background-color: var(--theme-accent) !important;
}

.theme-gradient-primary {
  background: var(--theme-gradient-primary) !important;
}

.theme-gradient-secondary {
  background: var(--theme-gradient-secondary) !important;
}

.theme-gradient-auth {
  background: var(--theme-gradient-auth) !important;
}

/* Authentication components theming */
.login-container,
.register-container {
  background: var(--theme-gradient-auth);
}

.login-header,
.register-header {
  background: var(--theme-gradient-auth) !important;
}

/* OAuth button theming */
.google-button {
  border-color: var(--theme-google-border) !important;
  color: var(--theme-google-text) !important;
  background-color: var(--theme-google-bg) !important;
}

.google-button:hover:not([disabled]) {
  background-color: var(--theme-google-hover) !important;
}

.facebook-button {
  border-color: var(--theme-facebook-border) !important;
  color: var(--theme-facebook-text) !important;
  background-color: var(--theme-facebook-bg) !important;
}

.facebook-button:hover:not([disabled]) {
  background-color: var(--theme-facebook-hover) !important;
}

.facebook-icon {
  color: var(--theme-facebook-text) !important;
}

/* Dashboard theming */
.header-card .mat-card-title,
.data-card .mat-card-title {
  color: var(--theme-primary) !important;
}

/* Toolbar theming */
.app-toolbar {
  background: var(--theme-primary) !important;
}

.user-info {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Success/Error snackbar theming */
.success-snackbar {
  background-color: var(--theme-success) !important;
  color: white !important;
}

.error-snackbar {
  background-color: var(--theme-error) !important;
  color: white !important;
}

/* Dark theme specific overrides */
.theme-dark {
  background-color: var(--theme-background);
  color: var(--theme-text-primary);
}

.theme-dark .mat-card {
  background-color: var(--theme-surface) !important;
  color: var(--theme-text-primary) !important;
}

.theme-dark .mat-form-field-label {
  color: var(--theme-text-secondary) !important;
}

.theme-dark .mat-form-field-underline {
  background-color: var(--theme-text-disabled) !important;
}

.theme-dark .mat-form-field-ripple {
  background-color: var(--theme-primary) !important;
}

/* Theme transition animations */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Disable transitions during theme change to prevent flicker */
.theme-changing * {
  transition: none !important;
}

/* Force Material components to use theme colors */
.mat-button, .mat-raised-button, .mat-stroked-button, .mat-fab, .mat-mini-fab, .mat-icon-button {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

.mat-card, .mat-toolbar, .mat-form-field {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Ensure theme variables are inherited */
body {
  background-color: var(--theme-background);
  color: var(--theme-text-primary);
}

/* Enhanced Material Component Theming */

/* Buttons */
.mat-mdc-raised-button.mat-primary {
  --mdc-protected-button-container-color: var(--theme-primary) !important;
  --mdc-protected-button-label-text-color: white !important;
  background-color: var(--theme-primary) !important;
  color: white !important;
}

.mat-mdc-outlined-button.mat-primary {
  --mdc-outlined-button-outline-color: var(--theme-primary) !important;
  --mdc-outlined-button-label-text-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  color: var(--theme-primary) !important;
}

.mat-mdc-unelevated-button.mat-primary {
  --mdc-filled-button-container-color: var(--theme-primary) !important;
  --mdc-filled-button-label-text-color: white !important;
  background-color: var(--theme-primary) !important;
  color: white !important;
}

.mat-mdc-button.mat-primary {
  --mdc-text-button-label-text-color: var(--theme-primary) !important;
  color: var(--theme-primary) !important;
}

/* Form Fields */
.mat-mdc-form-field.mat-focused .mdc-notched-outline__leading,
.mat-mdc-form-field.mat-focused .mdc-notched-outline__notch,
.mat-mdc-form-field.mat-focused .mdc-notched-outline__trailing {
  border-color: var(--theme-primary) !important;
}

.mat-mdc-form-field.mat-focused .mat-mdc-floating-label {
  color: var(--theme-primary) !important;
}

.mat-mdc-form-field .mat-mdc-input-element:focus {
  caret-color: var(--theme-primary) !important;
}

/* Checkboxes and Radio Buttons */
.mat-mdc-checkbox.mat-primary .mdc-checkbox__native-control:checked ~ .mdc-checkbox__background {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
}

.mat-mdc-radio-button.mat-primary .mdc-radio__native-control:checked + .mdc-radio__background .mdc-radio__outer-circle {
  border-color: var(--theme-primary) !important;
}

.mat-mdc-radio-button.mat-primary .mdc-radio__native-control:checked + .mdc-radio__background .mdc-radio__inner-circle {
  background-color: var(--theme-primary) !important;
}

/* Slide Toggle */
.mat-mdc-slide-toggle.mat-primary .mdc-switch--selected .mdc-switch__track {
  background-color: var(--theme-primary) !important;
}

.mat-mdc-slide-toggle.mat-primary .mdc-switch--selected .mdc-switch__handle::after {
  background-color: var(--theme-primary) !important;
}

/* Progress Components */
.mat-mdc-progress-bar .mdc-linear-progress__bar-inner {
  border-color: var(--theme-primary) !important;
}

.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,
.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic {
  stroke: var(--theme-primary) !important;
}

/* Tabs */
.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: var(--theme-primary) !important;
}

.mat-mdc-tab-group .mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron {
  color: var(--theme-primary) !important;
}

.mdc-tab-indicator__content--underline {
  border-color: var(--theme-primary) !important;
}

/* Chips */
.mat-mdc-chip.mat-primary {
  --mdc-chip-elevated-container-color: var(--theme-primary) !important;
  --mdc-chip-label-text-color: white !important;
  background-color: var(--theme-primary) !important;
  color: white !important;
}

/* Toolbar - Enhanced */
.mat-toolbar.mat-primary {
  background-color: var(--theme-primary) !important;
  color: white !important;
}

/* Menu */
.mat-mdc-menu-panel {
  background: var(--theme-surface) !important;
}

.mat-mdc-menu-item {
  color: var(--theme-text-primary) !important;
}

.mat-mdc-menu-item:hover {
  background-color: var(--theme-accent-light) !important;
}

/* Snackbar */
.mat-mdc-snack-bar-container.success-snackbar {
  --mdc-snackbar-container-color: var(--theme-success) !important;
  --mdc-snackbar-supporting-text-color: white !important;
}

.mat-mdc-snack-bar-container.error-snackbar {
  --mdc-snackbar-container-color: var(--theme-error) !important;
  --mdc-snackbar-supporting-text-color: white !important;
}
