# Oracul.Data - Data Layer

This project contains the data access layer for the Oracul application, implementing Entity Framework Core with a code-first approach.

## Architecture

The data layer follows the Repository and Unit of Work patterns with the following structure:

### Models
- **BaseEntity**: Abstract base class providing common properties (Id, CreatedAt, UpdatedAt, soft delete fields)
- **User**: User entity with authentication and profile information
- **Role**: Role entity for authorization
- **Permission**: Permission entity for fine-grained access control
- **UserRole**: Junction entity for User-Role many-to-many relationship
- **RolePermission**: Junction entity for Role-Permission many-to-many relationship

### Data Context
- **OraculDbContext**: Main EF Core DbContext with:
  - Entity configurations and relationships
  - Global query filters for soft delete
  - Automatic timestamp updates
  - Seed data for initial roles and permissions

### Repository Pattern
- **IRepository<T>**: Generic repository interface with common CRUD operations
- **Repository<T>**: Generic repository implementation
- **IUserRepository**: Specific user repository interface with user-related methods
- **UserRepository**: User repository implementation with specialized queries

### Unit of Work
- **IUnitOfWork**: Unit of Work interface for transaction management
- **UnitOfWork**: Implementation managing all repositories and transactions

## Features

### Soft Delete
All entities inherit from `BaseEntity` and support soft delete functionality:
- `IsDeleted` flag
- `DeletedAt` timestamp
- `DeletedBy` user tracking
- Global query filters automatically exclude soft-deleted records

### Audit Trail
Automatic tracking of:
- Creation timestamp (`CreatedAt`)
- Last update timestamp (`UpdatedAt`)
- User who created the record (`CreatedBy`)
- User who last updated the record (`UpdatedBy`)

### Pagination Support
Built-in pagination support in the generic repository:
```csharp
var (items, totalCount) = await repository.GetPagedAsync(
    pageNumber: 1, 
    pageSize: 10, 
    filter: x => x.IsActive,
    orderBy: q => q.OrderBy(x => x.Name)
);
```

### Seeded Data
Initial data includes:
- **Roles**: Administrator, User
- **Permissions**: Users.Read, Users.Write, Users.Delete, Roles.Read, Roles.Write, Roles.Delete
- **Role-Permission mappings**: Administrator gets all permissions, User gets read-only permissions

## Usage

### Registration in Startup/Program.cs
```csharp
using Oracul.Data.Extensions;

// Register data layer services
builder.Services.AddOraculDataLayer(builder.Configuration);
```

### Using in Controllers/Services
```csharp
public class UsersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public UsersController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<User> GetUserAsync(int id)
    {
        return await _unitOfWork.Users.GetByIdAsync(id);
    }

    public async Task CreateUserAsync(User user)
    {
        await _unitOfWork.Users.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();
    }
}
```

### Transaction Management
```csharp
// Using transactions
await _unitOfWork.BeginTransactionAsync();
try
{
    await _unitOfWork.Users.AddAsync(user);
    await _unitOfWork.UserRoles.AddAsync(userRole);
    await _unitOfWork.SaveChangesAsync();
    await _unitOfWork.CommitTransactionAsync();
}
catch
{
    await _unitOfWork.RollbackTransactionAsync();
    throw;
}
```

## Database Migrations

### Prerequisites
1. **Install EF Core Tools** (if not already installed):
   ```bash
   dotnet tool install --global dotnet-ef
   ```

2. **Verify Installation**:
   ```bash
   dotnet ef --version
   ```

### Quick Migration Commands

#### Using Helper Scripts (Recommended)
We've provided helper scripts to simplify migration commands:

**PowerShell (Windows):**
```powershell
# Update database (apply all pending migrations)
.\migrate.ps1

# Create new migration
.\migrate.ps1 -Action add -MigrationName "YourMigrationName"

# List all migrations
.\migrate.ps1 -Action list

# Remove last migration
.\migrate.ps1 -Action remove
```

**Batch File (Windows CMD):**
```cmd
# Update database
migrate.bat

# Create new migration
migrate.bat add YourMigrationName

# List all migrations
migrate.bat list
```

#### Manual Commands
If you prefer to run commands manually:

**Creating Migrations:**
```bash
dotnet ef migrations add MigrationName --project Oracul.Data --startup-project Oracul.Server
```

**Updating Database:**
```bash
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server
```

**Removing Last Migration:**
```bash
dotnet ef migrations remove --project Oracul.Data --startup-project Oracul.Server
```

**List All Migrations:**
```bash
dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server
```

**Generate SQL Script:**
```bash
dotnet ef migrations script --project Oracul.Data --startup-project Oracul.Server
```

### Troubleshooting Migration Issues

#### Issue 1: "dotnet-ef command not found"
**Solution:** Install EF Core Tools globally:
```bash
dotnet tool install --global dotnet-ef
```

#### Issue 2: "No DbContext was found"
**Solution:** Ensure you're running commands from the solution root directory and both projects build successfully:
```bash
dotnet build
```

#### Issue 3: "Pending model changes" error
**Solution:** This happens when using dynamic values in seed data. We've fixed this by using static dates.

#### Issue 4: Connection string issues
**Solution:** Verify your connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=OraculDb;User Id=sa;Password=yourpassword;TrustServerCertificate=True;"
  }
}
```

#### Issue 5: SQL Server connection problems
**Solutions:**
- Ensure SQL Server is running
- Verify connection string credentials
- Check if the database server is accessible
- For LocalDB: `"Server=(localdb)\\mssqllocaldb;Database=OraculDb;Trusted_Connection=true;"`

### Migration Best Practices

1. **Always backup your database** before applying migrations in production
2. **Review generated migration code** before applying
3. **Test migrations** on a copy of production data
4. **Use descriptive migration names** (e.g., "AddUserEmailIndex", "UpdateUserTable")
5. **Don't edit existing migrations** that have been applied to production
6. **Use transactions** for complex migrations

## Configuration

### Connection String
Configure in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=OraculDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### Custom Configuration
```csharp
// Custom DbContext configuration
builder.Services.AddOraculDataLayer(options =>
{
    options.UseSqlServer(connectionString);
    options.EnableSensitiveDataLogging(); // Only in development
});
```

## Dependencies

- Microsoft.EntityFrameworkCore (9.0.4)
- Microsoft.EntityFrameworkCore.SqlServer (9.0.4)
- Microsoft.EntityFrameworkCore.Tools (9.0.4)
- Microsoft.EntityFrameworkCore.Design (9.0.4)

## Best Practices

1. **Always use Unit of Work** for data operations to ensure proper transaction management
2. **Use async methods** for all database operations
3. **Implement proper error handling** in your services/controllers
4. **Use soft delete** instead of hard delete for important entities
5. **Leverage the repository pattern** for testability and maintainability
6. **Use pagination** for large data sets
7. **Apply proper validation** before saving entities
