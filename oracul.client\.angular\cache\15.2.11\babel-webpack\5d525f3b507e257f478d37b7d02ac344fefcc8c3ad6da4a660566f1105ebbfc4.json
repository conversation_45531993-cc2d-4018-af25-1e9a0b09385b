{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/token.service\";\nexport class AuthInterceptor {\n  constructor(tokenService, injector) {\n    this.tokenService = tokenService;\n    this.injector = injector;\n    this.isRefreshing = false;\n    this.refreshTokenSubject = new BehaviorSubject(null);\n  }\n  intercept(req, next) {\n    // Add auth header if user is authenticated\n    const authReq = this.addAuthHeader(req);\n    return next.handle(authReq).pipe(catchError(error => {\n      if (error.status === 401 && !authReq.url.includes('/auth/')) {\n        return this.handle401Error(authReq, next);\n      }\n      return throwError(error);\n    }));\n  }\n  addAuthHeader(req) {\n    const token = this.tokenService.getToken();\n    if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {\n      return req.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    return req;\n  }\n  handle401Error(req, next) {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n      return this.refreshToken().pipe(switchMap(response => {\n        this.isRefreshing = false;\n        if (response.success && response.accessToken) {\n          this.refreshTokenSubject.next(response.accessToken);\n          return next.handle(this.addAuthHeader(req));\n        } else {\n          this.tokenService.clearAllTokens();\n          return throwError('Token refresh failed');\n        }\n      }), catchError(error => {\n        this.isRefreshing = false;\n        this.tokenService.clearAllTokens();\n        return throwError(error);\n      }));\n    } else {\n      // Wait for refresh to complete\n      return this.refreshTokenSubject.pipe(filter(token => token != null), take(1), switchMap(() => next.handle(this.addAuthHeader(req))));\n    }\n  }\n  refreshToken() {\n    const refreshToken = this.tokenService.getRefreshToken();\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n    // Lazy load HttpClient to avoid circular dependency\n    if (!this.httpClient) {\n      this.httpClient = this.injector.get(HttpClient);\n    }\n    return this.httpClient.post('/api/auth/refresh-token', {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.accessToken) {\n        const rememberMe = this.tokenService.getRememberMe();\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        if (response.user) {\n          this.tokenService.setUser(response.user, rememberMe);\n        }\n      }\n    }), catchError(error => {\n      this.tokenService.clearAllTokens();\n      return throwError(error);\n    }));\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.TokenService), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAkFA,UAAU,QAAQ,sBAAsB;AAC1H,SAAqBC,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAC9D,SAASC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;;;AAKzE,OAAM,MAAOC,eAAe;EAK1BC,YACUC,YAA0B,EAC1BC,QAAkB;IADlB,iBAAY,GAAZD,YAAY;IACZ,aAAQ,GAARC,QAAQ;IANV,iBAAY,GAAG,KAAK;IACpB,wBAAmB,GAAyB,IAAIT,eAAe,CAAM,IAAI,CAAC;EAM/E;EAEHU,SAAS,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,GAAG,CAAC;IAEvC,OAAOC,IAAI,CAACG,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9Bf,UAAU,CAAEgB,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAI,CAACL,OAAO,CAACM,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3D,OAAO,IAAI,CAACC,cAAc,CAACR,OAAO,EAAED,IAAI,CAAC;;MAE3C,OAAOb,UAAU,CAACkB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEQH,aAAa,CAACH,GAAqB;IACzC,MAAMW,KAAK,GAAG,IAAI,CAACd,YAAY,CAACe,QAAQ,EAAE;IAE1C,IAAID,KAAK,IAAI,CAACX,GAAG,CAACQ,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACT,GAAG,CAACQ,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACpF,OAAOT,GAAG,CAACa,KAAK,CAAC;QACfC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUJ,KAAK;;OAEjC,CAAC;;IAGJ,OAAOX,GAAG;EACZ;EAEQU,cAAc,CAACV,GAAqB,EAAEC,IAAiB;IAC7D,IAAI,CAAC,IAAI,CAACe,YAAY,EAAE;MACtB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,mBAAmB,CAAChB,IAAI,CAAC,IAAI,CAAC;MAEnC,OAAO,IAAI,CAACiB,YAAY,EAAE,CAACb,IAAI,CAC7BZ,SAAS,CAAE0B,QAAsB,IAAI;QACnC,IAAI,CAACH,YAAY,GAAG,KAAK;QACzB,IAAIG,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAACJ,mBAAmB,CAAChB,IAAI,CAACkB,QAAQ,CAACE,WAAW,CAAC;UACnD,OAAOpB,IAAI,CAACG,MAAM,CAAC,IAAI,CAACD,aAAa,CAACH,GAAG,CAAC,CAAC;SAC5C,MAAM;UACL,IAAI,CAACH,YAAY,CAACyB,cAAc,EAAE;UAClC,OAAOlC,UAAU,CAAC,sBAAsB,CAAC;;MAE7C,CAAC,CAAC,EACFE,UAAU,CAAEgB,KAAK,IAAI;QACnB,IAAI,CAACU,YAAY,GAAG,KAAK;QACzB,IAAI,CAACnB,YAAY,CAACyB,cAAc,EAAE;QAClC,OAAOlC,UAAU,CAACkB,KAAK,CAAC;MAC1B,CAAC,CAAC,CACH;KACF,MAAM;MACL;MACA,OAAO,IAAI,CAACW,mBAAmB,CAACZ,IAAI,CAClCd,MAAM,CAACoB,KAAK,IAAIA,KAAK,IAAI,IAAI,CAAC,EAC9BnB,IAAI,CAAC,CAAC,CAAC,EACPC,SAAS,CAAC,MAAMQ,IAAI,CAACG,MAAM,CAAC,IAAI,CAACD,aAAa,CAACH,GAAG,CAAC,CAAC,CAAC,CACtD;;EAEL;EAEQkB,YAAY;IAClB,MAAMA,YAAY,GAAG,IAAI,CAACrB,YAAY,CAAC0B,eAAe,EAAE;IACxD,IAAI,CAACL,YAAY,EAAE;MACjB,OAAO9B,UAAU,CAAC,4BAA4B,CAAC;;IAGjD;IACA,IAAI,CAAC,IAAI,CAACoC,UAAU,EAAE;MACpB,IAAI,CAACA,UAAU,GAAG,IAAI,CAAC1B,QAAQ,CAAC2B,GAAG,CAACtC,UAAU,CAAC;;IAGjD,OAAO,IAAI,CAACqC,UAAU,CAACE,IAAI,CAAe,yBAAyB,EAAE;MAAER;IAAY,CAAE,CAAC,CACnFb,IAAI,CACHX,GAAG,CAACyB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;QAC5C,MAAMM,UAAU,GAAG,IAAI,CAAC9B,YAAY,CAAC+B,aAAa,EAAE;QACpD,IAAI,CAAC/B,YAAY,CAACgC,QAAQ,CAACV,QAAQ,CAACE,WAAW,EAAEM,UAAU,CAAC;QAC5D,IAAIR,QAAQ,CAACD,YAAY,EAAE;UACzB,IAAI,CAACrB,YAAY,CAACiC,eAAe,CAACX,QAAQ,CAACD,YAAY,EAAES,UAAU,CAAC;;QAEtE,IAAIR,QAAQ,CAACY,IAAI,EAAE;UACjB,IAAI,CAAClC,YAAY,CAACmC,OAAO,CAACb,QAAQ,CAACY,IAAI,EAAEJ,UAAU,CAAC;;;IAG1D,CAAC,CAAC,EACFrC,UAAU,CAACgB,KAAK,IAAG;MACjB,IAAI,CAACT,YAAY,CAACyB,cAAc,EAAE;MAClC,OAAOlC,UAAU,CAACkB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;;;uBApGWX,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAsC,SAAftC,eAAe;IAAA;EAAA", "names": ["HttpClient", "throwError", "BehaviorSubject", "catchError", "filter", "take", "switchMap", "tap", "AuthInterceptor", "constructor", "tokenService", "injector", "intercept", "req", "next", "authReq", "addAuthHeader", "handle", "pipe", "error", "status", "url", "includes", "handle401Error", "token", "getToken", "clone", "setHeaders", "Authorization", "isRefreshing", "refreshTokenSubject", "refreshToken", "response", "success", "accessToken", "clearAllTokens", "getRefreshToken", "httpClient", "get", "post", "rememberMe", "getRememberMe", "setToken", "setRefreshToken", "user", "setUser", "factory"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable, Injector } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpClient } from '@angular/common/http';\r\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\r\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\r\nimport { TokenService } from '../services/token.service';\r\nimport { AuthResponse } from '../models/auth.models';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n  private isRefreshing = false;\r\n  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);\r\n  private httpClient?: HttpClient;\r\n\r\n  constructor(\r\n    private tokenService: TokenService,\r\n    private injector: Injector\r\n  ) {}\r\n\r\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    // Add auth header if user is authenticated\r\n    const authReq = this.addAuthHeader(req);\r\n\r\n    return next.handle(authReq).pipe(\r\n      catchError((error: HttpErrorResponse) => {\r\n        if (error.status === 401 && !authReq.url.includes('/auth/')) {\r\n          return this.handle401Error(authReq, next);\r\n        }\r\n        return throwError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  private addAuthHeader(req: HttpRequest<any>): HttpRequest<any> {\r\n    const token = this.tokenService.getToken();\r\n\r\n    if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {\r\n      return req.clone({\r\n        setHeaders: {\r\n          Authorization: `Bearer ${token}`\r\n        }\r\n      });\r\n    }\r\n\r\n    return req;\r\n  }\r\n\r\n  private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    if (!this.isRefreshing) {\r\n      this.isRefreshing = true;\r\n      this.refreshTokenSubject.next(null);\r\n\r\n      return this.refreshToken().pipe(\r\n        switchMap((response: AuthResponse) => {\r\n          this.isRefreshing = false;\r\n          if (response.success && response.accessToken) {\r\n            this.refreshTokenSubject.next(response.accessToken);\r\n            return next.handle(this.addAuthHeader(req));\r\n          } else {\r\n            this.tokenService.clearAllTokens();\r\n            return throwError('Token refresh failed');\r\n          }\r\n        }),\r\n        catchError((error) => {\r\n          this.isRefreshing = false;\r\n          this.tokenService.clearAllTokens();\r\n          return throwError(error);\r\n        })\r\n      );\r\n    } else {\r\n      // Wait for refresh to complete\r\n      return this.refreshTokenSubject.pipe(\r\n        filter(token => token != null),\r\n        take(1),\r\n        switchMap(() => next.handle(this.addAuthHeader(req)))\r\n      );\r\n    }\r\n  }\r\n\r\n  private refreshToken(): Observable<AuthResponse> {\r\n    const refreshToken = this.tokenService.getRefreshToken();\r\n    if (!refreshToken) {\r\n      return throwError('No refresh token available');\r\n    }\r\n\r\n    // Lazy load HttpClient to avoid circular dependency\r\n    if (!this.httpClient) {\r\n      this.httpClient = this.injector.get(HttpClient);\r\n    }\r\n\r\n    return this.httpClient.post<AuthResponse>('/api/auth/refresh-token', { refreshToken })\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken) {\r\n            const rememberMe = this.tokenService.getRememberMe();\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            if (response.user) {\r\n              this.tokenService.setUser(response.user, rememberMe);\r\n            }\r\n          }\r\n        }),\r\n        catchError(error => {\r\n          this.tokenService.clearAllTokens();\r\n          return throwError(error);\r\n        })\r\n      );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}