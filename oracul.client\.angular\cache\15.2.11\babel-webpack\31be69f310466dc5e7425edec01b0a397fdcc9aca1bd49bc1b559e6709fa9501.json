{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-bar\";\nimport * as i7 from \"@angular/material/chips\";\nimport * as i8 from \"@angular/material/tooltip\";\nfunction ProfileCardComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.profile.professionalTitle, \" \");\n  }\n}\nfunction ProfileCardComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"mat-icon\", 22);\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.displayLocation, \" \");\n  }\n}\nfunction ProfileCardComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-icon\", 24);\n    i0.ɵɵtext(2, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.experienceYears, \" year\", ctx_r2.experienceYears !== 1 ? \"s\" : \"\", \" experience\");\n  }\n}\nfunction ProfileCardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"h4\", 26)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" About \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.truncateText(ctx_r3.profile.summary, ctx_r3.compact ? 120 : 200), \" \");\n  }\n}\nfunction ProfileCardComponent_div_13_mat_chip_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r12, \" \");\n  }\n}\nfunction ProfileCardComponent_div_13_mat_chip_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r11.profile.skills.length - ctx_r11.topSkills.length, \" more \");\n  }\n}\nfunction ProfileCardComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h4\", 26)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Skills & Expertise \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"mat-chip-set\", 30);\n    i0.ɵɵtemplate(7, ProfileCardComponent_div_13_mat_chip_7_Template, 2, 1, \"mat-chip\", 31);\n    i0.ɵɵtemplate(8, ProfileCardComponent_div_13_mat_chip_8_Template, 2, 1, \"mat-chip\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.topSkills);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.profile.skills.length > ctx_r4.topSkills.length);\n  }\n}\nfunction ProfileCardComponent_div_14_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProfileCardComponent_div_14_div_6_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onEmailClick($event));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"email\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 40);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r13.primaryEmail);\n  }\n}\nfunction ProfileCardComponent_div_14_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileCardComponent_div_14_div_7_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onPhoneClick($event));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 40);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.formatPhoneNumber(ctx_r14.primaryPhone));\n  }\n}\nfunction ProfileCardComponent_div_14_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ProfileCardComponent_div_14_div_8_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onWebsiteClick($event));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 40);\n    i0.ɵɵtext(5, \"Website\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileCardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h4\", 26)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Contact Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵtemplate(6, ProfileCardComponent_div_14_div_6_Template, 6, 1, \"div\", 37);\n    i0.ɵɵtemplate(7, ProfileCardComponent_div_14_div_7_Template, 6, 1, \"div\", 37);\n    i0.ɵɵtemplate(8, ProfileCardComponent_div_14_div_8_Template, 6, 0, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.primaryEmail);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.primaryPhone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.profile.contactInfo == null ? null : ctx_r5.profile.contactInfo.website);\n  }\n}\nfunction ProfileCardComponent_div_15_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r22.getTotalEndorsements(), \" endorsements\");\n  }\n}\nfunction ProfileCardComponent_div_15_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"work_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r23.profile.portfolioItems.length, \" project\", ctx_r23.profile.portfolioItems.length !== 1 ? \"s\" : \"\", \"\");\n  }\n}\nfunction ProfileCardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProfileCardComponent_div_15_div_6_Template, 5, 1, \"div\", 45);\n    i0.ɵɵtemplate(7, ProfileCardComponent_div_15_div_7_Template, 5, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r6.profile.profileViews, \" views\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.profile.skills.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r6.profile.portfolioItems == null ? null : ctx_r6.profile.portfolioItems.length) > 0);\n  }\n}\nfunction ProfileCardComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProfileCardComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onViewProfile());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCardComponent_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProfileCardComponent_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onContact());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 49);\n    i0.ɵɵelementStart(2, \"span\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r9.profile.profileCompletionPercentage)(\"color\", ctx_r9.profileCompletionColor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.profile.profileCompletionPercentage, \"% complete\");\n  }\n}\nexport class ProfileCardComponent {\n  constructor(router) {\n    this.router = router;\n    this.showContactButton = true;\n    this.showViewProfileButton = true;\n    this.compact = false;\n    this.contactClicked = new EventEmitter();\n    this.profileClicked = new EventEmitter();\n  }\n  get fullName() {\n    return `${this.profile.firstName} ${this.profile.lastName}`;\n  }\n  get displayLocation() {\n    return this.profile.location?.displayLocation || '';\n  }\n  get primaryEmail() {\n    return this.profile.contactInfo?.email || '';\n  }\n  get primaryPhone() {\n    const primaryPhone = this.profile.contactInfo?.phoneNumbers?.find(p => p.isPrimary && p.isPublic);\n    return primaryPhone?.number || '';\n  }\n  get topSkills() {\n    return this.profile.skills.sort((a, b) => b.endorsements - a.endorsements).slice(0, this.compact ? 3 : 5).map(skill => skill.name);\n  }\n  get experienceYears() {\n    if (!this.profile.experiences?.length) return 0;\n    const totalMonths = this.profile.experiences.reduce((total, exp) => {\n      const startDate = new Date(exp.startDate);\n      const endDate = exp.endDate ? new Date(exp.endDate) : new Date();\n      const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth());\n      return total + months;\n    }, 0);\n    return Math.floor(totalMonths / 12);\n  }\n  get hasContactInfo() {\n    return !!(this.primaryEmail || this.primaryPhone || this.profile.contactInfo?.website);\n  }\n  get profileCompletionColor() {\n    const percentage = this.profile.profileCompletionPercentage;\n    if (percentage >= 80) return 'primary';\n    if (percentage >= 60) return 'accent';\n    return 'warn';\n  }\n  onViewProfile() {\n    this.profileClicked.emit(this.profile);\n    this.router.navigate(['/profile', this.profile.slug]);\n  }\n  onContact() {\n    this.contactClicked.emit(this.profile);\n  }\n  onEmailClick(event) {\n    event.stopPropagation();\n    if (this.primaryEmail) {\n      window.location.href = `mailto:${this.primaryEmail}`;\n    }\n  }\n  onPhoneClick(event) {\n    event.stopPropagation();\n    if (this.primaryPhone) {\n      window.location.href = `tel:${this.primaryPhone}`;\n    }\n  }\n  onWebsiteClick(event) {\n    event.stopPropagation();\n    if (this.profile.contactInfo?.website) {\n      window.open(this.profile.contactInfo.website, '_blank');\n    }\n  }\n  formatPhoneNumber(phone) {\n    // Simple phone formatting - can be enhanced based on requirements\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 10) {\n      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n  }\n  truncateText(text, maxLength) {\n    if (!text) return '';\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  }\n  getTotalEndorsements() {\n    return this.profile.skills.reduce((total, skill) => total + skill.endorsements, 0);\n  }\n  static {\n    this.ɵfac = function ProfileCardComponent_Factory(t) {\n      return new (t || ProfileCardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileCardComponent,\n      selectors: [[\"app-profile-card\"]],\n      inputs: {\n        profile: \"profile\",\n        showContactButton: \"showContactButton\",\n        showViewProfileButton: \"showViewProfileButton\",\n        compact: \"compact\"\n      },\n      outputs: {\n        contactClicked: \"contactClicked\",\n        profileClicked: \"profileClicked\"\n      },\n      decls: 23,\n      vars: 15,\n      consts: [[1, \"profile-card\", 3, \"click\"], [1, \"profile-header\"], [\"mat-card-avatar\", \"\", 1, \"profile-avatar\"], [1, \"avatar-image\", 3, \"src\", \"alt\", \"error\"], [1, \"profile-name\"], [1, \"profile-details\"], [1, \"title-location\"], [\"class\", \"professional-title\", 4, \"ngIf\"], [\"class\", \"location\", 4, \"ngIf\"], [\"class\", \"experience-info\", 4, \"ngIf\"], [1, \"profile-content\"], [\"class\", \"about-section\", 4, \"ngIf\"], [\"class\", \"skills-section\", 4, \"ngIf\"], [\"class\", \"contact-section\", 4, \"ngIf\"], [\"class\", \"stats-section\", 4, \"ngIf\"], [1, \"profile-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"view-profile-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", \"class\", \"contact-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Share Profile\", 1, \"share-btn\"], [\"class\", \"completion-indicator\", 4, \"ngIf\"], [1, \"professional-title\"], [1, \"location\"], [1, \"location-icon\"], [1, \"experience-info\"], [1, \"experience-icon\"], [1, \"about-section\"], [1, \"section-title\"], [1, \"about-text\"], [1, \"skills-section\"], [1, \"skills-container\"], [1, \"skills-chips\"], [\"class\", \"skill-chip\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-skills-chip\", 4, \"ngIf\"], [1, \"skill-chip\"], [1, \"more-skills-chip\"], [1, \"contact-section\"], [1, \"contact-info\"], [\"class\", \"contact-item\", 4, \"ngIf\"], [1, \"contact-item\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Send Email\", 1, \"contact-button\", \"email-button\", 3, \"click\"], [1, \"contact-text\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Call\", 1, \"contact-button\", \"phone-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Visit Website\", 1, \"contact-button\", \"website-button\", 3, \"click\"], [1, \"stats-section\"], [1, \"stat-item\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"view-profile-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"contact-btn\", 3, \"click\"], [1, \"completion-indicator\"], [1, \"completion-bar\", 3, \"value\", \"color\"], [1, \"completion-text\"]],\n      template: function ProfileCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\", 0);\n          i0.ɵɵlistener(\"click\", function ProfileCardComponent_Template_mat_card_click_0_listener() {\n            return ctx.onViewProfile();\n          });\n          i0.ɵɵelementStart(1, \"mat-card-header\", 1)(2, \"div\", 2)(3, \"img\", 3);\n          i0.ɵɵlistener(\"error\", function ProfileCardComponent_Template_img_error_3_listener($event) {\n            return $event.target.src = \"/assets/images/default-avatar.png\";\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-card-title\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-card-subtitle\", 5)(7, \"div\", 6);\n          i0.ɵɵtemplate(8, ProfileCardComponent_span_8_Template, 2, 1, \"span\", 7);\n          i0.ɵɵtemplate(9, ProfileCardComponent_span_9_Template, 4, 1, \"span\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, ProfileCardComponent_div_10_Template, 5, 2, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"mat-card-content\", 10);\n          i0.ɵɵtemplate(12, ProfileCardComponent_div_12_Template, 7, 1, \"div\", 11);\n          i0.ɵɵtemplate(13, ProfileCardComponent_div_13_Template, 9, 2, \"div\", 12);\n          i0.ɵɵtemplate(14, ProfileCardComponent_div_14_Template, 9, 3, \"div\", 13);\n          i0.ɵɵtemplate(15, ProfileCardComponent_div_15_Template, 8, 3, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-card-actions\", 15);\n          i0.ɵɵtemplate(17, ProfileCardComponent_button_17_Template, 4, 0, \"button\", 16);\n          i0.ɵɵtemplate(18, ProfileCardComponent_button_18_Template, 4, 0, \"button\", 17);\n          i0.ɵɵelementStart(19, \"button\", 18)(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"share\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(22, ProfileCardComponent_div_22_Template, 4, 3, \"div\", 19);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"compact\", ctx.compact);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.profile.profilePhotoUrl || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.fullName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.profile.professionalTitle);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.displayLocation);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.experienceYears > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.profile.summary);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.topSkills.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasContactInfo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.compact);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showViewProfileButton);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showContactButton && ctx.hasContactInfo);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.profile.profileCompletionPercentage < 100);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.MatCard, i3.MatCardActions, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, i4.MatButton, i4.MatIconButton, i5.MatIcon, i6.MatProgressBar, i7.MatChip, i7.MatChipSet, i8.MatTooltip],\n      styles: [\".profile-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n}\\n\\n.profile-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n  transform: translateY(-2px);\\n  border-color: #673ab7;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 3px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.profile-name[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n  line-height: 1.2;\\n}\\n\\n.profile-details[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n}\\n\\n.title-location[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 8px;\\n}\\n\\n.professional-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #673ab7;\\n  font-size: 0.95rem;\\n}\\n\\n.location[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n\\n.location-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.experience-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #555;\\n  font-size: 0.85rem;\\n}\\n\\n.experience-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6f00;\\n}\\n\\n\\n.profile-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 8px 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  color: #673ab7;\\n}\\n\\n\\n.about-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.about-text[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.5;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n\\n\\n.skills-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.skills-container[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n\\n.skills-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 6px;\\n}\\n\\n.skill-chip[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.8rem;\\n  height: 28px;\\n}\\n\\n.more-skills-chip[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #673ab7;\\n  font-size: 0.8rem;\\n  height: 28px;\\n}\\n\\n\\n.contact-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.contact-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.contact-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  min-width: 32px;\\n  padding: 0;\\n}\\n\\n.contact-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.email-button[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n.phone-button[_ngcontent-%COMP%] {\\n  color: #388e3c;\\n}\\n\\n.website-button[_ngcontent-%COMP%] {\\n  color: #f57c00;\\n}\\n\\n.contact-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #555;\\n  flex: 1;\\n}\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 12px 0;\\n  border-top: 1px solid #e0e0e0;\\n  margin-top: auto;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #666;\\n  font-size: 0.8rem;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #999;\\n}\\n\\n\\n.profile-actions[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-top: 1px solid #e0e0e0;\\n  background-color: #fafafa;\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.view-profile-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n.contact-btn[_ngcontent-%COMP%] {\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n.share-btn[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  min-width: 36px;\\n}\\n\\n.profile-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  margin-right: 4px;\\n}\\n\\n.share-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0;\\n}\\n\\n\\n.completion-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  padding: 8px 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.completion-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 4px;\\n}\\n\\n.completion-text[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  white-space: nowrap;\\n}\\n\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%] {\\n  gap: 12px;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .skills-chips[_ngcontent-%COMP%] {\\n  gap: 4px;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .skill-chip[_ngcontent-%COMP%], .profile-card.compact[_ngcontent-%COMP%]   .more-skills-chip[_ngcontent-%COMP%] {\\n  height: 24px;\\n  font-size: 0.75rem;\\n}\\n\\n.profile-card.compact[_ngcontent-%COMP%]   .stats-section[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .profile-card[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  \\n  .profile-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  \\n  .profile-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  \\n  .profile-actions[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  \\n  .view-profile-btn[_ngcontent-%COMP%], .contact-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  \\n  .stats-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    align-items: flex-start;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .profile-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  \\n  .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  \\n  .contact-info[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  \\n  .contact-item[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;;;;;;;;;;;;ICe9DC,gCAAmE;IACjEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,iEACF;;;;;IACAA,gCAA+C;IACbA,2BAAW;IAAAA,iBAAW;IACtDA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,uDACF;;;;;IAGFA,+BAAyD;IACrBA,oBAAI;IAAAA,iBAAW;IACjDA,4BAAM;IAAAA,YAA2E;IAAAA,iBAAO;;;;IAAlFA,eAA2E;IAA3EA,kHAA2E;;;;;IAOrFA,+BAAmD;IAErCA,sBAAM;IAAAA,iBAAW;IAC3BA,uBACF;IAAAA,iBAAK;IACLA,6BAAsB;IACpBA,YACF;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,wGACF;;;;;IAWIA,oCAA6D;IAC3DA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,0CACF;;;;;IACAA,oCAE2B;IACzBA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,+FACF;;;;;IAdNA,+BAAyD;IAE3CA,0BAAU;IAAAA,iBAAW;IAC/BA,oCACF;IAAAA,iBAAK;IACLA,+BAA8B;IAE1BA,uFAEW;IACXA,uFAIW;IACbA,iBAAe;;;;IAReA,eAAY;IAAZA,0CAAY;IAIrCA,eAA8C;IAA9CA,6EAA8C;;;;;;IAenDA,+BAA+C;IAI3CA;MAAAA;MAAA;MAAA,OAASA,2CAAoB;IAAA,EAAC;IAE9BA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAE5BA,gCAA2B;IAAAA,YAAkB;IAAAA,iBAAO;;;;IAAzBA,eAAkB;IAAlBA,0CAAkB;;;;;;IAG/CA,+BAA+C;IAI3CA;MAAAA;MAAA;MAAA,OAASA,2CAAoB;IAAA,EAAC;IAE9BA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAE5BA,gCAA2B;IAAAA,YAAqC;IAAAA,iBAAO;;;;IAA5CA,eAAqC;IAArCA,qEAAqC;;;;;;IAGlEA,+BAA+D;IAI3DA;MAAAA;MAAA;MAAA,OAASA,6CAAsB;IAAA,EAAC;IAEhCA,gCAAU;IAAAA,wBAAQ;IAAAA,iBAAW;IAE/BA,gCAA2B;IAAAA,uBAAO;IAAAA,iBAAO;;;;;IApC/CA,+BAAoD;IAEtCA,4BAAY;IAAAA,iBAAW;IACjCA,qCACF;IAAAA,iBAAK;IACLA,+BAA0B;IACxBA,6EASM;IAENA,6EASM;IAENA,6EASM;IACRA,iBAAM;;;;IAhCuBA,eAAkB;IAAlBA,0CAAkB;IAWlBA,eAAkB;IAAlBA,0CAAkB;IAWlBA,eAAkC;IAAlCA,qGAAkC;;;;;IAmB/DA,+BAAyD;IAC7CA,wBAAQ;IAAAA,iBAAW;IAC7BA,4BAAM;IAAAA,YAAyC;IAAAA,iBAAO;;;;IAAhDA,eAAyC;IAAzCA,0EAAyC;;;;;IAEjDA,+BAAkE;IACtDA,4BAAY;IAAAA,iBAAW;IACjCA,4BAAM;IAAAA,YAA+F;IAAAA,iBAAO;;;;IAAtGA,eAA+F;IAA/FA,wIAA+F;;;;;IAXzGA,+BAA4C;IAE9BA,0BAAU;IAAAA,iBAAW;IAC/BA,4BAAM;IAAAA,YAAgC;IAAAA,iBAAO;IAE/CA,6EAGM;IACNA,6EAGM;IACRA,iBAAM;;;;IAVIA,eAAgC;IAAhCA,gEAAgC;IAEhBA,eAA+B;IAA/BA,uDAA+B;IAI/BA,eAAwC;IAAxCA,gHAAwC;;;;;;IASlEA,kCAK4B;IAA1BA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IACzBA,gCAAU;IAAAA,0BAAU;IAAAA,iBAAW;IAC/BA,8BACF;IAAAA,iBAAS;;;;;;IAETA,kCAKwB;IAAtBA;MAAAA;MAAA;MAAA,OAASA,kCAAW;IAAA,EAAC;IACrBA,gCAAU;IAAAA,4BAAY;IAAAA,iBAAW;IACjCA,yBACF;IAAAA,iBAAS;;;;;IAWXA,+BAAoF;IAClFA,uCAImB;IACnBA,gCAA8B;IAAAA,YAAmD;IAAAA,iBAAO;;;;IAJtFA,eAA6C;IAA7CA,kEAA6C;IAIjBA,eAAmD;IAAnDA,mFAAmD;;;ADtJrF,OAAM,MAAOC,oBAAoB;EAQ/BC,YAAoBC,MAAc;IAAd,WAAM,GAANA,MAAM;IANjB,sBAAiB,GAAG,IAAI;IACxB,0BAAqB,GAAG,IAAI;IAC5B,YAAO,GAAG,KAAK;IACd,mBAAc,GAAG,IAAIJ,YAAY,EAAe;IAChD,mBAAc,GAAG,IAAIA,YAAY,EAAe;EAErB;EAErC,IAAIK,QAAQ;IACV,OAAO,GAAG,IAAI,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,CAACD,OAAO,CAACE,QAAQ,EAAE;EAC7D;EAEA,IAAIC,eAAe;IACjB,OAAO,IAAI,CAACH,OAAO,CAACI,QAAQ,EAAED,eAAe,IAAI,EAAE;EACrD;EAEA,IAAIE,YAAY;IACd,OAAO,IAAI,CAACL,OAAO,CAACM,WAAW,EAAEC,KAAK,IAAI,EAAE;EAC9C;EAEA,IAAIC,YAAY;IACd,MAAMA,YAAY,GAAG,IAAI,CAACR,OAAO,CAACM,WAAW,EAAEG,YAAY,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,IAAID,CAAC,CAACE,QAAQ,CAAC;IACjG,OAAOL,YAAY,EAAEM,MAAM,IAAI,EAAE;EACnC;EAEA,IAAIC,SAAS;IACX,OAAO,IAAI,CAACf,OAAO,CAACgB,MAAM,CACvBC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,YAAY,GAAGF,CAAC,CAACE,YAAY,CAAC,CAC/CC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAC9BC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAC7B;EAEA,IAAIC,eAAe;IACjB,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC2B,WAAW,EAAEC,MAAM,EAAE,OAAO,CAAC;IAE/C,MAAMC,WAAW,GAAG,IAAI,CAAC7B,OAAO,CAAC2B,WAAW,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;MACjE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACF,GAAG,CAACC,SAAS,CAAC;MACzC,MAAME,OAAO,GAAGH,GAAG,CAACG,OAAO,GAAG,IAAID,IAAI,CAACF,GAAG,CAACG,OAAO,CAAC,GAAG,IAAID,IAAI,EAAE;MAChE,MAAME,MAAM,GAAG,CAACD,OAAO,CAACE,WAAW,EAAE,GAAGJ,SAAS,CAACI,WAAW,EAAE,IAAI,EAAE,IACtDF,OAAO,CAACG,QAAQ,EAAE,GAAGL,SAAS,CAACK,QAAQ,EAAE,CAAC;MACzD,OAAOP,KAAK,GAAGK,MAAM;IACvB,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOG,IAAI,CAACC,KAAK,CAACX,WAAW,GAAG,EAAE,CAAC;EACrC;EAEA,IAAIY,cAAc;IAChB,OAAO,CAAC,EAAE,IAAI,CAACpC,YAAY,IAAI,IAAI,CAACG,YAAY,IAAI,IAAI,CAACR,OAAO,CAACM,WAAW,EAAEoC,OAAO,CAAC;EACxF;EAEA,IAAIC,sBAAsB;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAC5C,OAAO,CAAC6C,2BAA2B;IAC3D,IAAID,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ;IACrC,OAAO,MAAM;EACf;EAEAE,aAAa;IACX,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAChD,OAAO,CAAC;IACtC,IAAI,CAACF,MAAM,CAACmD,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACjD,OAAO,CAACkD,IAAI,CAAC,CAAC;EACvD;EAEAC,SAAS;IACP,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC,IAAI,CAAChD,OAAO,CAAC;EACxC;EAEAqD,YAAY,CAACC,KAAY;IACvBA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAAClD,YAAY,EAAE;MACrBmD,MAAM,CAACpD,QAAQ,CAACqD,IAAI,GAAG,UAAU,IAAI,CAACpD,YAAY,EAAE;;EAExD;EAEAqD,YAAY,CAACJ,KAAY;IACvBA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAAC/C,YAAY,EAAE;MACrBgD,MAAM,CAACpD,QAAQ,CAACqD,IAAI,GAAG,OAAO,IAAI,CAACjD,YAAY,EAAE;;EAErD;EAEAmD,cAAc,CAACL,KAAY;IACzBA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACvD,OAAO,CAACM,WAAW,EAAEoC,OAAO,EAAE;MACrCc,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC5D,OAAO,CAACM,WAAW,CAACoC,OAAO,EAAE,QAAQ,CAAC;;EAE3D;EAEAmB,iBAAiB,CAACC,KAAa;IAC7B;IACA,MAAMC,OAAO,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACxC,IAAID,OAAO,CAACnC,MAAM,KAAK,EAAE,EAAE;MACzB,OAAO,IAAImC,OAAO,CAAC1C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK0C,OAAO,CAAC1C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI0C,OAAO,CAAC1C,KAAK,CAAC,CAAC,CAAC,EAAE;;IAE9E,OAAOyC,KAAK;EACd;EAEAG,YAAY,CAACC,IAAY,EAAEC,SAAiB;IAC1C,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACtC,MAAM,GAAGuC,SAAS,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK,GAAGD,IAAI;EAC9E;EAEAG,oBAAoB;IAClB,OAAO,IAAI,CAACrE,OAAO,CAACgB,MAAM,CAACc,MAAM,CAAC,CAACC,KAAK,EAAEP,KAAK,KAAKO,KAAK,GAAGP,KAAK,CAACJ,YAAY,EAAE,CAAC,CAAC;EACpF;;;uBAzGWxB,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAA0E;MAAAC;QAAAvE;QAAAwE;QAAAC;QAAAnD;MAAA;MAAAoD;QAAAtB;QAAAL;MAAA;MAAA4B;MAAAC;MAAAC;MAAAC;QAAA;UCTjCnF,mCAAmF;UAA1BA;YAAA,OAASoF,mBAAe;UAAA,EAAC;UAEhFpF,0CAAwC;UAMlCA;YAAA,2BAA2B,mCAAmC;UAAA,EAAC;UAJjEA,iBAIkE;UAGpEA,yCAAqC;UAAAA,YAAc;UAAAA,iBAAiB;UAEpEA,4CAA2C;UAEvCA,uEAEO;UACPA,uEAGO;UACTA,iBAAM;UAENA,uEAGM;UACRA,iBAAoB;UAGtBA,6CAA0C;UAExCA,wEAQM;UAGNA,wEAiBM;UAGNA,wEAuCM;UAGNA,wEAaM;UACRA,iBAAmB;UAGnBA,6CAA0C;UACxCA,8EAQS;UAETA,8EAQS;UAETA,mCAG6B;UACjBA,sBAAK;UAAAA,iBAAW;UAK9BA,wEAOM;UACRA,iBAAW;;;UAjKoBA,sCAAyB;UAKhDA,eAAsE;UAAtEA,0GAAsE;UAMrCA,eAAc;UAAdA,kCAAc;UAIbA,eAA+B;UAA/BA,oDAA+B;UAGzCA,eAAqB;UAArBA,0CAAqB;UAMjBA,eAAyB;UAAzBA,8CAAyB;UAS7BA,eAAqB;UAArBA,0CAAqB;UAWpBA,eAA0B;UAA1BA,+CAA0B;UAoBzBA,eAAoB;UAApBA,yCAAoB;UA0CtBA,eAAc;UAAdA,mCAAc;UAsBvCA,eAA2B;UAA3BA,gDAA2B;UAU3BA,eAAyC;UAAzCA,kEAAyC;UAeXA,eAA+C;UAA/CA,oEAA+C", "names": ["EventEmitter", "i0", "ProfileCardComponent", "constructor", "router", "fullName", "profile", "firstName", "lastName", "displayLocation", "location", "primaryEmail", "contactInfo", "email", "primaryPhone", "phoneNumbers", "find", "p", "isPrimary", "isPublic", "number", "topSkills", "skills", "sort", "a", "b", "endorsements", "slice", "compact", "map", "skill", "name", "experienceYears", "experiences", "length", "totalMonths", "reduce", "total", "exp", "startDate", "Date", "endDate", "months", "getFullYear", "getMonth", "Math", "floor", "hasContactInfo", "website", "profileCompletionColor", "percentage", "profileCompletionPercentage", "onViewProfile", "profileClicked", "emit", "navigate", "slug", "onContact", "contactClicked", "onEmailClick", "event", "stopPropagation", "window", "href", "onPhoneClick", "onWebsiteClick", "open", "formatPhoneNumber", "phone", "cleaned", "replace", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "getTotalEndorsements", "selectors", "inputs", "showContactButton", "showViewProfileButton", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-card\\profile-card.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-card\\profile-card.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { UserProfile } from '../../models/profile.models';\n\n@Component({\n  selector: 'app-profile-card',\n  templateUrl: './profile-card.component.html',\n  styleUrls: ['./profile-card.component.css']\n})\nexport class ProfileCardComponent {\n  @Input() profile!: UserProfile;\n  @Input() showContactButton = true;\n  @Input() showViewProfileButton = true;\n  @Input() compact = false;\n  @Output() contactClicked = new EventEmitter<UserProfile>();\n  @Output() profileClicked = new EventEmitter<UserProfile>();\n\n  constructor(private router: Router) {}\n\n  get fullName(): string {\n    return `${this.profile.firstName} ${this.profile.lastName}`;\n  }\n\n  get displayLocation(): string {\n    return this.profile.location?.displayLocation || '';\n  }\n\n  get primaryEmail(): string {\n    return this.profile.contactInfo?.email || '';\n  }\n\n  get primaryPhone(): string {\n    const primaryPhone = this.profile.contactInfo?.phoneNumbers?.find(p => p.isPrimary && p.isPublic);\n    return primaryPhone?.number || '';\n  }\n\n  get topSkills(): string[] {\n    return this.profile.skills\n      .sort((a, b) => b.endorsements - a.endorsements)\n      .slice(0, this.compact ? 3 : 5)\n      .map(skill => skill.name);\n  }\n\n  get experienceYears(): number {\n    if (!this.profile.experiences?.length) return 0;\n    \n    const totalMonths = this.profile.experiences.reduce((total, exp) => {\n      const startDate = new Date(exp.startDate);\n      const endDate = exp.endDate ? new Date(exp.endDate) : new Date();\n      const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 + \n                    (endDate.getMonth() - startDate.getMonth());\n      return total + months;\n    }, 0);\n    \n    return Math.floor(totalMonths / 12);\n  }\n\n  get hasContactInfo(): boolean {\n    return !!(this.primaryEmail || this.primaryPhone || this.profile.contactInfo?.website);\n  }\n\n  get profileCompletionColor(): string {\n    const percentage = this.profile.profileCompletionPercentage;\n    if (percentage >= 80) return 'primary';\n    if (percentage >= 60) return 'accent';\n    return 'warn';\n  }\n\n  onViewProfile(): void {\n    this.profileClicked.emit(this.profile);\n    this.router.navigate(['/profile', this.profile.slug]);\n  }\n\n  onContact(): void {\n    this.contactClicked.emit(this.profile);\n  }\n\n  onEmailClick(event: Event): void {\n    event.stopPropagation();\n    if (this.primaryEmail) {\n      window.location.href = `mailto:${this.primaryEmail}`;\n    }\n  }\n\n  onPhoneClick(event: Event): void {\n    event.stopPropagation();\n    if (this.primaryPhone) {\n      window.location.href = `tel:${this.primaryPhone}`;\n    }\n  }\n\n  onWebsiteClick(event: Event): void {\n    event.stopPropagation();\n    if (this.profile.contactInfo?.website) {\n      window.open(this.profile.contactInfo.website, '_blank');\n    }\n  }\n\n  formatPhoneNumber(phone: string): string {\n    // Simple phone formatting - can be enhanced based on requirements\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 10) {\n      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n  }\n\n  truncateText(text: string, maxLength: number): string {\n    if (!text) return '';\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  }\n\n  getTotalEndorsements(): number {\n    return this.profile.skills.reduce((total, skill) => total + skill.endorsements, 0);\n  }\n}\n", "<mat-card class=\"profile-card\" [class.compact]=\"compact\" (click)=\"onViewProfile()\">\n  <!-- Profile Header -->\n  <mat-card-header class=\"profile-header\">\n    <div mat-card-avatar class=\"profile-avatar\">\n      <img \n        [src]=\"profile.profilePhotoUrl || '/assets/images/default-avatar.png'\" \n        [alt]=\"fullName\"\n        class=\"avatar-image\"\n        (error)=\"$event.target.src='/assets/images/default-avatar.png'\">\n    </div>\n    \n    <mat-card-title class=\"profile-name\">{{ fullName }}</mat-card-title>\n    \n    <mat-card-subtitle class=\"profile-details\">\n      <div class=\"title-location\">\n        <span class=\"professional-title\" *ngIf=\"profile.professionalTitle\">\n          {{ profile.professionalTitle }}\n        </span>\n        <span class=\"location\" *ngIf=\"displayLocation\">\n          <mat-icon class=\"location-icon\">location_on</mat-icon>\n          {{ displayLocation }}\n        </span>\n      </div>\n      \n      <div class=\"experience-info\" *ngIf=\"experienceYears > 0\">\n        <mat-icon class=\"experience-icon\">work</mat-icon>\n        <span>{{ experienceYears }} year{{ experienceYears !== 1 ? 's' : '' }} experience</span>\n      </div>\n    </mat-card-subtitle>\n  </mat-card-header>\n\n  <mat-card-content class=\"profile-content\">\n    <!-- About Section -->\n    <div class=\"about-section\" *ngIf=\"profile.summary\">\n      <h4 class=\"section-title\">\n        <mat-icon>person</mat-icon>\n        About\n      </h4>\n      <p class=\"about-text\">\n        {{ truncateText(profile.summary, compact ? 120 : 200) }}\n      </p>\n    </div>\n\n    <!-- Skills & Expertise Section -->\n    <div class=\"skills-section\" *ngIf=\"topSkills.length > 0\">\n      <h4 class=\"section-title\">\n        <mat-icon>psychology</mat-icon>\n        Skills & Expertise\n      </h4>\n      <div class=\"skills-container\">\n        <mat-chip-set class=\"skills-chips\">\n          <mat-chip *ngFor=\"let skill of topSkills\" class=\"skill-chip\">\n            {{ skill }}\n          </mat-chip>\n          <mat-chip \n            *ngIf=\"profile.skills.length > topSkills.length\" \n            class=\"more-skills-chip\">\n            +{{ profile.skills.length - topSkills.length }} more\n          </mat-chip>\n        </mat-chip-set>\n      </div>\n    </div>\n\n    <!-- Contact Information Section -->\n    <div class=\"contact-section\" *ngIf=\"hasContactInfo\">\n      <h4 class=\"section-title\">\n        <mat-icon>contact_mail</mat-icon>\n        Contact Information\n      </h4>\n      <div class=\"contact-info\">\n        <div class=\"contact-item\" *ngIf=\"primaryEmail\">\n          <button \n            mat-icon-button \n            class=\"contact-button email-button\"\n            (click)=\"onEmailClick($event)\"\n            matTooltip=\"Send Email\">\n            <mat-icon>email</mat-icon>\n          </button>\n          <span class=\"contact-text\">{{ primaryEmail }}</span>\n        </div>\n        \n        <div class=\"contact-item\" *ngIf=\"primaryPhone\">\n          <button \n            mat-icon-button \n            class=\"contact-button phone-button\"\n            (click)=\"onPhoneClick($event)\"\n            matTooltip=\"Call\">\n            <mat-icon>phone</mat-icon>\n          </button>\n          <span class=\"contact-text\">{{ formatPhoneNumber(primaryPhone) }}</span>\n        </div>\n        \n        <div class=\"contact-item\" *ngIf=\"profile.contactInfo?.website\">\n          <button \n            mat-icon-button \n            class=\"contact-button website-button\"\n            (click)=\"onWebsiteClick($event)\"\n            matTooltip=\"Visit Website\">\n            <mat-icon>language</mat-icon>\n          </button>\n          <span class=\"contact-text\">Website</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Profile Stats -->\n    <div class=\"stats-section\" *ngIf=\"!compact\">\n      <div class=\"stat-item\">\n        <mat-icon>visibility</mat-icon>\n        <span>{{ profile.profileViews }} views</span>\n      </div>\n      <div class=\"stat-item\" *ngIf=\"profile.skills.length > 0\">\n        <mat-icon>thumb_up</mat-icon>\n        <span>{{ getTotalEndorsements() }} endorsements</span>\n      </div>\n      <div class=\"stat-item\" *ngIf=\"profile.portfolioItems?.length > 0\">\n        <mat-icon>work_outline</mat-icon>\n        <span>{{ profile.portfolioItems.length }} project{{ profile.portfolioItems.length !== 1 ? 's' : '' }}</span>\n      </div>\n    </div>\n  </mat-card-content>\n\n  <!-- Card Actions -->\n  <mat-card-actions class=\"profile-actions\">\n    <button \n      mat-raised-button \n      color=\"primary\" \n      class=\"view-profile-btn\"\n      *ngIf=\"showViewProfileButton\"\n      (click)=\"onViewProfile()\">\n      <mat-icon>visibility</mat-icon>\n      View Profile\n    </button>\n    \n    <button \n      mat-stroked-button \n      color=\"accent\" \n      class=\"contact-btn\"\n      *ngIf=\"showContactButton && hasContactInfo\"\n      (click)=\"onContact()\">\n      <mat-icon>contact_mail</mat-icon>\n      Contact\n    </button>\n    \n    <button \n      mat-icon-button \n      class=\"share-btn\"\n      matTooltip=\"Share Profile\">\n      <mat-icon>share</mat-icon>\n    </button>\n  </mat-card-actions>\n\n  <!-- Profile Completion Indicator (for own profile) -->\n  <div class=\"completion-indicator\" *ngIf=\"profile.profileCompletionPercentage < 100\">\n    <mat-progress-bar \n      [value]=\"profile.profileCompletionPercentage\" \n      [color]=\"profileCompletionColor\"\n      class=\"completion-bar\">\n    </mat-progress-bar>\n    <span class=\"completion-text\">{{ profile.profileCompletionPercentage }}% complete</span>\n  </div>\n</mat-card>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}