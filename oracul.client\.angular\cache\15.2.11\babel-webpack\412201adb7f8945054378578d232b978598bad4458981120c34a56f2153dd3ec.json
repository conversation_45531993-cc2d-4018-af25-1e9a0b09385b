{"ast": null, "code": "import { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = '/api/auth';\n    this.TOKEN_KEY = 'access_token';\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    this.USER_KEY = 'user_info';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.initializeAuth();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    const user = this.getStoredUser();\n    if (token && user) {\n      this.currentUserSubject.next(user);\n      this.isAuthenticatedSubject.next(true);\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        this.setToken(response.accessToken);\n        if (response.refreshToken) {\n          this.setRefreshToken(response.refreshToken);\n        }\n        this.setUser(response.user);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    // Determine endpoint based on data type\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\n    return this.http.post(`${this.API_URL}/${endpoint}`, userData).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        this.setToken(response.accessToken);\n        if (response.refreshToken) {\n          this.setRefreshToken(response.refreshToken);\n        }\n        this.setUser(response.user);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  isOracleRegistration(userData) {\n    return 'professionalTitle' in userData;\n  }\n  logout() {\n    return this.http.post(`${this.API_URL}/logout`, {}).pipe(tap(() => {\n      this.clearAuthData();\n    }), catchError(() => {\n      // Even if logout fails on server, clear local data\n      this.clearAuthData();\n      return throwError('Logout failed');\n    }));\n  }\n  refreshToken() {\n    const refreshToken = this.getRefreshToken();\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n    return this.http.post(`${this.API_URL}/refresh-token`, {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.accessToken) {\n        this.setToken(response.accessToken);\n        if (response.refreshToken) {\n          this.setRefreshToken(response.refreshToken);\n        }\n      }\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(error);\n    }));\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/me`).pipe(tap(user => {\n      this.setUser(user);\n      this.currentUserSubject.next(user);\n    }), catchError(this.handleError));\n  }\n  changePassword(request) {\n    return this.http.post(`${this.API_URL}/change-password`, request).pipe(catchError(this.handleError));\n  }\n  forgotPassword(request) {\n    return this.http.post(`${this.API_URL}/forgot-password`, request).pipe(catchError(this.handleError));\n  }\n  resetPassword(request) {\n    return this.http.post(`${this.API_URL}/reset-password`, request).pipe(catchError(this.handleError));\n  }\n  loginWithOAuth(request) {\n    return this.http.post(`${this.API_URL}/oauth-login`, request).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        this.setToken(response.accessToken);\n        if (response.refreshToken) {\n          this.setRefreshToken(response.refreshToken);\n        }\n        this.setUser(response.user);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  // Token management\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n  setToken(token) {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n  getRefreshToken() {\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n  }\n  setRefreshToken(token) {\n    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n  }\n  setUser(user) {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n  }\n  getStoredUser() {\n    const userStr = localStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  clearAuthData() {\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    return !!token && !this.isTokenExpired(token);\n  }\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const exp = payload.exp * 1000; // Convert to milliseconds\n      return Date.now() >= exp;\n    } catch {\n      return true;\n    }\n  }\n  hasRole(role) {\n    const user = this.currentUserSubject.value;\n    return user?.roles.includes(role) || false;\n  }\n  hasPermission(permission) {\n    const user = this.currentUserSubject.value;\n    return user?.permissions.includes(permission) || false;\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(errorMessage);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAkBrD,OAAM,MAAOC,WAAW;EAYtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,SAAI,GAAJD,IAAI;IACJ,WAAM,GAANC,MAAM;IAbC,YAAO,GAAG,WAAW;IACrB,cAAS,GAAG,cAAc;IAC1B,sBAAiB,GAAG,eAAe;IACnC,aAAQ,GAAG,WAAW;IAE/B,uBAAkB,GAAG,IAAIP,eAAe,CAAkB,IAAI,CAAC;IAChE,iBAAY,GAAG,IAAI,CAACQ,kBAAkB,CAACC,YAAY,EAAE;IAEpD,2BAAsB,GAAG,IAAIT,eAAe,CAAU,KAAK,CAAC;IAC7D,qBAAgB,GAAG,IAAI,CAACU,sBAAsB,CAACD,YAAY,EAAE;IAMlE,IAAI,CAACE,cAAc,EAAE;EACvB;EAEQA,cAAc;IACpB,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACC,aAAa,EAAE;IAEjC,IAAIH,KAAK,IAAIE,IAAI,EAAE;MACjB,IAAI,CAACN,kBAAkB,CAACQ,IAAI,CAACF,IAAI,CAAC;MAClC,IAAI,CAACJ,sBAAsB,CAACM,IAAI,CAAC,IAAI,CAAC;;EAE1C;EAEAC,KAAK,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACZ,IAAI,CAACa,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,QAAQ,EAAEF,WAAW,CAAC,CACtEG,IAAI,CACHlB,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACR,IAAI,EAAE;QAC7D,IAAI,CAACW,QAAQ,CAACH,QAAQ,CAACE,WAAW,CAAC;QACnC,IAAIF,QAAQ,CAACI,YAAY,EAAE;UACzB,IAAI,CAACC,eAAe,CAACL,QAAQ,CAACI,YAAY,CAAC;;QAE7C,IAAI,CAACE,OAAO,CAACN,QAAQ,CAACR,IAAI,CAAC;QAC3B,IAAI,CAACN,kBAAkB,CAACQ,IAAI,CAACM,QAAQ,CAACR,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACM,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFd,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQ,CAACC,QAAiD;IACxD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAAC,GAAG,iBAAiB,GAAG,UAAU;IAErF,OAAO,IAAI,CAACzB,IAAI,CAACa,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,IAAIY,QAAQ,EAAE,EAAED,QAAQ,CAAC,CACzEV,IAAI,CACHlB,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACR,IAAI,EAAE;QAC7D,IAAI,CAACW,QAAQ,CAACH,QAAQ,CAACE,WAAW,CAAC;QACnC,IAAIF,QAAQ,CAACI,YAAY,EAAE;UACzB,IAAI,CAACC,eAAe,CAACL,QAAQ,CAACI,YAAY,CAAC;;QAE7C,IAAI,CAACE,OAAO,CAACN,QAAQ,CAACR,IAAI,CAAC;QAC3B,IAAI,CAACN,kBAAkB,CAACQ,IAAI,CAACM,QAAQ,CAACR,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACM,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFd,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEQI,oBAAoB,CAACF,QAAiD;IAC5E,OAAO,mBAAmB,IAAIA,QAAQ;EACxC;EAEAG,MAAM;IACJ,OAAO,IAAI,CAAC5B,IAAI,CAACa,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE,EAAE,CAAC,CAChDC,IAAI,CACHlB,GAAG,CAAC,MAAK;MACP,IAAI,CAACgC,aAAa,EAAE;IACtB,CAAC,CAAC,EACFjC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACiC,aAAa,EAAE;MACpB,OAAOlC,UAAU,CAAC,eAAe,CAAC;IACpC,CAAC,CAAC,CACH;EACL;EAEAyB,YAAY;IACV,MAAMA,YAAY,GAAG,IAAI,CAACU,eAAe,EAAE;IAC3C,IAAI,CAACV,YAAY,EAAE;MACjB,OAAOzB,UAAU,CAAC,4BAA4B,CAAC;;IAGjD,OAAO,IAAI,CAACK,IAAI,CAACa,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,gBAAgB,EAAE;MAAEM;IAAY,CAAE,CAAC,CACnFL,IAAI,CACHlB,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;QAC5C,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAACE,WAAW,CAAC;QACnC,IAAIF,QAAQ,CAACI,YAAY,EAAE;UACzB,IAAI,CAACC,eAAe,CAACL,QAAQ,CAACI,YAAY,CAAC;;;IAGjD,CAAC,CAAC,EACFxB,UAAU,CAACmC,KAAK,IAAG;MACjB,IAAI,CAACF,aAAa,EAAE;MACpB,OAAOlC,UAAU,CAACoC,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEAC,cAAc;IACZ,OAAO,IAAI,CAAChC,IAAI,CAACiC,GAAG,CAAW,GAAG,IAAI,CAACnB,OAAO,KAAK,CAAC,CACjDC,IAAI,CACHlB,GAAG,CAACW,IAAI,IAAG;MACT,IAAI,CAACc,OAAO,CAACd,IAAI,CAAC;MAClB,IAAI,CAACN,kBAAkB,CAACQ,IAAI,CAACF,IAAI,CAAC;IACpC,CAAC,CAAC,EACFZ,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAW,cAAc,CAACC,OAA8B;IAC3C,OAAO,IAAI,CAACnC,IAAI,CAACa,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAAC;EACvC;EAEAa,cAAc,CAACD,OAA8B;IAC3C,OAAO,IAAI,CAACnC,IAAI,CAACa,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAAC;EACvC;EAEAc,aAAa,CAACF,OAA6B;IACzC,OAAO,IAAI,CAACnC,IAAI,CAACa,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,iBAAiB,EAAEqB,OAAO,CAAC,CAC/EpB,IAAI,CAACnB,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAAC;EACvC;EAEAe,cAAc,CAACH,OAA0B;IACvC,OAAO,IAAI,CAACnC,IAAI,CAACa,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,cAAc,EAAEqB,OAAO,CAAC,CACxEpB,IAAI,CACHlB,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACR,IAAI,EAAE;QAC7D,IAAI,CAACW,QAAQ,CAACH,QAAQ,CAACE,WAAW,CAAC;QACnC,IAAIF,QAAQ,CAACI,YAAY,EAAE;UACzB,IAAI,CAACC,eAAe,CAACL,QAAQ,CAACI,YAAY,CAAC;;QAE7C,IAAI,CAACE,OAAO,CAACN,QAAQ,CAACR,IAAI,CAAC;QAC3B,IAAI,CAACN,kBAAkB,CAACQ,IAAI,CAACM,QAAQ,CAACR,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACM,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFd,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAhB,QAAQ;IACN,OAAOgC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC;EAC7C;EAEQtB,QAAQ,CAACb,KAAa;IAC5BiC,YAAY,CAACG,OAAO,CAAC,IAAI,CAACD,SAAS,EAAEnC,KAAK,CAAC;EAC7C;EAEQwB,eAAe;IACrB,OAAOS,YAAY,CAACC,OAAO,CAAC,IAAI,CAACG,iBAAiB,CAAC;EACrD;EAEQtB,eAAe,CAACf,KAAa;IACnCiC,YAAY,CAACG,OAAO,CAAC,IAAI,CAACC,iBAAiB,EAAErC,KAAK,CAAC;EACrD;EAEQgB,OAAO,CAACd,IAAc;IAC5B+B,YAAY,CAACG,OAAO,CAAC,IAAI,CAACE,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACtC,IAAI,CAAC,CAAC;EAC3D;EAEQC,aAAa;IACnB,MAAMsC,OAAO,GAAGR,YAAY,CAACC,OAAO,CAAC,IAAI,CAACI,QAAQ,CAAC;IACnD,OAAOG,OAAO,GAAGF,IAAI,CAACG,KAAK,CAACD,OAAO,CAAC,GAAG,IAAI;EAC7C;EAEQlB,aAAa;IACnBU,YAAY,CAACU,UAAU,CAAC,IAAI,CAACR,SAAS,CAAC;IACvCF,YAAY,CAACU,UAAU,CAAC,IAAI,CAACN,iBAAiB,CAAC;IAC/CJ,YAAY,CAACU,UAAU,CAAC,IAAI,CAACL,QAAQ,CAAC;IACtC,IAAI,CAAC1C,kBAAkB,CAACQ,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACN,sBAAsB,CAACM,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACT,MAAM,CAACiD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAe;IACb,MAAM7C,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,OAAO,CAAC,CAACD,KAAK,IAAI,CAAC,IAAI,CAAC8C,cAAc,CAAC9C,KAAK,CAAC;EAC/C;EAEQ8C,cAAc,CAAC9C,KAAa;IAClC,IAAI;MACF,MAAM+C,OAAO,GAAGR,IAAI,CAACG,KAAK,CAACM,IAAI,CAAChD,KAAK,CAACiD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,GAAG,GAAGH,OAAO,CAACG,GAAG,GAAG,IAAI,CAAC,CAAC;MAChC,OAAOC,IAAI,CAACC,GAAG,EAAE,IAAIF,GAAG;KACzB,CAAC,MAAM;MACN,OAAO,IAAI;;EAEf;EAEAG,OAAO,CAACC,IAAY;IAClB,MAAMpD,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAAC2D,KAAK;IAC1C,OAAOrD,IAAI,EAAEsD,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAI,KAAK;EAC5C;EAEAI,aAAa,CAACC,UAAkB;IAC9B,MAAMzD,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAAC2D,KAAK;IAC1C,OAAOrD,IAAI,EAAE0D,WAAW,CAACH,QAAQ,CAACE,UAAU,CAAC,IAAI,KAAK;EACxD;EAEQ1C,WAAW,CAACQ,KAAU;IAC5B,IAAIoC,YAAY,GAAG,mBAAmB;IAEtC,IAAIpC,KAAK,CAACA,KAAK,EAAEqC,OAAO,EAAE;MACxBD,YAAY,GAAGpC,KAAK,CAACA,KAAK,CAACqC,OAAO;KACnC,MAAM,IAAIrC,KAAK,CAACqC,OAAO,EAAE;MACxBD,YAAY,GAAGpC,KAAK,CAACqC,OAAO;;IAG9B,OAAOzE,UAAU,CAACwE,YAAY,CAAC;EACjC;;;uBA/NWrE,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAuE,SAAXvE,WAAW;MAAAwE,YAFV;IAAM;EAAA", "names": ["BehaviorSubject", "throwError", "catchError", "tap", "AuthService", "constructor", "http", "router", "currentUserSubject", "asObservable", "isAuthenticatedSubject", "initializeAuth", "token", "getToken", "user", "getStoredUser", "next", "login", "credentials", "post", "API_URL", "pipe", "response", "success", "accessToken", "setToken", "refreshToken", "setRefreshToken", "setUser", "handleError", "register", "userData", "endpoint", "isOracleRegistration", "logout", "clearAuthData", "getRefreshToken", "error", "getCurrentUser", "get", "changePassword", "request", "forgotPassword", "resetPassword", "loginWithOAuth", "localStorage", "getItem", "TOKEN_KEY", "setItem", "REFRESH_TOKEN_KEY", "USER_KEY", "JSON", "stringify", "userStr", "parse", "removeItem", "navigate", "isAuthenticated", "isTokenExpired", "payload", "atob", "split", "exp", "Date", "now", "hasRole", "role", "value", "roles", "includes", "hasPermission", "permission", "permissions", "errorMessage", "message", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { map, catchError, tap } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  LoginRequest,\r\n  RegisterRequest,\r\n  OracleRegisterRequest,\r\n  AuthResponse,\r\n  UserInfo,\r\n  ChangePasswordRequest,\r\n  ForgotPasswordRequest,\r\n  ResetPasswordRequest,\r\n  ApiResponse,\r\n  OAuthLoginRequest\r\n} from '../models/auth.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private readonly API_URL = '/api/auth';\r\n  private readonly TOKEN_KEY = 'access_token';\r\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\r\n  private readonly USER_KEY = 'user_info';\r\n\r\n  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\r\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private router: Router\r\n  ) {\r\n    this.initializeAuth();\r\n  }\r\n\r\n  private initializeAuth(): void {\r\n    const token = this.getToken();\r\n    const user = this.getStoredUser();\r\n\r\n    if (token && user) {\r\n      this.currentUserSubject.next(user);\r\n      this.isAuthenticatedSubject.next(true);\r\n    }\r\n  }\r\n\r\n  login(credentials: LoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            this.setToken(response.accessToken);\r\n            if (response.refreshToken) {\r\n              this.setRefreshToken(response.refreshToken);\r\n            }\r\n            this.setUser(response.user);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {\r\n    // Determine endpoint based on data type\r\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            this.setToken(response.accessToken);\r\n            if (response.refreshToken) {\r\n              this.setRefreshToken(response.refreshToken);\r\n            }\r\n            this.setUser(response.user);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {\r\n    return 'professionalTitle' in userData;\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(`${this.API_URL}/logout`, {})\r\n      .pipe(\r\n        tap(() => {\r\n          this.clearAuthData();\r\n        }),\r\n        catchError(() => {\r\n          // Even if logout fails on server, clear local data\r\n          this.clearAuthData();\r\n          return throwError('Logout failed');\r\n        })\r\n      );\r\n  }\r\n\r\n  refreshToken(): Observable<AuthResponse> {\r\n    const refreshToken = this.getRefreshToken();\r\n    if (!refreshToken) {\r\n      return throwError('No refresh token available');\r\n    }\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken) {\r\n            this.setToken(response.accessToken);\r\n            if (response.refreshToken) {\r\n              this.setRefreshToken(response.refreshToken);\r\n            }\r\n          }\r\n        }),\r\n        catchError(error => {\r\n          this.clearAuthData();\r\n          return throwError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  getCurrentUser(): Observable<UserInfo> {\r\n    return this.http.get<UserInfo>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(user => {\r\n          this.setUser(user);\r\n          this.currentUserSubject.next(user);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            this.setToken(response.accessToken);\r\n            if (response.refreshToken) {\r\n              this.setRefreshToken(response.refreshToken);\r\n            }\r\n            this.setUser(response.user);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Token management\r\n  getToken(): string | null {\r\n    return localStorage.getItem(this.TOKEN_KEY);\r\n  }\r\n\r\n  private setToken(token: string): void {\r\n    localStorage.setItem(this.TOKEN_KEY, token);\r\n  }\r\n\r\n  private getRefreshToken(): string | null {\r\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY);\r\n  }\r\n\r\n  private setRefreshToken(token: string): void {\r\n    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\r\n  }\r\n\r\n  private setUser(user: UserInfo): void {\r\n    localStorage.setItem(this.USER_KEY, JSON.stringify(user));\r\n  }\r\n\r\n  private getStoredUser(): UserInfo | null {\r\n    const userStr = localStorage.getItem(this.USER_KEY);\r\n    return userStr ? JSON.parse(userStr) : null;\r\n  }\r\n\r\n  private clearAuthData(): void {\r\n    localStorage.removeItem(this.TOKEN_KEY);\r\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\r\n    localStorage.removeItem(this.USER_KEY);\r\n    this.currentUserSubject.next(null);\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    const token = this.getToken();\r\n    return !!token && !this.isTokenExpired(token);\r\n  }\r\n\r\n  private isTokenExpired(token: string): boolean {\r\n    try {\r\n      const payload = JSON.parse(atob(token.split('.')[1]));\r\n      const exp = payload.exp * 1000; // Convert to milliseconds\r\n      return Date.now() >= exp;\r\n    } catch {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  hasRole(role: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.roles.includes(role) || false;\r\n  }\r\n\r\n  hasPermission(permission: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.permissions.includes(permission) || false;\r\n  }\r\n\r\n  private handleError(error: any): Observable<never> {\r\n    let errorMessage = 'An error occurred';\r\n\r\n    if (error.error?.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.message) {\r\n      errorMessage = error.message;\r\n    }\r\n\r\n    return throwError(errorMessage);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}