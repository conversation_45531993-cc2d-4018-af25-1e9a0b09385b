{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/table';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, CDK_TABLE_TEMPLATE, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CDK_ROW_TEMPLATE, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]]];\nconst _c1 = [\"caption\", \"colgroup, col\"];\nfunction MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r1.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.dataAccessor(data_r2, ctx_r1.name), \" \");\n  }\n}\nclass MatRecycleRows {}\nMatRecycleRows.ɵfac = function MatRecycleRows_Factory(t) {\n  return new (t || MatRecycleRows)();\n};\nMatRecycleRows.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatRecycleRows,\n  selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\nclass MatTable extends CdkTable {\n  constructor() {\n    super(...arguments);\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    this.stickyCssClass = 'mat-mdc-table-sticky';\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    this.needsPositionStickyOnElement = false;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    // After ngOnInit, the `CdkTable` has created and inserted the table sections (thead, tbody,\n    // tfoot). MDC requires the `mdc-data-table__content` class to be added to the body. Note that\n    // this only applies to native tables, because we don't wrap the content of flexbox-based ones.\n    if (this._isNativeHtmlTable) {\n      const tbody = this._elementRef.nativeElement.querySelector('tbody');\n      tbody.classList.add('mdc-data-table__content');\n    }\n  }\n}\nMatTable.ɵfac = /* @__PURE__ */function () {\n  let ɵMatTable_BaseFactory;\n  return function MatTable_Factory(t) {\n    return (ɵMatTable_BaseFactory || (ɵMatTable_BaseFactory = i0.ɵɵgetInheritedFactory(MatTable)))(t || MatTable);\n  };\n}();\nMatTable.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTable,\n  selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n  hostVars: 2,\n  hostBindings: function MatTable_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n    }\n  },\n  exportAs: [\"matTable\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTable,\n    useExisting: MatTable\n  }, {\n    provide: CDK_TABLE,\n    useExisting: MatTable\n  }, {\n    provide: _COALESCED_STYLE_SCHEDULER,\n    useClass: _CoalescedStyleScheduler\n  },\n  // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n  //  is only included in the build if used.\n  {\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _DisposeViewRepeaterStrategy\n  },\n  // Prevent nested tables from seeing this table's StickyPositioningListener.\n  {\n    provide: STICKY_POSITIONING_LISTENER,\n    useValue: null\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 6,\n  vars: 0,\n  consts: [[\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n  template: function MatTable_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵprojection(0);\n      i0.ɵɵprojection(1, 1);\n      i0.ɵɵelementContainer(2, 0)(3, 1)(4, 2)(5, 3);\n    }\n  },\n  dependencies: [i1.DataRowOutlet, i1.HeaderRowOutlet, i1.FooterRowOutlet, i1.NoDataRowOutlet],\n  styles: [\".mdc-data-table{border-radius:var(--mdc-shape-medium, 4px);border-width:1px;border-style:solid}.mdc-data-table .mdc-data-table__header-cell:first-child{border-top-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:first-child,.mdc-data-table .mdc-data-table__header-cell:first-child[dir=rtl]{border-top-right-radius:var(--mdc-shape-medium, 4px);border-top-left-radius:0}.mdc-data-table .mdc-data-table__header-cell:last-child{border-top-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:last-child,.mdc-data-table .mdc-data-table__header-cell:last-child[dir=rtl]{border-top-left-radius:var(--mdc-shape-medium, 4px);border-top-right-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child{border-bottom-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child[dir=rtl]{border-bottom-right-radius:var(--mdc-shape-medium, 4px);border-bottom-left-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child{border-bottom-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child[dir=rtl]{border-bottom-left-radius:var(--mdc-shape-medium, 4px);border-bottom-right-radius:0}.mdc-data-table__cell,.mdc-data-table__header-cell{border-bottom-width:1px;border-bottom-style:solid}.mdc-data-table__pagination{border-top-width:1px;border-top-style:solid}.mdc-data-table__row:last-child .mdc-data-table__cell{border-bottom:none}.mdc-data-table__row{height:52px}.mdc-data-table__pagination{min-height:52px}.mdc-data-table__header-row{height:56px}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table--sticky-header .mdc-data-table__header-cell{position:sticky;top:0;z-index:1}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__pagination{box-sizing:border-box;display:flex;justify-content:flex-end}.mdc-data-table__pagination-trailing{margin-left:4px;margin-right:0;align-items:center;display:flex;flex-wrap:wrap;justify-content:flex-end}[dir=rtl] .mdc-data-table__pagination-trailing,.mdc-data-table__pagination-trailing[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__pagination-navigation{align-items:center;display:flex}.mdc-data-table__pagination-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__pagination-button .mdc-button__icon,.mdc-data-table__pagination-button .mdc-button__icon[dir=rtl]{transform:rotate(180deg)}[dir=rtl] .mdc-data-table__pagination-button,.mdc-data-table__pagination-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__pagination-total{margin-left:14px;margin-right:36px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-total,.mdc-data-table__pagination-total[dir=rtl]{margin-left:36px;margin-right:14px}.mdc-data-table__pagination-rows-per-page{margin-left:0;margin-right:22px;align-items:center;display:inline-flex}[dir=rtl] .mdc-data-table__pagination-rows-per-page,.mdc-data-table__pagination-rows-per-page[dir=rtl]{margin-left:22px;margin-right:0}.mdc-data-table__pagination-rows-per-page-label{margin-left:0;margin-right:12px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-rows-per-page-label,.mdc-data-table__pagination-rows-per-page-label[dir=rtl]{margin-left:12px;margin-right:0}.mdc-data-table__pagination-rows-per-page-select{min-width:var(--mdc-menu-min-width, 80px);margin:8px 0}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{width:100%;min-width:80px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{height:36px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{font-size:.75rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__dropdown-icon{width:20px;height:20px}.mdc-data-table__pagination-rows-per-page-select.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 56px)}.mdc-data-table__pagination-rows-per-page-select .mdc-list-item.mdc-list-item--with-one-line{height:36px}.mdc-data-table__progress-indicator{display:none;position:absolute;width:100%}.mdc-data-table--in-progress .mdc-data-table__progress-indicator{display:block}.mdc-data-table__scrim{background-color:var(--mdc-theme-surface, #fff);height:100%;opacity:.32;position:absolute;top:0;width:100%}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table-sticky{position:sticky !important}.mat-mdc-table{table-layout:auto;white-space:normal}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table .mat-mdc-row:hover,.mat-mdc-table .mat-mdc-footer-row:hover{background-color:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTable, [{\n    type: Component,\n    args: [{\n      selector: 'mat-table, table[mat-table]',\n      exportAs: 'matTable',\n      template: CDK_TABLE_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-table mdc-data-table__table',\n        '[class.mdc-table-fixed-layout]': 'fixedLayout'\n      },\n      providers: [{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      styles: [\".mdc-data-table{border-radius:var(--mdc-shape-medium, 4px);border-width:1px;border-style:solid}.mdc-data-table .mdc-data-table__header-cell:first-child{border-top-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:first-child,.mdc-data-table .mdc-data-table__header-cell:first-child[dir=rtl]{border-top-right-radius:var(--mdc-shape-medium, 4px);border-top-left-radius:0}.mdc-data-table .mdc-data-table__header-cell:last-child{border-top-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:last-child,.mdc-data-table .mdc-data-table__header-cell:last-child[dir=rtl]{border-top-left-radius:var(--mdc-shape-medium, 4px);border-top-right-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child{border-bottom-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child[dir=rtl]{border-bottom-right-radius:var(--mdc-shape-medium, 4px);border-bottom-left-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child{border-bottom-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child[dir=rtl]{border-bottom-left-radius:var(--mdc-shape-medium, 4px);border-bottom-right-radius:0}.mdc-data-table__cell,.mdc-data-table__header-cell{border-bottom-width:1px;border-bottom-style:solid}.mdc-data-table__pagination{border-top-width:1px;border-top-style:solid}.mdc-data-table__row:last-child .mdc-data-table__cell{border-bottom:none}.mdc-data-table__row{height:52px}.mdc-data-table__pagination{min-height:52px}.mdc-data-table__header-row{height:56px}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table--sticky-header .mdc-data-table__header-cell{position:sticky;top:0;z-index:1}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__pagination{box-sizing:border-box;display:flex;justify-content:flex-end}.mdc-data-table__pagination-trailing{margin-left:4px;margin-right:0;align-items:center;display:flex;flex-wrap:wrap;justify-content:flex-end}[dir=rtl] .mdc-data-table__pagination-trailing,.mdc-data-table__pagination-trailing[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__pagination-navigation{align-items:center;display:flex}.mdc-data-table__pagination-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__pagination-button .mdc-button__icon,.mdc-data-table__pagination-button .mdc-button__icon[dir=rtl]{transform:rotate(180deg)}[dir=rtl] .mdc-data-table__pagination-button,.mdc-data-table__pagination-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__pagination-total{margin-left:14px;margin-right:36px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-total,.mdc-data-table__pagination-total[dir=rtl]{margin-left:36px;margin-right:14px}.mdc-data-table__pagination-rows-per-page{margin-left:0;margin-right:22px;align-items:center;display:inline-flex}[dir=rtl] .mdc-data-table__pagination-rows-per-page,.mdc-data-table__pagination-rows-per-page[dir=rtl]{margin-left:22px;margin-right:0}.mdc-data-table__pagination-rows-per-page-label{margin-left:0;margin-right:12px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-rows-per-page-label,.mdc-data-table__pagination-rows-per-page-label[dir=rtl]{margin-left:12px;margin-right:0}.mdc-data-table__pagination-rows-per-page-select{min-width:var(--mdc-menu-min-width, 80px);margin:8px 0}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{width:100%;min-width:80px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{height:36px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{font-size:.75rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__dropdown-icon{width:20px;height:20px}.mdc-data-table__pagination-rows-per-page-select.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 56px)}.mdc-data-table__pagination-rows-per-page-select .mdc-list-item.mdc-list-item--with-one-line{height:36px}.mdc-data-table__progress-indicator{display:none;position:absolute;width:100%}.mdc-data-table--in-progress .mdc-data-table__progress-indicator{display:block}.mdc-data-table__scrim{background-color:var(--mdc-theme-surface, #fff);height:100%;opacity:.32;position:absolute;top:0;width:100%}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table-sticky{position:sticky !important}.mat-mdc-table{table-layout:auto;white-space:normal}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table .mat-mdc-row:hover,.mat-mdc-table .mat-mdc-footer-row:hover{background-color:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {}\nMatCellDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatCellDef_BaseFactory;\n  return function MatCellDef_Factory(t) {\n    return (ɵMatCellDef_BaseFactory || (ɵMatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatCellDef)))(t || MatCellDef);\n  };\n}();\nMatCellDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCellDef,\n  selectors: [[\"\", \"matCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkCellDef,\n    useExisting: MatCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matCellDef]',\n      providers: [{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {}\nMatHeaderCellDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatHeaderCellDef_BaseFactory;\n  return function MatHeaderCellDef_Factory(t) {\n    return (ɵMatHeaderCellDef_BaseFactory || (ɵMatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCellDef)))(t || MatHeaderCellDef);\n  };\n}();\nMatHeaderCellDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatHeaderCellDef,\n  selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderCellDef,\n    useExisting: MatHeaderCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderCellDef]',\n      providers: [{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {}\nMatFooterCellDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatFooterCellDef_BaseFactory;\n  return function MatFooterCellDef_Factory(t) {\n    return (ɵMatFooterCellDef_BaseFactory || (ɵMatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCellDef)))(t || MatFooterCellDef);\n  };\n}();\nMatFooterCellDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatFooterCellDef,\n  selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterCellDef,\n    useExisting: MatFooterCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterCellDef]',\n      providers: [{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n}\nMatColumnDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatColumnDef_BaseFactory;\n  return function MatColumnDef_Factory(t) {\n    return (ɵMatColumnDef_BaseFactory || (ɵMatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatColumnDef)))(t || MatColumnDef);\n  };\n}();\nMatColumnDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatColumnDef,\n  selectors: [[\"\", \"matColumnDef\", \"\"]],\n  inputs: {\n    sticky: \"sticky\",\n    name: [\"matColumnDef\", \"name\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkColumnDef,\n    useExisting: MatColumnDef\n  }, {\n    provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n    useExisting: MatColumnDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matColumnDef]',\n      inputs: ['sticky'],\n      providers: [{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]\n    }]\n  }], null, {\n    name: [{\n      type: Input,\n      args: ['matColumnDef']\n    }]\n  });\n})();\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {}\nMatHeaderCell.ɵfac = /* @__PURE__ */function () {\n  let ɵMatHeaderCell_BaseFactory;\n  return function MatHeaderCell_Factory(t) {\n    return (ɵMatHeaderCell_BaseFactory || (ɵMatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCell)))(t || MatHeaderCell);\n  };\n}();\nMatHeaderCell.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatHeaderCell,\n  selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n  hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-header-cell, th[mat-header-cell]',\n      host: {\n        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], null, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {}\nMatFooterCell.ɵfac = /* @__PURE__ */function () {\n  let ɵMatFooterCell_BaseFactory;\n  return function MatFooterCell_Factory(t) {\n    return (ɵMatFooterCell_BaseFactory || (ɵMatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCell)))(t || MatFooterCell);\n  };\n}();\nMatFooterCell.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatFooterCell,\n  selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-footer-cell, td[mat-footer-cell]',\n      host: {\n        'class': 'mat-mdc-footer-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {}\nMatCell.ɵfac = /* @__PURE__ */function () {\n  let ɵMatCell_BaseFactory;\n  return function MatCell_Factory(t) {\n    return (ɵMatCell_BaseFactory || (ɵMatCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatCell)))(t || MatCell);\n  };\n}();\nMatCell.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCell,\n  selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-cell, td[mat-cell]',\n      host: {\n        'class': 'mat-mdc-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {}\nMatHeaderRowDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatHeaderRowDef_BaseFactory;\n  return function MatHeaderRowDef_Factory(t) {\n    return (ɵMatHeaderRowDef_BaseFactory || (ɵMatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRowDef)))(t || MatHeaderRowDef);\n  };\n}();\nMatHeaderRowDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatHeaderRowDef,\n  selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n  inputs: {\n    columns: [\"matHeaderRowDef\", \"columns\"],\n    sticky: [\"matHeaderRowDefSticky\", \"sticky\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderRowDef,\n    useExisting: MatHeaderRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderRowDef]',\n      providers: [{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }],\n      inputs: ['columns: matHeaderRowDef', 'sticky: matHeaderRowDefSticky']\n    }]\n  }], null, null);\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {}\nMatFooterRowDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatFooterRowDef_BaseFactory;\n  return function MatFooterRowDef_Factory(t) {\n    return (ɵMatFooterRowDef_BaseFactory || (ɵMatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRowDef)))(t || MatFooterRowDef);\n  };\n}();\nMatFooterRowDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatFooterRowDef,\n  selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n  inputs: {\n    columns: [\"matFooterRowDef\", \"columns\"],\n    sticky: [\"matFooterRowDefSticky\", \"sticky\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterRowDef,\n    useExisting: MatFooterRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterRowDef]',\n      providers: [{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }],\n      inputs: ['columns: matFooterRowDef', 'sticky: matFooterRowDefSticky']\n    }]\n  }], null, null);\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {}\nMatRowDef.ɵfac = /* @__PURE__ */function () {\n  let ɵMatRowDef_BaseFactory;\n  return function MatRowDef_Factory(t) {\n    return (ɵMatRowDef_BaseFactory || (ɵMatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatRowDef)))(t || MatRowDef);\n  };\n}();\nMatRowDef.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatRowDef,\n  selectors: [[\"\", \"matRowDef\", \"\"]],\n  inputs: {\n    columns: [\"matRowDefColumns\", \"columns\"],\n    when: [\"matRowDefWhen\", \"when\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkRowDef,\n    useExisting: MatRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matRowDef]',\n      providers: [{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }],\n      inputs: ['columns: matRowDefColumns', 'when: matRowDefWhen']\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {}\nMatHeaderRow.ɵfac = /* @__PURE__ */function () {\n  let ɵMatHeaderRow_BaseFactory;\n  return function MatHeaderRow_Factory(t) {\n    return (ɵMatHeaderRow_BaseFactory || (ɵMatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRow)))(t || MatHeaderRow);\n  };\n}();\nMatHeaderRow.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatHeaderRow,\n  selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n  exportAs: [\"matHeaderRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderRow,\n    useExisting: MatHeaderRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function MatHeaderRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [i1.CdkCellOutlet],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-header-row, tr[mat-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matHeaderRow',\n      providers: [{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {}\nMatFooterRow.ɵfac = /* @__PURE__ */function () {\n  let ɵMatFooterRow_BaseFactory;\n  return function MatFooterRow_Factory(t) {\n    return (ɵMatFooterRow_BaseFactory || (ɵMatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRow)))(t || MatFooterRow);\n  };\n}();\nMatFooterRow.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatFooterRow,\n  selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n  exportAs: [\"matFooterRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterRow,\n    useExisting: MatFooterRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function MatFooterRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [i1.CdkCellOutlet],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-footer-row, tr[mat-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-footer-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matFooterRow',\n      providers: [{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {}\nMatRow.ɵfac = /* @__PURE__ */function () {\n  let ɵMatRow_BaseFactory;\n  return function MatRow_Factory(t) {\n    return (ɵMatRow_BaseFactory || (ɵMatRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatRow)))(t || MatRow);\n  };\n}();\nMatRow.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatRow,\n  selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n  exportAs: [\"matRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkRow,\n    useExisting: MatRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function MatRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [i1.CdkCellOutlet],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-row, tr[mat-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matRow',\n      providers: [{\n        provide: CdkRow,\n        useExisting: MatRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n  constructor() {\n    super(...arguments);\n    this._contentClassName = 'mat-mdc-no-data-row';\n  }\n}\nMatNoDataRow.ɵfac = /* @__PURE__ */function () {\n  let ɵMatNoDataRow_BaseFactory;\n  return function MatNoDataRow_Factory(t) {\n    return (ɵMatNoDataRow_BaseFactory || (ɵMatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatNoDataRow)))(t || MatNoDataRow);\n  };\n}();\nMatNoDataRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatNoDataRow,\n  selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkNoDataRow,\n    useExisting: MatNoDataRow\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matNoDataRow]',\n      providers: [{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {}\nMatTextColumn.ɵfac = /* @__PURE__ */function () {\n  let ɵMatTextColumn_BaseFactory;\n  return function MatTextColumn_Factory(t) {\n    return (ɵMatTextColumn_BaseFactory || (ɵMatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(MatTextColumn)))(t || MatTextColumn);\n  };\n}();\nMatTextColumn.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTextColumn,\n  selectors: [[\"mat-text-column\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 0,\n  consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n  template: function MatTextColumn_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainerStart(0, 0);\n      i0.ɵɵtemplate(1, MatTextColumn_th_1_Template, 2, 3, \"th\", 1);\n      i0.ɵɵtemplate(2, MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n      i0.ɵɵelementContainerEnd();\n    }\n  },\n  dependencies: [MatHeaderCellDef, MatColumnDef, MatCellDef, MatHeaderCell, MatCell],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'mat-text-column',\n      template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nclass MatTableModule {}\nMatTableModule.ɵfac = function MatTableModule_Factory(t) {\n  return new (t || MatTableModule)();\n};\nMatTableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTableModule\n});\nMatTableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkTableModule],\n      exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n      declarations: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/** Shared base class with MDC-based implementation. */\nclass _MatTableDataSource extends DataSource {\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  constructor(initialData = []) {\n    super();\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    this._renderData = new BehaviorSubject([]);\n    /** Stream that emits when a new filter string is set on the data source. */\n    this._filter = new BehaviorSubject('');\n    /** Used to react to internal changes of the paginator that are made by the data source itself. */\n    this._internalPageChanges = new Subject();\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    this._renderChangesSubscription = null;\n    /**\n     * Data accessor function that is used for accessing data properties for sorting through\n     * the default sortData function.\n     * This default function assumes that the sort header IDs (which defaults to the column name)\n     * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n     * May be set to a custom function for different behavior.\n     * @param data Data object that is being accessed.\n     * @param sortHeaderId The name of the column that represents the data.\n     */\n    this.sortingDataAccessor = (data, sortHeaderId) => {\n      const value = data[sortHeaderId];\n      if (_isNumberValue(value)) {\n        const numberValue = Number(value);\n        // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n        // leave them as strings. For more info: https://goo.gl/y5vbSg\n        return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n      }\n      return value;\n    };\n    /**\n     * Gets a sorted copy of the data array based on the state of the MatSort. Called\n     * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n     * By default, the function retrieves the active sort and its direction and compares data\n     * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n     * of data ordering.\n     * @param data The array of data that should be sorted.\n     * @param sort The connected MatSort that holds the current sort state.\n     */\n    this.sortData = (data, sort) => {\n      const active = sort.active;\n      const direction = sort.direction;\n      if (!active || direction == '') {\n        return data;\n      }\n      return data.sort((a, b) => {\n        let valueA = this.sortingDataAccessor(a, active);\n        let valueB = this.sortingDataAccessor(b, active);\n        // If there are data in the column that can be converted to a number,\n        // it must be ensured that the rest of the data\n        // is of the same type so as not to order incorrectly.\n        const valueAType = typeof valueA;\n        const valueBType = typeof valueB;\n        if (valueAType !== valueBType) {\n          if (valueAType === 'number') {\n            valueA += '';\n          }\n          if (valueBType === 'number') {\n            valueB += '';\n          }\n        }\n        // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n        // one value exists while the other doesn't. In this case, existing value should come last.\n        // This avoids inconsistent results when comparing values to undefined/null.\n        // If neither value exists, return 0 (equal).\n        let comparatorResult = 0;\n        if (valueA != null && valueB != null) {\n          // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n          if (valueA > valueB) {\n            comparatorResult = 1;\n          } else if (valueA < valueB) {\n            comparatorResult = -1;\n          }\n        } else if (valueA != null) {\n          comparatorResult = 1;\n        } else if (valueB != null) {\n          comparatorResult = -1;\n        }\n        return comparatorResult * (direction == 'asc' ? 1 : -1);\n      });\n    };\n    /**\n     * Checks if a data object matches the data source's filter string. By default, each data object\n     * is converted to a string of its properties and returns true if the filter has\n     * at least one occurrence in that string. By default, the filter string has its whitespace\n     * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n     * filter matching.\n     * @param data Data object used to check against the filter.\n     * @param filter Filter string that has been set on the data source.\n     * @returns Whether the filter matches against the data\n     */\n    this.filterPredicate = (data, filter) => {\n      // Transform the data into a lowercase string of all property values.\n      const dataStr = Object.keys(data).reduce((currentTerm, key) => {\n        // Use an obscure Unicode character to delimit the words in the concatenated string.\n        // This avoids matches where the values of two columns combined will match the user's query\n        // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n        // that has a very low chance of being typed in by somebody in a text field. This one in\n        // particular is \"White up-pointing triangle with dot\" from\n        // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n        return currentTerm + data[key] + '◬';\n      }, '').toLowerCase();\n      // Transform the filter by converting it to lowercase and removing whitespace.\n      const transformedFilter = filter.trim().toLowerCase();\n      return dataStr.indexOf(transformedFilter) != -1;\n    };\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends _MatTableDataSource {}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn, _MatTableDataSource };", "map": {"version": 3, "names": ["i0", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "i1", "CdkTable", "CDK_TABLE", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "STICKY_POSITIONING_LISTENER", "CDK_TABLE_TEMPLATE", "CdkCellDef", "CdkHeaderCellDef", "CdkFooterCellDef", "CdkColumnDef", "CdkHeaderCell", "CdkFooterCell", "CdkCell", "CdkHeaderRowDef", "CdkFooterRowDef", "CdkRowDef", "CdkHeaderRow", "CDK_ROW_TEMPLATE", "CdkFooterRow", "CdkRow", "CdkNoDataRow", "CdkTextColumn", "CdkTableModule", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_DisposeViewRepeaterStrategy", "DataSource", "MatCommonModule", "BehaviorSubject", "Subject", "merge", "of", "combineLatest", "_isNumberValue", "map", "MatRecycleRows", "ɵfac", "ɵdir", "provide", "useClass", "type", "args", "selector", "providers", "MatTable", "constructor", "arguments", "stickyCssClass", "needsPositionStickyOnElement", "ngOnInit", "_isNativeHtmlTable", "tbody", "_elementRef", "nativeElement", "querySelector", "classList", "add", "ɵcmp", "useExisting", "useValue", "DataRowOutlet", "HeaderRowOutlet", "FooterRowOutlet", "NoDataRowOutlet", "exportAs", "template", "host", "encapsulation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "styles", "MatCellDef", "MatHeaderCellDef", "MatFooterCellDef", "MatColumnDef", "name", "_name", "_setNameInput", "_updateColumnCssClassName", "_columnCssClassName", "push", "cssClassFriendlyName", "inputs", "MatHeaderCell", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>ell", "MatHeaderRowDef", "MatFooterRowDef", "MatRowDef", "MatHeaderRow", "CdkCellOutlet", "MatFooterRow", "MatRow", "MatNoDataRow", "_contentClassName", "MatTextColumn", "EXPORTED_DECLARATIONS", "MatTableModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "MAX_SAFE_INTEGER", "_MatTableDataSource", "data", "_data", "value", "Array", "isArray", "next", "_renderChangesSubscription", "_filterData", "filter", "_filter", "sort", "_sort", "_updateChangeSubscription", "paginator", "_paginator", "initialData", "_renderData", "_internalPageChanges", "sortingDataAccessor", "sortHeaderId", "numberValue", "Number", "sortData", "active", "direction", "a", "b", "valueA", "valueB", "valueAType", "valueBType", "comparatorResult", "filterPredicate", "dataStr", "Object", "keys", "reduce", "currentTerm", "key", "toLowerCase", "<PERSON><PERSON><PERSON>er", "trim", "indexOf", "sortChange", "initialized", "pageChange", "page", "dataStream", "filteredData", "pipe", "orderedData", "_orderData", "paginatedData", "_pageData", "unsubscribe", "subscribe", "obj", "_updatePaginator", "length", "slice", "startIndex", "pageIndex", "pageSize", "filteredDataLength", "Promise", "resolve", "then", "lastPageIndex", "Math", "ceil", "newPageIndex", "min", "connect", "disconnect", "MatTableDataSource"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/table';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, CDK_TABLE_TEMPLATE, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CDK_ROW_TEMPLATE, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass MatRecycleRows {\n}\nMatRecycleRows.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatRecycleRows.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatRecycleRows, selector: \"mat-table[recycleRows], table[mat-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\nclass MatTable extends CdkTable {\n    constructor() {\n        super(...arguments);\n        /** Overrides the sticky CSS class set by the `CdkTable`. */\n        this.stickyCssClass = 'mat-mdc-table-sticky';\n        /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n        this.needsPositionStickyOnElement = false;\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        // After ngOnInit, the `CdkTable` has created and inserted the table sections (thead, tbody,\n        // tfoot). MDC requires the `mdc-data-table__content` class to be added to the body. Note that\n        // this only applies to native tables, because we don't wrap the content of flexbox-based ones.\n        if (this._isNativeHtmlTable) {\n            const tbody = this._elementRef.nativeElement.querySelector('tbody');\n            tbody.classList.add('mdc-data-table__content');\n        }\n    }\n}\nMatTable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTable, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatTable.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTable, selector: \"mat-table, table[mat-table]\", host: { properties: { \"class.mdc-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"mat-mdc-table mdc-data-table__table\" }, providers: [\n        { provide: CdkTable, useExisting: MatTable },\n        { provide: CDK_TABLE, useExisting: MatTable },\n        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n        // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n        //  is only included in the build if used.\n        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n        // Prevent nested tables from seeing this table's StickyPositioningListener.\n        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n    ], exportAs: [\"matTable\"], usesInheritance: true, ngImport: i0, template: \"\\n  <ng-content select=\\\"caption\\\"></ng-content>\\n  <ng-content select=\\\"colgroup, col\\\"></ng-content>\\n  <ng-container headerRowOutlet></ng-container>\\n  <ng-container rowOutlet></ng-container>\\n  <ng-container noDataRowOutlet></ng-container>\\n  <ng-container footerRowOutlet></ng-container>\\n\", isInline: true, styles: [\".mdc-data-table{border-radius:var(--mdc-shape-medium, 4px);border-width:1px;border-style:solid}.mdc-data-table .mdc-data-table__header-cell:first-child{border-top-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:first-child,.mdc-data-table .mdc-data-table__header-cell:first-child[dir=rtl]{border-top-right-radius:var(--mdc-shape-medium, 4px);border-top-left-radius:0}.mdc-data-table .mdc-data-table__header-cell:last-child{border-top-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:last-child,.mdc-data-table .mdc-data-table__header-cell:last-child[dir=rtl]{border-top-left-radius:var(--mdc-shape-medium, 4px);border-top-right-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child{border-bottom-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child[dir=rtl]{border-bottom-right-radius:var(--mdc-shape-medium, 4px);border-bottom-left-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child{border-bottom-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child[dir=rtl]{border-bottom-left-radius:var(--mdc-shape-medium, 4px);border-bottom-right-radius:0}.mdc-data-table__cell,.mdc-data-table__header-cell{border-bottom-width:1px;border-bottom-style:solid}.mdc-data-table__pagination{border-top-width:1px;border-top-style:solid}.mdc-data-table__row:last-child .mdc-data-table__cell{border-bottom:none}.mdc-data-table__row{height:52px}.mdc-data-table__pagination{min-height:52px}.mdc-data-table__header-row{height:56px}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table--sticky-header .mdc-data-table__header-cell{position:sticky;top:0;z-index:1}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__pagination{box-sizing:border-box;display:flex;justify-content:flex-end}.mdc-data-table__pagination-trailing{margin-left:4px;margin-right:0;align-items:center;display:flex;flex-wrap:wrap;justify-content:flex-end}[dir=rtl] .mdc-data-table__pagination-trailing,.mdc-data-table__pagination-trailing[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__pagination-navigation{align-items:center;display:flex}.mdc-data-table__pagination-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__pagination-button .mdc-button__icon,.mdc-data-table__pagination-button .mdc-button__icon[dir=rtl]{transform:rotate(180deg)}[dir=rtl] .mdc-data-table__pagination-button,.mdc-data-table__pagination-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__pagination-total{margin-left:14px;margin-right:36px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-total,.mdc-data-table__pagination-total[dir=rtl]{margin-left:36px;margin-right:14px}.mdc-data-table__pagination-rows-per-page{margin-left:0;margin-right:22px;align-items:center;display:inline-flex}[dir=rtl] .mdc-data-table__pagination-rows-per-page,.mdc-data-table__pagination-rows-per-page[dir=rtl]{margin-left:22px;margin-right:0}.mdc-data-table__pagination-rows-per-page-label{margin-left:0;margin-right:12px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-rows-per-page-label,.mdc-data-table__pagination-rows-per-page-label[dir=rtl]{margin-left:12px;margin-right:0}.mdc-data-table__pagination-rows-per-page-select{min-width:var(--mdc-menu-min-width, 80px);margin:8px 0}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{width:100%;min-width:80px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{height:36px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{font-size:.75rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__dropdown-icon{width:20px;height:20px}.mdc-data-table__pagination-rows-per-page-select.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 56px)}.mdc-data-table__pagination-rows-per-page-select .mdc-list-item.mdc-list-item--with-one-line{height:36px}.mdc-data-table__progress-indicator{display:none;position:absolute;width:100%}.mdc-data-table--in-progress .mdc-data-table__progress-indicator{display:block}.mdc-data-table__scrim{background-color:var(--mdc-theme-surface, #fff);height:100%;opacity:.32;position:absolute;top:0;width:100%}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table-sticky{position:sticky !important}.mat-mdc-table{table-layout:auto;white-space:normal}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table .mat-mdc-row:hover,.mat-mdc-table .mat-mdc-footer-row:hover{background-color:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"], dependencies: [{ kind: \"directive\", type: i1.DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: i1.HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: i1.FooterRowOutlet, selector: \"[footerRowOutlet]\" }, { kind: \"directive\", type: i1.NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-table, table[mat-table]', exportAs: 'matTable', template: CDK_TABLE_TEMPLATE, host: {\n                        'class': 'mat-mdc-table mdc-data-table__table',\n                        '[class.mdc-table-fixed-layout]': 'fixedLayout',\n                    }, providers: [\n                        { provide: CdkTable, useExisting: MatTable },\n                        { provide: CDK_TABLE, useExisting: MatTable },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n                        //  is only included in the build if used.\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, styles: [\".mdc-data-table{border-radius:var(--mdc-shape-medium, 4px);border-width:1px;border-style:solid}.mdc-data-table .mdc-data-table__header-cell:first-child{border-top-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:first-child,.mdc-data-table .mdc-data-table__header-cell:first-child[dir=rtl]{border-top-right-radius:var(--mdc-shape-medium, 4px);border-top-left-radius:0}.mdc-data-table .mdc-data-table__header-cell:last-child{border-top-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table .mdc-data-table__header-cell:last-child,.mdc-data-table .mdc-data-table__header-cell:last-child[dir=rtl]{border-top-left-radius:var(--mdc-shape-medium, 4px);border-top-right-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child{border-bottom-left-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:first-child[dir=rtl]{border-bottom-right-radius:var(--mdc-shape-medium, 4px);border-bottom-left-radius:0}.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child{border-bottom-right-radius:var(--mdc-shape-medium, 4px)}[dir=rtl] .mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child,.mdc-data-table.mdc-data-table--without-footer .mdc-data-table__row:last-child .mdc-data-table__cell:last-child[dir=rtl]{border-bottom-left-radius:var(--mdc-shape-medium, 4px);border-bottom-right-radius:0}.mdc-data-table__cell,.mdc-data-table__header-cell{border-bottom-width:1px;border-bottom-style:solid}.mdc-data-table__pagination{border-top-width:1px;border-top-style:solid}.mdc-data-table__row:last-child .mdc-data-table__cell{border-bottom:none}.mdc-data-table__row{height:52px}.mdc-data-table__pagination{min-height:52px}.mdc-data-table__header-row{height:56px}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table--sticky-header .mdc-data-table__header-cell{position:sticky;top:0;z-index:1}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--checkbox{width:1px}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__sort-icon-button{width:28px;height:28px;padding:2px;transform:rotate(0.0001deg);margin-left:4px;margin-right:0;opacity:0}.mdc-data-table__sort-icon-button .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__ripple{width:28px;height:28px;margin-top:0px;margin-bottom:0px;margin-right:0px;margin-left:0px}.mdc-data-table__sort-icon-button.mdc-icon-button--reduced-size .mdc-icon-button__focus-ring{max-height:28px;max-width:28px}.mdc-data-table__sort-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:28px;left:50%;width:28px;transform:translate(-50%, -50%)}[dir=rtl] .mdc-data-table__sort-icon-button,.mdc-data-table__sort-icon-button[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--numeric .mdc-data-table__sort-icon-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__header-cell--sorted-descending .mdc-data-table__sort-icon-button{transform:rotate(-180deg)}.mdc-data-table__sort-icon-button:focus,.mdc-data-table__header-cell:hover .mdc-data-table__sort-icon-button,.mdc-data-table__header-cell--sorted .mdc-data-table__sort-icon-button{opacity:1}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__header-cell--with-sort{cursor:pointer}.mdc-data-table__sort-status-label{clip:rect(1px, 1px, 1px, 1px);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__cell--checkbox{width:1px}.mdc-data-table__pagination{box-sizing:border-box;display:flex;justify-content:flex-end}.mdc-data-table__pagination-trailing{margin-left:4px;margin-right:0;align-items:center;display:flex;flex-wrap:wrap;justify-content:flex-end}[dir=rtl] .mdc-data-table__pagination-trailing,.mdc-data-table__pagination-trailing[dir=rtl]{margin-left:0;margin-right:4px}.mdc-data-table__pagination-navigation{align-items:center;display:flex}.mdc-data-table__pagination-button{margin-left:0;margin-right:4px}[dir=rtl] .mdc-data-table__pagination-button .mdc-button__icon,.mdc-data-table__pagination-button .mdc-button__icon[dir=rtl]{transform:rotate(180deg)}[dir=rtl] .mdc-data-table__pagination-button,.mdc-data-table__pagination-button[dir=rtl]{margin-left:4px;margin-right:0}.mdc-data-table__pagination-total{margin-left:14px;margin-right:36px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-total,.mdc-data-table__pagination-total[dir=rtl]{margin-left:36px;margin-right:14px}.mdc-data-table__pagination-rows-per-page{margin-left:0;margin-right:22px;align-items:center;display:inline-flex}[dir=rtl] .mdc-data-table__pagination-rows-per-page,.mdc-data-table__pagination-rows-per-page[dir=rtl]{margin-left:22px;margin-right:0}.mdc-data-table__pagination-rows-per-page-label{margin-left:0;margin-right:12px;white-space:nowrap}[dir=rtl] .mdc-data-table__pagination-rows-per-page-label,.mdc-data-table__pagination-rows-per-page-label[dir=rtl]{margin-left:12px;margin-right:0}.mdc-data-table__pagination-rows-per-page-select{min-width:var(--mdc-menu-min-width, 80px);margin:8px 0}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{width:100%;min-width:80px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor{height:36px}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-floating-label--float-above{font-size:.75rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-data-table__pagination-rows-per-page-select .mdc-select__dropdown-icon{width:20px;height:20px}.mdc-data-table__pagination-rows-per-page-select.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 56px)}.mdc-data-table__pagination-rows-per-page-select .mdc-list-item.mdc-list-item--with-one-line{height:36px}.mdc-data-table__progress-indicator{display:none;position:absolute;width:100%}.mdc-data-table--in-progress .mdc-data-table__progress-indicator{display:block}.mdc-data-table__scrim{background-color:var(--mdc-theme-surface, #fff);height:100%;opacity:.32;position:absolute;top:0;width:100%}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table-sticky{position:sticky !important}.mat-mdc-table{table-layout:auto;white-space:normal}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table .mat-mdc-row:hover,.mat-mdc-table .mat-mdc-footer-row:hover{background-color:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"] }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n}\nMatCellDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatCellDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCellDef, selector: \"[matCellDef]\", providers: [{ provide: CdkCellDef, useExisting: MatCellDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matCellDef]',\n                    providers: [{ provide: CdkCellDef, useExisting: MatCellDef }],\n                }]\n        }] });\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n}\nMatHeaderCellDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatHeaderCellDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\", providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderCellDef]',\n                    providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }],\n                }]\n        }] });\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n}\nMatFooterCellDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatFooterCellDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatFooterCellDef, selector: \"[matFooterCellDef]\", providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterCellDef]',\n                    providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }],\n                }]\n        }] });\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /**\n     * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n     * In the future, this will only add \"mat-column-\" and columnCssClassName\n     * will change from type string[] to string.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        super._updateColumnCssClassName();\n        this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n    }\n}\nMatColumnDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatColumnDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatColumnDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: { sticky: \"sticky\", name: [\"matColumnDef\", \"name\"] }, providers: [\n        { provide: CdkColumnDef, useExisting: MatColumnDef },\n        { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n    ], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matColumnDef]',\n                    inputs: ['sticky'],\n                    providers: [\n                        { provide: CdkColumnDef, useExisting: MatColumnDef },\n                        { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n                    ],\n                }]\n        }], propDecorators: { name: [{\n                type: Input,\n                args: ['matColumnDef']\n            }] } });\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n}\nMatHeaderCell.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatHeaderCell.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"mat-mdc-header-cell mdc-data-table__header-cell\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-header-cell, th[mat-header-cell]',\n                    host: {\n                        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }] });\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n}\nMatFooterCell.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatFooterCell.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatFooterCell, selector: \"mat-footer-cell, td[mat-footer-cell]\", host: { classAttribute: \"mat-mdc-footer-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-footer-cell, td[mat-footer-cell]',\n                    host: {\n                        'class': 'mat-mdc-footer-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n}\nMatCell.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatCell.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatCell, selector: \"mat-cell, td[mat-cell]\", host: { classAttribute: \"mat-mdc-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-cell, td[mat-cell]',\n                    host: {\n                        'class': 'mat-mdc-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n}\nMatHeaderRowDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatHeaderRowDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatHeaderRowDef, selector: \"[matHeaderRowDef]\", inputs: { columns: [\"matHeaderRowDef\", \"columns\"], sticky: [\"matHeaderRowDefSticky\", \"sticky\"] }, providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderRowDef]',\n                    providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }],\n                    inputs: ['columns: matHeaderRowDef', 'sticky: matHeaderRowDefSticky'],\n                }]\n        }] });\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n}\nMatFooterRowDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatFooterRowDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatFooterRowDef, selector: \"[matFooterRowDef]\", inputs: { columns: [\"matFooterRowDef\", \"columns\"], sticky: [\"matFooterRowDefSticky\", \"sticky\"] }, providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterRowDef]',\n                    providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }],\n                    inputs: ['columns: matFooterRowDef', 'sticky: matFooterRowDefSticky'],\n                }]\n        }] });\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n}\nMatRowDef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatRowDef.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatRowDef, selector: \"[matRowDef]\", inputs: { columns: [\"matRowDefColumns\", \"columns\"], when: [\"matRowDefWhen\", \"when\"] }, providers: [{ provide: CdkRowDef, useExisting: MatRowDef }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matRowDef]',\n                    providers: [{ provide: CdkRowDef, useExisting: MatRowDef }],\n                    inputs: ['columns: matRowDefColumns', 'when: matRowDefWhen'],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n}\nMatHeaderRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatHeaderRow.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatHeaderRow, selector: \"mat-header-row, tr[mat-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-header-row mdc-data-table__header-row\" }, providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }], exportAs: [\"matHeaderRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-header-row, tr[mat-header-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matHeaderRow',\n                    providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n}\nMatFooterRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatFooterRow.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatFooterRow, selector: \"mat-footer-row, tr[mat-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-footer-row mdc-data-table__row\" }, providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }], exportAs: [\"matFooterRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-footer-row, tr[mat-footer-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-footer-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matFooterRow',\n                    providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n}\nMatRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatRow.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatRow, selector: \"mat-row, tr[mat-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-row mdc-data-table__row\" }, providers: [{ provide: CdkRow, useExisting: MatRow }], exportAs: [\"matRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-row, tr[mat-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matRow',\n                    providers: [{ provide: CdkRow, useExisting: MatRow }],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n    constructor() {\n        super(...arguments);\n        this._contentClassName = 'mat-mdc-no-data-row';\n    }\n}\nMatNoDataRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNoDataRow, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatNoDataRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatNoDataRow, selector: \"ng-template[matNoDataRow]\", providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matNoDataRow]',\n                    providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n}\nMatTextColumn.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTextColumn, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatTextColumn.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTextColumn, selector: \"mat-text-column\", usesInheritance: true, ngImport: i0, template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\" }, { kind: \"directive\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: [\"sticky\", \"matColumnDef\"] }, { kind: \"directive\", type: MatCellDef, selector: \"[matCellDef]\" }, { kind: \"directive\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\" }, { kind: \"directive\", type: MatCell, selector: \"mat-cell, td[mat-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-text-column',\n                    template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst EXPORTED_DECLARATIONS = [\n    // Table\n    MatTable,\n    MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef,\n    MatHeaderRowDef,\n    MatColumnDef,\n    MatCellDef,\n    MatRowDef,\n    MatFooterCellDef,\n    MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell,\n    MatCell,\n    MatFooterCell,\n    // Row directives\n    MatHeaderRow,\n    MatRow,\n    MatFooterRow,\n    MatNoDataRow,\n    MatTextColumn,\n];\nclass MatTableModule {\n}\nMatTableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTableModule, declarations: [\n        // Table\n        MatTable,\n        MatRecycleRows,\n        // Template defs\n        MatHeaderCellDef,\n        MatHeaderRowDef,\n        MatColumnDef,\n        MatCellDef,\n        MatRowDef,\n        MatFooterCellDef,\n        MatFooterRowDef,\n        // Cell directives\n        MatHeaderCell,\n        MatCell,\n        MatFooterCell,\n        // Row directives\n        MatHeaderRow,\n        MatRow,\n        MatFooterRow,\n        MatNoDataRow,\n        MatTextColumn], imports: [MatCommonModule, CdkTableModule], exports: [MatCommonModule, \n        // Table\n        MatTable,\n        MatRecycleRows,\n        // Template defs\n        MatHeaderCellDef,\n        MatHeaderRowDef,\n        MatColumnDef,\n        MatCellDef,\n        MatRowDef,\n        MatFooterCellDef,\n        MatFooterRowDef,\n        // Cell directives\n        MatHeaderCell,\n        MatCell,\n        MatFooterCell,\n        // Row directives\n        MatHeaderRow,\n        MatRow,\n        MatFooterRow,\n        MatNoDataRow,\n        MatTextColumn] });\nMatTableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CdkTableModule],\n                    exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n                    declarations: EXPORTED_DECLARATIONS,\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/** Shared base class with MDC-based implementation. */\nclass _MatTableDataSource extends DataSource {\n    /** Array of data that should be rendered by the table, where each object represents one row. */\n    get data() {\n        return this._data.value;\n    }\n    set data(data) {\n        data = Array.isArray(data) ? data : [];\n        this._data.next(data);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(data);\n        }\n    }\n    /**\n     * Filter term that should be used to filter out objects from the data array. To override how\n     * data objects match to this filter string, provide a custom function for filterPredicate.\n     */\n    get filter() {\n        return this._filter.value;\n    }\n    set filter(filter) {\n        this._filter.next(filter);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(this.data);\n        }\n    }\n    /**\n     * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n     * emitted by the MatSort will trigger an update to the table's rendered data.\n     */\n    get sort() {\n        return this._sort;\n    }\n    set sort(sort) {\n        this._sort = sort;\n        this._updateChangeSubscription();\n    }\n    /**\n     * Instance of the paginator component used by the table to control what page of the data is\n     * displayed. Page changes emitted by the paginator will trigger an update to the\n     * table's rendered data.\n     *\n     * Note that the data source uses the paginator's properties to calculate which page of data\n     * should be displayed. If the paginator receives its properties as template inputs,\n     * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n     * initialized before assigning it to this data source.\n     */\n    get paginator() {\n        return this._paginator;\n    }\n    set paginator(paginator) {\n        this._paginator = paginator;\n        this._updateChangeSubscription();\n    }\n    constructor(initialData = []) {\n        super();\n        /** Stream emitting render data to the table (depends on ordered data changes). */\n        this._renderData = new BehaviorSubject([]);\n        /** Stream that emits when a new filter string is set on the data source. */\n        this._filter = new BehaviorSubject('');\n        /** Used to react to internal changes of the paginator that are made by the data source itself. */\n        this._internalPageChanges = new Subject();\n        /**\n         * Subscription to the changes that should trigger an update to the table's rendered rows, such\n         * as filtering, sorting, pagination, or base data changes.\n         */\n        this._renderChangesSubscription = null;\n        /**\n         * Data accessor function that is used for accessing data properties for sorting through\n         * the default sortData function.\n         * This default function assumes that the sort header IDs (which defaults to the column name)\n         * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n         * May be set to a custom function for different behavior.\n         * @param data Data object that is being accessed.\n         * @param sortHeaderId The name of the column that represents the data.\n         */\n        this.sortingDataAccessor = (data, sortHeaderId) => {\n            const value = data[sortHeaderId];\n            if (_isNumberValue(value)) {\n                const numberValue = Number(value);\n                // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n                // leave them as strings. For more info: https://goo.gl/y5vbSg\n                return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n            }\n            return value;\n        };\n        /**\n         * Gets a sorted copy of the data array based on the state of the MatSort. Called\n         * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n         * By default, the function retrieves the active sort and its direction and compares data\n         * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n         * of data ordering.\n         * @param data The array of data that should be sorted.\n         * @param sort The connected MatSort that holds the current sort state.\n         */\n        this.sortData = (data, sort) => {\n            const active = sort.active;\n            const direction = sort.direction;\n            if (!active || direction == '') {\n                return data;\n            }\n            return data.sort((a, b) => {\n                let valueA = this.sortingDataAccessor(a, active);\n                let valueB = this.sortingDataAccessor(b, active);\n                // If there are data in the column that can be converted to a number,\n                // it must be ensured that the rest of the data\n                // is of the same type so as not to order incorrectly.\n                const valueAType = typeof valueA;\n                const valueBType = typeof valueB;\n                if (valueAType !== valueBType) {\n                    if (valueAType === 'number') {\n                        valueA += '';\n                    }\n                    if (valueBType === 'number') {\n                        valueB += '';\n                    }\n                }\n                // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n                // one value exists while the other doesn't. In this case, existing value should come last.\n                // This avoids inconsistent results when comparing values to undefined/null.\n                // If neither value exists, return 0 (equal).\n                let comparatorResult = 0;\n                if (valueA != null && valueB != null) {\n                    // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n                    if (valueA > valueB) {\n                        comparatorResult = 1;\n                    }\n                    else if (valueA < valueB) {\n                        comparatorResult = -1;\n                    }\n                }\n                else if (valueA != null) {\n                    comparatorResult = 1;\n                }\n                else if (valueB != null) {\n                    comparatorResult = -1;\n                }\n                return comparatorResult * (direction == 'asc' ? 1 : -1);\n            });\n        };\n        /**\n         * Checks if a data object matches the data source's filter string. By default, each data object\n         * is converted to a string of its properties and returns true if the filter has\n         * at least one occurrence in that string. By default, the filter string has its whitespace\n         * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n         * filter matching.\n         * @param data Data object used to check against the filter.\n         * @param filter Filter string that has been set on the data source.\n         * @returns Whether the filter matches against the data\n         */\n        this.filterPredicate = (data, filter) => {\n            // Transform the data into a lowercase string of all property values.\n            const dataStr = Object.keys(data)\n                .reduce((currentTerm, key) => {\n                // Use an obscure Unicode character to delimit the words in the concatenated string.\n                // This avoids matches where the values of two columns combined will match the user's query\n                // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n                // that has a very low chance of being typed in by somebody in a text field. This one in\n                // particular is \"White up-pointing triangle with dot\" from\n                // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n                return currentTerm + data[key] + '◬';\n            }, '')\n                .toLowerCase();\n            // Transform the filter by converting it to lowercase and removing whitespace.\n            const transformedFilter = filter.trim().toLowerCase();\n            return dataStr.indexOf(transformedFilter) != -1;\n        };\n        this._data = new BehaviorSubject(initialData);\n        this._updateChangeSubscription();\n    }\n    /**\n     * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n     * changes occur, process the current state of the filter, sort, and pagination along with\n     * the provided base data and send it to the table for rendering.\n     */\n    _updateChangeSubscription() {\n        // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n        // The events should emit whenever the component emits a change or initializes, or if no\n        // component is provided, a stream with just a null event should be provided.\n        // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n        // pipeline can progress to the next step. Note that the value from these streams are not used,\n        // they purely act as a signal to progress in the pipeline.\n        const sortChange = this._sort\n            ? merge(this._sort.sortChange, this._sort.initialized)\n            : of(null);\n        const pageChange = this._paginator\n            ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized)\n            : of(null);\n        const dataStream = this._data;\n        // Watch for base data or filter changes to provide a filtered set of data.\n        const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n        // Watch for filtered data or sort changes to provide an ordered set of data.\n        const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n        // Watch for ordered data or page changes to provide a paged set of data.\n        const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n        // Watched for paged data changes and send the result to the table to render.\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n    }\n    /**\n     * Returns a filtered data array where each filter object contains the filter string within\n     * the result of the filterPredicate function. If no filter is set, returns the data array\n     * as provided.\n     */\n    _filterData(data) {\n        // If there is a filter string, filter out data that does not contain it.\n        // Each data object is converted to a string using the function defined by filterPredicate.\n        // May be overridden for customization.\n        this.filteredData =\n            this.filter == null || this.filter === ''\n                ? data\n                : data.filter(obj => this.filterPredicate(obj, this.filter));\n        if (this.paginator) {\n            this._updatePaginator(this.filteredData.length);\n        }\n        return this.filteredData;\n    }\n    /**\n     * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n     * data array as provided. Uses the default data accessor for data lookup, unless a\n     * sortDataAccessor function is defined.\n     */\n    _orderData(data) {\n        // If there is no active sort or direction, return the data without trying to sort.\n        if (!this.sort) {\n            return data;\n        }\n        return this.sortData(data.slice(), this.sort);\n    }\n    /**\n     * Returns a paged slice of the provided data array according to the provided paginator's page\n     * index and length. If there is no paginator provided, returns the data array as provided.\n     */\n    _pageData(data) {\n        if (!this.paginator) {\n            return data;\n        }\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        return data.slice(startIndex, startIndex + this.paginator.pageSize);\n    }\n    /**\n     * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n     * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n     * guard against making property changes within a round of change detection.\n     */\n    _updatePaginator(filteredDataLength) {\n        Promise.resolve().then(() => {\n            const paginator = this.paginator;\n            if (!paginator) {\n                return;\n            }\n            paginator.length = filteredDataLength;\n            // If the page index is set beyond the page, reduce it to the last page.\n            if (paginator.pageIndex > 0) {\n                const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n                const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n                if (newPageIndex !== paginator.pageIndex) {\n                    paginator.pageIndex = newPageIndex;\n                    // Since the paginator only emits after user-generated changes,\n                    // we need our own stream so we know to should re-render the data.\n                    this._internalPageChanges.next();\n                }\n            }\n        });\n    }\n    /**\n     * Used by the MatTable. Called when it connects to the data source.\n     * @docs-private\n     */\n    connect() {\n        if (!this._renderChangesSubscription) {\n            this._updateChangeSubscription();\n        }\n        return this._renderData;\n    }\n    /**\n     * Used by the MatTable. Called when it disconnects from the data source.\n     * @docs-private\n     */\n    disconnect() {\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = null;\n    }\n}\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends _MatTableDataSource {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn, _MatTableDataSource };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACjH,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,QAAQ,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AACpZ,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,UAAU,QAAQ,0BAA0B;AAC1I,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACzE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,GAAG,QAAQ,gBAAgB;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAAA;AAAA;EAAA;IAMsG1C,EAAE,2BAkW/B;IAlW6BA,EAAE,UAoWlG;IApWgGA,EAAE,eAoW7F;EAAA;EAAA;IAAA,eApW2FA,EAAE;IAAFA,EAAE,0CAkWhC;IAlW8BA,EAAE,aAoWlG;IApWgGA,EAAE,gDAoWlG;EAAA;AAAA;AAAA;EAAA;IApWgGA,EAAE,2BAqWjC;IArW+BA,EAAE,UAuWlG;IAvWgGA,EAAE,eAuW7F;EAAA;EAAA;IAAA;IAAA,eAvW2FA,EAAE;IAAFA,EAAE,0CAqWlC;IArWgCA,EAAE,aAuWlG;IAvWgGA,EAAE,wEAuWlG;EAAA;AAAA;AAzWN,MAAM2C,cAAc,CAAC;AAErBA,cAAc,CAACC,IAAI;EAAA,iBAA6FD,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAACE,IAAI,kBADmF7C,EAAE;EAAA,MACJ2C,cAAc;EAAA;EAAA,WADZ3C,EAAE,oBAC0F,CAAC;IAAE8C,OAAO,EAAEf,uBAAuB;IAAEgB,QAAQ,EAAEf;EAA6B,CAAC,CAAC;AAAA,EAAiB;AACjS;EAAA,mDAFsGhC,EAAE,mBAER2C,cAAc,EAAc,CAAC;IACjHK,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEf,uBAAuB;QAAEgB,QAAQ,EAAEf;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMoB,QAAQ,SAAS5C,QAAQ,CAAC;EAC5B6C,WAAW,GAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,cAAc,GAAG,sBAAsB;IAC5C;IACA,IAAI,CAACC,4BAA4B,GAAG,KAAK;EAC7C;EACAC,QAAQ,GAAG;IACP,KAAK,CAACA,QAAQ,EAAE;IAChB;IACA;IACA;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MACzB,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACnEH,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IAClD;EACJ;AACJ;AACAZ,QAAQ,CAACR,IAAI;EAAA;EAAA;IAAA,0DA5ByF5C,EAAE,uBA4BEoD,QAAQ,SAARA,QAAQ;EAAA;AAAA,GAAqD;AACvKA,QAAQ,CAACa,IAAI,kBA7ByFjE,EAAE;EAAA,MA6BVoD,QAAQ;EAAA;EAAA;EAAA;EAAA;IAAA;MA7BApD,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA6BoL,CACpR;IAAE8C,OAAO,EAAEtC,QAAQ;IAAE0D,WAAW,EAAEd;EAAS,CAAC,EAC5C;IAAEN,OAAO,EAAErC,SAAS;IAAEyD,WAAW,EAAEd;EAAS,CAAC,EAC7C;IAAEN,OAAO,EAAEpC,0BAA0B;IAAEqC,QAAQ,EAAEpC;EAAyB,CAAC;EAC3E;EACA;EACA;IAAEmC,OAAO,EAAEf,uBAAuB;IAAEgB,QAAQ,EAAEd;EAA6B,CAAC;EAC5E;EACA;IAAEa,OAAO,EAAElC,2BAA2B;IAAEuD,QAAQ,EAAE;EAAK,CAAC,CAC3D,GAtCiGnE,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAsCuB;MAtCzBA,EAAE,mBAsC6E;MAtC/EA,EAAE,yBAsC8H;IAAA;EAAA;EAAA,eAA4yZO,EAAE,CAAC6D,aAAa,EAAwD7D,EAAE,CAAC8D,eAAe,EAA8D9D,EAAE,CAAC+D,eAAe,EAA8D/D,EAAE,CAACgE,eAAe;EAAA;EAAA;AAAA,EAAoI;AACh5a;EAAA,mDAvCsGvE,EAAE,mBAuCRoD,QAAQ,EAAc,CAAC;IAC3GJ,IAAI,EAAE9C,SAAS;IACf+C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEsB,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE5D,kBAAkB;MAAE6D,IAAI,EAAE;QAChG,OAAO,EAAE,qCAAqC;QAC9C,gCAAgC,EAAE;MACtC,CAAC;MAAEvB,SAAS,EAAE,CACV;QAAEL,OAAO,EAAEtC,QAAQ;QAAE0D,WAAW,EAAEd;MAAS,CAAC,EAC5C;QAAEN,OAAO,EAAErC,SAAS;QAAEyD,WAAW,EAAEd;MAAS,CAAC,EAC7C;QAAEN,OAAO,EAAEpC,0BAA0B;QAAEqC,QAAQ,EAAEpC;MAAyB,CAAC;MAC3E;MACA;MACA;QAAEmC,OAAO,EAAEf,uBAAuB;QAAEgB,QAAQ,EAAEd;MAA6B,CAAC;MAC5E;MACA;QAAEa,OAAO,EAAElC,2BAA2B;QAAEuD,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEQ,aAAa,EAAExE,iBAAiB,CAACyE,IAAI;MAAEC,eAAe,EAAEzE,uBAAuB,CAAC0E,OAAO;MAAEC,MAAM,EAAE,CAAC,olZAAolZ;IAAE,CAAC;EACxsZ,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAASlE,UAAU,CAAC;AAEpCkE,UAAU,CAACpC,IAAI;EAAA;EAAA;IAAA,8DArEuF5C,EAAE,uBAqEIgF,UAAU,SAAVA,UAAU;EAAA;AAAA,GAAqD;AAC3KA,UAAU,CAACnC,IAAI,kBAtEuF7C,EAAE;EAAA,MAsERgF,UAAU;EAAA;EAAA,WAtEJhF,EAAE,oBAsEyC,CAAC;IAAE8C,OAAO,EAAEhC,UAAU;IAAEoD,WAAW,EAAEc;EAAW,CAAC,CAAC,GAtE7FhF,EAAE;AAAA,EAsEmI;AAC3O;EAAA,mDAvEsGA,EAAE,mBAuERgF,UAAU,EAAc,CAAC;IAC7GhC,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEhC,UAAU;QAAEoD,WAAW,EAAEc;MAAW,CAAC;IAChE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,SAASlE,gBAAgB,CAAC;AAEhDkE,gBAAgB,CAACrC,IAAI;EAAA;EAAA;IAAA,0EApFiF5C,EAAE,uBAoFUiF,gBAAgB,SAAhBA,gBAAgB;EAAA;AAAA,GAAqD;AACvLA,gBAAgB,CAACpC,IAAI,kBArFiF7C,EAAE;EAAA,MAqFFiF,gBAAgB;EAAA;EAAA,WArFhBjF,EAAE,oBAqF2D,CAAC;IAAE8C,OAAO,EAAE/B,gBAAgB;IAAEmD,WAAW,EAAEe;EAAiB,CAAC,CAAC,GArF3HjF,EAAE;AAAA,EAqFiK;AACzQ;EAAA,mDAtFsGA,EAAE,mBAsFRiF,gBAAgB,EAAc,CAAC;IACnHjC,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAE/B,gBAAgB;QAAEmD,WAAW,EAAEe;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,SAASlE,gBAAgB,CAAC;AAEhDkE,gBAAgB,CAACtC,IAAI;EAAA;EAAA;IAAA,0EAnGiF5C,EAAE,uBAmGUkF,gBAAgB,SAAhBA,gBAAgB;EAAA;AAAA,GAAqD;AACvLA,gBAAgB,CAACrC,IAAI,kBApGiF7C,EAAE;EAAA,MAoGFkF,gBAAgB;EAAA;EAAA,WApGhBlF,EAAE,oBAoG2D,CAAC;IAAE8C,OAAO,EAAE9B,gBAAgB;IAAEkD,WAAW,EAAEgB;EAAiB,CAAC,CAAC,GApG3HlF,EAAE;AAAA,EAoGiK;AACzQ;EAAA,mDArGsGA,EAAE,mBAqGRkF,gBAAgB,EAAc,CAAC;IACnHlC,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAE9B,gBAAgB;QAAEkD,WAAW,EAAEgB;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASlE,YAAY,CAAC;EACpC;EACA,IAAImE,IAAI,GAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAI,CAACA,IAAI,EAAE;IACX,IAAI,CAACE,aAAa,CAACF,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,yBAAyB,GAAG;IACxB,KAAK,CAACA,yBAAyB,EAAE;IACjC,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAE,cAAa,IAAI,CAACC,oBAAqB,EAAC,CAAC;EAC5E;AACJ;AACAP,YAAY,CAACvC,IAAI;EAAA;EAAA;IAAA,kEAnIqF5C,EAAE,uBAmIMmF,YAAY,SAAZA,YAAY;EAAA;AAAA,GAAqD;AAC/KA,YAAY,CAACtC,IAAI,kBApIqF7C,EAAE;EAAA,MAoINmF,YAAY;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WApIRnF,EAAE,oBAoI6G,CAC7M;IAAE8C,OAAO,EAAE7B,YAAY;IAAEiD,WAAW,EAAEiB;EAAa,CAAC,EACpD;IAAErC,OAAO,EAAE,4BAA4B;IAAEoB,WAAW,EAAEiB;EAAa,CAAC,CACvE,GAvIiGnF,EAAE;AAAA,EAuI3D;AAC7C;EAAA,mDAxIsGA,EAAE,mBAwIRmF,YAAY,EAAc,CAAC;IAC/GnC,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1ByC,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClBxC,SAAS,EAAE,CACP;QAAEL,OAAO,EAAE7B,YAAY;QAAEiD,WAAW,EAAEiB;MAAa,CAAC,EACpD;QAAErC,OAAO,EAAE,4BAA4B;QAAEoB,WAAW,EAAEiB;MAAa,CAAC;IAE5E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEC,IAAI,EAAE,CAAC;MACrBpC,IAAI,EAAE3C,KAAK;MACX4C,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM2C,aAAa,SAAS1E,aAAa,CAAC;AAE1C0E,aAAa,CAAChD,IAAI;EAAA;EAAA;IAAA,oEAzJoF5C,EAAE,uBAyJO4F,aAAa,SAAbA,aAAa;EAAA;AAAA,GAAqD;AACjLA,aAAa,CAAC/C,IAAI,kBA1JoF7C,EAAE;EAAA,MA0JL4F,aAAa;EAAA;EAAA,oBAAkF,cAAc;EAAA,WA1J1G5F,EAAE;AAAA,EA0JuN;AAC/T;EAAA,mDA3JsGA,EAAE,mBA2JR4F,aAAa,EAAc,CAAC;IAChH5C,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDwB,IAAI,EAAE;QACF,OAAO,EAAE,iDAAiD;QAC1D,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMmB,aAAa,SAAS1E,aAAa,CAAC;AAE1C0E,aAAa,CAACjD,IAAI;EAAA;EAAA;IAAA,oEAxKoF5C,EAAE,uBAwKO6F,aAAa,SAAbA,aAAa;EAAA;AAAA,GAAqD;AACjLA,aAAa,CAAChD,IAAI,kBAzKoF7C,EAAE;EAAA,MAyKL6F,aAAa;EAAA;EAAA;EAAA,WAzKV7F,EAAE;AAAA,EAyKwK;AAChR;EAAA,mDA1KsGA,EAAE,mBA0KR6F,aAAa,EAAc,CAAC;IAChH7C,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDwB,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMoB,OAAO,SAAS1E,OAAO,CAAC;AAE9B0E,OAAO,CAAClD,IAAI;EAAA;EAAA;IAAA,wDAtL0F5C,EAAE,uBAsLC8F,OAAO,SAAPA,OAAO;EAAA;AAAA,GAAqD;AACrKA,OAAO,CAACjD,IAAI,kBAvL0F7C,EAAE;EAAA,MAuLX8F,OAAO;EAAA;EAAA;EAAA,WAvLE9F,EAAE;AAAA,EAuLuI;AAC/O;EAAA,mDAxLsGA,EAAE,mBAwLR8F,OAAO,EAAc,CAAC;IAC1G9C,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCwB,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,eAAe,SAAS1E,eAAe,CAAC;AAE9C0E,eAAe,CAACnD,IAAI;EAAA;EAAA;IAAA,wEA/MkF5C,EAAE,uBA+MS+F,eAAe,SAAfA,eAAe;EAAA;AAAA,GAAqD;AACrLA,eAAe,CAAClD,IAAI,kBAhNkF7C,EAAE;EAAA,MAgNH+F,eAAe;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAhNd/F,EAAE,oBAgN0J,CAAC;IAAE8C,OAAO,EAAEzB,eAAe;IAAE6C,WAAW,EAAE6B;EAAgB,CAAC,CAAC,GAhNxN/F,EAAE;AAAA,EAgN8P;AACtW;EAAA,mDAjNsGA,EAAE,mBAiNR+F,eAAe,EAAc,CAAC;IAClH/C,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEzB,eAAe;QAAE6C,WAAW,EAAE6B;MAAgB,CAAC,CAAC;MACvEJ,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMK,eAAe,SAAS1E,eAAe,CAAC;AAE9C0E,eAAe,CAACpD,IAAI;EAAA;EAAA;IAAA,wEA/NkF5C,EAAE,uBA+NSgG,eAAe,SAAfA,eAAe;EAAA;AAAA,GAAqD;AACrLA,eAAe,CAACnD,IAAI,kBAhOkF7C,EAAE;EAAA,MAgOHgG,eAAe;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAhOdhG,EAAE,oBAgO0J,CAAC;IAAE8C,OAAO,EAAExB,eAAe;IAAE4C,WAAW,EAAE8B;EAAgB,CAAC,CAAC,GAhOxNhG,EAAE;AAAA,EAgO8P;AACtW;EAAA,mDAjOsGA,EAAE,mBAiORgG,eAAe,EAAc,CAAC;IAClHhD,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAExB,eAAe;QAAE4C,WAAW,EAAE8B;MAAgB,CAAC,CAAC;MACvEL,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMM,SAAS,SAAS1E,SAAS,CAAC;AAElC0E,SAAS,CAACrD,IAAI;EAAA;EAAA;IAAA,4DAhPwF5C,EAAE,uBAgPGiG,SAAS,SAATA,SAAS;EAAA;AAAA,GAAqD;AACzKA,SAAS,CAACpD,IAAI,kBAjPwF7C,EAAE;EAAA,MAiPTiG,SAAS;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAjPFjG,EAAE,oBAiP6H,CAAC;IAAE8C,OAAO,EAAEvB,SAAS;IAAE2C,WAAW,EAAE+B;EAAU,CAAC,CAAC,GAjP/KjG,EAAE;AAAA,EAiPqN;AAC7T;EAAA,mDAlPsGA,EAAE,mBAkPRiG,SAAS,EAAc,CAAC;IAC5GjD,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEvB,SAAS;QAAE2C,WAAW,EAAE+B;MAAU,CAAC,CAAC;MAC3DN,MAAM,EAAE,CAAC,2BAA2B,EAAE,qBAAqB;IAC/D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMO,YAAY,SAAS1E,YAAY,CAAC;AAExC0E,YAAY,CAACtD,IAAI;EAAA;EAAA;IAAA,kEA7PqF5C,EAAE,uBA6PMkG,YAAY,SAAZA,YAAY;EAAA;AAAA,GAAqD;AAC/KA,YAAY,CAACjC,IAAI,kBA9PqFjE,EAAE;EAAA,MA8PNkG,YAAY;EAAA;EAAA,oBAAgF,KAAK;EAAA;EAAA,WA9P7FlG,EAAE,oBA8P6K,CAAC;IAAE8C,OAAO,EAAEtB,YAAY;IAAE0C,WAAW,EAAEgC;EAAa,CAAC,CAAC,GA9PrOlG,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,yBA8P4V;IAAA;EAAA;EAAA,eAA6DO,EAAE,CAAC4F,aAAa;EAAA;AAAA,EAAkI;AACnpB;EAAA,mDA/PsGnG,EAAE,mBA+PRkG,YAAY,EAAc,CAAC;IAC/GlD,IAAI,EAAE9C,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CuB,QAAQ,EAAEhD,gBAAgB;MAC1BiD,IAAI,EAAE;QACF,OAAO,EAAE,+CAA+C;QACxD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAG,eAAe,EAAEzE,uBAAuB,CAAC0E,OAAO;MAChDH,aAAa,EAAExE,iBAAiB,CAACyE,IAAI;MACrCJ,QAAQ,EAAE,cAAc;MACxBrB,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEtB,YAAY;QAAE0C,WAAW,EAAEgC;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAME,YAAY,SAAS1E,YAAY,CAAC;AAExC0E,YAAY,CAACxD,IAAI;EAAA;EAAA;IAAA,kEAnRqF5C,EAAE,uBAmRMoG,YAAY,SAAZA,YAAY;EAAA;AAAA,GAAqD;AAC/KA,YAAY,CAACnC,IAAI,kBApRqFjE,EAAE;EAAA,MAoRNoG,YAAY;EAAA;EAAA,oBAAgF,KAAK;EAAA;EAAA,WApR7FpG,EAAE,oBAoRsK,CAAC;IAAE8C,OAAO,EAAEpB,YAAY;IAAEwC,WAAW,EAAEkC;EAAa,CAAC,CAAC,GApR9NpG,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,yBAoRqV;IAAA;EAAA;EAAA,eAA6DO,EAAE,CAAC4F,aAAa;EAAA;AAAA,EAAkI;AAC5oB;EAAA,mDArRsGnG,EAAE,mBAqRRoG,YAAY,EAAc,CAAC;IAC/GpD,IAAI,EAAE9C,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CuB,QAAQ,EAAEhD,gBAAgB;MAC1BiD,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAG,eAAe,EAAEzE,uBAAuB,CAAC0E,OAAO;MAChDH,aAAa,EAAExE,iBAAiB,CAACyE,IAAI;MACrCJ,QAAQ,EAAE,cAAc;MACxBrB,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEpB,YAAY;QAAEwC,WAAW,EAAEkC;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,MAAM,SAAS1E,MAAM,CAAC;AAE5B0E,MAAM,CAACzD,IAAI;EAAA;EAAA;IAAA,sDAzS2F5C,EAAE,uBAySAqG,MAAM,SAANA,MAAM;EAAA;AAAA,GAAqD;AACnKA,MAAM,CAACpC,IAAI,kBA1S2FjE,EAAE;EAAA,MA0SZqG,MAAM;EAAA;EAAA,oBAAkE,KAAK;EAAA;EAAA,WA1SnErG,EAAE,oBA0SqI,CAAC;IAAE8C,OAAO,EAAEnB,MAAM;IAAEuC,WAAW,EAAEmC;EAAO,CAAC,CAAC,GA1SjLrG,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,yBA0SkS;IAAA;EAAA;EAAA,eAA6DO,EAAE,CAAC4F,aAAa;EAAA;AAAA,EAAkI;AACzlB;EAAA,mDA3SsGnG,EAAE,mBA2SRqG,MAAM,EAAc,CAAC;IACzGrD,IAAI,EAAE9C,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCuB,QAAQ,EAAEhD,gBAAgB;MAC1BiD,IAAI,EAAE;QACF,OAAO,EAAE,iCAAiC;QAC1C,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAG,eAAe,EAAEzE,uBAAuB,CAAC0E,OAAO;MAChDH,aAAa,EAAExE,iBAAiB,CAACyE,IAAI;MACrCJ,QAAQ,EAAE,QAAQ;MAClBrB,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAEnB,MAAM;QAAEuC,WAAW,EAAEmC;MAAO,CAAC;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,YAAY,SAAS1E,YAAY,CAAC;EACpCyB,WAAW,GAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACiD,iBAAiB,GAAG,qBAAqB;EAClD;AACJ;AACAD,YAAY,CAAC1D,IAAI;EAAA;EAAA;IAAA,kEAnUqF5C,EAAE,uBAmUMsG,YAAY,SAAZA,YAAY;EAAA;AAAA,GAAqD;AAC/KA,YAAY,CAACzD,IAAI,kBApUqF7C,EAAE;EAAA,MAoUNsG,YAAY;EAAA;EAAA,WApURtG,EAAE,oBAoU0D,CAAC;IAAE8C,OAAO,EAAElB,YAAY;IAAEsC,WAAW,EAAEoC;EAAa,CAAC,CAAC,GApUlHtG,EAAE;AAAA,EAoUwJ;AAChQ;EAAA,mDArUsGA,EAAE,mBAqURsG,YAAY,EAAc,CAAC;IAC/GtD,IAAI,EAAE/C,SAAS;IACfgD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CAAC;QAAEL,OAAO,EAAElB,YAAY;QAAEsC,WAAW,EAAEoC;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,aAAa,SAAS3E,aAAa,CAAC;AAE1C2E,aAAa,CAAC5D,IAAI;EAAA;EAAA;IAAA,oEA/VoF5C,EAAE,uBA+VOwG,aAAa,SAAbA,aAAa;EAAA;AAAA,GAAqD;AACjLA,aAAa,CAACvC,IAAI,kBAhWoFjE,EAAE;EAAA,MAgWLwG,aAAa;EAAA;EAAA,WAhWVxG,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,8BAiWzE;MAjWuEA,EAAE,0DAoW7F;MApW2FA,EAAE,0DAuW7F;MAvW2FA,EAAE,wBAwWrF;IAAA;EAAA;EAAA,eAC4CiF,gBAAgB,EAA+DE,YAAY,EAA+FH,UAAU,EAAyDY,aAAa,EAAiFE,OAAO;EAAA;AAAA,EAAyI;AAC1iB;EAAA,mDA1WsG9F,EAAE,mBA0WRwG,aAAa,EAAc,CAAC;IAChHxD,IAAI,EAAE9C,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BuB,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBE,aAAa,EAAExE,iBAAiB,CAACyE,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAC,eAAe,EAAEzE,uBAAuB,CAAC0E;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2B,qBAAqB,GAAG;AAC1B;AACArD,QAAQ,EACRT,cAAc;AACd;AACAsC,gBAAgB,EAChBc,eAAe,EACfZ,YAAY,EACZH,UAAU,EACViB,SAAS,EACTf,gBAAgB,EAChBc,eAAe;AACf;AACAJ,aAAa,EACbE,OAAO,EACPD,aAAa;AACb;AACAK,YAAY,EACZG,MAAM,EACND,YAAY,EACZE,YAAY,EACZE,aAAa,CAChB;AACD,MAAME,cAAc,CAAC;AAErBA,cAAc,CAAC9D,IAAI;EAAA,iBAA6F8D,cAAc;AAAA,CAAkD;AAChLA,cAAc,CAACC,IAAI,kBApamF3G,EAAE;EAAA,MAoaS0G;AAAc,EA0CtG;AACzBA,cAAc,CAACE,IAAI,kBA/cmF5G,EAAE;EAAA,UA+cmCmC,eAAe,EAAEL,cAAc,EAAEK,eAAe;AAAA,EAAI;AAC/L;EAAA,mDAhdsGnC,EAAE,mBAgdR0G,cAAc,EAAc,CAAC;IACjH1D,IAAI,EAAE1C,QAAQ;IACd2C,IAAI,EAAE,CAAC;MACC4D,OAAO,EAAE,CAAC1E,eAAe,EAAEL,cAAc,CAAC;MAC1CgF,OAAO,EAAE,CAAC3E,eAAe,EAAEsE,qBAAqB,CAAC;MACjDM,YAAY,EAAEN;IAClB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,gBAAgB,GAAG,gBAAgB;AACzC;AACA,MAAMC,mBAAmB,SAAS/E,UAAU,CAAC;EACzC;EACA,IAAIgF,IAAI,GAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,KAAK;EAC3B;EACA,IAAIF,IAAI,CAACA,IAAI,EAAE;IACXA,IAAI,GAAGG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACtC,IAAI,CAACC,KAAK,CAACI,IAAI,CAACL,IAAI,CAAC;IACrB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIQ,MAAM,GAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAACP,KAAK;EAC7B;EACA,IAAIM,MAAM,CAACA,MAAM,EAAE;IACf,IAAI,CAACC,OAAO,CAACJ,IAAI,CAACG,MAAM,CAAC;IACzB;IACA;IACA,IAAI,CAAC,IAAI,CAACF,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,IAAI,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIU,IAAI,GAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAI,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,yBAAyB,EAAE;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,SAAS,GAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAAS,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAACD,yBAAyB,EAAE;EACpC;EACAzE,WAAW,CAAC4E,WAAW,GAAG,EAAE,EAAE;IAC1B,KAAK,EAAE;IACP;IACA,IAAI,CAACC,WAAW,GAAG,IAAI9F,eAAe,CAAC,EAAE,CAAC;IAC1C;IACA,IAAI,CAACuF,OAAO,GAAG,IAAIvF,eAAe,CAAC,EAAE,CAAC;IACtC;IACA,IAAI,CAAC+F,oBAAoB,GAAG,IAAI9F,OAAO,EAAE;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAACmF,0BAA0B,GAAG,IAAI;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACY,mBAAmB,GAAG,CAAClB,IAAI,EAAEmB,YAAY,KAAK;MAC/C,MAAMjB,KAAK,GAAGF,IAAI,CAACmB,YAAY,CAAC;MAChC,IAAI5F,cAAc,CAAC2E,KAAK,CAAC,EAAE;QACvB,MAAMkB,WAAW,GAAGC,MAAM,CAACnB,KAAK,CAAC;QACjC;QACA;QACA,OAAOkB,WAAW,GAAGtB,gBAAgB,GAAGsB,WAAW,GAAGlB,KAAK;MAC/D;MACA,OAAOA,KAAK;IAChB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoB,QAAQ,GAAG,CAACtB,IAAI,EAAEU,IAAI,KAAK;MAC5B,MAAMa,MAAM,GAAGb,IAAI,CAACa,MAAM;MAC1B,MAAMC,SAAS,GAAGd,IAAI,CAACc,SAAS;MAChC,IAAI,CAACD,MAAM,IAAIC,SAAS,IAAI,EAAE,EAAE;QAC5B,OAAOxB,IAAI;MACf;MACA,OAAOA,IAAI,CAACU,IAAI,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAK;QACvB,IAAIC,MAAM,GAAG,IAAI,CAACT,mBAAmB,CAACO,CAAC,EAAEF,MAAM,CAAC;QAChD,IAAIK,MAAM,GAAG,IAAI,CAACV,mBAAmB,CAACQ,CAAC,EAAEH,MAAM,CAAC;QAChD;QACA;QACA;QACA,MAAMM,UAAU,GAAG,OAAOF,MAAM;QAChC,MAAMG,UAAU,GAAG,OAAOF,MAAM;QAChC,IAAIC,UAAU,KAAKC,UAAU,EAAE;UAC3B,IAAID,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;UACA,IAAIG,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;QACJ;QACA;QACA;QACA;QACA;QACA,IAAIG,gBAAgB,GAAG,CAAC;QACxB,IAAIJ,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClC;UACA,IAAID,MAAM,GAAGC,MAAM,EAAE;YACjBG,gBAAgB,GAAG,CAAC;UACxB,CAAC,MACI,IAAIJ,MAAM,GAAGC,MAAM,EAAE;YACtBG,gBAAgB,GAAG,CAAC,CAAC;UACzB;QACJ,CAAC,MACI,IAAIJ,MAAM,IAAI,IAAI,EAAE;UACrBI,gBAAgB,GAAG,CAAC;QACxB,CAAC,MACI,IAAIH,MAAM,IAAI,IAAI,EAAE;UACrBG,gBAAgB,GAAG,CAAC,CAAC;QACzB;QACA,OAAOA,gBAAgB,IAAIP,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACQ,eAAe,GAAG,CAAChC,IAAI,EAAEQ,MAAM,KAAK;MACrC;MACA,MAAMyB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACnC,IAAI,CAAC,CAC5BoC,MAAM,CAAC,CAACC,WAAW,EAAEC,GAAG,KAAK;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA,OAAOD,WAAW,GAAGrC,IAAI,CAACsC,GAAG,CAAC,GAAG,GAAG;MACxC,CAAC,EAAE,EAAE,CAAC,CACDC,WAAW,EAAE;MAClB;MACA,MAAMC,iBAAiB,GAAGhC,MAAM,CAACiC,IAAI,EAAE,CAACF,WAAW,EAAE;MACrD,OAAON,OAAO,CAACS,OAAO,CAACF,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IACD,IAAI,CAACvC,KAAK,GAAG,IAAI/E,eAAe,CAAC6F,WAAW,CAAC;IAC7C,IAAI,CAACH,yBAAyB,EAAE;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIA,yBAAyB,GAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM+B,UAAU,GAAG,IAAI,CAAChC,KAAK,GACvBvF,KAAK,CAAC,IAAI,CAACuF,KAAK,CAACgC,UAAU,EAAE,IAAI,CAAChC,KAAK,CAACiC,WAAW,CAAC,GACpDvH,EAAE,CAAC,IAAI,CAAC;IACd,MAAMwH,UAAU,GAAG,IAAI,CAAC/B,UAAU,GAC5B1F,KAAK,CAAC,IAAI,CAAC0F,UAAU,CAACgC,IAAI,EAAE,IAAI,CAAC7B,oBAAoB,EAAE,IAAI,CAACH,UAAU,CAAC8B,WAAW,CAAC,GACnFvH,EAAE,CAAC,IAAI,CAAC;IACd,MAAM0H,UAAU,GAAG,IAAI,CAAC9C,KAAK;IAC7B;IACA,MAAM+C,YAAY,GAAG1H,aAAa,CAAC,CAACyH,UAAU,EAAE,IAAI,CAACtC,OAAO,CAAC,CAAC,CAACwC,IAAI,CAACzH,GAAG,CAAC,CAAC,CAACwE,IAAI,CAAC,KAAK,IAAI,CAACO,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5G;IACA,MAAMkD,WAAW,GAAG5H,aAAa,CAAC,CAAC0H,YAAY,EAAEL,UAAU,CAAC,CAAC,CAACM,IAAI,CAACzH,GAAG,CAAC,CAAC,CAACwE,IAAI,CAAC,KAAK,IAAI,CAACmD,UAAU,CAACnD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,MAAMoD,aAAa,GAAG9H,aAAa,CAAC,CAAC4H,WAAW,EAAEL,UAAU,CAAC,CAAC,CAACI,IAAI,CAACzH,GAAG,CAAC,CAAC,CAACwE,IAAI,CAAC,KAAK,IAAI,CAACqD,SAAS,CAACrD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,IAAI,CAACM,0BAA0B,EAAEgD,WAAW,EAAE;IAC9C,IAAI,CAAChD,0BAA0B,GAAG8C,aAAa,CAACG,SAAS,CAACvD,IAAI,IAAI,IAAI,CAACgB,WAAW,CAACX,IAAI,CAACL,IAAI,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAW,CAACP,IAAI,EAAE;IACd;IACA;IACA;IACA,IAAI,CAACgD,YAAY,GACb,IAAI,CAACxC,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,EAAE,GACnCR,IAAI,GACJA,IAAI,CAACQ,MAAM,CAACgD,GAAG,IAAI,IAAI,CAACxB,eAAe,CAACwB,GAAG,EAAE,IAAI,CAAChD,MAAM,CAAC,CAAC;IACpE,IAAI,IAAI,CAACK,SAAS,EAAE;MAChB,IAAI,CAAC4C,gBAAgB,CAAC,IAAI,CAACT,YAAY,CAACU,MAAM,CAAC;IACnD;IACA,OAAO,IAAI,CAACV,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIG,UAAU,CAACnD,IAAI,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE;MACZ,OAAOV,IAAI;IACf;IACA,OAAO,IAAI,CAACsB,QAAQ,CAACtB,IAAI,CAAC2D,KAAK,EAAE,EAAE,IAAI,CAACjD,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACI2C,SAAS,CAACrD,IAAI,EAAE;IACZ,IAAI,CAAC,IAAI,CAACa,SAAS,EAAE;MACjB,OAAOb,IAAI;IACf;IACA,MAAM4D,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACgD,SAAS,GAAG,IAAI,CAAChD,SAAS,CAACiD,QAAQ;IACrE,OAAO9D,IAAI,CAAC2D,KAAK,CAACC,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACiD,QAAQ,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIL,gBAAgB,CAACM,kBAAkB,EAAE;IACjCC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;MACzB,MAAMrD,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAI,CAACA,SAAS,EAAE;QACZ;MACJ;MACAA,SAAS,CAAC6C,MAAM,GAAGK,kBAAkB;MACrC;MACA,IAAIlD,SAAS,CAACgD,SAAS,GAAG,CAAC,EAAE;QACzB,MAAMM,aAAa,GAAGC,IAAI,CAACC,IAAI,CAACxD,SAAS,CAAC6C,MAAM,GAAG7C,SAAS,CAACiD,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;QAC/E,MAAMQ,YAAY,GAAGF,IAAI,CAACG,GAAG,CAAC1D,SAAS,CAACgD,SAAS,EAAEM,aAAa,CAAC;QACjE,IAAIG,YAAY,KAAKzD,SAAS,CAACgD,SAAS,EAAE;UACtChD,SAAS,CAACgD,SAAS,GAAGS,YAAY;UAClC;UACA;UACA,IAAI,CAACrD,oBAAoB,CAACZ,IAAI,EAAE;QACpC;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACImE,OAAO,GAAG;IACN,IAAI,CAAC,IAAI,CAAClE,0BAA0B,EAAE;MAClC,IAAI,CAACM,yBAAyB,EAAE;IACpC;IACA,OAAO,IAAI,CAACI,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIyD,UAAU,GAAG;IACT,IAAI,CAACnE,0BAA0B,EAAEgD,WAAW,EAAE;IAC9C,IAAI,CAAChD,0BAA0B,GAAG,IAAI;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,kBAAkB,SAAS3E,mBAAmB,CAAC;;AAGrD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASnB,OAAO,EAAEd,UAAU,EAAEG,YAAY,EAAEU,aAAa,EAAEX,gBAAgB,EAAEkB,YAAY,EAAEJ,eAAe,EAAEJ,aAAa,EAAEX,gBAAgB,EAAEiB,YAAY,EAAEH,eAAe,EAAEO,YAAY,EAAE3D,cAAc,EAAE0D,MAAM,EAAEJ,SAAS,EAAE7C,QAAQ,EAAEwI,kBAAkB,EAAElF,cAAc,EAAEF,aAAa,EAAES,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}