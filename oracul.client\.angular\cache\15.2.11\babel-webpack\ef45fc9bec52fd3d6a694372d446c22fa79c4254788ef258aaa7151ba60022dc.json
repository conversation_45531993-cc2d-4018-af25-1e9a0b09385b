{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../profile/services/mock-profile.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/i18n/translation.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"@angular/material/chips\";\nfunction HomeComponent_div_31_mat_card_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r7.name, \" \");\n  }\n}\nfunction HomeComponent_div_31_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_31_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const profile_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.viewProfile(profile_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h3\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"p\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 42)(17, \"div\", 43);\n    i0.ɵɵtemplate(18, HomeComponent_div_31_mat_card_1_span_18_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 45)(20, \"div\", 46)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 46)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"mat-card-actions\")(31, \"button\", 47)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 48)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const profile_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", profile_r5.profilePhotoUrl, i0.ɵɵsanitizeUrl)(\"alt\", profile_r5.firstName + \" \" + profile_r5.lastName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.firstName, \" \", profile_r5.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(profile_r5.professionalTitle);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(profile_r5.location == null ? null : profile_r5.location.displayLocation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(profile_r5.headline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", profile_r5.skills.slice(0, 3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.profileViews, \" \", ctx_r4.t.home.views, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getTotalEndorsements(profile_r5), \" \", ctx_r4.t.home.endorsements, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.home.viewProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.common.contact, \" \");\n  }\n}\nfunction HomeComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeComponent_div_31_mat_card_1_Template, 39, 14, \"mat-card\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProfiles);\n  }\n}\nfunction HomeComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.t.common.loading, \"...\");\n  }\n}\nfunction HomeComponent_mat_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_43_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const article_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.readArticle(article_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 53)(7, \"span\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"p\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-chip\", 58);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 47)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 59)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"bookmark_border\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const article_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r10.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r10.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"By \", article_r10.author, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, article_r10.publishedAt, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", article_r10.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r10.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nfunction HomeComponent_mat_card_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_54_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const sign_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.viewHoroscope(sign_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 64)(13, \"div\", 65)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"local_fire_department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"casino\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 65)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"mat-card-actions\")(29, \"button\", 47)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const sign_r13 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(sign_r13.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.dateRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(sign_r13.todayPrediction);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(sign_r13.element);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.t.home.luckyNumber, \": \", sign_r13.luckyNumber, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(sign_r13.luckyColor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.home.fullReading, \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(profileService, router, t) {\n    this.profileService = profileService;\n    this.router = router;\n    this.t = t;\n    this.featuredProfiles = [];\n    this.featuredArticles = [];\n    this.horoscopeSigns = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n  }\n  loadHomeData() {\n    this.isLoading = true;\n    // Load featured profiles - get the main profile\n    this.profileService.getProfile('luna-starweaver').subscribe(profile => {\n      this.featuredProfiles = [profile];\n      // Create additional mock profiles for variety\n      for (let i = 2; i <= 6; i++) {\n        const mockProfile = {\n          ...profile\n        };\n        mockProfile.id = i;\n        mockProfile.firstName = `Astrologer${i}`;\n        mockProfile.lastName = `Demo${i}`;\n        mockProfile.slug = `astrologer${i}-demo${i}`;\n        mockProfile.professionalTitle = this.getRandomTitle();\n        mockProfile.profilePhotoUrl = this.getRandomProfileImage();\n        this.featuredProfiles.push(mockProfile);\n      }\n    });\n    // Load featured articles\n    this.featuredArticles = this.getMockArticles();\n    // Load horoscope data\n    this.horoscopeSigns = this.getMockHoroscope();\n    this.isLoading = false;\n  }\n  getMockArticles() {\n    return [{\n      id: 1,\n      title: 'Разбиране на вашата натална карта: Ръководство за начинаещи',\n      excerpt: 'Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.',\n      author: 'Луна Звездоплетка',\n      publishedAt: new Date('2024-01-15'),\n      readTime: 8,\n      category: 'Основи на Астрологията',\n      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',\n      slug: 'understanding-birth-chart-beginners-guide'\n    }, {\n      id: 2,\n      title: 'Ръководство за оцеляване при Меркурий ретроград 2024',\n      excerpt: 'Навигирайте предизвикателствата на Меркурий ретроград с увереност. Научете практически съвети за превръщане на космическия хаос в възможности за растеж.',\n      author: 'Космически Мъдрец',\n      publishedAt: new Date('2024-02-01'),\n      readTime: 6,\n      category: 'Планетарни Транзити',\n      imageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop',\n      slug: 'mercury-retrograde-survival-guide-2024'\n    }, {\n      id: 3,\n      title: 'Ритуали на пълнолунието за манифестация',\n      excerpt: 'Използвайте мощната енергия на пълнолунието, за да манифестирате най-дълбоките си желания и да освободите това, което вече не ви служи.',\n      author: 'Лунна Мистичка',\n      publishedAt: new Date('2024-02-10'),\n      readTime: 10,\n      category: 'Лунна Магия',\n      imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop',\n      slug: 'full-moon-rituals-manifestation'\n    }, {\n      id: 4,\n      title: 'Кристално лечение: Избор на правилните камъни',\n      excerpt: 'Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.',\n      author: 'Пазител на Кристали',\n      publishedAt: new Date('2024-02-20'),\n      readTime: 7,\n      category: 'Кристално Лечение',\n      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop',\n      slug: 'crystal-healing-choosing-right-stones'\n    }];\n  }\n  getMockHoroscope() {\n    return [{\n      id: 1,\n      name: 'Овен',\n      symbol: '♈',\n      element: 'Огън',\n      dateRange: '21 март - 19 април',\n      todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\n      luckyNumber: 7,\n      luckyColor: 'Червен',\n      compatibility: ['Лъв', 'Стрелец', 'Близнаци']\n    }, {\n      id: 2,\n      name: 'Телец',\n      symbol: '♉',\n      element: 'Земя',\n      dateRange: '20 април - 20 май',\n      todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\n      luckyNumber: 3,\n      luckyColor: 'Зелен',\n      compatibility: ['Дева', 'Козирог', 'Рак']\n    }, {\n      id: 3,\n      name: 'Близнаци',\n      symbol: '♊',\n      element: 'Въздух',\n      dateRange: '21 май - 20 юни',\n      todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\n      luckyNumber: 5,\n      luckyColor: 'Жълт',\n      compatibility: ['Везни', 'Водолей', 'Овен']\n    }, {\n      id: 4,\n      name: 'Рак',\n      symbol: '♋',\n      element: 'Вода',\n      dateRange: '21 юни - 22 юли',\n      todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\n      luckyNumber: 2,\n      luckyColor: 'Сребърен',\n      compatibility: ['Скорпион', 'Риби', 'Телец']\n    }, {\n      id: 5,\n      name: 'Leo',\n      symbol: '♌',\n      element: 'Fire',\n      dateRange: 'Jul 23 - Aug 22',\n      todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\n      luckyNumber: 1,\n      luckyColor: 'Gold',\n      compatibility: ['Aries', 'Sagittarius', 'Gemini']\n    }, {\n      id: 6,\n      name: 'Virgo',\n      symbol: '♍',\n      element: 'Earth',\n      dateRange: 'Aug 23 - Sep 22',\n      todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\n      luckyNumber: 6,\n      luckyColor: 'Navy Blue',\n      compatibility: ['Taurus', 'Capricorn', 'Cancer']\n    }];\n  }\n  navigateToLogin() {\n    this.router.navigate(['/login']);\n  }\n  navigateToRegister() {\n    this.router.navigate(['/register']);\n  }\n  viewProfile(profile) {\n    this.router.navigate(['/profile', profile.slug]);\n  }\n  readArticle(article) {\n    // For now, just navigate to login to read full article\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: `/articles/${article.slug}`\n      }\n    });\n  }\n  viewHoroscope(sign) {\n    // For now, just navigate to login to view detailed horoscope\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: `/horoscope/${sign.name.toLowerCase()}`\n      }\n    });\n  }\n  searchProfiles() {\n    this.router.navigate(['/profiles/search']);\n  }\n  getTotalEndorsements(profile) {\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\n  }\n  getRandomTitle() {\n    const titles = ['Professional Astrologer & Spiritual Guide', 'Vedic Astrology Expert', 'Tarot Reader & Cosmic Counselor', 'Crystal Healer & Moon Mystic', 'Numerology & Astrology Specialist', 'Intuitive Astrologer & Life Coach'];\n    return titles[Math.floor(Math.random() * titles.length)];\n  }\n  getRandomProfileImage() {\n    const images = ['https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face', 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'];\n    return images[Math.floor(Math.random() * images.length)];\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.MockProfileService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 70,\n      vars: 19,\n      consts: [[1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"cta-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"secondary-button\", 3, \"click\"], [1, \"hero-image\"], [\"src\", \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\", \"alt\", \"Mystical cosmic background\", 1, \"hero-img\"], [1, \"featured-section\"], [1, \"section-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"view-all-btn\", 3, \"click\"], [\"class\", \"profiles-grid\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"horoscope-section\"], [1, \"horoscope-grid\"], [\"class\", \"horoscope-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"cta-section\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-subtitle\"], [1, \"cta-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"cta-primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"cta-secondary\", 3, \"click\"], [1, \"profiles-grid\"], [\"class\", \"profile-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-card\", 3, \"click\"], [1, \"profile-header\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-title\"], [1, \"profile-location\"], [1, \"profile-content\"], [1, \"profile-headline\"], [1, \"profile-skills\"], [1, \"skills-container\"], [\"class\", \"skill-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-stats\"], [1, \"stat\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [1, \"skill-chip\"], [1, \"loading-container\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"date\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-icon-button\", \"\"], [1, \"horoscope-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"sign-avatar\"], [1, \"sign-symbol\"], [1, \"prediction\"], [1, \"sign-details\"], [1, \"detail\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_12_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵelement(17, \"img\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"section\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"h2\", 13)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 14);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_27_listener() {\n            return ctx.searchProfiles();\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, HomeComponent_div_31_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(32, HomeComponent_div_32_Template, 4, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"section\", 18)(34, \"div\", 11)(35, \"div\", 12)(36, \"h2\", 13)(37, \"mat-icon\");\n          i0.ɵɵtext(38, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 14);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵtemplate(43, HomeComponent_mat_card_43_Template, 30, 13, \"mat-card\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"section\", 21)(45, \"div\", 11)(46, \"div\", 12)(47, \"h2\", 13)(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"brightness_7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\", 14);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 22);\n          i0.ɵɵtemplate(54, HomeComponent_mat_card_54_Template, 33, 9, \"mat-card\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"section\", 24)(56, \"div\", 25)(57, \"h2\", 26);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\", 27);\n          i0.ɵɵtext(60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_62_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_66_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.heroTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.heroSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.startJourney, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.common.login, \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.featuredAstrologers, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.featuredAstrologersSubtitle);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.browseAllAstrologers, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.cosmicWisdomArticles, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.cosmicWisdomSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredArticles);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.dailyHoroscope, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.dailyHoroscopeSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.horoscopeSigns);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.ctaTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.ctaSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.createFreeAccount, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.alreadyMember, \" \");\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardActions, i6.MatCardAvatar, i6.MatCardContent, i6.MatCardHeader, i6.MatCardImage, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatIcon, i8.MatProgressSpinner, i9.MatChip, i4.DatePipe],\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  padding: 80px 20px;\\r\\n  min-height: 500px;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-content[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  width: 100%;\\r\\n  display: grid;\\r\\n  grid-template-columns: 1fr 1fr;\\r\\n  gap: 60px;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.hero-text[_ngcontent-%COMP%] {\\r\\n  max-width: 500px;\\r\\n}\\r\\n\\r\\n.hero-title[_ngcontent-%COMP%] {\\r\\n  font-size: 3.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n\\r\\n.hero-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.hero-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-button[_ngcontent-%COMP%], .secondary-button[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.hero-image[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-img[_ngcontent-%COMP%] {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  border-radius: 16px;\\r\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\r\\n}\\r\\n\\r\\n\\r\\n.featured-section[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%], .horoscope-section[_ngcontent-%COMP%] {\\r\\n  padding: 80px 20px;\\r\\n  background: var(--theme-background);\\r\\n}\\r\\n\\r\\n.section-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.section-header[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  margin-bottom: 60px;\\r\\n}\\r\\n\\r\\n.section-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.section-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin-bottom: 30px;\\r\\n}\\r\\n\\r\\n.view-all-btn[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n.profiles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\r\\n  gap: 30px;\\r\\n  margin-bottom: 40px;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-8px);\\r\\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.profile-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.profile-avatar[_ngcontent-%COMP%] {\\r\\n  width: 60px;\\r\\n  height: 60px;\\r\\n  border-radius: 50%;\\r\\n  object-fit: cover;\\r\\n  border: 3px solid var(--theme-accent);\\r\\n}\\r\\n\\r\\n.profile-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.profile-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.3rem;\\r\\n  font-weight: 600;\\r\\n  margin: 0 0 4px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.profile-title[_ngcontent-%COMP%] {\\r\\n  font-size: 1rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin: 0 0 8px 0;\\r\\n}\\r\\n\\r\\n.profile-location[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.profile-content[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.profile-headline[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-skills[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.skills-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.skill-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-accent);\\r\\n  color: var(--theme-text-primary);\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 0.85rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.profile-stats[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n}\\r\\n\\r\\n\\r\\n.articles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\r\\n  gap: 30px;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n}\\r\\n\\r\\n.article-image[_ngcontent-%COMP%] {\\r\\n  height: 200px;\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.article-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n  font-size: 0.85rem;\\r\\n}\\r\\n\\r\\n.author[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.date[_ngcontent-%COMP%], .read-time[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.6;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.category-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-primary);\\r\\n  color: white;\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n.horoscope-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  border: 2px solid var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n  border-color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n.sign-avatar[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  width: 50px;\\r\\n  height: 50px;\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.sign-symbol[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: bold;\\r\\n}\\r\\n\\r\\n.prediction[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.sign-details[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.85rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n.cta-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-secondary);\\r\\n  color: white;\\r\\n  padding: 80px 20px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.cta-content[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.cta-title[_ngcontent-%COMP%] {\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.cta-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.cta-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-primary[_ngcontent-%COMP%], .cta-secondary[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 20px;\\r\\n  padding: 60px 20px;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .hero-content[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n    gap: 40px;\\r\\n    text-align: center;\\r\\n  }\\r\\n  \\r\\n  .hero-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2.5rem;\\r\\n  }\\r\\n  \\r\\n  .profiles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n  \\r\\n  .articles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n  \\r\\n  .horoscope-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\r\\n  }\\r\\n  \\r\\n  .section-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n  \\r\\n  .cta-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n  \\r\\n  .hero-actions[_ngcontent-%COMP%], .cta-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;IA4DcA,gCAA0E;IACxEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,8CACF;;;;;;IApBRA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAoB;IAAA,EAAC;IACpGA,+BAA4B;IAC1BA,0BAA+G;IAC/GA,+BAA0B;IACCA,YAA8C;IAAAA,iBAAK;IAC5EA,6BAAyB;IAAAA,YAA+B;IAAAA,iBAAI;IAC5DA,+BAA8B;IAClBA,4BAAW;IAAAA,iBAAW;IAChCA,6BAAM;IAAAA,aAAuC;IAAAA,iBAAO;IAK1DA,gCAA6B;IACCA,aAAsB;IAAAA,iBAAI;IAEtDA,gCAA4B;IAExBA,qFAEO;IACTA,iBAAM;IAGRA,gCAA2B;IAEbA,2BAAU;IAAAA,iBAAW;IAC/BA,6BAAM;IAAAA,aAA6C;IAAAA,iBAAO;IAE5DA,gCAAkB;IACNA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,aAA6D;IAAAA,iBAAO;IAKhFA,yCAAkB;IAEJA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;IACTA,mCAAkC;IACtBA,wBAAO;IAAAA,iBAAW;IAC5BA,aACF;IAAAA,iBAAS;;;;;IA1CJA,eAA+B;IAA/BA,kEAA+B;IAETA,eAA8C;IAA9CA,6EAA8C;IAC9CA,eAA+B;IAA/BA,kDAA+B;IAGhDA,eAAuC;IAAvCA,8FAAuC;IAMrBA,eAAsB;IAAtBA,yCAAsB;IAItBA,eAA6B;IAA7BA,uDAA6B;IAS/CA,eAA6C;IAA7CA,gFAA6C;IAI7CA,eAA6D;IAA7DA,uGAA6D;IAQrEA,eACF;IADEA,0DACF;IAGEA,eACF;IADEA,wDACF;;;;;IA7CNA,+BAA8C;IAC5CA,kFA8CW;IACbA,iBAAM;;;;IA/C+CA,eAAmB;IAAnBA,iDAAmB;;;;;IAkDxEA,+BAAiD;IAC/CA,8BAA2B;IAC3BA,yBAAG;IAAAA,YAAyB;IAAAA,iBAAI;;;;IAA7BA,eAAyB;IAAzBA,yDAAyB;;;;;;IAiB5BA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACpGA,0BAAyF;IAEzFA,uCAAiB;IACCA,YAAmB;IAAAA,iBAAiB;IACpDA,yCAAmB;IAEMA,YAAuB;IAAAA,iBAAO;IACnDA,gCAAmB;IAAAA,aAA2C;;IAAAA,iBAAO;IACrEA,iCAAwB;IAAAA,aAA2C;IAAAA,iBAAO;IAKhFA,yCAAkB;IACWA,aAAqB;IAAAA,iBAAI;IACpDA,qCAAgC;IAAAA,aAAsB;IAAAA,iBAAW;IAGnEA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;IACTA,mCAAwB;IACZA,sBAAK;IAAAA,iBAAW;IAE5BA,mCAAwB;IACZA,gCAAe;IAAAA,iBAAW;;;;;IA3BpBA,eAAwB;IAAxBA,4DAAwB;IAG1BA,eAAmB;IAAnBA,uCAAmB;IAGVA,eAAuB;IAAvBA,oDAAuB;IACzBA,eAA2C;IAA3CA,iFAA2C;IACtCA,eAA2C;IAA3CA,+EAA2C;IAM5CA,eAAqB;IAArBA,yCAAqB;IAChBA,eAAsB;IAAtBA,0CAAsB;IAMpDA,eACF;IADEA,0DACF;;;;;;IAyBJA,oCAAmG;IAA9BA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChGA,uCAAiB;IAEaA,YAAiB;IAAAA,iBAAO;IAEpDA,sCAAgB;IAAAA,YAAe;IAAAA,iBAAiB;IAChDA,yCAAmB;IAAAA,YAAoB;IAAAA,iBAAoB;IAG7DA,wCAAkB;IACMA,aAA0B;IAAAA,iBAAI;IAEpDA,gCAA0B;IAEZA,sCAAqB;IAAAA,iBAAW;IAC1CA,6BAAM;IAAAA,aAAkB;IAAAA,iBAAO;IAEjCA,gCAAoB;IACRA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAAgD;IAAAA,iBAAO;IAE/DA,gCAAoB;IACRA,wBAAO;IAAAA,iBAAW;IAC5BA,6BAAM;IAAAA,aAAqB;IAAAA,iBAAO;IAKxCA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;;;;;IA7BmBA,eAAiB;IAAjBA,qCAAiB;IAE7BA,eAAe;IAAfA,mCAAe;IACZA,eAAoB;IAApBA,wCAAoB;IAIjBA,eAA0B;IAA1BA,8CAA0B;IAKtCA,eAAkB;IAAlBA,sCAAkB;IAIlBA,eAAgD;IAAhDA,oFAAgD;IAIhDA,eAAqB;IAArBA,yCAAqB;IAQ7BA,eACF;IADEA,0DACF;;;AC3JV,OAAM,MAAOC,aAAa;EAMxBC,YACUC,cAAkC,EAClCC,MAAc,EACfC,CAAqB;IAFpB,mBAAc,GAAdF,cAAc;IACd,WAAM,GAANC,MAAM;IACP,MAAC,GAADC,CAAC;IARV,qBAAgB,GAAkB,EAAE;IACpC,qBAAgB,GAAc,EAAE;IAChC,mBAAc,GAAoB,EAAE;IACpC,cAAS,GAAG,IAAI;EAMb;EAEHC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAY;IAClB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACL,cAAc,CAACM,UAAU,CAAC,iBAAiB,CAAC,CAACC,SAAS,CAACC,OAAO,IAAG;MACpE,IAAI,CAACC,gBAAgB,GAAG,CAACD,OAAO,CAAC;MAEjC;MACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMC,WAAW,GAAG;UAAE,GAAGH;QAAO,CAAE;QAClCG,WAAW,CAACC,EAAE,GAAGF,CAAC;QAClBC,WAAW,CAACE,SAAS,GAAG,aAAaH,CAAC,EAAE;QACxCC,WAAW,CAACG,QAAQ,GAAG,OAAOJ,CAAC,EAAE;QACjCC,WAAW,CAACI,IAAI,GAAG,aAAaL,CAAC,QAAQA,CAAC,EAAE;QAC5CC,WAAW,CAACK,iBAAiB,GAAG,IAAI,CAACC,cAAc,EAAE;QACrDN,WAAW,CAACO,eAAe,GAAG,IAAI,CAACC,qBAAqB,EAAE;QAC1D,IAAI,CAACV,gBAAgB,CAACW,IAAI,CAACT,WAAW,CAAC;;IAE3C,CAAC,CAAC;IAEF;IACA,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACC,eAAe,EAAE;IAE9C;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAE7C,IAAI,CAACnB,SAAS,GAAG,KAAK;EACxB;EAEQiB,eAAe;IACrB,OAAO,CACL;MACEV,EAAE,EAAE,CAAC;MACLa,KAAK,EAAE,6DAA6D;MACpEC,OAAO,EAAE,8IAA8I;MACvJC,MAAM,EAAE,mBAAmB;MAC3BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE,mFAAmF;MAC7FjB,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,CAAC;MACLa,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,0JAA0J;MACnKC,MAAM,EAAE,mBAAmB;MAC3BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE,mFAAmF;MAC7FjB,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,CAAC;MACLa,KAAK,EAAE,yCAAyC;MAChDC,OAAO,EAAE,yIAAyI;MAClJC,MAAM,EAAE,gBAAgB;MACxBC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE,mFAAmF;MAC7FjB,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,CAAC;MACLa,KAAK,EAAE,+CAA+C;MACtDC,OAAO,EAAE,8HAA8H;MACvIC,MAAM,EAAE,qBAAqB;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,mFAAmF;MAC7FjB,IAAI,EAAE;KACP,CACF;EACH;EAEQS,gBAAgB;IACtB,OAAO,CACL;MACEZ,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,oBAAoB;MAC/BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU;KAC7C,EACD;MACE5B,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,mBAAmB;MAC9BC,eAAe,EAAE,qHAAqH;MACtIC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,OAAO;MACnBC,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK;KACzC,EACD;MACE5B,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,+FAA+F;MAChHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM;KAC3C,EACD;MACE5B,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,4HAA4H;MAC7IC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,UAAU;MACtBC,aAAa,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO;KAC5C,EACD;MACE5B,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,yGAAyG;MAC1HC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ;KACjD,EACD;MACE5B,EAAE,EAAE,CAAC;MACLqB,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,WAAW;MACvBC,aAAa,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ;KAChD,CACF;EACH;EAEAC,eAAe;IACb,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,kBAAkB;IAChB,IAAI,CAAC1C,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAE,WAAW,CAACpC,OAAoB;IAC9B,IAAI,CAACP,MAAM,CAACyC,QAAQ,CAAC,CAAC,UAAU,EAAElC,OAAO,CAACO,IAAI,CAAC,CAAC;EAClD;EAEA8B,WAAW,CAACC,OAAgB;IAC1B;IACA,IAAI,CAAC7C,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BK,WAAW,EAAE;QAAEC,SAAS,EAAE,aAAaF,OAAO,CAAC/B,IAAI;MAAE;KACtD,CAAC;EACJ;EAEAkC,aAAa,CAACC,IAAmB;IAC/B;IACA,IAAI,CAACjD,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BK,WAAW,EAAE;QAAEC,SAAS,EAAE,cAAcE,IAAI,CAACjB,IAAI,CAACkB,WAAW,EAAE;MAAE;KAClE,CAAC;EACJ;EAEAC,cAAc;IACZ,IAAI,CAACnD,MAAM,CAACyC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAW,oBAAoB,CAAC7C,OAAoB;IACvC,OAAOA,OAAO,CAAC8C,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,YAAY,EAAE,CAAC,CAAC;EAC3E;EAEQzC,cAAc;IACpB,MAAM0C,MAAM,GAAG,CACb,2CAA2C,EAC3C,wBAAwB,EACxB,iCAAiC,EACjC,8BAA8B,EAC9B,mCAAmC,EACnC,mCAAmC,CACpC;IACD,OAAOA,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGH,MAAM,CAACI,MAAM,CAAC,CAAC;EAC1D;EAEQ5C,qBAAqB;IAC3B,MAAM6C,MAAM,GAAG,CACb,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,EAC7F,6FAA6F,EAC7F,0FAA0F,CAC3F;IACD,OAAOA,MAAM,CAACJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGE,MAAM,CAACD,MAAM,CAAC,CAAC;EAC1D;;;uBA5NWjE,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAmE;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDnC1BxE,kCAA8B;UAGDA,YAAsB;UAAAA,iBAAK;UAClDA,4BAAyB;UACvBA,YACF;UAAAA,iBAAI;UACJA,8BAA0B;UACqCA;YAAA,OAASyE,wBAAoB;UAAA,EAAC;UACzFzE,gCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,kCAAgG;UAA5BA;YAAA,OAASyE,qBAAiB;UAAA,EAAC;UAC7FzE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;UAGbA,+BAAwB;UACtBA,0BACuD;UACzDA,iBAAM;UAKVA,oCAAkC;UAIhBA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAwC;UAAAA,iBAAI;UACxEA,mCAA2F;UAAhDA;YAAA,OAASyE,oBAAgB;UAAA,EAAC;UACnEzE,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UAGXA,iEAgDM;UAGNA,iEAGM;UACRA,iBAAM;UAIRA,oCAAkC;UAIhBA,wBAAO;UAAAA,iBAAW;UAC5BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAiC;UAAAA,iBAAI;UAGnEA,gCAA2B;UACzBA,6EA+BW;UACbA,iBAAM;UAKVA,oCAAmC;UAIjBA,6BAAY;UAAAA,iBAAW;UACjCA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAmC;UAAAA,iBAAI;UAGrEA,gCAA4B;UAC1BA,4EAkCW;UACbA,iBAAM;UAKVA,oCAA6B;UAEHA,aAAqB;UAAAA,iBAAK;UAChDA,8BAAwB;UACtBA,aACF;UAAAA,iBAAI;UACJA,gCAAyB;UACuCA;YAAA,OAASyE,wBAAoB;UAAA,EAAC;UAC1FzE,iCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,mCAA6F;UAA5BA;YAAA,OAASyE,qBAAiB;UAAA,EAAC;UAC1FzE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;;;UAjNcA,eAAsB;UAAtBA,0CAAsB;UAE3CA,eACF;UADEA,wDACF;UAIIA,eACF;UADEA,wDACF;UAGEA,eACF;UADEA,mDACF;UAgBAA,eACF;UADEA,+DACF;UAC4BA,eAAwC;UAAxCA,4DAAwC;UAGlEA,eACF;UADEA,gEACF;UAG0BA,eAAgB;UAAhBA,qCAAgB;UAmDZA,eAAe;UAAfA,oCAAe;UAa3CA,eACF;UADEA,gEACF;UAC4BA,eAAiC;UAAjCA,qDAAiC;UAIVA,eAAmB;UAAnBA,8CAAmB;UA0CpEA,eACF;UADEA,0DACF;UAC4BA,eAAmC;UAAnCA,uDAAmC;UAIbA,eAAiB;UAAjBA,4CAAiB;UA0C/CA,eAAqB;UAArBA,yCAAqB;UAEzCA,eACF;UADEA,uDACF;UAIIA,eACF;UADEA,6DACF;UAGEA,eACF;UADEA,yDACF", "names": ["i0", "HomeComponent", "constructor", "profileService", "router", "t", "ngOnInit", "loadHomeData", "isLoading", "getProfile", "subscribe", "profile", "featuredPro<PERSON>les", "i", "mockProfile", "id", "firstName", "lastName", "slug", "professional<PERSON>itle", "getRandomTitle", "profilePhotoUrl", "getRandomProfileImage", "push", "featuredArticles", "getMockArticles", "horoscopeSigns", "getMockHoroscope", "title", "excerpt", "author", "publishedAt", "Date", "readTime", "category", "imageUrl", "name", "symbol", "element", "date<PERSON><PERSON><PERSON>", "todayPrediction", "<PERSON><PERSON><PERSON><PERSON>", "luckyColor", "compatibility", "navigateToLogin", "navigate", "navigateToRegister", "viewProfile", "readArticle", "article", "queryParams", "returnUrl", "viewHoroscope", "sign", "toLowerCase", "searchProfiles", "getTotalEndorsements", "skills", "reduce", "sum", "skill", "endorsements", "titles", "Math", "floor", "random", "length", "images", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.ts"], "sourcesContent": ["<!-- Hero Section -->\r\n<section class=\"hero-section\">\r\n  <div class=\"hero-content\">\r\n    <div class=\"hero-text\">\r\n      <h1 class=\"hero-title\">{{ t.home.heroTitle }}</h1>\r\n      <p class=\"hero-subtitle\">\r\n        {{ t.home.heroSubtitle }}\r\n      </p>\r\n      <div class=\"hero-actions\">\r\n        <button mat-raised-button color=\"primary\" class=\"cta-button\" (click)=\"navigateToRegister()\">\r\n          <mat-icon>star</mat-icon>\r\n          {{ t.home.startJourney }}\r\n        </button>\r\n        <button mat-stroked-button color=\"primary\" class=\"secondary-button\" (click)=\"navigateToLogin()\">\r\n          <mat-icon>login</mat-icon>\r\n          {{ t.common.login }}\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"hero-image\">\r\n      <img src=\"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\"\r\n           alt=\"Mystical cosmic background\" class=\"hero-img\">\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Astrologers Section -->\r\n<section class=\"featured-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>people</mat-icon>\r\n        {{ t.home.featuredAstrologers }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.featuredAstrologersSubtitle }}</p>\r\n      <button mat-stroked-button color=\"primary\" (click)=\"searchProfiles()\" class=\"view-all-btn\">\r\n        <mat-icon>search</mat-icon>\r\n        {{ t.home.browseAllAstrologers }}\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"profiles-grid\" *ngIf=\"!isLoading\">\r\n      <mat-card class=\"profile-card\" *ngFor=\"let profile of featuredProfiles\" (click)=\"viewProfile(profile)\">\r\n        <div class=\"profile-header\">\r\n          <img [src]=\"profile.profilePhotoUrl\" [alt]=\"profile.firstName + ' ' + profile.lastName\" class=\"profile-avatar\">\r\n          <div class=\"profile-info\">\r\n            <h3 class=\"profile-name\">{{ profile.firstName }} {{ profile.lastName }}</h3>\r\n            <p class=\"profile-title\">{{ profile.professionalTitle }}</p>\r\n            <div class=\"profile-location\">\r\n              <mat-icon>location_on</mat-icon>\r\n              <span>{{ profile.location?.displayLocation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"profile-content\">\r\n          <p class=\"profile-headline\">{{ profile.headline }}</p>\r\n\r\n          <div class=\"profile-skills\">\r\n            <div class=\"skills-container\">\r\n              <span *ngFor=\"let skill of profile.skills.slice(0, 3)\" class=\"skill-chip\">\r\n                {{ skill.name }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"profile-stats\">\r\n            <div class=\"stat\">\r\n              <mat-icon>visibility</mat-icon>\r\n              <span>{{ profile.profileViews }} {{ t.home.views }}</span>\r\n            </div>\r\n            <div class=\"stat\">\r\n              <mat-icon>thumb_up</mat-icon>\r\n              <span>{{ getTotalEndorsements(profile) }} {{ t.home.endorsements }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>visibility</mat-icon>\r\n            {{ t.home.viewProfile }}\r\n          </button>\r\n          <button mat-button color=\"accent\">\r\n            <mat-icon>message</mat-icon>\r\n            {{ t.common.contact }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\r\n      <mat-spinner></mat-spinner>\r\n      <p>{{ t.common.loading }}...</p>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Articles Section -->\r\n<section class=\"articles-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>article</mat-icon>\r\n        {{ t.home.cosmicWisdomArticles }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.cosmicWisdomSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"articles-grid\">\r\n      <mat-card class=\"article-card\" *ngFor=\"let article of featuredArticles\" (click)=\"readArticle(article)\">\r\n        <img mat-card-image [src]=\"article.imageUrl\" [alt]=\"article.title\" class=\"article-image\">\r\n\r\n        <mat-card-header>\r\n          <mat-card-title>{{ article.title }}</mat-card-title>\r\n          <mat-card-subtitle>\r\n            <div class=\"article-meta\">\r\n              <span class=\"author\">By {{ article.author }}</span>\r\n              <span class=\"date\">{{ article.publishedAt | date:'MMM d, y' }}</span>\r\n              <span class=\"read-time\">{{ article.readTime }} {{ t.home.minRead }}</span>\r\n            </div>\r\n          </mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n          <mat-chip class=\"category-chip\">{{ article.category }}</mat-chip>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.readArticle }}\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>share</mat-icon>\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>bookmark_border</mat-icon>\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Daily Horoscope Section -->\r\n<section class=\"horoscope-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>brightness_7</mat-icon>\r\n        {{ t.home.dailyHoroscope }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.dailyHoroscopeSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"horoscope-grid\">\r\n      <mat-card class=\"horoscope-card\" *ngFor=\"let sign of horoscopeSigns\" (click)=\"viewHoroscope(sign)\">\r\n        <mat-card-header>\r\n          <div mat-card-avatar class=\"sign-avatar\">\r\n            <span class=\"sign-symbol\">{{ sign.symbol }}</span>\r\n          </div>\r\n          <mat-card-title>{{ sign.name }}</mat-card-title>\r\n          <mat-card-subtitle>{{ sign.dateRange }}</mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"prediction\">{{ sign.todayPrediction }}</p>\r\n\r\n          <div class=\"sign-details\">\r\n            <div class=\"detail\">\r\n              <mat-icon>local_fire_department</mat-icon>\r\n              <span>{{ sign.element }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>casino</mat-icon>\r\n              <span>{{ t.home.luckyNumber }}: {{ sign.luckyNumber }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>palette</mat-icon>\r\n              <span>{{ sign.luckyColor }}</span>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.fullReading }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Call to Action Section -->\r\n<section class=\"cta-section\">\r\n  <div class=\"cta-content\">\r\n    <h2 class=\"cta-title\">{{ t.home.ctaTitle }}</h2>\r\n    <p class=\"cta-subtitle\">\r\n      {{ t.home.ctaSubtitle }}\r\n    </p>\r\n    <div class=\"cta-actions\">\r\n      <button mat-raised-button color=\"primary\" class=\"cta-primary\" (click)=\"navigateToRegister()\">\r\n        <mat-icon>star</mat-icon>\r\n        {{ t.home.createFreeAccount }}\r\n      </button>\r\n      <button mat-stroked-button color=\"primary\" class=\"cta-secondary\" (click)=\"navigateToLogin()\">\r\n        <mat-icon>login</mat-icon>\r\n        {{ t.home.alreadyMember }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</section>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { UserProfile } from '../profile/models/profile.models';\r\nimport { MockProfileService } from '../profile/services/mock-profile.service';\r\nimport { TranslationService } from '../core/i18n/translation.service';\r\n\r\nexport interface Article {\r\n  id: number;\r\n  title: string;\r\n  excerpt: string;\r\n  author: string;\r\n  publishedAt: Date;\r\n  readTime: number;\r\n  category: string;\r\n  imageUrl: string;\r\n  slug: string;\r\n}\r\n\r\nexport interface HoroscopeSign {\r\n  id: number;\r\n  name: string;\r\n  symbol: string;\r\n  element: string;\r\n  dateRange: string;\r\n  todayPrediction: string;\r\n  luckyNumber: number;\r\n  luckyColor: string;\r\n  compatibility: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.css']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  featuredProfiles: UserProfile[] = [];\r\n  featuredArticles: Article[] = [];\r\n  horoscopeSigns: HoroscopeSign[] = [];\r\n  isLoading = true;\r\n\r\n  constructor(\r\n    private profileService: MockProfileService,\r\n    private router: Router,\r\n    public t: TranslationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadHomeData();\r\n  }\r\n\r\n  private loadHomeData(): void {\r\n    this.isLoading = true;\r\n\r\n    // Load featured profiles - get the main profile\r\n    this.profileService.getProfile('luna-starweaver').subscribe(profile => {\r\n      this.featuredProfiles = [profile];\r\n\r\n      // Create additional mock profiles for variety\r\n      for (let i = 2; i <= 6; i++) {\r\n        const mockProfile = { ...profile };\r\n        mockProfile.id = i;\r\n        mockProfile.firstName = `Astrologer${i}`;\r\n        mockProfile.lastName = `Demo${i}`;\r\n        mockProfile.slug = `astrologer${i}-demo${i}`;\r\n        mockProfile.professionalTitle = this.getRandomTitle();\r\n        mockProfile.profilePhotoUrl = this.getRandomProfileImage();\r\n        this.featuredProfiles.push(mockProfile);\r\n      }\r\n    });\r\n\r\n    // Load featured articles\r\n    this.featuredArticles = this.getMockArticles();\r\n\r\n    // Load horoscope data\r\n    this.horoscopeSigns = this.getMockHoroscope();\r\n\r\n    this.isLoading = false;\r\n  }\r\n\r\n  private getMockArticles(): Article[] {\r\n    return [\r\n      {\r\n        id: 1,\r\n        title: 'Разбиране на вашата натална карта: Ръководство за начинаещи',\r\n        excerpt: 'Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.',\r\n        author: 'Луна Звездоплетка',\r\n        publishedAt: new Date('2024-01-15'),\r\n        readTime: 8,\r\n        category: 'Основи на Астрологията',\r\n        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',\r\n        slug: 'understanding-birth-chart-beginners-guide'\r\n      },\r\n      {\r\n        id: 2,\r\n        title: 'Ръководство за оцеляване при Меркурий ретроград 2024',\r\n        excerpt: 'Навигирайте предизвикателствата на Меркурий ретроград с увереност. Научете практически съвети за превръщане на космическия хаос в възможности за растеж.',\r\n        author: 'Космически Мъдрец',\r\n        publishedAt: new Date('2024-02-01'),\r\n        readTime: 6,\r\n        category: 'Планетарни Транзити',\r\n        imageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop',\r\n        slug: 'mercury-retrograde-survival-guide-2024'\r\n      },\r\n      {\r\n        id: 3,\r\n        title: 'Ритуали на пълнолунието за манифестация',\r\n        excerpt: 'Използвайте мощната енергия на пълнолунието, за да манифестирате най-дълбоките си желания и да освободите това, което вече не ви служи.',\r\n        author: 'Лунна Мистичка',\r\n        publishedAt: new Date('2024-02-10'),\r\n        readTime: 10,\r\n        category: 'Лунна Магия',\r\n        imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop',\r\n        slug: 'full-moon-rituals-manifestation'\r\n      },\r\n      {\r\n        id: 4,\r\n        title: 'Кристално лечение: Избор на правилните камъни',\r\n        excerpt: 'Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.',\r\n        author: 'Пазител на Кристали',\r\n        publishedAt: new Date('2024-02-20'),\r\n        readTime: 7,\r\n        category: 'Кристално Лечение',\r\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop',\r\n        slug: 'crystal-healing-choosing-right-stones'\r\n      }\r\n    ];\r\n  }\r\n\r\n  private getMockHoroscope(): HoroscopeSign[] {\r\n    return [\r\n      {\r\n        id: 1,\r\n        name: 'Овен',\r\n        symbol: '♈',\r\n        element: 'Огън',\r\n        dateRange: '21 март - 19 април',\r\n        todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\r\n        luckyNumber: 7,\r\n        luckyColor: 'Червен',\r\n        compatibility: ['Лъв', 'Стрелец', 'Близнаци']\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Телец',\r\n        symbol: '♉',\r\n        element: 'Земя',\r\n        dateRange: '20 април - 20 май',\r\n        todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\r\n        luckyNumber: 3,\r\n        luckyColor: 'Зелен',\r\n        compatibility: ['Дева', 'Козирог', 'Рак']\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Близнаци',\r\n        symbol: '♊',\r\n        element: 'Въздух',\r\n        dateRange: '21 май - 20 юни',\r\n        todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\r\n        luckyNumber: 5,\r\n        luckyColor: 'Жълт',\r\n        compatibility: ['Везни', 'Водолей', 'Овен']\r\n      },\r\n      {\r\n        id: 4,\r\n        name: 'Рак',\r\n        symbol: '♋',\r\n        element: 'Вода',\r\n        dateRange: '21 юни - 22 юли',\r\n        todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\r\n        luckyNumber: 2,\r\n        luckyColor: 'Сребърен',\r\n        compatibility: ['Скорпион', 'Риби', 'Телец']\r\n      },\r\n      {\r\n        id: 5,\r\n        name: 'Leo',\r\n        symbol: '♌',\r\n        element: 'Fire',\r\n        dateRange: 'Jul 23 - Aug 22',\r\n        todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\r\n        luckyNumber: 1,\r\n        luckyColor: 'Gold',\r\n        compatibility: ['Aries', 'Sagittarius', 'Gemini']\r\n      },\r\n      {\r\n        id: 6,\r\n        name: 'Virgo',\r\n        symbol: '♍',\r\n        element: 'Earth',\r\n        dateRange: 'Aug 23 - Sep 22',\r\n        todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\r\n        luckyNumber: 6,\r\n        luckyColor: 'Navy Blue',\r\n        compatibility: ['Taurus', 'Capricorn', 'Cancer']\r\n      }\r\n    ];\r\n  }\r\n\r\n  navigateToLogin(): void {\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    this.router.navigate(['/register']);\r\n  }\r\n\r\n  viewProfile(profile: UserProfile): void {\r\n    this.router.navigate(['/profile', profile.slug]);\r\n  }\r\n\r\n  readArticle(article: Article): void {\r\n    // For now, just navigate to login to read full article\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: `/articles/${article.slug}` }\r\n    });\r\n  }\r\n\r\n  viewHoroscope(sign: HoroscopeSign): void {\r\n    // For now, just navigate to login to view detailed horoscope\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: `/horoscope/${sign.name.toLowerCase()}` }\r\n    });\r\n  }\r\n\r\n  searchProfiles(): void {\r\n    this.router.navigate(['/profiles/search']);\r\n  }\r\n\r\n  getTotalEndorsements(profile: UserProfile): number {\r\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\r\n  }\r\n\r\n  private getRandomTitle(): string {\r\n    const titles = [\r\n      'Professional Astrologer & Spiritual Guide',\r\n      'Vedic Astrology Expert',\r\n      'Tarot Reader & Cosmic Counselor',\r\n      'Crystal Healer & Moon Mystic',\r\n      'Numerology & Astrology Specialist',\r\n      'Intuitive Astrologer & Life Coach'\r\n    ];\r\n    return titles[Math.floor(Math.random() * titles.length)];\r\n  }\r\n\r\n  private getRandomProfileImage(): string {\r\n    const images = [\r\n      'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=150&h=150&fit=crop&crop=face',\r\n      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\r\n      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\r\n      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\r\n      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'\r\n    ];\r\n    return images[Math.floor(Math.random() * images.length)];\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}