# Profile API Testing Script
# This script demonstrates the complete profile backend functionality

Write-Host "=== Profile Backend API Testing ===" -ForegroundColor Green
Write-Host ""

$baseUrl = "http://localhost:5144/api"

# Test 1: Seed the database with astrology profile data
Write-Host "1. Seeding database with astrology profile data..." -ForegroundColor Yellow
try {
    $seedResponse = Invoke-WebRequest -Uri "$baseUrl/seed/profiles" -Method POST -ContentType "application/json"
    $seedData = $seedResponse.Content | ConvertFrom-Json
    Write-Host "✓ Seed Status: $($seedData.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ Seed failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 2: Get profile by slug
Write-Host "2. Getting profile by slug (luna-starweaver)..." -ForegroundColor Yellow
try {
    $profileResponse = Invoke-WebRequest -Uri "$baseUrl/profile/slug/luna-starweaver" -Method GET
    $profileData = $profileResponse.Content | ConvertFrom-Json
    Write-Host "✓ Profile loaded: $($profileData.data.firstName) $($profileData.data.lastName)" -ForegroundColor Green
    Write-Host "  - Professional Title: $($profileData.data.professionalTitle)" -ForegroundColor Cyan
    Write-Host "  - Profile Views: $($profileData.data.profileViews)" -ForegroundColor Cyan
    Write-Host "  - Skills Count: $($profileData.data.skills.Count)" -ForegroundColor Cyan
    Write-Host "  - Blog Posts: $($profileData.data.blogPosts.Count)" -ForegroundColor Cyan
    Write-Host "  - Certifications: $($profileData.data.certifications.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Profile retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 3: Get public profiles
Write-Host "3. Getting public profiles..." -ForegroundColor Yellow
try {
    $publicResponse = Invoke-WebRequest -Uri "$baseUrl/profile/public?page=1&pageSize=10" -Method GET
    $publicData = $publicResponse.Content | ConvertFrom-Json
    Write-Host "✓ Public profiles found: $($publicData.data.profiles.Count)" -ForegroundColor Green
    Write-Host "  - Total count: $($publicData.data.totalCount)" -ForegroundColor Cyan
    Write-Host "  - Current page: $($publicData.data.currentPage)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Public profiles retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 4: Search profiles
Write-Host "4. Searching profiles for 'astrology'..." -ForegroundColor Yellow
try {
    $searchBody = @{
        searchTerm = "astrology"
        page = 1
        pageSize = 20
    } | ConvertTo-Json

    $searchResponse = Invoke-WebRequest -Uri "$baseUrl/profile/search" -Method POST -Body $searchBody -ContentType "application/json"
    $searchData = $searchResponse.Content | ConvertFrom-Json
    Write-Host "✓ Search completed: $($searchData.data.profiles.Count) profiles found" -ForegroundColor Green
    
    if ($searchData.data.profiles.Count -gt 0) {
        $firstProfile = $searchData.data.profiles[0]
        Write-Host "  - First result: $($firstProfile.firstName) $($firstProfile.lastName)" -ForegroundColor Cyan
        Write-Host "  - Username: $($firstProfile.username)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ Profile search failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 5: Record profile view
Write-Host "5. Recording profile view..." -ForegroundColor Yellow
try {
    $viewBody = @{
        profileId = 1
        referrer = "test-script"
        userAgent = "PowerShell-Test"
    } | ConvertTo-Json

    $viewResponse = Invoke-WebRequest -Uri "$baseUrl/profile/view" -Method POST -Body $viewBody -ContentType "application/json"
    $viewData = $viewResponse.Content | ConvertFrom-Json
    Write-Host "✓ Profile view recorded: $($viewData.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ Profile view recording failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 6: Get profile by ID to verify view count increased
Write-Host "6. Verifying view count increased..." -ForegroundColor Yellow
try {
    $profileResponse2 = Invoke-WebRequest -Uri "$baseUrl/profile/1" -Method GET
    $profileData2 = $profileResponse2.Content | ConvertFrom-Json
    Write-Host "✓ Updated profile views: $($profileData2.data.profileViews)" -ForegroundColor Green
} catch {
    Write-Host "✗ Profile verification failed: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "=== API Testing Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Backend Features Demonstrated:" -ForegroundColor White
Write-Host "✓ Database seeding with astrology-focused data" -ForegroundColor Green
Write-Host "✓ Profile retrieval by slug and ID" -ForegroundColor Green
Write-Host "✓ Public profiles listing with pagination" -ForegroundColor Green
Write-Host "✓ Profile search functionality" -ForegroundColor Green
Write-Host "✓ Profile view tracking" -ForegroundColor Green
Write-Host "✓ Bulgarian language error messages" -ForegroundColor Green
Write-Host "✓ Complete astrology profile data structure" -ForegroundColor Green
Write-Host ""
Write-Host "Ready for frontend integration!" -ForegroundColor Magenta
