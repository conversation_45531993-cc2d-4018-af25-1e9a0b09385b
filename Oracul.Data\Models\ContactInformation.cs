using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Contact information entity for user profile
    /// </summary>
    public class ContactInformation : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [MaxLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        public bool IsEmailPublic { get; set; } = false;

        [MaxLength(500)]
        public string? Website { get; set; }

        [MaxLength(500)]
        public string? PortfolioUrl { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual BusinessAddress? BusinessAddress { get; set; }
        public virtual ICollection<PhoneNumber> PhoneNumbers { get; set; } = new List<PhoneNumber>();
    }
}
