{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/oauth.service\";\nimport * as i4 from \"../../services/service.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/divider\";\nfunction RegisterComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFirstNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLastNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getEmailErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getConfirmPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_mat_error_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" You must accept the terms and conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_icon_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_icon_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, oauthService, serviceService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.serviceService = serviceService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.returnUrl = '/';\n    // Oracle-specific properties\n    this.availableServices = [];\n    this.availableSpecializations = ['Tarot Reading', 'Astrology', 'Palm Reading', 'Crystal Healing', 'Spiritual Guidance', 'Numerology', 'Chakra Balancing', 'Energy Healing', 'Dream Interpretation', 'Aura Reading', 'Meditation', 'Life Coaching'];\n    this.availableLanguages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Chinese', 'Japanese', 'Arabic', 'Hindi', 'Dutch'];\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: [''],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]],\n      // Oracle-specific fields\n      isOracle: [false],\n      isAvailable: [true],\n      about: [''],\n      hourlyRate: [''],\n      yearsOfExperience: [''],\n      specializations: this.formBuilder.array([]),\n      languages: this.formBuilder.array([]),\n      serviceIds: this.formBuilder.array([])\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n    // Redirect if already logged in\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      if (isAuth) {\n        this.router.navigate([this.returnUrl]);\n      }\n    });\n    // Load available services\n    this.loadServices();\n  }\n  passwordMatchValidator(control) {\n    const password = control.get('password');\n    const confirmPassword = control.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return {\n        'passwordMismatch': true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      const registerRequest = {\n        firstName: this.registerForm.value.firstName,\n        lastName: this.registerForm.value.lastName,\n        email: this.registerForm.value.email,\n        phoneNumber: this.registerForm.value.phoneNumber || undefined,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword,\n        acceptTerms: this.registerForm.value.acceptTerms\n      };\n      this.authService.register(registerRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open('Registration successful! Welcome to Oracul!', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate([this.returnUrl]);\n          } else {\n            this.snackBar.open(response.message || 'Registration failed', 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error || 'Registration failed. Please try again.', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFirstNameErrorMessage() {\n    const control = this.registerForm.get('firstName');\n    if (control?.hasError('required')) {\n      return 'First name is required';\n    }\n    if (control?.hasError('minlength')) {\n      return 'First name must be at least 2 characters long';\n    }\n    return '';\n  }\n  getLastNameErrorMessage() {\n    const control = this.registerForm.get('lastName');\n    if (control?.hasError('required')) {\n      return 'Last name is required';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Last name must be at least 2 characters long';\n    }\n    return '';\n  }\n  getEmailErrorMessage() {\n    const control = this.registerForm.get('email');\n    if (control?.hasError('required')) {\n      return 'Email is required';\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    return '';\n  }\n  getPasswordErrorMessage() {\n    const control = this.registerForm.get('password');\n    if (control?.hasError('required')) {\n      return 'Password is required';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Password must be at least 6 characters long';\n    }\n    return '';\n  }\n  getConfirmPasswordErrorMessage() {\n    const control = this.registerForm.get('confirmPassword');\n    if (control?.hasError('required')) {\n      return 'Please confirm your password';\n    }\n    if (this.registerForm.hasError('passwordMismatch')) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  navigateToLogin() {\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  signUpWithGoogle() {\n    this.isLoading = true;\n    this.oauthService.signInWithGooglePopup().subscribe({\n      next: oauthUser => {\n        this.handleOAuthSignUp(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Google sign-up failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  signUpWithFacebook() {\n    this.isLoading = true;\n    this.oauthService.signInWithFacebook().subscribe({\n      next: oauthUser => {\n        this.handleOAuthSignUp(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Facebook sign-up failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  handleOAuthSignUp(oauthUser) {\n    const oauthRequest = {\n      provider: oauthUser.provider,\n      accessToken: oauthUser.accessToken,\n      email: oauthUser.email,\n      firstName: oauthUser.firstName,\n      lastName: oauthUser.lastName,\n      profilePictureUrl: oauthUser.profilePictureUrl\n    };\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          this.snackBar.open(`Welcome! Account created with ${oauthUser.provider}`, 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        } else {\n          this.snackBar.open(response.message || 'OAuth registration failed', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'OAuth registration failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.ServiceService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 90,\n      vars: 21,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"register-icon\"], [1, \"register-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter first name\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter last name\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Create a password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"terms-section\"], [\"formControlName\", \"acceptTerms\", \"color\", \"primary\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [1, \"divider-container\"], [1, \"divider-text\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"google-button\", 3, \"disabled\", \"click\"], [\"src\", \"https://developers.google.com/identity/images/g-logo.png\", \"alt\", \"Google\", 1, \"oauth-icon\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"facebook-button\", 3, \"disabled\", \"click\"], [1, \"oauth-icon\", \"facebook-icon\"], [1, \"register-actions\"], [1, \"login-section\"], [1, \"login-text\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"full-width\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\")(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Create Your Account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Join Oracul and start your journey \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"div\", 5)(12, \"mat-form-field\", 6)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 7);\n          i0.ɵɵelementStart(16, \"mat-icon\", 8);\n          i0.ɵɵtext(17, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, RegisterComponent_mat_error_18_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 6)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 10);\n          i0.ɵɵelementStart(23, \"mat-icon\", 8);\n          i0.ɵɵtext(24, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, RegisterComponent_mat_error_25_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 11)(27, \"mat-label\");\n          i0.ɵɵtext(28, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 12);\n          i0.ɵɵelementStart(30, \"mat-icon\", 8);\n          i0.ɵɵtext(31, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, RegisterComponent_mat_error_32_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-form-field\", 11)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Phone Number (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 13);\n          i0.ɵɵelementStart(37, \"mat-icon\", 8);\n          i0.ɵɵtext(38, \"phone\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 11)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 14);\n          i0.ɵɵelementStart(43, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_43_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(44, \"mat-icon\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, RegisterComponent_mat_error_46_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 11)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 16);\n          i0.ɵɵelementStart(51, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_51_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(54, RegisterComponent_mat_error_54_Template, 2, 1, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 17)(56, \"mat-checkbox\", 18);\n          i0.ɵɵtext(57, \" I agree to the \");\n          i0.ɵɵelementStart(58, \"a\", 19);\n          i0.ɵɵtext(59, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" and \");\n          i0.ɵɵelementStart(61, \"a\", 19);\n          i0.ɵɵtext(62, \"Privacy Policy\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(63, RegisterComponent_mat_error_63_Template, 2, 0, \"mat-error\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 20);\n          i0.ɵɵtemplate(65, RegisterComponent_mat_icon_65_Template, 2, 0, \"mat-icon\", 9);\n          i0.ɵɵtemplate(66, RegisterComponent_mat_icon_66_Template, 2, 0, \"mat-icon\", 9);\n          i0.ɵɵtext(67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 21);\n          i0.ɵɵelement(69, \"mat-divider\");\n          i0.ɵɵelementStart(70, \"span\", 22);\n          i0.ɵɵtext(71, \"or\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"mat-divider\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 23)(74, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_74_listener() {\n            return ctx.signUpWithGoogle();\n          });\n          i0.ɵɵelement(75, \"img\", 25);\n          i0.ɵɵtext(76, \" Sign up with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_77_listener() {\n            return ctx.signUpWithFacebook();\n          });\n          i0.ɵɵelementStart(78, \"mat-icon\", 27);\n          i0.ɵɵtext(79, \"facebook\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Sign up with Facebook \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"mat-card-actions\", 28);\n          i0.ɵɵelement(82, \"mat-divider\");\n          i0.ɵɵelementStart(83, \"div\", 29)(84, \"p\", 30);\n          i0.ɵɵtext(85, \"Already have an account?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_86_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(87, \"mat-icon\");\n          i0.ɵɵtext(88, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(89, \" Sign In \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_8_0;\n          let tmp_13_0;\n          let tmp_14_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.touched) || ctx.registerForm.hasError(\"passwordMismatch\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_14_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Creating Account...\" : \"Create Account\", \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        }\n      },\n      dependencies: [i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatProgressSpinner, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, i13.MatInput, i14.MatCheckbox, i15.MatDivider],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  min-height: 100vh;\\r\\n  padding: 20px;\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n}\\r\\n\\r\\n.register-card[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  max-width: 500px;\\r\\n  padding: 0;\\r\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  color: white;\\r\\n  padding: 24px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 8px;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 24px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.register-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 28px;\\r\\n  width: 28px;\\r\\n  height: 28px;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 14px;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.register-form[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.name-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.half-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%] {\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   .mat-checkbox[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\r\\n  color: #673ab7;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\r\\n  text-decoration: underline;\\r\\n}\\r\\n\\r\\n.register-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 16px;\\r\\n  font-weight: 500;\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.register-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n.register-actions[_ngcontent-%COMP%] {\\r\\n  padding: 0 24px 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.login-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.login-text[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.mat-form-field[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n\\r\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-error[_ngcontent-%COMP%] {\\r\\n  font-size: 12px;\\r\\n  margin-top: 4px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.divider-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  margin: 24px 0 16px;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.divider-text[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n  white-space: nowrap;\\r\\n}\\r\\n\\r\\n.oauth-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.oauth-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 14px;\\r\\n  font-weight: 500;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n.oauth-icon[_ngcontent-%COMP%] {\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%] {\\r\\n  border-color: #dadce0;\\r\\n  color: #3c4043;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f8f9fa;\\r\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%] {\\r\\n  border-color: #1877f2;\\r\\n  color: #1877f2;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f0f2f5;\\r\\n}\\r\\n\\r\\n.facebook-icon[_ngcontent-%COMP%] {\\r\\n  color: #1877f2;\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 600px) {\\r\\n  .register-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .register-card[_ngcontent-%COMP%] {\\r\\n    max-width: 100%;\\r\\n  }\\r\\n\\r\\n  .register-form[_ngcontent-%COMP%] {\\r\\n    padding: 20px;\\r\\n  }\\r\\n\\r\\n  .register-actions[_ngcontent-%COMP%] {\\r\\n    padding: 0 20px 20px;\\r\\n  }\\r\\n\\r\\n  .name-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 0;\\r\\n  }\\r\\n\\r\\n  .half-width[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n  .success-snackbar {\\r\\n  background-color: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n  .error-snackbar {\\r\\n  background-color: #f44336;\\r\\n  color: white;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,UAAU,QAAoC,gBAAgB;;;;;;;;;;;;;;;;;;;ICwBnFC,iCAAoG;IAClGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,kEACF;;;;;IAYAA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,iEACF;;;;;IAcFA,iCAA4F;IAC1FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,8DACF;;;;;IAiCAA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,iEACF;;;;;IAqBAA,iCAA+J;IAC7JA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,wEACF;;;;;IAQAA,iCAAwG;IACtGA,0DACF;IAAAA,iBAAY;;;;;IAUZA,gCAA4B;IAC1BA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA6B;IAAAA,0BAAU;IAAAA,iBAAW;;;AD5H5D,OAAM,MAAOC,iBAAiB;EAmB5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IANrB,gBAAW,GAAXN,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,mBAAc,GAAdC,cAAc;IACd,WAAM,GAANC,MAAM;IACN,UAAK,GAALC,KAAK;IACL,aAAQ,GAARC,QAAQ;IAxBlB,cAAS,GAAG,KAAK;IACjB,iBAAY,GAAG,IAAI;IACnB,wBAAmB,GAAG,IAAI;IAC1B,cAAS,GAAG,GAAG;IAEf;IACA,sBAAiB,GAAc,EAAE;IACjC,6BAAwB,GAAG,CACzB,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE,iBAAiB,EAC/D,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,EACxE,sBAAsB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,CACtE;IACD,uBAAkB,GAAG,CACnB,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EACjE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAC7D;IAWC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACiB,KAAK,CAAC,CAAC;MACpDC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DK,eAAe,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACc,QAAQ,CAAC,CAAC;MAC5CO,WAAW,EAAE,CAAC,KAAK,EAAE,CAACrB,UAAU,CAACsB,YAAY,CAAC,CAAC;MAC/C;MACAC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,WAAW,EAAE,CAAC,IAAI,CAAC;MACnBC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,eAAe,EAAE,IAAI,CAACxB,WAAW,CAACyB,KAAK,CAAC,EAAE,CAAC;MAC3CC,SAAS,EAAE,IAAI,CAAC1B,WAAW,CAACyB,KAAK,CAAC,EAAE,CAAC;MACrCE,UAAU,EAAE,IAAI,CAAC3B,WAAW,CAACyB,KAAK,CAAC,EAAE;KACtC,EAAE;MAAEG,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC1B,KAAK,CAAC2B,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG;IAEpE;IACA,IAAI,CAAChC,WAAW,CAACiC,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAIA,MAAM,EAAE;QACV,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;;IAE1C,CAAC,CAAC;IAEF;IACA,IAAI,CAACO,YAAY,EAAE;EACrB;EAEAT,sBAAsB,CAACU,OAAwB;IAC7C,MAAMxB,QAAQ,GAAGwB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACxC,MAAMxB,eAAe,GAAGuB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEtD,IAAIzB,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAAC0B,KAAK,KAAKzB,eAAe,CAACyB,KAAK,EAAE;MAC3E,OAAO;QAAE,kBAAkB,EAAE;MAAI,CAAE;;IAErC,OAAO,IAAI;EACb;EAEAC,QAAQ;IACN,IAAI,IAAI,CAACnC,YAAY,CAACoC,KAAK,EAAE;MAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMC,eAAe,GAAoB;QACvCpC,SAAS,EAAE,IAAI,CAACF,YAAY,CAACkC,KAAK,CAAChC,SAAS;QAC5CG,QAAQ,EAAE,IAAI,CAACL,YAAY,CAACkC,KAAK,CAAC7B,QAAQ;QAC1CC,KAAK,EAAE,IAAI,CAACN,YAAY,CAACkC,KAAK,CAAC5B,KAAK;QACpCC,WAAW,EAAE,IAAI,CAACP,YAAY,CAACkC,KAAK,CAAC3B,WAAW,IAAIgC,SAAS;QAC7D/B,QAAQ,EAAE,IAAI,CAACR,YAAY,CAACkC,KAAK,CAAC1B,QAAQ;QAC1CC,eAAe,EAAE,IAAI,CAACT,YAAY,CAACkC,KAAK,CAACzB,eAAe;QACxDC,WAAW,EAAE,IAAI,CAACV,YAAY,CAACkC,KAAK,CAACxB;OACtC;MAED,IAAI,CAAChB,WAAW,CAAC8C,QAAQ,CAACF,eAAe,CAAC,CAACV,SAAS,CAAC;QACnDa,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACL,SAAS,GAAG,KAAK;UACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAAC5C,QAAQ,CAAC6C,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;cACzEC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,kBAAkB;aAChC,CAAC;YACF,IAAI,CAACjD,MAAM,CAACiC,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;WACvC,MAAM;YACL,IAAI,CAACzB,QAAQ,CAAC6C,IAAI,CAACF,QAAQ,CAACK,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;cACrEF,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,gBAAgB;aAC9B,CAAC;;QAEN,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACX,SAAS,GAAG,KAAK;UACtB,IAAI,CAACtC,QAAQ,CAAC6C,IAAI,CAACI,KAAK,IAAI,wCAAwC,EAAE,OAAO,EAAE;YAC7EH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACG,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoB;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnD,YAAY,CAACoD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMtB,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAACqB,GAAG,CAAC;MAC1CtB,OAAO,EAAEuB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,wBAAwB;IACtB,MAAMxB,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAAC,WAAW,CAAC;IAClD,IAAID,OAAO,EAAEyB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,wBAAwB;;IAEjC,IAAIzB,OAAO,EAAEyB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEAC,uBAAuB;IACrB,MAAM1B,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAAC,UAAU,CAAC;IACjD,IAAID,OAAO,EAAEyB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,uBAAuB;;IAEhC,IAAIzB,OAAO,EAAEyB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,8CAA8C;;IAEvD,OAAO,EAAE;EACX;EAEAE,oBAAoB;IAClB,MAAM3B,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAAC,OAAO,CAAC;IAC9C,IAAID,OAAO,EAAEyB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,mBAAmB;;IAE5B,IAAIzB,OAAO,EAAEyB,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,OAAO,EAAE;EACX;EAEAG,uBAAuB;IACrB,MAAM5B,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAAC,UAAU,CAAC;IACjD,IAAID,OAAO,EAAEyB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,sBAAsB;;IAE/B,IAAIzB,OAAO,EAAEyB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,6CAA6C;;IAEtD,OAAO,EAAE;EACX;EAEAI,8BAA8B;IAC5B,MAAM7B,OAAO,GAAG,IAAI,CAAChC,YAAY,CAACiC,GAAG,CAAC,iBAAiB,CAAC;IACxD,IAAID,OAAO,EAAEyB,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,8BAA8B;;IAEvC,IAAI,IAAI,CAACzD,YAAY,CAACyD,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAClD,OAAO,wBAAwB;;IAEjC,OAAO,EAAE;EACX;EAEAK,eAAe;IACb,IAAI,CAACjE,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAAEJ,WAAW,EAAE;QAAEF,SAAS,EAAE,IAAI,CAACA;MAAS;IAAE,CAAE,CAAC;EAClF;EAEAuC,gBAAgB;IACd,IAAI,CAAC1B,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC1C,YAAY,CAACqE,qBAAqB,EAAE,CAACpC,SAAS,CAAC;MAClDa,IAAI,EAAGwB,SAAoB,IAAI;QAC7B,IAAI,CAACC,iBAAiB,CAACD,SAAS,CAAC;MACnC,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACtC,QAAQ,CAAC6C,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;UACtEC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEAqB,kBAAkB;IAChB,IAAI,CAAC9B,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC1C,YAAY,CAACyE,kBAAkB,EAAE,CAACxC,SAAS,CAAC;MAC/Ca,IAAI,EAAGwB,SAAoB,IAAI;QAC7B,IAAI,CAACC,iBAAiB,CAACD,SAAS,CAAC;MACnC,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACtC,QAAQ,CAAC6C,IAAI,CAAC,4CAA4C,EAAE,OAAO,EAAE;UACxEC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQoB,iBAAiB,CAACD,SAAoB;IAC5C,MAAMI,YAAY,GAAsB;MACtCC,QAAQ,EAAEL,SAAS,CAACK,QAAQ;MAC5BC,WAAW,EAAEN,SAAS,CAACM,WAAW;MAClCjE,KAAK,EAAE2D,SAAS,CAAC3D,KAAK;MACtBJ,SAAS,EAAE+D,SAAS,CAAC/D,SAAS;MAC9BG,QAAQ,EAAE4D,SAAS,CAAC5D,QAAQ;MAC5BmE,iBAAiB,EAAEP,SAAS,CAACO;KAC9B;IAED,IAAI,CAAC9E,WAAW,CAAC+E,cAAc,CAACJ,YAAY,CAAC,CAACzC,SAAS,CAAC;MACtDa,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC5C,QAAQ,CAAC6C,IAAI,CAAC,iCAAiCqB,SAAS,CAACK,QAAQ,EAAE,EAAE,OAAO,EAAE;YACjFzB,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAACjD,MAAM,CAACiC,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;SACvC,MAAM;UACL,IAAI,CAACzB,QAAQ,CAAC6C,IAAI,CAACF,QAAQ,CAACK,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;YAC3EF,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACtC,QAAQ,CAAC6C,IAAI,CAACI,KAAK,IAAI,8CAA8C,EAAE,OAAO,EAAE;UACnFH,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;;;uBA1PWvD,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAmF;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCd9BxF,8BAAgC;UAIQA,0BAAU;UAAAA,iBAAW;UACrDA,qCACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,oDACF;UAAAA,iBAAoB;UAGtBA,wCAAkB;UACiBA;YAAA,OAAYyF,cAAU;UAAA,EAAC;UAEtDzF,+BAAsB;UAEPA,2BAAU;UAAAA,iBAAY;UACjCA,4BAK4B;UAC5BA,oCAAoB;UAAAA,uBAAM;UAAAA,iBAAW;UACrCA,gFAEY;UACdA,iBAAiB;UAEjBA,0CAAwD;UAC3CA,0BAAS;UAAAA,iBAAY;UAChCA,6BAK6B;UAC7BA,oCAAoB;UAAAA,uBAAM;UAAAA,iBAAW;UACrCA,gFAEY;UACdA,iBAAiB;UAInBA,2CAAwD;UAC3CA,8BAAa;UAAAA,iBAAY;UACpCA,6BAKuB;UACvBA,oCAAoB;UAAAA,sBAAK;UAAAA,iBAAW;UACpCA,gFAEY;UACdA,iBAAiB;UAGjBA,2CAAwD;UAC3CA,wCAAuB;UAAAA,iBAAY;UAC9CA,6BAKqB;UACrBA,oCAAoB;UAAAA,sBAAK;UAAAA,iBAAW;UAItCA,2CAAwD;UAC3CA,yBAAQ;UAAAA,iBAAY;UAC/BA,6BAK8B;UAC9BA,mCAMqC;UAFnCA;YAAA;UAAA,EAAsC;UAGtCA,iCAAU;UAAAA,aAAoD;UAAAA,iBAAW;UAE3EA,gFAEY;UACdA,iBAAiB;UAGjBA,2CAAwD;UAC3CA,iCAAgB;UAAAA,iBAAY;UACvCA,6BAK8B;UAC9BA,mCAM4C;UAF1CA;YAAA;UAAA,EAAoD;UAGpDA,iCAAU;UAAAA,aAA2D;UAAAA,iBAAW;UAElFA,gFAEY;UACdA,iBAAiB;UAGjBA,gCAA2B;UAEvBA,iCAAe;UAAAA,8BAA4B;UAAAA,iCAAgB;UAAAA,iBAAI;UAACA,sBAAI;UAAAA,8BAA4B;UAAAA,+BAAc;UAAAA,iBAAI;UAEpHA,gFAEY;UACdA,iBAAM;UAGNA,mCAKyB;UACvBA,8EAEW;UACXA,8EAAkD;UAClDA,aACF;UAAAA,iBAAS;UAGTA,gCAA+B;UAC7BA,+BAA2B;UAC3BA,iCAA2B;UAAAA,mBAAE;UAAAA,iBAAO;UACpCA,+BAA2B;UAC7BA,iBAAM;UAGNA,gCAA2B;UAOvBA;YAAA,OAASyF,sBAAkB;UAAA,EAAC;UAC5BzF,2BAAoG;UACpGA,sCACF;UAAAA,iBAAS;UAGTA,mCAKiC;UAA/BA;YAAA,OAASyF,wBAAoB;UAAA,EAAC;UAC9BzF,qCAA2C;UAAAA,yBAAQ;UAAAA,iBAAW;UAC9DA,wCACF;UAAAA,iBAAS;UAKfA,6CAA2C;UACzCA,+BAA2B;UAE3BA,gCAA2B;UACHA,yCAAwB;UAAAA,iBAAI;UAClDA,mCAKqB;UADnBA;YAAA,OAASyF,qBAAiB;UAAA,EAAC;UAE3BzF,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,0BACF;UAAAA,iBAAS;;;;;;;;;UAhLLA,gBAA0B;UAA1BA,4CAA0B;UAYdA,eAAsF;UAAtFA,2LAAsF;UActFA,eAAoF;UAApFA,yLAAoF;UAgBtFA,eAA8E;UAA9EA,mLAA8E;UAsBxFA,gBAA2C;UAA3CA,6DAA2C;UAS3CA,eAAmC;UAAnCA,6CAAmC;UAEzBA,eAAoD;UAApDA,wEAAoD;UAEpDA,eAAoF;UAApFA,yLAAoF;UAU9FA,eAAkD;UAAlDA,oEAAkD;UASlDA,eAAmC;UAAnCA,6CAAmC;UAEzBA,eAA2D;UAA3DA,+EAA2D;UAE3DA,eAAiJ;UAAjJA,4PAAiJ;UAUjJA,eAA0F;UAA1FA,mMAA0F;UAWtGA,eAAsB;UAAtBA,wCAAsB;UACXA,eAAe;UAAfA,oCAAe;UAGfA,eAAgB;UAAhBA,qCAAgB;UAC3BA,eACF;UADEA,yFACF;UAgBIA,eAAsB;UAAtBA,wCAAsB;UAWtBA,eAAsB;UAAtBA,wCAAsB", "names": ["Validators", "i0", "RegisterComponent", "constructor", "formBuilder", "authService", "oauthService", "serviceService", "router", "route", "snackBar", "registerForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "email", "phoneNumber", "password", "confirmPassword", "acceptTerms", "requiredTrue", "is<PERSON><PERSON>le", "isAvailable", "about", "hourlyRate", "yearsOfExperience", "specializations", "array", "languages", "serviceIds", "validators", "passwordMatchValidator", "ngOnInit", "returnUrl", "snapshot", "queryParams", "isAuthenticated$", "subscribe", "isAuth", "navigate", "loadServices", "control", "get", "value", "onSubmit", "valid", "isLoading", "registerRequest", "undefined", "register", "next", "response", "success", "open", "duration", "panelClass", "message", "error", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "getFirstNameErrorMessage", "<PERSON><PERSON><PERSON><PERSON>", "getLastNameErrorMessage", "getEmailErrorMessage", "getPasswordErrorMessage", "getConfirmPasswordErrorMessage", "navigateToLogin", "signUpWithGoogle", "signInWithGooglePopup", "<PERSON><PERSON><PERSON><PERSON>ser", "handleOAuthSignUp", "signUpWithFacebook", "signInWithFacebook", "oauthRequest", "provider", "accessToken", "profilePictureUrl", "loginWithOAuth", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\register\\register.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, AbstractControl, FormArray } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { OAuthService, OAuthUser } from '../services/oauth.service';\r\nimport { RegisterRequest, OAuthLoginRequest } from '../models/auth.models';\r\nimport { ServiceService, Service } from '../../services/service.service';\r\n\r\n@Component({\r\n  selector: 'app-register',\r\n  templateUrl: './register.component.html',\r\n  styleUrls: ['./register.component.css']\r\n})\r\nexport class RegisterComponent implements OnInit {\r\n  registerForm: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  hideConfirmPassword = true;\r\n  returnUrl = '/';\r\n\r\n  // Oracle-specific properties\r\n  availableServices: Service[] = [];\r\n  availableSpecializations = [\r\n    'Tarot Reading', 'Astrology', 'Palm Reading', 'Crystal Healing',\r\n    'Spiritual Guidance', 'Numerology', 'Chakra Balancing', 'Energy Healing',\r\n    'Dream Interpretation', 'Aura Reading', 'Meditation', 'Life Coaching'\r\n  ];\r\n  availableLanguages = [\r\n    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',\r\n    'Russian', 'Chinese', 'Japanese', 'Arabic', 'Hindi', 'Dutch'\r\n  ];\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private oauthService: OAuthService,\r\n    private serviceService: ServiceService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.registerForm = this.formBuilder.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phoneNumber: [''],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      confirmPassword: ['', [Validators.required]],\r\n      acceptTerms: [false, [Validators.requiredTrue]],\r\n      // Oracle-specific fields\r\n      isOracle: [false],\r\n      isAvailable: [true],\r\n      about: [''],\r\n      hourlyRate: [''],\r\n      yearsOfExperience: [''],\r\n      specializations: this.formBuilder.array([]),\r\n      languages: this.formBuilder.array([]),\r\n      serviceIds: this.formBuilder.array([])\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\r\n\r\n    // Redirect if already logged in\r\n    this.authService.isAuthenticated$.subscribe(isAuth => {\r\n      if (isAuth) {\r\n        this.router.navigate([this.returnUrl]);\r\n      }\r\n    });\r\n\r\n    // Load available services\r\n    this.loadServices();\r\n  }\r\n\r\n  passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {\r\n    const password = control.get('password');\r\n    const confirmPassword = control.get('confirmPassword');\r\n\r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      return { 'passwordMismatch': true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.registerForm.valid) {\r\n      this.isLoading = true;\r\n\r\n      const registerRequest: RegisterRequest = {\r\n        firstName: this.registerForm.value.firstName,\r\n        lastName: this.registerForm.value.lastName,\r\n        email: this.registerForm.value.email,\r\n        phoneNumber: this.registerForm.value.phoneNumber || undefined,\r\n        password: this.registerForm.value.password,\r\n        confirmPassword: this.registerForm.value.confirmPassword,\r\n        acceptTerms: this.registerForm.value.acceptTerms\r\n      };\r\n\r\n      this.authService.register(registerRequest).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          if (response.success) {\r\n            this.snackBar.open('Registration successful! Welcome to Oracul!', 'Close', {\r\n              duration: 5000,\r\n              panelClass: ['success-snackbar']\r\n            });\r\n            this.router.navigate([this.returnUrl]);\r\n          } else {\r\n            this.snackBar.open(response.message || 'Registration failed', 'Close', {\r\n              duration: 5000,\r\n              panelClass: ['error-snackbar']\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          this.snackBar.open(error || 'Registration failed. Please try again.', 'Close', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.registerForm.controls).forEach(key => {\r\n      const control = this.registerForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getFirstNameErrorMessage(): string {\r\n    const control = this.registerForm.get('firstName');\r\n    if (control?.hasError('required')) {\r\n      return 'First name is required';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'First name must be at least 2 characters long';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getLastNameErrorMessage(): string {\r\n    const control = this.registerForm.get('lastName');\r\n    if (control?.hasError('required')) {\r\n      return 'Last name is required';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'Last name must be at least 2 characters long';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getEmailErrorMessage(): string {\r\n    const control = this.registerForm.get('email');\r\n    if (control?.hasError('required')) {\r\n      return 'Email is required';\r\n    }\r\n    if (control?.hasError('email')) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getPasswordErrorMessage(): string {\r\n    const control = this.registerForm.get('password');\r\n    if (control?.hasError('required')) {\r\n      return 'Password is required';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'Password must be at least 6 characters long';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getConfirmPasswordErrorMessage(): string {\r\n    const control = this.registerForm.get('confirmPassword');\r\n    if (control?.hasError('required')) {\r\n      return 'Please confirm your password';\r\n    }\r\n    if (this.registerForm.hasError('passwordMismatch')) {\r\n      return 'Passwords do not match';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  navigateToLogin(): void {\r\n    this.router.navigate(['/login'], { queryParams: { returnUrl: this.returnUrl } });\r\n  }\r\n\r\n  signUpWithGoogle(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithGooglePopup().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthSignUp(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Google sign-up failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  signUpWithFacebook(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithFacebook().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthSignUp(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Facebook sign-up failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleOAuthSignUp(oauthUser: OAuthUser): void {\r\n    const oauthRequest: OAuthLoginRequest = {\r\n      provider: oauthUser.provider,\r\n      accessToken: oauthUser.accessToken,\r\n      email: oauthUser.email,\r\n      firstName: oauthUser.firstName,\r\n      lastName: oauthUser.lastName,\r\n      profilePictureUrl: oauthUser.profilePictureUrl\r\n    };\r\n\r\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          this.snackBar.open(`Welcome! Account created with ${oauthUser.provider}`, 'Close', {\r\n            duration: 3000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n          this.router.navigate([this.returnUrl]);\r\n        } else {\r\n          this.snackBar.open(response.message || 'OAuth registration failed', 'Close', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'OAuth registration failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"register-container\">\r\n  <mat-card class=\"register-card\">\r\n    <mat-card-header class=\"register-header\">\r\n      <mat-card-title>\r\n        <mat-icon class=\"register-icon\">person_add</mat-icon>\r\n        Create Your Account\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Join <PERSON> and start your journey\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\r\n        <!-- Name Fields Row -->\r\n        <div class=\"name-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>First Name</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"firstName\"\r\n              placeholder=\"Enter first name\"\r\n              autocomplete=\"given-name\">\r\n            <mat-icon matSuffix>person</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\r\n              {{ getFirstNameErrorMessage() }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Last Name</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"lastName\"\r\n              placeholder=\"Enter last name\"\r\n              autocomplete=\"family-name\">\r\n            <mat-icon matSuffix>person</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\r\n              {{ getLastNameErrorMessage() }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Email Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Email Address</mat-label>\r\n          <input\r\n            matInput\r\n            type=\"email\"\r\n            formControlName=\"email\"\r\n            placeholder=\"Enter your email\"\r\n            autocomplete=\"email\">\r\n          <mat-icon matSuffix>email</mat-icon>\r\n          <mat-error *ngIf=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\r\n            {{ getEmailErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Phone Number Field (Optional) -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Phone Number (Optional)</mat-label>\r\n          <input\r\n            matInput\r\n            type=\"tel\"\r\n            formControlName=\"phoneNumber\"\r\n            placeholder=\"Enter your phone number\"\r\n            autocomplete=\"tel\">\r\n          <mat-icon matSuffix>phone</mat-icon>\r\n        </mat-form-field>\r\n\r\n        <!-- Password Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Password</mat-label>\r\n          <input\r\n            matInput\r\n            [type]=\"hidePassword ? 'password' : 'text'\"\r\n            formControlName=\"password\"\r\n            placeholder=\"Create a password\"\r\n            autocomplete=\"new-password\">\r\n          <button\r\n            mat-icon-button\r\n            matSuffix\r\n            type=\"button\"\r\n            (click)=\"hidePassword = !hidePassword\"\r\n            [attr.aria-label]=\"'Hide password'\"\r\n            [attr.aria-pressed]=\"hidePassword\">\r\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error *ngIf=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\r\n            {{ getPasswordErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Confirm Password Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Confirm Password</mat-label>\r\n          <input\r\n            matInput\r\n            [type]=\"hideConfirmPassword ? 'password' : 'text'\"\r\n            formControlName=\"confirmPassword\"\r\n            placeholder=\"Confirm your password\"\r\n            autocomplete=\"new-password\">\r\n          <button\r\n            mat-icon-button\r\n            matSuffix\r\n            type=\"button\"\r\n            (click)=\"hideConfirmPassword = !hideConfirmPassword\"\r\n            [attr.aria-label]=\"'Hide password'\"\r\n            [attr.aria-pressed]=\"hideConfirmPassword\">\r\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error *ngIf=\"(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || registerForm.hasError('passwordMismatch')\">\r\n            {{ getConfirmPasswordErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Terms and Conditions -->\r\n        <div class=\"terms-section\">\r\n          <mat-checkbox formControlName=\"acceptTerms\" color=\"primary\">\r\n            I agree to the <a href=\"#\" target=\"_blank\">Terms of Service</a> and <a href=\"#\" target=\"_blank\">Privacy Policy</a>\r\n          </mat-checkbox>\r\n          <mat-error *ngIf=\"registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched\">\r\n            You must accept the terms and conditions\r\n          </mat-error>\r\n        </div>\r\n\r\n        <!-- Register Button -->\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          type=\"submit\"\r\n          class=\"full-width register-button\"\r\n          [disabled]=\"isLoading\">\r\n          <mat-icon *ngIf=\"isLoading\">\r\n            <mat-spinner diameter=\"20\"></mat-spinner>\r\n          </mat-icon>\r\n          <mat-icon *ngIf=\"!isLoading\">person_add</mat-icon>\r\n          {{ isLoading ? 'Creating Account...' : 'Create Account' }}\r\n        </button>\r\n\r\n        <!-- Divider -->\r\n        <div class=\"divider-container\">\r\n          <mat-divider></mat-divider>\r\n          <span class=\"divider-text\">or</span>\r\n          <mat-divider></mat-divider>\r\n        </div>\r\n\r\n        <!-- OAuth Buttons -->\r\n        <div class=\"oauth-buttons\">\r\n          <!-- Google Sign-Up Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button google-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signUpWithGoogle()\">\r\n            <img src=\"https://developers.google.com/identity/images/g-logo.png\" alt=\"Google\" class=\"oauth-icon\">\r\n            Sign up with Google\r\n          </button>\r\n\r\n          <!-- Facebook Sign-Up Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button facebook-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signUpWithFacebook()\">\r\n            <mat-icon class=\"oauth-icon facebook-icon\">facebook</mat-icon>\r\n            Sign up with Facebook\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions class=\"register-actions\">\r\n      <mat-divider></mat-divider>\r\n\r\n      <div class=\"login-section\">\r\n        <p class=\"login-text\">Already have an account?</p>\r\n        <button\r\n          mat-stroked-button\r\n          color=\"primary\"\r\n          type=\"button\"\r\n          (click)=\"navigateToLogin()\"\r\n          class=\"full-width\">\r\n          <mat-icon>login</mat-icon>\r\n          Sign In\r\n        </button>\r\n      </div>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}