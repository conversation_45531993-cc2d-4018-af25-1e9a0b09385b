import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AvatarService {
  private readonly defaultAvatarColors = [
    '#d2a6d0', // soft lilac
    '#67455c', // dark purple
    '#e6dbec', // very light lilac
    '#a07ba0', // pink-lilac
    '#3f2f4e', // deep mystical purple
    '#8e7cc3', // lavender
    '#b19cd9', // light purple
    '#6a4c93', // royal purple
    '#9d4edd', // bright purple
    '#c77dff'  // light violet
  ];

  constructor() {}

  /**
   * Get avatar URL with fallback to generated avatar
   */
  getAvatarUrl(profilePhotoUrl: string | null | undefined, firstName: string, lastName: string): string {
    if (profilePhotoUrl && profilePhotoUrl.trim()) {
      return profilePhotoUrl;
    }
    
    return this.generateAvatarUrl(firstName, lastName);
  }

  /**
   * Generate a data URL for an avatar with initials
   */
  generateAvatarUrl(firstName: string, lastName: string): string {
    const initials = this.getInitials(firstName, lastName);
    const backgroundColor = this.getColorForName(firstName + lastName);
    const textColor = this.getContrastColor(backgroundColor);
    
    const svg = this.createAvatarSvg(initials, backgroundColor, textColor);
    return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;
  }

  /**
   * Get initials from first and last name
   */
  private getInitials(firstName: string, lastName: string): string {
    const first = firstName?.charAt(0)?.toUpperCase() || '';
    const last = lastName?.charAt(0)?.toUpperCase() || '';
    return first + last || '?';
  }

  /**
   * Get a consistent color based on the name
   */
  private getColorForName(name: string): string {
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % this.defaultAvatarColors.length;
    return this.defaultAvatarColors[index];
  }

  /**
   * Get contrasting text color (white or dark) based on background
   */
  private getContrastColor(backgroundColor: string): string {
    // Convert hex to RGB
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    return luminance > 0.5 ? '#333333' : '#ffffff';
  }

  /**
   * Create SVG avatar with initials
   */
  private createAvatarSvg(initials: string, backgroundColor: string, textColor: string): string {
    return `
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="50" fill="${backgroundColor}"/>
        <text x="50" y="50" font-family="Arial, sans-serif" font-size="36" font-weight="500" 
              text-anchor="middle" dominant-baseline="central" fill="${textColor}">
          ${initials}
        </text>
      </svg>
    `.trim();
  }

  /**
   * Handle image error by setting a generated avatar
   */
  onImageError(event: Event, firstName: string, lastName: string): void {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.src = this.generateAvatarUrl(firstName, lastName);
    }
  }

  /**
   * Get a mystical/astrological themed avatar for oracle profiles
   */
  getMysticalAvatarUrl(firstName: string, lastName: string): string {
    const initials = this.getInitials(firstName, lastName);
    const backgroundColor = this.getColorForName(firstName + lastName);
    const textColor = this.getContrastColor(backgroundColor);
    
    const svg = this.createMysticalAvatarSvg(initials, backgroundColor, textColor);
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  /**
   * Create mystical-themed SVG avatar with stars and moon elements
   */
  private createMysticalAvatarSvg(initials: string, backgroundColor: string, textColor: string): string {
    return `
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <radialGradient id="mysticalGrad" cx="50%" cy="30%" r="70%">
            <stop offset="0%" style="stop-color:${backgroundColor};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${this.darkenColor(backgroundColor, 20)};stop-opacity:1" />
          </radialGradient>
        </defs>
        <circle cx="50" cy="50" r="50" fill="url(#mysticalGrad)"/>
        
        <!-- Stars -->
        <polygon points="20,25 22,30 27,30 23,33 25,38 20,35 15,38 17,33 13,30 18,30" 
                 fill="${textColor}" opacity="0.3" transform="scale(0.4)"/>
        <polygon points="75,20 77,25 82,25 78,28 80,33 75,30 70,33 72,28 68,25 73,25" 
                 fill="${textColor}" opacity="0.3" transform="scale(0.3)"/>
        <polygon points="80,70 82,75 87,75 83,78 85,83 80,80 75,83 77,78 73,75 78,75" 
                 fill="${textColor}" opacity="0.2" transform="scale(0.3)"/>
        
        <!-- Crescent moon -->
        <path d="M 15 15 A 8 8 0 1 1 15 25 A 6 6 0 1 0 15 15" 
              fill="${textColor}" opacity="0.4"/>
        
        <text x="50" y="50" font-family="Arial, sans-serif" font-size="32" font-weight="600" 
              text-anchor="middle" dominant-baseline="central" fill="${textColor}">
          ${initials}
        </text>
      </svg>
    `.trim();
  }

  /**
   * Darken a hex color by a percentage
   */
  private darkenColor(color: string, percent: number): string {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.round(255 * percent / 100));
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.round(255 * percent / 100));
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.round(255 * percent / 100));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }
}
