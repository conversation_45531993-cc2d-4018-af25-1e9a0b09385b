namespace Oracul.Data.Models
{
    /// <summary>
    /// Junction entity for Role-Permission many-to-many relationship
    /// </summary>
    public class RolePermission : BaseEntity
    {
        public int RoleId { get; set; }
        public int PermissionId { get; set; }

        // Navigation properties
        public virtual Role Role { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
    }
}
