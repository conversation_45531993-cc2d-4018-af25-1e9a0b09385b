<!-- Hero Section -->
<section id="hero" class="hero-section">
  <div class="hero-content">
    <div class="hero-text">
      <h1 class="hero-title">{{ t.home.heroTitle }}</h1>
      <p class="hero-subtitle">
        {{ t.home.heroSubtitle }}
      </p>
      <div class="hero-actions">
        <button mat-raised-button color="primary" class="cta-button" (click)="navigateToRegister()" type="button">
          <mat-icon>star</mat-icon>
          {{ t.home.startJourney }}
        </button>
        <button mat-stroked-button color="primary" class="secondary-button" (click)="navigateToLogin()">
          <mat-icon>login</mat-icon>
          {{ t.common.login }}
        </button>
      </div>
    </div>
    <div class="hero-image">
      <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop"
           alt="Mystical cosmic background" class="hero-img">
    </div>
  </div>
</section>

<!-- Featured Astrologers Section -->
<section id="astrologers" class="featured-section">
  <div class="section-container">
    <div class="section-header">
      <h2 class="section-title">
        <mat-icon>people</mat-icon>
        {{ t.home.featuredAstrologers }}
      </h2>
      <p class="section-subtitle">{{ t.home.featuredAstrologersSubtitle }}</p>
      <button mat-stroked-button color="primary" (click)="searchProfiles()" class="view-all-btn">
        <mat-icon>search</mat-icon>
        {{ t.home.browseAllAstrologers }}
      </button>
    </div>

    <div class="profiles-grid" *ngIf="!isLoading">
      <mat-card class="profile-card" *ngFor="let profile of featuredProfiles" (click)="viewProfile(profile)">
        <div class="profile-header">
          <img [src]="avatarService.getAvatarUrl(profile.profilePhotoUrl, profile.firstName, profile.lastName)"
               [alt]="profile.firstName + ' ' + profile.lastName"
               class="profile-avatar"
               (error)="avatarService.onImageError($event, profile.firstName, profile.lastName)">
          <div class="profile-info">
            <h3 class="profile-name">{{ profile.firstName }} {{ profile.lastName }}</h3>
            <p class="profile-title">{{ profile.professionalTitle }}</p>
            <div class="profile-location">
              <mat-icon>location_on</mat-icon>
              <span>{{ profile.location?.displayLocation }}</span>
            </div>
          </div>
        </div>

        <div class="profile-content">
          <p class="profile-headline">{{ profile.headline }}</p>

          <div class="profile-skills">
            <div class="skills-container">
              <span *ngFor="let skill of profile.skills.slice(0, 3)" class="skill-chip">
                {{ skill.name }}
              </span>
            </div>
          </div>

          <div class="profile-stats">
            <div class="stat">
              <mat-icon>visibility</mat-icon>
              <span>{{ profile.profileViews }} {{ t.home.views }}</span>
            </div>
            <div class="stat">
              <mat-icon>thumb_up</mat-icon>
              <span>{{ getTotalEndorsements(profile) }} {{ t.home.endorsements }}</span>
            </div>
          </div>
        </div>

        <mat-card-actions>
          <button mat-button color="primary">
            <mat-icon>visibility</mat-icon>
            {{ t.home.viewProfile }}
          </button>
          <button mat-button color="accent">
            <mat-icon>message</mat-icon>
            {{ t.common.contact }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- Loading State -->
    <div class="loading-container" *ngIf="isLoading">
      <mat-spinner></mat-spinner>
      <p>{{ t.common.loading }}...</p>
    </div>
  </div>
</section>

<!-- Articles Section -->
<section id="articles" class="articles-section">
  <div class="section-container">
    <div class="section-header">
      <h2 class="section-title">
        <mat-icon>article</mat-icon>
        {{ t.home.cosmicWisdomArticles }}
      </h2>
      <p class="section-subtitle">{{ t.home.cosmicWisdomSubtitle }}</p>
    </div>

    <div class="articles-grid">
      <mat-card class="article-card" *ngFor="let article of featuredArticles" (click)="readArticle(article)">
        <img mat-card-image [src]="article.imageUrl" [alt]="article.title" class="article-image">

        <mat-card-header>
          <mat-card-title>{{ article.title }}</mat-card-title>
          <mat-card-subtitle>
            <div class="article-meta">
              <span class="author">By {{ article.author }}</span>
              <span class="date">{{ article.publishedAt | date:'MMM d, y' }}</span>
              <span class="read-time">{{ article.readTime }} {{ t.home.minRead }}</span>
            </div>
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <p class="article-excerpt">{{ article.excerpt }}</p>
          <mat-chip class="category-chip">{{ article.category }}</mat-chip>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button color="primary">
            <mat-icon>read_more</mat-icon>
            {{ t.home.readArticle }}
          </button>
          <button mat-icon-button>
            <mat-icon>share</mat-icon>
          </button>
          <button mat-icon-button>
            <mat-icon>bookmark_border</mat-icon>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</section>

<!-- Daily Horoscope Section -->
<section id="horoscope" class="horoscope-section">
  <div class="section-container">
    <div class="section-header">
      <h2 class="section-title">
        <mat-icon>brightness_7</mat-icon>
        {{ t.home.dailyHoroscope }}
      </h2>
      <p class="section-subtitle">{{ t.home.dailyHoroscopeSubtitle }}</p>
    </div>

    <div class="horoscope-grid">
      <mat-card class="horoscope-card" *ngFor="let sign of horoscopeSigns" (click)="viewHoroscope(sign)">
        <mat-card-header>
          <div mat-card-avatar class="sign-avatar">
            <span class="sign-symbol">{{ sign.symbol }}</span>
          </div>
          <mat-card-title>{{ sign.name }}</mat-card-title>
          <mat-card-subtitle>{{ sign.dateRange }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <p class="prediction">{{ sign.todayPrediction }}</p>

          <div class="sign-details">
            <div class="detail">
              <mat-icon>local_fire_department</mat-icon>
              <span>{{ sign.element }}</span>
            </div>
            <div class="detail">
              <mat-icon>casino</mat-icon>
              <span>{{ t.home.luckyNumber }}: {{ sign.luckyNumber }}</span>
            </div>
            <div class="detail">
              <mat-icon>palette</mat-icon>
              <span>{{ sign.luckyColor }}</span>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button color="primary">
            <mat-icon>read_more</mat-icon>
            {{ t.home.fullReading }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section">
  <div class="cta-content">
    <h2 class="cta-title">{{ t.home.ctaTitle }}</h2>
    <p class="cta-subtitle">
      {{ t.home.ctaSubtitle }}
    </p>
    <div class="cta-actions">
      <button mat-raised-button color="primary" class="cta-primary" (click)="navigateToRegister()" type="button">
        <mat-icon>star</mat-icon>
        {{ t.home.createFreeAccount }}
      </button>
      <button mat-stroked-button color="primary" class="cta-secondary" (click)="navigateToLogin()">
        <mat-icon>login</mat-icon>
        {{ t.home.alreadyMember }}
      </button>
    </div>
  </div>
</section>
