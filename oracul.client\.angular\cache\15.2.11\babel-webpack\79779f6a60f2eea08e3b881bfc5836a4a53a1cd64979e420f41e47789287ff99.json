{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "defaultIfEmpty", "defaultValue", "source", "subscriber", "hasValue", "subscribe", "value", "next", "complete"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/rxjs/dist/esm/internal/operators/defaultIfEmpty.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            subscriber.next(value);\n        }, () => {\n            if (!hasValue) {\n                subscriber.next(defaultValue);\n            }\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,cAAc,CAACC,YAAY,EAAE;EACzC,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpBF,MAAM,CAACG,SAAS,CAACN,wBAAwB,CAACI,UAAU,EAAGG,KAAK,IAAK;MAC7DF,QAAQ,GAAG,IAAI;MACfD,UAAU,CAACI,IAAI,CAACD,KAAK,CAAC;IAC1B,CAAC,EAAE,MAAM;MACL,IAAI,CAACF,QAAQ,EAAE;QACXD,UAAU,CAACI,IAAI,CAACN,YAAY,CAAC;MACjC;MACAE,UAAU,CAACK,QAAQ,EAAE;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}