<div class="profile-container" *ngIf="!isLoading && profile">
  <!-- Cover Photo Section -->
  <div class="cover-section">
    <div class="cover-photo" [style.background-image]="profile.coverPhotoUrl ? 'url(' + profile.coverPhotoUrl + ')' : 'var(--theme-gradient-primary)'">
      <div class="cover-overlay"></div>
    </div>
  </div>

  <!-- Profile Header -->
  <div class="profile-header">
    <div class="header-content">
      <div class="profile-photo-container">
        <img
          [src]="avatarUrl"
          [alt]="profile.firstName + ' ' + profile.lastName"
          class="profile-photo"
          (error)="onImageError($event)">
        <div class="online-indicator" *ngIf="!isOwnProfile"></div>
      </div>

      <div class="profile-info">
        <h1 class="profile-name">{{ profile.firstName }} {{ profile.lastName }}</h1>
        <h2 class="profile-title" *ngIf="profile.professionalTitle">{{ profile.professionalTitle }}</h2>
        <p class="profile-headline" *ngIf="profile.headline">{{ profile.headline }}</p>

        <div class="profile-meta">
          <div class="location" *ngIf="profile.location?.displayLocation">
            <mat-icon>location_on</mat-icon>
            <span>{{ profile.location?.displayLocation }}</span>
          </div>
          <div class="experience-years">
            <mat-icon>work</mat-icon>
            <span>{{ getExperienceYears() }} years experience</span>
          </div>
          <div class="profile-views" *ngIf="!isOwnProfile">
            <mat-icon>visibility</mat-icon>
            <span>{{ profile.profileViews }} profile views</span>
          </div>
        </div>
      </div>

      <div class="profile-actions">
        <button
          mat-raised-button
          color="primary"
          class="contact-btn"
          (click)="onContactClick()"
          *ngIf="!isOwnProfile">
          <mat-icon>email</mat-icon>
          Contact
        </button>

        <button
          mat-raised-button
          color="primary"
          (click)="onEditProfile()"
          *ngIf="isOwnProfile">
          <mat-icon>edit</mat-icon>
          Edit Profile
        </button>

        <button
          mat-stroked-button
          (click)="onFollowUser()"
          *ngIf="!isOwnProfile && isAuthenticated">
          <mat-icon>person_add</mat-icon>
          Follow
        </button>

        <button
          mat-icon-button
          [matMenuTriggerFor]="profileMenu"
          class="more-actions">
          <mat-icon>more_vert</mat-icon>
        </button>

        <mat-menu #profileMenu="matMenu">
          <button mat-menu-item (click)="onShareProfile()">
            <mat-icon>share</mat-icon>
            <span>Share Profile</span>
          </button>
          <button mat-menu-item *ngIf="!isOwnProfile">
            <mat-icon>flag</mat-icon>
            <span>Report</span>
          </button>
        </mat-menu>
      </div>
    </div>

    <!-- Profile Completion Progress (Own Profile Only) -->
    <div class="profile-completion" *ngIf="isOwnProfile">
      <div class="completion-header">
        <span>Profile Completion</span>
        <span class="completion-percentage">{{ profile.profileCompletionPercentage }}%</span>
      </div>
      <mat-progress-bar
        [value]="profile.profileCompletionPercentage"
        [color]="getProfileCompletionColor()">
      </mat-progress-bar>
    </div>
  </div>

  <!-- Main Content -->
  <div class="profile-content">
    <div class="content-grid">
      <!-- Left Column -->
      <div class="left-column">
        <!-- About Section -->
        <mat-card class="profile-section about-section" *ngIf="profile.summary">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>person</mat-icon>
              About
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="summary-text" [innerHTML]="profile.summary"></div>
          </mat-card-content>
        </mat-card>

        <!-- Experience Section -->
        <mat-card class="profile-section experience-section" *ngIf="profile.experiences.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>work</mat-icon>
              Experience
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="experience-timeline">
              <div class="experience-item" *ngFor="let exp of profile.experiences">
                <div class="experience-header">
                  <img
                    [src]="exp.companyLogoUrl || '/assets/images/default-company.png'"
                    [alt]="exp.company"
                    class="company-logo">
                  <div class="experience-info">
                    <h3 class="position">{{ exp.position }}</h3>
                    <h4 class="company">{{ exp.company }}</h4>
                    <p class="duration">
                      {{ formatDate(exp.startDate) }} -
                      {{ exp.isCurrent ? 'Present' : formatDate(exp.endDate!) }}
                      <span class="location" *ngIf="exp.location"> • {{ exp.location }}</span>
                    </p>
                  </div>
                </div>
                <div class="experience-description" *ngIf="exp.description">
                  <p>{{ exp.description }}</p>
                </div>
                <div class="experience-achievements" *ngIf="exp.achievements?.length">
                  <ul>
                    <li *ngFor="let achievement of exp.achievements">{{ achievement }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Portfolio Section -->
        <mat-card class="profile-section portfolio-section" *ngIf="profile.portfolioItems.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>work_outline</mat-icon>
              Portfolio
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="portfolio-grid">
              <div class="portfolio-item" *ngFor="let item of profile.portfolioItems">
                <div class="portfolio-image">
                  <img [src]="item.imageUrls[0]" [alt]="item.title" *ngIf="item.imageUrls.length">
                </div>
                <div class="portfolio-info">
                  <h3>{{ item.title }}</h3>
                  <p>{{ item.description }}</p>
                  <div class="portfolio-tech">
                    <div class="tech-chips">
                      <span class="tech-chip" *ngFor="let tech of item.technologies">{{ tech }}</span>
                    </div>
                  </div>
                  <div class="portfolio-links" *ngIf="item.projectUrl || item.githubUrl">
                    <a mat-button [href]="item.projectUrl" target="_blank" *ngIf="item.projectUrl">
                      <mat-icon>launch</mat-icon>
                      View Project
                    </a>
                    <a mat-button [href]="item.githubUrl" target="_blank" *ngIf="item.githubUrl">
                      <mat-icon>code</mat-icon>
                      View Code
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Blog Posts Section -->
        <mat-card class="profile-section blog-section" *ngIf="blogPosts.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>article</mat-icon>
              Recent Articles
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="blog-posts">
              <div class="blog-post" *ngFor="let post of blogPosts.slice(0, 3)">
                <div class="blog-image" *ngIf="post.featuredImageUrl">
                  <img [src]="post.featuredImageUrl" [alt]="post.title">
                </div>
                <div class="blog-content">
                  <h3>{{ post.title }}</h3>
                  <p>{{ post.excerpt }}</p>
                  <div class="blog-meta">
                    <span class="publish-date">{{ formatDate(post.publishedAt) }}</span>
                    <span class="read-count">{{ post.readCount }} reads</span>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Right Column -->
      <div class="right-column">
        <!-- Contact Info -->
        <mat-card class="profile-section contact-section">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>contact_mail</mat-icon>
              Contact Information
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="contact-item" *ngIf="profile.contactInfo.email && profile.contactInfo.isEmailPublic">
              <mat-icon>email</mat-icon>
              <a [href]="'mailto:' + profile.contactInfo.email">{{ profile.contactInfo.email }}</a>
            </div>
            <div class="contact-item" *ngFor="let phone of profile.contactInfo.phoneNumbers" [hidden]="!phone.isPublic">
              <mat-icon>phone</mat-icon>
              <a [href]="'tel:' + phone.number">{{ phone.number }} ({{ phone.type }})</a>
            </div>
            <div class="contact-item" *ngIf="profile.contactInfo.website">
              <mat-icon>language</mat-icon>
              <a [href]="profile.contactInfo.website" target="_blank">Website</a>
            </div>
            <div class="contact-item" *ngIf="profile.contactInfo.portfolioUrl">
              <mat-icon>work</mat-icon>
              <a [href]="profile.contactInfo.portfolioUrl" target="_blank">Portfolio</a>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Skills Section -->
        <mat-card class="profile-section skills-section" *ngIf="profile.skills.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>psychology</mat-icon>
              Skills & Expertise
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="skills-by-category" *ngFor="let category of getSkillsByCategory() | keyvalue">
              <h4 class="skill-category">{{ category.key }}</h4>
              <div class="skills-list">
                <div class="skill-item" *ngFor="let skill of category.value">
                  <div class="skill-info">
                    <span class="skill-name">{{ skill.name }}</span>
                    <span class="endorsement-count">{{ skill.endorsements }} endorsements</span>
                  </div>
                  <button
                    mat-icon-button
                    class="endorse-btn"
                    (click)="onEndorseSkill(skill.id)"
                    *ngIf="!isOwnProfile && isAuthenticated && !skill.isEndorsedByCurrentUser"
                    matTooltip="Endorse this skill">
                    <mat-icon>thumb_up</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Achievements Section -->
        <mat-card class="profile-section achievements-section" *ngIf="profile.achievements.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>emoji_events</mat-icon>
              Achievements
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="achievements-list">
              <div class="achievement-item" *ngFor="let achievement of profile.achievements">
                <div class="achievement-icon">
                  <img [src]="achievement.imageUrl || '/assets/images/default-achievement.png'" [alt]="achievement.title">
                </div>
                <div class="achievement-info">
                  <h4>{{ achievement.title }}</h4>
                  <p>{{ achievement.description }}</p>
                  <span class="achievement-date">{{ formatDate(achievement.achievedAt) }}</span>
                  <span class="achievement-org" *ngIf="achievement.organization"> • {{ achievement.organization }}</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Certifications Section -->
        <mat-card class="profile-section certifications-section" *ngIf="profile.certifications.length">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>verified</mat-icon>
              Certifications
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="certifications-list">
              <div class="certification-item" *ngFor="let cert of profile.certifications">
                <div class="certification-icon">
                  <img [src]="cert.imageUrl || '/assets/images/default-certification.png'" [alt]="cert.name">
                </div>
                <div class="certification-info">
                  <h4>{{ cert.name }}</h4>
                  <p>{{ cert.issuingOrganization }}</p>
                  <span class="certification-date">
                    Issued {{ formatDate(cert.issueDate) }}
                    <span *ngIf="cert.expirationDate"> • Expires {{ formatDate(cert.expirationDate) }}</span>
                  </span>
                  <a [href]="cert.credentialUrl" target="_blank" *ngIf="cert.credentialUrl" class="credential-link">
                    <mat-icon>launch</mat-icon>
                    View Credential
                  </a>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Analytics (Own Profile Only) -->
        <mat-card class="profile-section analytics-section" *ngIf="isOwnProfile && analytics">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>analytics</mat-icon>
              Profile Analytics
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="analytics-grid">
              <div class="analytics-item">
                <span class="analytics-value">{{ analytics.profileViews }}</span>
                <span class="analytics-label">Total Views</span>
              </div>
              <div class="analytics-item">
                <span class="analytics-value">{{ analytics.uniqueVisitors }}</span>
                <span class="analytics-label">Unique Visitors</span>
              </div>
              <div class="analytics-item">
                <span class="analytics-value">{{ analytics.skillEndorsements }}</span>
                <span class="analytics-label">Skill Endorsements</span>
              </div>
              <div class="analytics-item">
                <span class="analytics-value">{{ analytics.contactButtonClicks }}</span>
                <span class="analytics-label">Contact Clicks</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>
