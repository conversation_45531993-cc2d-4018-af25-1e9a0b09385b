# Profile Backend Implementation - Complete Guide

## Overview

This document describes the complete backend implementation for the astrology-focused profile details page. The backend system provides a comprehensive API for managing user profiles with astrology-themed data structures that match the existing Angular frontend mockup data.

## Architecture

### Data Layer (Oracul.Data)

The data layer follows Entity Framework Core code-first approach with the following key entities:

#### Core Profile Entities
- **UserProfile**: Main profile entity with astrology-focused fields
- **ProfileLocation**: Geographical information for users
- **ContactInformation**: Contact details including business address and phone numbers
- **ProfileSkill**: Astrology and spiritual expertise skills
- **BlogPost**: Astrology articles and insights
- **Achievement**: Astrology accomplishments and recognitions
- **Certification**: Astrology and spiritual certifications
- **WorkExperience**: Career history in astrology/spiritual fields
- **PortfolioItem**: Astrology projects and case studies
- **SocialLink**: Social media connections

#### Supporting Entities
- **BusinessAddress**: Business location details
- **PhoneNumber**: Contact phone numbers
- **SkillEndorsement**: User skill endorsements
- **BlogPostTag**: Article categorization
- **WorkAchievement**: Specific work accomplishments
- **PortfolioImage**: Portfolio item images
- **PortfolioTechnology**: Astrology techniques and tools used
- **ClientTestimonial**: Client reviews and testimonials

### Service Layer (Oracul.Server)

#### ProfileService
Comprehensive service for profile CRUD operations with Bulgarian language error messages:

- **Profile Management**: Create, read, update, delete profiles
- **Profile Search**: Search by name, skills, location
- **Profile Analytics**: View tracking and completion percentage calculation
- **Data Validation**: Username/slug availability checking
- **Business Logic**: Profile completion percentage calculation

#### ProfileSeedService
Service for populating the database with astrology-focused mockup data that matches the Angular frontend structure.

### API Layer (Oracul.Server/Controllers)

#### ProfileController
RESTful API endpoints with proper authentication and authorization:

```
GET    /api/profile/{id}              - Get profile by ID
GET    /api/profile/slug/{slug}       - Get profile by slug
GET    /api/profile/me                - Get current user's profile (auth required)
POST   /api/profile                   - Create new profile (auth required)
PUT    /api/profile                   - Update profile (auth required)
DELETE /api/profile                   - Delete profile (auth required)
POST   /api/profile/search            - Search profiles
GET    /api/profile/public            - Get public profiles with pagination
POST   /api/profile/view              - Record profile view
POST   /api/profile/upload/profile-photo - Upload profile photo (auth required)
POST   /api/profile/upload/cover-photo   - Upload cover photo (auth required)
```

#### SeedController
Development endpoint for database seeding:

```
POST   /api/seed/profiles             - Seed database with astrology profile data
```

## Database Schema

The migration `AddProfileEntities` creates the following tables:

- UserProfiles (main profile table)
- ProfileLocations
- ContactInformations
- BusinessAddresses
- PhoneNumbers
- ProfileSkills
- SkillEndorsements
- BlogPosts
- BlogPostTags
- Achievements
- Certifications
- WorkExperiences
- WorkAchievements
- PortfolioItems
- PortfolioImages
- PortfolioTechnologies
- ClientTestimonials
- SocialLinks

All tables include soft delete functionality and audit fields (CreatedAt, UpdatedAt, etc.).

## Data Migration Strategy

### 1. Database Setup
```bash
# Generate migration
dotnet ef migrations add AddProfileEntities --project Oracul.Data --startup-project Oracul.Server

# Apply migration
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server
```

### 2. Seed Data
The system includes astrology-focused seed data that matches the Angular frontend mockup:

```bash
# Start the server
dotnet run --project Oracul.Server

# Seed the database
POST http://localhost:5144/api/seed/profiles
```

### 3. Data Structure Compatibility
The backend API returns the exact same data structure as the Angular frontend mock service, ensuring seamless integration.

## Frontend Integration

### 1. Update Angular Services
Replace the mock ProfileService with HTTP calls to the backend API:

```typescript
// Replace mock data calls with HTTP requests
getProfile(slug: string): Observable<UserProfile> {
  return this.http.get<ApiResponse<UserProfile>>(`${this.apiUrl}/profile/slug/${slug}`)
    .pipe(map(response => response.data));
}

getCurrentUserProfile(): Observable<UserProfile> {
  return this.http.get<ApiResponse<UserProfile>>(`${this.apiUrl}/profile/me`)
    .pipe(map(response => response.data));
}

updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {
  return this.http.put<ApiResponse<UserProfile>>(`${this.apiUrl}/profile`, updates)
    .pipe(map(response => response.data));
}
```

### 2. Authentication Integration
The backend uses JWT authentication. Ensure the Angular app includes the JWT token in API requests:

```typescript
// Add JWT token to HTTP headers
const token = localStorage.getItem('authToken');
const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
```

### 3. Error Handling
The backend returns Bulgarian language error messages that match the frontend localization:

```typescript
// Handle API errors
.pipe(
  catchError((error: HttpErrorResponse) => {
    const apiError = error.error as ApiResponse<any>;
    this.notificationService.showError(apiError.message);
    return throwError(error);
  })
)
```

## API Response Format

All API endpoints return a consistent response format:

```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors: string[];
}
```

## Authentication & Authorization

- **Public Endpoints**: Profile viewing, search, public profiles list
- **Authenticated Endpoints**: Profile creation, updates, deletion, file uploads
- **Authorization**: Users can only modify their own profiles

## File Upload Support

The system includes endpoints for uploading profile and cover photos with:
- File type validation (JPEG, PNG, GIF)
- File size limits (5MB for profile photos, 10MB for cover photos)
- Mock implementation (returns placeholder URLs)

## Bulgarian Language Support

All error messages and API responses are in Bulgarian, matching the frontend localization:
- "Профилът е зареден успешно" (Profile loaded successfully)
- "Профилът не е намерен" (Profile not found)
- "Невалидни данни" (Invalid data)

## Testing

### 1. API Testing
Use the provided endpoints to test functionality:

```bash
# Get profile by slug
GET http://localhost:5144/api/profile/slug/luna-starweaver

# Get public profiles
GET http://localhost:5144/api/profile/public

# Search profiles
POST http://localhost:5144/api/profile/search
Content-Type: application/json
{
  "searchTerm": "astrology",
  "page": 1,
  "pageSize": 20
}
```

### 2. Database Verification
Check that the seeded data matches the frontend mockup structure and contains astrology-focused content.

## Next Steps

1. **Replace Frontend Mock Service**: Update Angular services to use the backend API
2. **Add Authentication**: Implement JWT token handling in the frontend
3. **File Upload Integration**: Implement actual file storage (cloud storage, local storage)
4. **Advanced Search**: Add more sophisticated search and filtering capabilities
5. **Analytics**: Implement detailed profile analytics and reporting
6. **Caching**: Add Redis caching for improved performance
7. **Rate Limiting**: Implement API rate limiting for production use

## Production Considerations

- **Security**: Implement proper input validation and sanitization
- **Performance**: Add database indexing and query optimization
- **Monitoring**: Add logging and application monitoring
- **Backup**: Implement database backup strategies
- **Scaling**: Consider database sharding for large user bases

The backend system is now fully functional and ready for frontend integration, providing a complete astrology-focused profile management system with Bulgarian language support.
