.profile-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.profile-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #673ab7;
}

.profile-card.compact {
  max-height: 400px;
}

/* Profile Header */
.profile-header {
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e0e0e0;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

.profile-details {
  margin-top: 4px;
}

.title-location {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.professional-title {
  font-weight: 500;
  color: #673ab7;
  font-size: 0.95rem;
}

.location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 0.85rem;
}

.location-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.experience-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #555;
  font-size: 0.85rem;
}

.experience-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #ff6f00;
}

/* Profile Content */
.profile-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.section-title mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #673ab7;
}

/* About Section */
.about-section {
  margin-bottom: 16px;
}

.about-text {
  color: #666;
  line-height: 1.5;
  margin: 0;
  font-size: 0.9rem;
}

/* Skills Section */
.skills-section {
  margin-bottom: 16px;
}

.skills-container {
  margin-top: 8px;
}

.skills-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-chip {
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 0.8rem;
  height: 28px;
}

.more-skills-chip {
  background-color: #f3e5f5;
  color: #673ab7;
  font-size: 0.8rem;
  height: 28px;
}

/* Contact Section */
.contact-section {
  margin-bottom: 16px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-button {
  width: 32px;
  height: 32px;
  min-width: 32px;
  padding: 0;
}

.contact-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.email-button {
  color: #1976d2;
}

.phone-button {
  color: #388e3c;
}

.website-button {
  color: #f57c00;
}

.contact-text {
  font-size: 0.9rem;
  color: #555;
  flex: 1;
}

/* Stats Section */
.stats-section {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-top: 1px solid #e0e0e0;
  margin-top: auto;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 0.8rem;
}

.stat-item mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #999;
}

/* Card Actions */
.profile-actions {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
  display: flex;
  gap: 8px;
  align-items: center;
}

.view-profile-btn {
  flex: 1;
  height: 36px;
  font-size: 0.9rem;
}

.contact-btn {
  height: 36px;
  font-size: 0.9rem;
}

.share-btn {
  width: 36px;
  height: 36px;
  min-width: 36px;
}

.profile-actions button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.share-btn mat-icon {
  margin-right: 0;
}

/* Profile Completion */
.completion-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-top: 1px solid #e0e0e0;
}

.completion-bar {
  flex: 1;
  height: 4px;
}

.completion-text {
  font-size: 0.8rem;
  color: #666;
  white-space: nowrap;
}

/* Compact Mode Adjustments */
.profile-card.compact .profile-content {
  gap: 12px;
}

.profile-card.compact .section-title {
  font-size: 0.9rem;
}

.profile-card.compact .about-text {
  font-size: 0.85rem;
}

.profile-card.compact .skills-chips {
  gap: 4px;
}

.profile-card.compact .skill-chip,
.profile-card.compact .more-skills-chip {
  height: 24px;
  font-size: 0.75rem;
}

.profile-card.compact .stats-section {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-card {
    margin-bottom: 16px;
  }
  
  .profile-header {
    padding: 12px;
  }
  
  .profile-content {
    padding: 12px;
    gap: 12px;
  }
  
  .profile-actions {
    padding: 8px 12px;
    flex-direction: column;
    gap: 8px;
  }
  
  .view-profile-btn,
  .contact-btn {
    width: 100%;
  }
  
  .stats-section {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .profile-avatar {
    width: 50px;
    height: 50px;
  }
  
  .profile-name {
    font-size: 1.1rem;
  }
  
  .contact-info {
    gap: 6px;
  }
  
  .contact-item {
    gap: 6px;
  }
}
