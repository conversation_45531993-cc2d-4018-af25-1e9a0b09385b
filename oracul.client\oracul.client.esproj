<Project Sdk="Microsoft.VisualStudio.JavaScript.Sdk/1.0.2191419">
  <PropertyGroup>
    <StartupCommand>npm start</StartupCommand>
    <JavaScriptTestFramework>Jasmine</JavaScriptTestFramework>
    <!-- Allows the build (or compile) script located on package.json to run on Build -->
    <ShouldRunBuildScript>false</ShouldRunBuildScript>
    <!-- Folder where production build objects will be placed -->
    <BuildOutputFolder>$(MSBuildProjectDirectory)\dist\oracul.client\</BuildOutputFolder>
  </PropertyGroup>
</Project>