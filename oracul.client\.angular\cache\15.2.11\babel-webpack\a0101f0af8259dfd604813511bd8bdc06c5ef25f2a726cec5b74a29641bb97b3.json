{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Skip<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nconst _c0 = [\"*\"];\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient provider for use with Angular Material icons. ' + 'Please include the HttpClientModule from @angular/common/http in your ' + 'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n  constructor(_httpClient, _sanitizer, document, _errorHandler) {\n    this._httpClient = _httpClient;\n    this._sanitizer = _sanitizer;\n    this._errorHandler = _errorHandler;\n    /**\n     * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n     */\n    this._svgIconConfigs = new Map();\n    /**\n     * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n     * Multiple icon sets can be registered under the same namespace.\n     */\n    this._iconSetConfigs = new Map();\n    /** Cache for icons loaded by direct URLs. */\n    this._cachedIconsByUrl = new Map();\n    /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n    this._inProgressUrlFetches = new Map();\n    /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n    this._fontCssClassesByAlias = new Map();\n    /** Registered icon resolver functions. */\n    this._resolvers = [];\n    /**\n     * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n     * specified. The default 'material-icons' value assumes that the material icon font has been\n     * loaded as described at http://google.github.io/material-design-icons/#icon-font-for-the-web\n     */\n    this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n    this._document = document;\n  }\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName, url, options) {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName, literal, options) {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(namespace, iconName, url, options) {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver) {\n    this._resolvers.push(resolver);\n    return this;\n  }\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url, options) {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal, options) {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace, url, options) {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias, classNames = alias) {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias) {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames) {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass() {\n    return this._defaultFontSetClass;\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl) {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n    if (cachedIcon) {\n      return of(cloneSvg(cachedIcon));\n    }\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name, namespace = '') {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n    return throwError(getMatIconNameNotFoundError(key));\n  }\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  _getSvgFromConfig(config) {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return of(cloneSvg(this._svgElementFromConfig(config)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return of(namedIcon);\n    }\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n      return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n        // Swallow errors fetching individual URLs so the\n        // combined Observable won't necessarily fail.\n        const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n        return of(null);\n      }));\n    });\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(map(() => {\n      const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n      // TODO: add an ngDevMode check\n      if (!foundIcon) {\n        throw getMatIconNameNotFoundError(name);\n      }\n      return foundIcon;\n    }));\n  }\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  _loadSvgIconFromConfig(config) {\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n  }\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  _loadSvgIconSetFromConfig(config) {\n    if (config.svgText) {\n      return of(null);\n    }\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n  }\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractSvgIconFromSet(iconSet, iconName, options) {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n    if (!iconSource) {\n      return null;\n    }\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true);\n    iconElement.removeAttribute('id');\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement, options);\n    }\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n    return this._setSvgAttributes(svg, options);\n  }\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  _svgElementFromString(str) {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n    return svg;\n  }\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  _toSvgElement(element) {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {\n        name,\n        value\n      } = attributes[i];\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n    return svg;\n  }\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  _setSvgAttributes(svg, options) {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n    return svg;\n  }\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  _fetchIcon(iconConfig) {\n    const {\n      url: safeUrl,\n      options\n    } = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n    const req = this._httpClient.get(url, {\n      responseType: 'text',\n      withCredentials\n    }).pipe(map(svg => {\n      // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n      // trusted HTML.\n      return trustedHTMLFromString(svg);\n    }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconConfig(namespace, iconName, config) {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconSetConfig(namespace, config) {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n    return this;\n  }\n  /** Parses a config's text into an SVG element. */\n  _svgElementFromConfig(config) {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n    return config.svgElement;\n  }\n  /** Tries to create an icon config through the registered resolver functions. */\n  _getIconConfigFromResolvers(namespace, name) {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n      if (result) {\n        return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n      }\n    }\n    return undefined;\n  }\n}\nMatIconRegistry.ɵfac = function MatIconRegistry_Factory(t) {\n  return new (t || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n};\nMatIconRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatIconRegistry,\n  factory: MatIconRegistry.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.HttpClient,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.DomSanitizer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ErrorHandler\n    }];\n  }, null);\n})();\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[new Optional(), new SkipSelf(), MatIconRegistry], [new Optional(), HttpClient], DomSanitizer, ErrorHandler, [new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatIcon.\n/** @docs-private */\nconst _MatIconBase = mixinColor(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n});\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon extends _MatIconBase {\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n  get inline() {\n    return this._inline;\n  }\n  set inline(inline) {\n    this._inline = coerceBooleanProperty(inline);\n  }\n  /** Name of the icon in the SVG icon set. */\n  get svgIcon() {\n    return this._svgIcon;\n  }\n  set svgIcon(value) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  /** Font set that the icon is a part of. */\n  get fontSet() {\n    return this._fontSet;\n  }\n  set fontSet(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  /** Name of an icon within a font set. */\n  get fontIcon() {\n    return this._fontIcon;\n  }\n  set fontIcon(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  constructor(elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n    super(elementRef);\n    this._iconRegistry = _iconRegistry;\n    this._location = _location;\n    this._errorHandler = _errorHandler;\n    this._inline = false;\n    this._previousFontSetClass = [];\n    /** Subscription to the current in-progress SVG icon request. */\n    this._currentIconFetch = Subscription.EMPTY;\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this.defaultColor = defaults.color;\n      }\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  _splitIconName(iconName) {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]];\n      // Use default namespace.\n      case 2:\n        return parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`);\n      // TODO: add an ngDevMode check\n    }\n  }\n\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n  _usingFontIcon() {\n    return !this.svgIcon;\n  }\n  _setSvgElement(svg) {\n    this._clearSvgElement();\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n  _clearSvgElement() {\n    const layoutElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n  _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n    const elem = this._elementRef.nativeElement;\n    const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n    if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  _cleanupFontValue(value) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  _prependPathToReferences(path) {\n    const elements = this._elementsWithExternalReferences;\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  _cacheChildrenWithExternalReferences(element) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n          attributes.push({\n            name: attr,\n            value: match[1]\n          });\n        }\n      });\n    }\n  }\n  /** Sets a new SVG icon with a particular name. */\n  _updateSvgIcon(rawName) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n      if (iconName) {\n        this._svgName = iconName;\n      }\n      this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n        const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n      });\n    }\n  }\n}\nMatIcon.ɵfac = function MatIcon_Factory(t) {\n  return new (t || MatIcon)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatIconRegistry), i0.ɵɵinjectAttribute('aria-hidden'), i0.ɵɵdirectiveInject(MAT_ICON_LOCATION), i0.ɵɵdirectiveInject(i0.ErrorHandler), i0.ɵɵdirectiveInject(MAT_ICON_DEFAULT_OPTIONS, 8));\n};\nMatIcon.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatIcon,\n  selectors: [[\"mat-icon\"]],\n  hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n  hostVars: 8,\n  hostBindings: function MatIcon_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n      i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n    }\n  },\n  inputs: {\n    color: \"color\",\n    inline: \"inline\",\n    svgIcon: \"svgIcon\",\n    fontSet: \"fontSet\",\n    fontIcon: \"fontIcon\"\n  },\n  exportAs: [\"matIcon\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatIcon_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIcon, [{\n    type: Component,\n    args: [{\n      template: '<ng-content></ng-content>',\n      selector: 'mat-icon',\n      exportAs: 'matIcon',\n      inputs: ['color'],\n      host: {\n        'role': 'img',\n        'class': 'mat-icon notranslate',\n        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n        '[class.mat-icon-inline]': 'inline',\n        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: MatIconRegistry\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['aria-hidden']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_ICON_LOCATION]\n      }]\n    }, {\n      type: i0.ErrorHandler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_ICON_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    inline: [{\n      type: Input\n    }],\n    svgIcon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatIconModule {}\nMatIconModule.ɵfac = function MatIconModule_Factory(t) {\n  return new (t || MatIconModule)();\n};\nMatIconModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatIconModule\n});\nMatIconModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatIcon, MatCommonModule],\n      declarations: [MatIcon]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };", "map": {"version": 3, "names": ["i0", "SecurityContext", "Injectable", "Optional", "Inject", "SkipSelf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InjectionToken", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Input", "NgModule", "mixinColor", "MatCommonModule", "coerceBooleanProperty", "DOCUMENT", "of", "throwError", "fork<PERSON><PERSON>n", "Subscription", "tap", "map", "catchError", "finalize", "share", "take", "i1", "HttpClient", "i2", "Dom<PERSON><PERSON><PERSON>zer", "policy", "getPolicy", "undefined", "window", "ttWindow", "trustedTypes", "createPolicy", "createHTML", "s", "trustedHTMLFromString", "html", "getMatIconNameNotFoundError", "iconName", "Error", "getMatIconNoHttpProviderError", "getMatIconFailedToSanitizeUrlError", "url", "getMatIconFailedToSanitizeLiteralError", "literal", "SvgIconConfig", "constructor", "svgText", "options", "MatIconRegistry", "_httpClient", "_sanitizer", "document", "_error<PERSON><PERSON><PERSON>", "_svgIconConfigs", "Map", "_iconSetConfigs", "_cachedIconsByUrl", "_inProgressUrlFetches", "_fontCssClassesByAlias", "_resolvers", "_defaultFontSetClass", "_document", "addSvgIcon", "addSvgIconInNamespace", "addSvgIconLiteral", "addSvgIconLiteralInNamespace", "namespace", "_addSvgIconConfig", "addSvgIconResolver", "resolver", "push", "cleanLiteral", "sanitize", "HTML", "trustedLiteral", "addSvgIconSet", "addSvgIconSetInNamespace", "addSvgIconSetLiteral", "addSvgIconSetLiteralInNamespace", "_addSvgIconSetConfig", "registerFontClassAlias", "alias", "classNames", "set", "classNameForFontAlias", "get", "setDefaultFontSetClass", "getDefaultFontSetClass", "getSvgIconFromUrl", "safeUrl", "RESOURCE_URL", "cachedIcon", "cloneSvg", "_loadSvgIconFromConfig", "pipe", "svg", "getNamedSvgIcon", "name", "key", "<PERSON><PERSON><PERSON>", "config", "_getSvgFromConfig", "_getIconConfigFromResolvers", "iconSetConfigs", "_getSvgFromIconSetConfigs", "ngOnDestroy", "clear", "_svgElementFromConfig", "namedIcon", "_extractIconWithNameFromAnySet", "iconSetFetchRequests", "filter", "iconSetConfig", "_loadSvgIconSetFromConfig", "err", "errorMessage", "message", "handleError", "foundIcon", "i", "length", "toString", "indexOf", "_extractSvgIconFromSet", "_fetchIcon", "iconSet", "iconSource", "querySelector", "iconElement", "cloneNode", "removeAttribute", "nodeName", "toLowerCase", "_setSvgAttributes", "_toSvgElement", "_svgElementFromString", "append<PERSON><PERSON><PERSON>", "str", "div", "createElement", "innerHTML", "element", "attributes", "value", "setAttribute", "childNodes", "nodeType", "ELEMENT_NODE", "viewBox", "iconConfig", "withCredentials", "inProgressFetch", "req", "responseType", "delete", "configNamespace", "svgElement", "result", "isSafeUrlWithOptions", "ɵfac", "ɵprov", "type", "args", "providedIn", "decorators", "ICON_REGISTRY_PROVIDER_FACTORY", "parentRegistry", "httpClient", "sanitizer", "<PERSON><PERSON><PERSON><PERSON>", "ICON_REGISTRY_PROVIDER", "provide", "deps", "useFactory", "_MatIconBase", "_elementRef", "MAT_ICON_DEFAULT_OPTIONS", "MAT_ICON_LOCATION", "factory", "MAT_ICON_LOCATION_FACTORY", "_location", "location", "getPathname", "pathname", "search", "funcIriAttributes", "funcIriAttributeSelector", "attr", "join", "funcIriPattern", "MatIcon", "inline", "_inline", "svgIcon", "_svgIcon", "_updateSvgIcon", "_clearSvgElement", "fontSet", "_fontSet", "newValue", "_cleanupFontValue", "_updateFontIconClasses", "fontIcon", "_fontIcon", "elementRef", "_iconRegistry", "ariaHidden", "defaults", "_previousFontSetClass", "_currentIconFetch", "EMPTY", "color", "defaultColor", "nativeElement", "_splitIconName", "parts", "split", "ngOnInit", "ngAfterViewChecked", "cachedElements", "_elementsWithExternalReferences", "size", "newPath", "_previousPath", "_prependPathToReferences", "unsubscribe", "_usingFontIcon", "_setSvgElement", "path", "_cacheChildrenWithExternalReferences", "layoutElement", "childCount", "child", "remove", "elem", "fontSetClasses", "className", "for<PERSON>ach", "classList", "add", "_previousFontIconClass", "includes", "trim", "elements", "attrs", "elementsWithFuncIri", "querySelectorAll", "elementWithReference", "getAttribute", "match", "rawName", "_svgNamespace", "_svgName", "subscribe", "ElementRef", "ɵcmp", "template", "selector", "exportAs", "inputs", "host", "encapsulation", "None", "changeDetection", "OnPush", "styles", "MatIconModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Skip<PERSON>elf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n    if (policy === undefined) {\n        policy = null;\n        if (typeof window !== 'undefined') {\n            const ttWindow = window;\n            if (ttWindow.trustedTypes !== undefined) {\n                policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n                    createHTML: (s) => s,\n                });\n            }\n        }\n    }\n    return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n    return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n    return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n    return Error('Could not find HttpClient provider for use with Angular Material icons. ' +\n        'Please include the HttpClientModule from @angular/common/http in your ' +\n        'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n    return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n        `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n    return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n        `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n    constructor(url, svgText, options) {\n        this.url = url;\n        this.svgText = svgText;\n        this.options = options;\n    }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n        this._httpClient = _httpClient;\n        this._sanitizer = _sanitizer;\n        this._errorHandler = _errorHandler;\n        /**\n         * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n         */\n        this._svgIconConfigs = new Map();\n        /**\n         * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n         * Multiple icon sets can be registered under the same namespace.\n         */\n        this._iconSetConfigs = new Map();\n        /** Cache for icons loaded by direct URLs. */\n        this._cachedIconsByUrl = new Map();\n        /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n        this._inProgressUrlFetches = new Map();\n        /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n        this._fontCssClassesByAlias = new Map();\n        /** Registered icon resolver functions. */\n        this._resolvers = [];\n        /**\n         * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n         * specified. The default 'material-icons' value assumes that the material icon font has been\n         * loaded as described at http://google.github.io/material-design-icons/#icon-font-for-the-web\n         */\n        this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n        this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n        return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n        return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n        this._resolvers.push(resolver);\n        return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        // TODO: add an ngDevMode check\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n        return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n        return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * If the registered font is a ligature font, then don't forget to also include the special\n     * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n     *\n     * ```ts\n     * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n     * ```\n     *\n     * And use like this:\n     *\n     * ```html\n     * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n     * ```\n     *\n     * @param alias Alias for the font.\n     * @param classNames Class names override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, classNames = alias) {\n        this._fontCssClassesByAlias.set(alias, classNames);\n        return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n        return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    setDefaultFontSetClass(...classNames) {\n        this._defaultFontSetClass = classNames;\n        return this;\n    }\n    /**\n     * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n        return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        const cachedIcon = this._cachedIconsByUrl.get(url);\n        if (cachedIcon) {\n            return of(cloneSvg(cachedIcon));\n        }\n        return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n        const key = iconKey(namespace, name);\n        let config = this._svgIconConfigs.get(key);\n        // Return (copy of) cached icon if possible.\n        if (config) {\n            return this._getSvgFromConfig(config);\n        }\n        // Otherwise try to resolve the config from one of the resolver functions.\n        config = this._getIconConfigFromResolvers(namespace, name);\n        if (config) {\n            this._svgIconConfigs.set(key, config);\n            return this._getSvgFromConfig(config);\n        }\n        // See if we have any icon sets registered for the namespace.\n        const iconSetConfigs = this._iconSetConfigs.get(namespace);\n        if (iconSetConfigs) {\n            return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n        }\n        return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n        this._resolvers = [];\n        this._svgIconConfigs.clear();\n        this._iconSetConfigs.clear();\n        this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n        if (config.svgText) {\n            // We already have the SVG element for this icon, return a copy.\n            return of(cloneSvg(this._svgElementFromConfig(config)));\n        }\n        else {\n            // Fetch the icon from the config's URL, cache it, and return a copy.\n            return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n        }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n        // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n        // requested name.\n        const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        if (namedIcon) {\n            // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n            // time anyway, there's probably not much advantage compared to just always extracting\n            // it from the icon set.\n            return of(namedIcon);\n        }\n        // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n        // fetched, fetch them now and look for iconName in the results.\n        const iconSetFetchRequests = iconSetConfigs\n            .filter(iconSetConfig => !iconSetConfig.svgText)\n            .map(iconSetConfig => {\n            return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError((err) => {\n                const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n                // Swallow errors fetching individual URLs so the\n                // combined Observable won't necessarily fail.\n                const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n                return of(null);\n            }));\n        });\n        // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n        // cached SVG element (unless the request failed), and we can check again for the icon.\n        return forkJoin(iconSetFetchRequests).pipe(map(() => {\n            const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n            // TODO: add an ngDevMode check\n            if (!foundIcon) {\n                throw getMatIconNameNotFoundError(name);\n            }\n            return foundIcon;\n        }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n        // Iterate backwards, so icon sets added later have precedence.\n        for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n            const config = iconSetConfigs[i];\n            // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n            // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n            // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n            // some of the parsing.\n            if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n                const svg = this._svgElementFromConfig(config);\n                const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n                if (foundIcon) {\n                    return foundIcon;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n        if (config.svgText) {\n            return of(null);\n        }\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n        // Use the `id=\"iconName\"` syntax in order to escape special\n        // characters in the ID (versus using the #iconName syntax).\n        const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n        if (!iconSource) {\n            return null;\n        }\n        // Clone the element and remove the ID to prevent multiple elements from being added\n        // to the page with the same ID.\n        const iconElement = iconSource.cloneNode(true);\n        iconElement.removeAttribute('id');\n        // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n        // the content of a new <svg> node.\n        if (iconElement.nodeName.toLowerCase() === 'svg') {\n            return this._setSvgAttributes(iconElement, options);\n        }\n        // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n        // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n        // tag is problematic on Firefox, because it needs to include the current page path.\n        if (iconElement.nodeName.toLowerCase() === 'symbol') {\n            return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n        }\n        // createElement('SVG') doesn't work as expected; the DOM ends up with\n        // the correct nodes, but the SVG content doesn't render. Instead we\n        // have to create an empty SVG node using innerHTML and append its content.\n        // Elements created using DOMParser.parseFromString have the same problem.\n        // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        // Clone the node so we don't remove it from the parent icon set element.\n        svg.appendChild(iconElement);\n        return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        // TODO: add an ngDevMode check\n        if (!svg) {\n            throw Error('<svg> tag not found');\n        }\n        return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        const attributes = element.attributes;\n        // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n        for (let i = 0; i < attributes.length; i++) {\n            const { name, value } = attributes[i];\n            if (name !== 'id') {\n                svg.setAttribute(name, value);\n            }\n        }\n        for (let i = 0; i < element.childNodes.length; i++) {\n            if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n                svg.appendChild(element.childNodes[i].cloneNode(true));\n            }\n        }\n        return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n        svg.setAttribute('fit', '');\n        svg.setAttribute('height', '100%');\n        svg.setAttribute('width', '100%');\n        svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n        svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n        if (options && options.viewBox) {\n            svg.setAttribute('viewBox', options.viewBox);\n        }\n        return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n        const { url: safeUrl, options } = iconConfig;\n        const withCredentials = options?.withCredentials ?? false;\n        if (!this._httpClient) {\n            throw getMatIconNoHttpProviderError();\n        }\n        // TODO: add an ngDevMode check\n        if (safeUrl == null) {\n            throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n        }\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        // TODO: add an ngDevMode check\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n        // already a request in progress for that URL. It's necessary to call share() on the\n        // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n        const inProgressFetch = this._inProgressUrlFetches.get(url);\n        if (inProgressFetch) {\n            return inProgressFetch;\n        }\n        const req = this._httpClient.get(url, { responseType: 'text', withCredentials }).pipe(map(svg => {\n            // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n            // trusted HTML.\n            return trustedHTMLFromString(svg);\n        }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n        this._inProgressUrlFetches.set(url, req);\n        return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n        this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n        return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n        const configNamespace = this._iconSetConfigs.get(namespace);\n        if (configNamespace) {\n            configNamespace.push(config);\n        }\n        else {\n            this._iconSetConfigs.set(namespace, [config]);\n        }\n        return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n        if (!config.svgElement) {\n            const svg = this._svgElementFromString(config.svgText);\n            this._setSvgAttributes(svg, config.options);\n            config.svgElement = svg;\n        }\n        return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n        for (let i = 0; i < this._resolvers.length; i++) {\n            const result = this._resolvers[i](name, namespace);\n            if (result) {\n                return isSafeUrlWithOptions(result)\n                    ? new SvgIconConfig(result.url, null, result.options)\n                    : new SvgIconConfig(result, null);\n            }\n        }\n        return undefined;\n    }\n}\nMatIconRegistry.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconRegistry, deps: [{ token: i1.HttpClient, optional: true }, { token: i2.DomSanitizer }, { token: DOCUMENT, optional: true }, { token: i0.ErrorHandler }], target: i0.ɵɵFactoryTarget.Injectable });\nMatIconRegistry.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconRegistry, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.HttpClient, decorators: [{\n                    type: Optional\n                }] }, { type: i2.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ErrorHandler }]; } });\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n    return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n    // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n    provide: MatIconRegistry,\n    deps: [\n        [new Optional(), new SkipSelf(), MatIconRegistry],\n        [new Optional(), HttpClient],\n        DomSanitizer,\n        ErrorHandler,\n        [new Optional(), DOCUMENT],\n    ],\n    useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n    return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n    return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n    return !!(value.url && value.options);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatIcon.\n/** @docs-private */\nconst _MatIconBase = mixinColor(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n});\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n    providedIn: 'root',\n    factory: MAT_ICON_LOCATION_FACTORY,\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n    'clip-path',\n    'color-profile',\n    'src',\n    'cursor',\n    'fill',\n    'filter',\n    'marker',\n    'marker-start',\n    'marker-mid',\n    'marker-end',\n    'mask',\n    'stroke',\n];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon extends _MatIconBase {\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(inline) {\n        this._inline = coerceBooleanProperty(inline);\n    }\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() {\n        return this._svgIcon;\n    }\n    set svgIcon(value) {\n        if (value !== this._svgIcon) {\n            if (value) {\n                this._updateSvgIcon(value);\n            }\n            else if (this._svgIcon) {\n                this._clearSvgElement();\n            }\n            this._svgIcon = value;\n        }\n    }\n    /** Font set that the icon is a part of. */\n    get fontSet() {\n        return this._fontSet;\n    }\n    set fontSet(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontSet) {\n            this._fontSet = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /** Name of an icon within a font set. */\n    get fontIcon() {\n        return this._fontIcon;\n    }\n    set fontIcon(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontIcon) {\n            this._fontIcon = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    constructor(elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n        super(elementRef);\n        this._iconRegistry = _iconRegistry;\n        this._location = _location;\n        this._errorHandler = _errorHandler;\n        this._inline = false;\n        this._previousFontSetClass = [];\n        /** Subscription to the current in-progress SVG icon request. */\n        this._currentIconFetch = Subscription.EMPTY;\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this.defaultColor = defaults.color;\n            }\n            if (defaults.fontSet) {\n                this.fontSet = defaults.fontSet;\n            }\n        }\n        // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n        // the right thing to do for the majority of icon use-cases.\n        if (!ariaHidden) {\n            elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n        }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n        if (!iconName) {\n            return ['', ''];\n        }\n        const parts = iconName.split(':');\n        switch (parts.length) {\n            case 1:\n                return ['', parts[0]]; // Use default namespace.\n            case 2:\n                return parts;\n            default:\n                throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n        }\n    }\n    ngOnInit() {\n        // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n        // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n        this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n        const cachedElements = this._elementsWithExternalReferences;\n        if (cachedElements && cachedElements.size) {\n            const newPath = this._location.getPathname();\n            // We need to check whether the URL has changed on each change detection since\n            // the browser doesn't have an API that will let us react on link clicks and\n            // we can't depend on the Angular router. The references need to be updated,\n            // because while most browsers don't care whether the URL is correct after\n            // the first render, Safari will break if the user navigates to a different\n            // page and the SVG isn't re-rendered.\n            if (newPath !== this._previousPath) {\n                this._previousPath = newPath;\n                this._prependPathToReferences(newPath);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._currentIconFetch.unsubscribe();\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n    }\n    _usingFontIcon() {\n        return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n        this._clearSvgElement();\n        // Note: we do this fix here, rather than the icon registry, because the\n        // references have to point to the URL at the time that the icon was created.\n        const path = this._location.getPathname();\n        this._previousPath = path;\n        this._cacheChildrenWithExternalReferences(svg);\n        this._prependPathToReferences(path);\n        this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n        const layoutElement = this._elementRef.nativeElement;\n        let childCount = layoutElement.childNodes.length;\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n        // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n        // we can't use innerHTML, because IE will throw if the element has a data binding.\n        while (childCount--) {\n            const child = layoutElement.childNodes[childCount];\n            // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n            // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n            if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n                child.remove();\n            }\n        }\n    }\n    _updateFontIconClasses() {\n        if (!this._usingFontIcon()) {\n            return;\n        }\n        const elem = this._elementRef.nativeElement;\n        const fontSetClasses = (this.fontSet\n            ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n            : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n        this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n        fontSetClasses.forEach(className => elem.classList.add(className));\n        this._previousFontSetClass = fontSetClasses;\n        if (this.fontIcon !== this._previousFontIconClass &&\n            !fontSetClasses.includes('mat-ligature-font')) {\n            if (this._previousFontIconClass) {\n                elem.classList.remove(this._previousFontIconClass);\n            }\n            if (this.fontIcon) {\n                elem.classList.add(this.fontIcon);\n            }\n            this._previousFontIconClass = this.fontIcon;\n        }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n        return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n        const elements = this._elementsWithExternalReferences;\n        if (elements) {\n            elements.forEach((attrs, element) => {\n                attrs.forEach(attr => {\n                    element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n                });\n            });\n        }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n        const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n        const elements = (this._elementsWithExternalReferences =\n            this._elementsWithExternalReferences || new Map());\n        for (let i = 0; i < elementsWithFuncIri.length; i++) {\n            funcIriAttributes.forEach(attr => {\n                const elementWithReference = elementsWithFuncIri[i];\n                const value = elementWithReference.getAttribute(attr);\n                const match = value ? value.match(funcIriPattern) : null;\n                if (match) {\n                    let attributes = elements.get(elementWithReference);\n                    if (!attributes) {\n                        attributes = [];\n                        elements.set(elementWithReference, attributes);\n                    }\n                    attributes.push({ name: attr, value: match[1] });\n                }\n            });\n        }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n        this._svgNamespace = null;\n        this._svgName = null;\n        this._currentIconFetch.unsubscribe();\n        if (rawName) {\n            const [namespace, iconName] = this._splitIconName(rawName);\n            if (namespace) {\n                this._svgNamespace = namespace;\n            }\n            if (iconName) {\n                this._svgName = iconName;\n            }\n            this._currentIconFetch = this._iconRegistry\n                .getNamedSvgIcon(iconName, namespace)\n                .pipe(take(1))\n                .subscribe(svg => this._setSvgElement(svg), (err) => {\n                const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n            });\n        }\n    }\n}\nMatIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIcon, deps: [{ token: i0.ElementRef }, { token: MatIconRegistry }, { token: 'aria-hidden', attribute: true }, { token: MAT_ICON_LOCATION }, { token: i0.ErrorHandler }, { token: MAT_ICON_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatIcon.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatIcon, selector: \"mat-icon\", inputs: { color: \"color\", inline: \"inline\", svgIcon: \"svgIcon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, host: { attributes: { \"role\": \"img\" }, properties: { \"attr.data-mat-icon-type\": \"_usingFontIcon() ? \\\"font\\\" : \\\"svg\\\"\", \"attr.data-mat-icon-name\": \"_svgName || fontIcon\", \"attr.data-mat-icon-namespace\": \"_svgNamespace || fontSet\", \"attr.fontIcon\": \"_usingFontIcon() ? fontIcon : null\", \"class.mat-icon-inline\": \"inline\", \"class.mat-icon-no-color\": \"color !== \\\"primary\\\" && color !== \\\"accent\\\" && color !== \\\"warn\\\"\" }, classAttribute: \"mat-icon notranslate\" }, exportAs: [\"matIcon\"], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIcon, decorators: [{\n            type: Component,\n            args: [{ template: '<ng-content></ng-content>', selector: 'mat-icon', exportAs: 'matIcon', inputs: ['color'], host: {\n                        'role': 'img',\n                        'class': 'mat-icon notranslate',\n                        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n                        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n                        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n                        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n                        '[class.mat-icon-inline]': 'inline',\n                        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: MatIconRegistry }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['aria-hidden']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_ICON_LOCATION]\n                }] }, { type: i0.ErrorHandler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_ICON_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { inline: [{\n                type: Input\n            }], svgIcon: [{\n                type: Input\n            }], fontSet: [{\n                type: Input\n            }], fontIcon: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatIconModule {\n}\nMatIconModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatIconModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconModule, declarations: [MatIcon], imports: [MatCommonModule], exports: [MatIcon, MatCommonModule] });\nMatIconModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatIcon, MatCommonModule],\n                    declarations: [MatIcon],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAChN,SAASC,UAAU,EAAEC,eAAe,QAAQ,wBAAwB;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,EAAE,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,MAAM;AAC7D,SAASC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AAC5E,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,YAAY,QAAQ,2BAA2B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAIA,IAAIC,MAAM;AACV;AACA;AACA;AACA;AACA,SAASC,SAAS,GAAG;EACjB,IAAID,MAAM,KAAKE,SAAS,EAAE;IACtBF,MAAM,GAAG,IAAI;IACb,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;MAC/B,MAAMC,QAAQ,GAAGD,MAAM;MACvB,IAAIC,QAAQ,CAACC,YAAY,KAAKH,SAAS,EAAE;QACrCF,MAAM,GAAGI,QAAQ,CAACC,YAAY,CAACC,YAAY,CAAC,oBAAoB,EAAE;UAC9DC,UAAU,EAAGC,CAAC,IAAKA;QACvB,CAAC,CAAC;MACN;IACJ;EACJ;EACA,OAAOR,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqB,CAACC,IAAI,EAAE;EACjC,OAAOT,SAAS,EAAE,EAAEM,UAAU,CAACG,IAAI,CAAC,IAAIA,IAAI;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2B,CAACC,QAAQ,EAAE;EAC3C,OAAOC,KAAK,CAAE,sCAAqCD,QAAS,GAAE,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,6BAA6B,GAAG;EACrC,OAAOD,KAAK,CAAC,0EAA0E,GACnF,wEAAwE,GACxE,cAAc,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kCAAkC,CAACC,GAAG,EAAE;EAC7C,OAAOH,KAAK,CAAE,wEAAuE,GAChF,kDAAiDG,GAAI,IAAG,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sCAAsC,CAACC,OAAO,EAAE;EACrD,OAAOL,KAAK,CAAE,0EAAyE,GAClF,kDAAiDK,OAAQ,IAAG,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAW,CAACJ,GAAG,EAAEK,OAAO,EAAEC,OAAO,EAAE;IAC/B,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBH,WAAW,CAACI,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC1D,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,EAAE;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAID,GAAG,EAAE;IAChC;IACA,IAAI,CAACE,iBAAiB,GAAG,IAAIF,GAAG,EAAE;IAClC;IACA,IAAI,CAACG,qBAAqB,GAAG,IAAIH,GAAG,EAAE;IACtC;IACA,IAAI,CAACI,sBAAsB,GAAG,IAAIJ,GAAG,EAAE;IACvC;IACA,IAAI,CAACK,UAAU,GAAG,EAAE;IACpB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACC,SAAS,GAAGV,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIW,UAAU,CAACzB,QAAQ,EAAEI,GAAG,EAAEM,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACgB,qBAAqB,CAAC,EAAE,EAAE1B,QAAQ,EAAEI,GAAG,EAAEM,OAAO,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIiB,iBAAiB,CAAC3B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,EAAE;IAC1C,OAAO,IAAI,CAACkB,4BAA4B,CAAC,EAAE,EAAE5B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,qBAAqB,CAACG,SAAS,EAAE7B,QAAQ,EAAEI,GAAG,EAAEM,OAAO,EAAE;IACrD,OAAO,IAAI,CAACoB,iBAAiB,CAACD,SAAS,EAAE7B,QAAQ,EAAE,IAAIO,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEM,OAAO,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqB,kBAAkB,CAACC,QAAQ,EAAE;IACzB,IAAI,CAACV,UAAU,CAACW,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIJ,4BAA4B,CAACC,SAAS,EAAE7B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,EAAE;IAChE,MAAMwB,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACgF,IAAI,EAAE9B,OAAO,CAAC;IAC5E;IACA,IAAI,CAAC4B,YAAY,EAAE;MACf,MAAM7B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACJ,iBAAiB,CAACD,SAAS,EAAE7B,QAAQ,EAAE,IAAIO,aAAa,CAAC,EAAE,EAAE8B,cAAc,EAAE3B,OAAO,CAAC,CAAC;EACtG;EACA;AACJ;AACA;AACA;EACI4B,aAAa,CAAClC,GAAG,EAAEM,OAAO,EAAE;IACxB,OAAO,IAAI,CAAC6B,wBAAwB,CAAC,EAAE,EAAEnC,GAAG,EAAEM,OAAO,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACI8B,oBAAoB,CAAClC,OAAO,EAAEI,OAAO,EAAE;IACnC,OAAO,IAAI,CAAC+B,+BAA+B,CAAC,EAAE,EAAEnC,OAAO,EAAEI,OAAO,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;EACI6B,wBAAwB,CAACV,SAAS,EAAEzB,GAAG,EAAEM,OAAO,EAAE;IAC9C,OAAO,IAAI,CAACgC,oBAAoB,CAACb,SAAS,EAAE,IAAItB,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEM,OAAO,CAAC,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;EACI+B,+BAA+B,CAACZ,SAAS,EAAEvB,OAAO,EAAEI,OAAO,EAAE;IACzD,MAAMwB,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACgF,IAAI,EAAE9B,OAAO,CAAC;IAC5E,IAAI,CAAC4B,YAAY,EAAE;MACf,MAAM7B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACQ,oBAAoB,CAACb,SAAS,EAAE,IAAItB,aAAa,CAAC,EAAE,EAAE8B,cAAc,EAAE3B,OAAO,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,sBAAsB,CAACC,KAAK,EAAEC,UAAU,GAAGD,KAAK,EAAE;IAC9C,IAAI,CAACvB,sBAAsB,CAACyB,GAAG,CAACF,KAAK,EAAEC,UAAU,CAAC;IAClD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,qBAAqB,CAACH,KAAK,EAAE;IACzB,OAAO,IAAI,CAACvB,sBAAsB,CAAC2B,GAAG,CAACJ,KAAK,CAAC,IAAIA,KAAK;EAC1D;EACA;AACJ;AACA;AACA;EACIK,sBAAsB,CAAC,GAAGJ,UAAU,EAAE;IAClC,IAAI,CAACtB,oBAAoB,GAAGsB,UAAU;IACtC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIK,sBAAsB,GAAG;IACrB,OAAO,IAAI,CAAC3B,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,iBAAiB,CAACC,OAAO,EAAE;IACvB,MAAMhD,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAED,OAAO,CAAC;IAC3E,IAAI,CAAChD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACiD,OAAO,CAAC;IACrD;IACA,MAAME,UAAU,GAAG,IAAI,CAACnC,iBAAiB,CAAC6B,GAAG,CAAC5C,GAAG,CAAC;IAClD,IAAIkD,UAAU,EAAE;MACZ,OAAOhF,EAAE,CAACiF,QAAQ,CAACD,UAAU,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACE,sBAAsB,CAAC,IAAIjD,aAAa,CAAC6C,OAAO,EAAE,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC/E,GAAG,CAACgF,GAAG,IAAI,IAAI,CAACvC,iBAAiB,CAAC2B,GAAG,CAAC1C,GAAG,EAAEsD,GAAG,CAAC,CAAC,EAAE/E,GAAG,CAAC+E,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;EAC1J;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAe,CAACC,IAAI,EAAE/B,SAAS,GAAG,EAAE,EAAE;IAClC,MAAMgC,GAAG,GAAGC,OAAO,CAACjC,SAAS,EAAE+B,IAAI,CAAC;IACpC,IAAIG,MAAM,GAAG,IAAI,CAAC/C,eAAe,CAACgC,GAAG,CAACa,GAAG,CAAC;IAC1C;IACA,IAAIE,MAAM,EAAE;MACR,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACAA,MAAM,GAAG,IAAI,CAACE,2BAA2B,CAACpC,SAAS,EAAE+B,IAAI,CAAC;IAC1D,IAAIG,MAAM,EAAE;MACR,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACe,GAAG,EAAEE,MAAM,CAAC;MACrC,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAAChD,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC1D,IAAIqC,cAAc,EAAE;MAChB,OAAO,IAAI,CAACC,yBAAyB,CAACP,IAAI,EAAEM,cAAc,CAAC;IAC/D;IACA,OAAO3F,UAAU,CAACwB,2BAA2B,CAAC8D,GAAG,CAAC,CAAC;EACvD;EACAO,WAAW,GAAG;IACV,IAAI,CAAC9C,UAAU,GAAG,EAAE;IACpB,IAAI,CAACN,eAAe,CAACqD,KAAK,EAAE;IAC5B,IAAI,CAACnD,eAAe,CAACmD,KAAK,EAAE;IAC5B,IAAI,CAAClD,iBAAiB,CAACkD,KAAK,EAAE;EAClC;EACA;AACJ;AACA;EACIL,iBAAiB,CAACD,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACtD,OAAO,EAAE;MAChB;MACA,OAAOnC,EAAE,CAACiF,QAAQ,CAAC,IAAI,CAACe,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC,MACI;MACD;MACA,OAAO,IAAI,CAACP,sBAAsB,CAACO,MAAM,CAAC,CAACN,IAAI,CAAC9E,GAAG,CAAC+E,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,yBAAyB,CAACP,IAAI,EAAEM,cAAc,EAAE;IAC5C;IACA;IACA,MAAMK,SAAS,GAAG,IAAI,CAACC,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;IAC3E,IAAIK,SAAS,EAAE;MACX;MACA;MACA;MACA,OAAOjG,EAAE,CAACiG,SAAS,CAAC;IACxB;IACA;IACA;IACA,MAAME,oBAAoB,GAAGP,cAAc,CACtCQ,MAAM,CAACC,aAAa,IAAI,CAACA,aAAa,CAAClE,OAAO,CAAC,CAC/C9B,GAAG,CAACgG,aAAa,IAAI;MACtB,OAAO,IAAI,CAACC,yBAAyB,CAACD,aAAa,CAAC,CAAClB,IAAI,CAAC7E,UAAU,CAAEiG,GAAG,IAAK;QAC1E,MAAMzE,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAEsB,aAAa,CAACvE,GAAG,CAAC;QACrF;QACA;QACA,MAAM0E,YAAY,GAAI,yBAAwB1E,GAAI,YAAWyE,GAAG,CAACE,OAAQ,EAAC;QAC1E,IAAI,CAAChE,aAAa,CAACiE,WAAW,CAAC,IAAI/E,KAAK,CAAC6E,YAAY,CAAC,CAAC;QACvD,OAAOxG,EAAE,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA;IACA,OAAOE,QAAQ,CAACiG,oBAAoB,CAAC,CAAChB,IAAI,CAAC9E,GAAG,CAAC,MAAM;MACjD,MAAMsG,SAAS,GAAG,IAAI,CAACT,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;MAC3E;MACA,IAAI,CAACe,SAAS,EAAE;QACZ,MAAMlF,2BAA2B,CAAC6D,IAAI,CAAC;MAC3C;MACA,OAAOqB,SAAS;IACpB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;EACIT,8BAA8B,CAACxE,QAAQ,EAAEkE,cAAc,EAAE;IACrD;IACA,KAAK,IAAIgB,CAAC,GAAGhB,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjD,MAAMnB,MAAM,GAAGG,cAAc,CAACgB,CAAC,CAAC;MAChC;MACA;MACA;MACA;MACA,IAAInB,MAAM,CAACtD,OAAO,IAAIsD,MAAM,CAACtD,OAAO,CAAC2E,QAAQ,EAAE,CAACC,OAAO,CAACrF,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QACpE,MAAM0D,GAAG,GAAG,IAAI,CAACY,qBAAqB,CAACP,MAAM,CAAC;QAC9C,MAAMkB,SAAS,GAAG,IAAI,CAACK,sBAAsB,CAAC5B,GAAG,EAAE1D,QAAQ,EAAE+D,MAAM,CAACrD,OAAO,CAAC;QAC5E,IAAIuE,SAAS,EAAE;UACX,OAAOA,SAAS;QACpB;MACJ;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIzB,sBAAsB,CAACO,MAAM,EAAE;IAC3B,OAAO,IAAI,CAACwB,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAC/E,GAAG,CAAC+B,OAAO,IAAKsD,MAAM,CAACtD,OAAO,GAAGA,OAAQ,CAAC,EAAE9B,GAAG,CAAC,MAAM,IAAI,CAAC2F,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;EAClI;EACA;AACJ;AACA;AACA;EACIa,yBAAyB,CAACb,MAAM,EAAE;IAC9B,IAAIA,MAAM,CAACtD,OAAO,EAAE;MAChB,OAAOnC,EAAE,CAAC,IAAI,CAAC;IACnB;IACA,OAAO,IAAI,CAACiH,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAC/E,GAAG,CAAC+B,OAAO,IAAKsD,MAAM,CAACtD,OAAO,GAAGA,OAAQ,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI6E,sBAAsB,CAACE,OAAO,EAAExF,QAAQ,EAAEU,OAAO,EAAE;IAC/C;IACA;IACA,MAAM+E,UAAU,GAAGD,OAAO,CAACE,aAAa,CAAE,QAAO1F,QAAS,IAAG,CAAC;IAC9D,IAAI,CAACyF,UAAU,EAAE;MACb,OAAO,IAAI;IACf;IACA;IACA;IACA,MAAME,WAAW,GAAGF,UAAU,CAACG,SAAS,CAAC,IAAI,CAAC;IAC9CD,WAAW,CAACE,eAAe,CAAC,IAAI,CAAC;IACjC;IACA;IACA,IAAIF,WAAW,CAACG,QAAQ,CAACC,WAAW,EAAE,KAAK,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACC,iBAAiB,CAACL,WAAW,EAAEjF,OAAO,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAIiF,WAAW,CAACG,QAAQ,CAACC,WAAW,EAAE,KAAK,QAAQ,EAAE;MACjD,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAACN,WAAW,CAAC,EAAEjF,OAAO,CAAC;IAC3E;IACA;IACA;IACA;IACA;IACA;IACA,MAAMgD,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACrG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E;IACA6D,GAAG,CAACyC,WAAW,CAACR,WAAW,CAAC;IAC5B,OAAO,IAAI,CAACK,iBAAiB,CAACtC,GAAG,EAAEhD,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;EACIwF,qBAAqB,CAACE,GAAG,EAAE;IACvB,MAAMC,GAAG,GAAG,IAAI,CAAC7E,SAAS,CAAC8E,aAAa,CAAC,KAAK,CAAC;IAC/CD,GAAG,CAACE,SAAS,GAAGH,GAAG;IACnB,MAAM1C,GAAG,GAAG2C,GAAG,CAACX,aAAa,CAAC,KAAK,CAAC;IACpC;IACA,IAAI,CAAChC,GAAG,EAAE;MACN,MAAMzD,KAAK,CAAC,qBAAqB,CAAC;IACtC;IACA,OAAOyD,GAAG;EACd;EACA;AACJ;AACA;EACIuC,aAAa,CAACO,OAAO,EAAE;IACnB,MAAM9C,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACrG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E,MAAM4G,UAAU,GAAGD,OAAO,CAACC,UAAU;IACrC;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,UAAU,CAACtB,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAM;QAAEtB,IAAI;QAAE8C;MAAM,CAAC,GAAGD,UAAU,CAACvB,CAAC,CAAC;MACrC,IAAItB,IAAI,KAAK,IAAI,EAAE;QACfF,GAAG,CAACiD,YAAY,CAAC/C,IAAI,EAAE8C,KAAK,CAAC;MACjC;IACJ;IACA,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,OAAO,CAACI,UAAU,CAACzB,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIsB,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAAC2B,QAAQ,KAAK,IAAI,CAACrF,SAAS,CAACsF,YAAY,EAAE;QAChEpD,GAAG,CAACyC,WAAW,CAACK,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAACU,SAAS,CAAC,IAAI,CAAC,CAAC;MAC1D;IACJ;IACA,OAAOlC,GAAG;EACd;EACA;AACJ;AACA;EACIsC,iBAAiB,CAACtC,GAAG,EAAEhD,OAAO,EAAE;IAC5BgD,GAAG,CAACiD,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;IAC3BjD,GAAG,CAACiD,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAClCjD,GAAG,CAACiD,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;IACjCjD,GAAG,CAACiD,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC;IACxDjD,GAAG,CAACiD,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IACxC,IAAIjG,OAAO,IAAIA,OAAO,CAACqG,OAAO,EAAE;MAC5BrD,GAAG,CAACiD,YAAY,CAAC,SAAS,EAAEjG,OAAO,CAACqG,OAAO,CAAC;IAChD;IACA,OAAOrD,GAAG;EACd;EACA;AACJ;AACA;AACA;EACI6B,UAAU,CAACyB,UAAU,EAAE;IACnB,MAAM;MAAE5G,GAAG,EAAEgD,OAAO;MAAE1C;IAAQ,CAAC,GAAGsG,UAAU;IAC5C,MAAMC,eAAe,GAAGvG,OAAO,EAAEuG,eAAe,IAAI,KAAK;IACzD,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;MACnB,MAAMV,6BAA6B,EAAE;IACzC;IACA;IACA,IAAIkD,OAAO,IAAI,IAAI,EAAE;MACjB,MAAMnD,KAAK,CAAE,+BAA8BmD,OAAQ,IAAG,CAAC;IAC3D;IACA,MAAMhD,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAED,OAAO,CAAC;IAC3E;IACA,IAAI,CAAChD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACiD,OAAO,CAAC;IACrD;IACA;IACA;IACA;IACA,MAAM8D,eAAe,GAAG,IAAI,CAAC9F,qBAAqB,CAAC4B,GAAG,CAAC5C,GAAG,CAAC;IAC3D,IAAI8G,eAAe,EAAE;MACjB,OAAOA,eAAe;IAC1B;IACA,MAAMC,GAAG,GAAG,IAAI,CAACvG,WAAW,CAACoC,GAAG,CAAC5C,GAAG,EAAE;MAAEgH,YAAY,EAAE,MAAM;MAAEH;IAAgB,CAAC,CAAC,CAACxD,IAAI,CAAC9E,GAAG,CAAC+E,GAAG,IAAI;MAC7F;MACA;MACA,OAAO7D,qBAAqB,CAAC6D,GAAG,CAAC;IACrC,CAAC,CAAC,EAAE7E,QAAQ,CAAC,MAAM,IAAI,CAACuC,qBAAqB,CAACiG,MAAM,CAACjH,GAAG,CAAC,CAAC,EAAEtB,KAAK,EAAE,CAAC;IACpE,IAAI,CAACsC,qBAAqB,CAAC0B,GAAG,CAAC1C,GAAG,EAAE+G,GAAG,CAAC;IACxC,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrF,iBAAiB,CAACD,SAAS,EAAE7B,QAAQ,EAAE+D,MAAM,EAAE;IAC3C,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACgB,OAAO,CAACjC,SAAS,EAAE7B,QAAQ,CAAC,EAAE+D,MAAM,CAAC;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIrB,oBAAoB,CAACb,SAAS,EAAEkC,MAAM,EAAE;IACpC,MAAMuD,eAAe,GAAG,IAAI,CAACpG,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC3D,IAAIyF,eAAe,EAAE;MACjBA,eAAe,CAACrF,IAAI,CAAC8B,MAAM,CAAC;IAChC,CAAC,MACI;MACD,IAAI,CAAC7C,eAAe,CAAC4B,GAAG,CAACjB,SAAS,EAAE,CAACkC,MAAM,CAAC,CAAC;IACjD;IACA,OAAO,IAAI;EACf;EACA;EACAO,qBAAqB,CAACP,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,CAACwD,UAAU,EAAE;MACpB,MAAM7D,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACnC,MAAM,CAACtD,OAAO,CAAC;MACtD,IAAI,CAACuF,iBAAiB,CAACtC,GAAG,EAAEK,MAAM,CAACrD,OAAO,CAAC;MAC3CqD,MAAM,CAACwD,UAAU,GAAG7D,GAAG;IAC3B;IACA,OAAOK,MAAM,CAACwD,UAAU;EAC5B;EACA;EACAtD,2BAA2B,CAACpC,SAAS,EAAE+B,IAAI,EAAE;IACzC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,UAAU,CAAC6D,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMsC,MAAM,GAAG,IAAI,CAAClG,UAAU,CAAC4D,CAAC,CAAC,CAACtB,IAAI,EAAE/B,SAAS,CAAC;MAClD,IAAI2F,MAAM,EAAE;QACR,OAAOC,oBAAoB,CAACD,MAAM,CAAC,GAC7B,IAAIjH,aAAa,CAACiH,MAAM,CAACpH,GAAG,EAAE,IAAI,EAAEoH,MAAM,CAAC9G,OAAO,CAAC,GACnD,IAAIH,aAAa,CAACiH,MAAM,EAAE,IAAI,CAAC;MACzC;IACJ;IACA,OAAOlI,SAAS;EACpB;AACJ;AACAqB,eAAe,CAAC+G,IAAI;EAAA,iBAA6F/G,eAAe,EAAzBxD,EAAE,UAAyC6B,EAAE,CAACC,UAAU,MAAxD9B,EAAE,UAAmF+B,EAAE,CAACC,YAAY,GAApGhC,EAAE,UAA+GkB,QAAQ,MAAzHlB,EAAE,UAAoJA,EAAE,CAACM,YAAY;AAAA,CAA6C;AACzTkD,eAAe,CAACgH,KAAK,kBADkFxK,EAAE;EAAA,OACYwD,eAAe;EAAA,SAAfA,eAAe;EAAA,YAAc;AAAM,EAAG;AAC3J;EAAA,mDAFuGxD,EAAE,mBAETwD,eAAe,EAAc,CAAC;IAClHiH,IAAI,EAAEvK,UAAU;IAChBwK,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE5I,EAAE,CAACC,UAAU;MAAE8I,UAAU,EAAE,CAAC;QAClEH,IAAI,EAAEtK;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsK,IAAI,EAAE1I,EAAE,CAACC;IAAa,CAAC,EAAE;MAAEyI,IAAI,EAAEtI,SAAS;MAAEyI,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEtK;MACV,CAAC,EAAE;QACCsK,IAAI,EAAErK,MAAM;QACZsK,IAAI,EAAE,CAACxJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEuJ,IAAI,EAAEzK,EAAE,CAACM;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;AACnD;AACA,SAASuK,8BAA8B,CAACC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEtH,QAAQ,EAAE;EACnG,OAAOmH,cAAc,IAAI,IAAItH,eAAe,CAACuH,UAAU,EAAEC,SAAS,EAAErH,QAAQ,EAAEsH,YAAY,CAAC;AAC/F;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,OAAO,EAAE3H,eAAe;EACxB4H,IAAI,EAAE,CACF,CAAC,IAAIjL,QAAQ,EAAE,EAAE,IAAIE,QAAQ,EAAE,EAAEmD,eAAe,CAAC,EACjD,CAAC,IAAIrD,QAAQ,EAAE,EAAE2B,UAAU,CAAC,EAC5BE,YAAY,EACZ1B,YAAY,EACZ,CAAC,IAAIH,QAAQ,EAAE,EAAEe,QAAQ,CAAC,CAC7B;EACDmK,UAAU,EAAER;AAChB,CAAC;AACD;AACA,SAASzE,QAAQ,CAACG,GAAG,EAAE;EACnB,OAAOA,GAAG,CAACkC,SAAS,CAAC,IAAI,CAAC;AAC9B;AACA;AACA,SAAS9B,OAAO,CAACjC,SAAS,EAAE+B,IAAI,EAAE;EAC9B,OAAO/B,SAAS,GAAG,GAAG,GAAG+B,IAAI;AACjC;AACA,SAAS6D,oBAAoB,CAACf,KAAK,EAAE;EACjC,OAAO,CAAC,EAAEA,KAAK,CAACtG,GAAG,IAAIsG,KAAK,CAAChG,OAAO,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+H,YAAY,GAAGvK,UAAU,CAAC,MAAM;EAClCsC,WAAW,CAACkI,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC;AACF;AACA,MAAMC,wBAAwB,GAAG,IAAIjL,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,MAAMkL,iBAAiB,GAAG,IAAIlL,cAAc,CAAC,mBAAmB,EAAE;EAC9DoK,UAAU,EAAE,MAAM;EAClBe,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,yBAAyB,GAAG;EACjC,MAAMtH,SAAS,GAAG7D,MAAM,CAACU,QAAQ,CAAC;EAClC,MAAM0K,SAAS,GAAGvH,SAAS,GAAGA,SAAS,CAACwH,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAE,MAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AACA;AACA,MAAMC,iBAAiB,GAAG,CACtB,WAAW,EACX,eAAe,EACf,KAAK,EACL,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,QAAQ,CACX;AACD;AACA,MAAMC,wBAAwB,GAAGD,iBAAiB,CAACzK,GAAG,CAAC2K,IAAI,IAAK,IAAGA,IAAK,GAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACtF;AACA,MAAMC,cAAc,GAAG,2BAA2B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,SAAShB,YAAY,CAAC;EAC/B;AACJ;AACA;AACA;EACI,IAAIiB,MAAM,GAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAM,CAACA,MAAM,EAAE;IACf,IAAI,CAACC,OAAO,GAAGvL,qBAAqB,CAACsL,MAAM,CAAC;EAChD;EACA;EACA,IAAIE,OAAO,GAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAO,CAAClD,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACmD,QAAQ,EAAE;MACzB,IAAInD,KAAK,EAAE;QACP,IAAI,CAACoD,cAAc,CAACpD,KAAK,CAAC;MAC9B,CAAC,MACI,IAAI,IAAI,CAACmD,QAAQ,EAAE;QACpB,IAAI,CAACE,gBAAgB,EAAE;MAC3B;MACA,IAAI,CAACF,QAAQ,GAAGnD,KAAK;IACzB;EACJ;EACA;EACA,IAAIsD,OAAO,GAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAO,CAACtD,KAAK,EAAE;IACf,MAAMwD,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACzD,KAAK,CAAC;IAC9C,IAAIwD,QAAQ,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC5B,IAAI,CAACA,QAAQ,GAAGC,QAAQ;MACxB,IAAI,CAACE,sBAAsB,EAAE;IACjC;EACJ;EACA;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAAC3D,KAAK,EAAE;IAChB,MAAMwD,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACzD,KAAK,CAAC;IAC9C,IAAIwD,QAAQ,KAAK,IAAI,CAACI,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGJ,QAAQ;MACzB,IAAI,CAACE,sBAAsB,EAAE;IACjC;EACJ;EACA5J,WAAW,CAAC+J,UAAU,EAAEC,aAAa,EAAEC,UAAU,EAAE1B,SAAS,EAAEhI,aAAa,EAAE2J,QAAQ,EAAE;IACnF,KAAK,CAACH,UAAU,CAAC;IACjB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAChI,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC4I,OAAO,GAAG,KAAK;IACpB,IAAI,CAACgB,qBAAqB,GAAG,EAAE;IAC/B;IACA,IAAI,CAACC,iBAAiB,GAAGnM,YAAY,CAACoM,KAAK;IAC3C,IAAIH,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,YAAY,GAAGL,QAAQ,CAACI,KAAK;MACnD;MACA,IAAIJ,QAAQ,CAACV,OAAO,EAAE;QAClB,IAAI,CAACA,OAAO,GAAGU,QAAQ,CAACV,OAAO;MACnC;IACJ;IACA;IACA;IACA,IAAI,CAACS,UAAU,EAAE;MACbF,UAAU,CAACS,aAAa,CAACrE,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsE,cAAc,CAACjL,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACnB;IACA,MAAMkL,KAAK,GAAGlL,QAAQ,CAACmL,KAAK,CAAC,GAAG,CAAC;IACjC,QAAQD,KAAK,CAAC/F,MAAM;MAChB,KAAK,CAAC;QACF,OAAO,CAAC,EAAE,EAAE+F,KAAK,CAAC,CAAC,CAAC,CAAC;MAAE;MAC3B,KAAK,CAAC;QACF,OAAOA,KAAK;MAChB;QACI,MAAMjL,KAAK,CAAE,uBAAsBD,QAAS,GAAE,CAAC;MAAE;IAAA;EAE7D;;EACAoL,QAAQ,GAAG;IACP;IACA;IACA,IAAI,CAAChB,sBAAsB,EAAE;EACjC;EACAiB,kBAAkB,GAAG;IACjB,MAAMC,cAAc,GAAG,IAAI,CAACC,+BAA+B;IAC3D,IAAID,cAAc,IAAIA,cAAc,CAACE,IAAI,EAAE;MACvC,MAAMC,OAAO,GAAG,IAAI,CAAC1C,SAAS,CAACE,WAAW,EAAE;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIwC,OAAO,KAAK,IAAI,CAACC,aAAa,EAAE;QAChC,IAAI,CAACA,aAAa,GAAGD,OAAO;QAC5B,IAAI,CAACE,wBAAwB,CAACF,OAAO,CAAC;MAC1C;IACJ;EACJ;EACArH,WAAW,GAAG;IACV,IAAI,CAACwG,iBAAiB,CAACgB,WAAW,EAAE;IACpC,IAAI,IAAI,CAACL,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAAClH,KAAK,EAAE;IAChD;EACJ;EACAwH,cAAc,GAAG;IACb,OAAO,CAAC,IAAI,CAACjC,OAAO;EACxB;EACAkC,cAAc,CAACpI,GAAG,EAAE;IAChB,IAAI,CAACqG,gBAAgB,EAAE;IACvB;IACA;IACA,MAAMgC,IAAI,GAAG,IAAI,CAAChD,SAAS,CAACE,WAAW,EAAE;IACzC,IAAI,CAACyC,aAAa,GAAGK,IAAI;IACzB,IAAI,CAACC,oCAAoC,CAACtI,GAAG,CAAC;IAC9C,IAAI,CAACiI,wBAAwB,CAACI,IAAI,CAAC;IACnC,IAAI,CAACrD,WAAW,CAACsC,aAAa,CAAC7E,WAAW,CAACzC,GAAG,CAAC;EACnD;EACAqG,gBAAgB,GAAG;IACf,MAAMkC,aAAa,GAAG,IAAI,CAACvD,WAAW,CAACsC,aAAa;IACpD,IAAIkB,UAAU,GAAGD,aAAa,CAACrF,UAAU,CAACzB,MAAM;IAChD,IAAI,IAAI,CAACoG,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAAClH,KAAK,EAAE;IAChD;IACA;IACA;IACA,OAAO6H,UAAU,EAAE,EAAE;MACjB,MAAMC,KAAK,GAAGF,aAAa,CAACrF,UAAU,CAACsF,UAAU,CAAC;MAClD;MACA;MACA,IAAIC,KAAK,CAACtF,QAAQ,KAAK,CAAC,IAAIsF,KAAK,CAACrG,QAAQ,CAACC,WAAW,EAAE,KAAK,KAAK,EAAE;QAChEoG,KAAK,CAACC,MAAM,EAAE;MAClB;IACJ;EACJ;EACAhC,sBAAsB,GAAG;IACrB,IAAI,CAAC,IAAI,CAACyB,cAAc,EAAE,EAAE;MACxB;IACJ;IACA,MAAMQ,IAAI,GAAG,IAAI,CAAC3D,WAAW,CAACsC,aAAa;IAC3C,MAAMsB,cAAc,GAAG,CAAC,IAAI,CAACtC,OAAO,GAC9B,IAAI,CAACQ,aAAa,CAACzH,qBAAqB,CAAC,IAAI,CAACiH,OAAO,CAAC,CAACmB,KAAK,CAAC,IAAI,CAAC,GAClE,IAAI,CAACX,aAAa,CAACtH,sBAAsB,EAAE,EAAEwB,MAAM,CAAC6H,SAAS,IAAIA,SAAS,CAACpH,MAAM,GAAG,CAAC,CAAC;IAC5F,IAAI,CAACwF,qBAAqB,CAAC6B,OAAO,CAACD,SAAS,IAAIF,IAAI,CAACI,SAAS,CAACL,MAAM,CAACG,SAAS,CAAC,CAAC;IACjFD,cAAc,CAACE,OAAO,CAACD,SAAS,IAAIF,IAAI,CAACI,SAAS,CAACC,GAAG,CAACH,SAAS,CAAC,CAAC;IAClE,IAAI,CAAC5B,qBAAqB,GAAG2B,cAAc;IAC3C,IAAI,IAAI,CAACjC,QAAQ,KAAK,IAAI,CAACsC,sBAAsB,IAC7C,CAACL,cAAc,CAACM,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACD,sBAAsB,EAAE;QAC7BN,IAAI,CAACI,SAAS,CAACL,MAAM,CAAC,IAAI,CAACO,sBAAsB,CAAC;MACtD;MACA,IAAI,IAAI,CAACtC,QAAQ,EAAE;QACfgC,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,IAAI,CAACrC,QAAQ,CAAC;MACrC;MACA,IAAI,CAACsC,sBAAsB,GAAG,IAAI,CAACtC,QAAQ;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIF,iBAAiB,CAACzD,KAAK,EAAE;IACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACmG,IAAI,EAAE,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGzE,KAAK;EACzE;EACA;AACJ;AACA;AACA;AACA;EACIiF,wBAAwB,CAACI,IAAI,EAAE;IAC3B,MAAMe,QAAQ,GAAG,IAAI,CAACvB,+BAA+B;IACrD,IAAIuB,QAAQ,EAAE;MACVA,QAAQ,CAACN,OAAO,CAAC,CAACO,KAAK,EAAEvG,OAAO,KAAK;QACjCuG,KAAK,CAACP,OAAO,CAAClD,IAAI,IAAI;UAClB9C,OAAO,CAACG,YAAY,CAAC2C,IAAI,CAAC1F,IAAI,EAAG,QAAOmI,IAAK,IAAGzC,IAAI,CAAC5C,KAAM,IAAG,CAAC;QACnE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACIsF,oCAAoC,CAACxF,OAAO,EAAE;IAC1C,MAAMwG,mBAAmB,GAAGxG,OAAO,CAACyG,gBAAgB,CAAC5D,wBAAwB,CAAC;IAC9E,MAAMyD,QAAQ,GAAI,IAAI,CAACvB,+BAA+B,GAClD,IAAI,CAACA,+BAA+B,IAAI,IAAItK,GAAG,EAAG;IACtD,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8H,mBAAmB,CAAC7H,MAAM,EAAED,CAAC,EAAE,EAAE;MACjDkE,iBAAiB,CAACoD,OAAO,CAAClD,IAAI,IAAI;QAC9B,MAAM4D,oBAAoB,GAAGF,mBAAmB,CAAC9H,CAAC,CAAC;QACnD,MAAMwB,KAAK,GAAGwG,oBAAoB,CAACC,YAAY,CAAC7D,IAAI,CAAC;QACrD,MAAM8D,KAAK,GAAG1G,KAAK,GAAGA,KAAK,CAAC0G,KAAK,CAAC5D,cAAc,CAAC,GAAG,IAAI;QACxD,IAAI4D,KAAK,EAAE;UACP,IAAI3G,UAAU,GAAGqG,QAAQ,CAAC9J,GAAG,CAACkK,oBAAoB,CAAC;UACnD,IAAI,CAACzG,UAAU,EAAE;YACbA,UAAU,GAAG,EAAE;YACfqG,QAAQ,CAAChK,GAAG,CAACoK,oBAAoB,EAAEzG,UAAU,CAAC;UAClD;UACAA,UAAU,CAACxE,IAAI,CAAC;YAAE2B,IAAI,EAAE0F,IAAI;YAAE5C,KAAK,EAAE0G,KAAK,CAAC,CAAC;UAAE,CAAC,CAAC;QACpD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAtD,cAAc,CAACuD,OAAO,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC3C,iBAAiB,CAACgB,WAAW,EAAE;IACpC,IAAIyB,OAAO,EAAE;MACT,MAAM,CAACxL,SAAS,EAAE7B,QAAQ,CAAC,GAAG,IAAI,CAACiL,cAAc,CAACoC,OAAO,CAAC;MAC1D,IAAIxL,SAAS,EAAE;QACX,IAAI,CAACyL,aAAa,GAAGzL,SAAS;MAClC;MACA,IAAI7B,QAAQ,EAAE;QACV,IAAI,CAACuN,QAAQ,GAAGvN,QAAQ;MAC5B;MACA,IAAI,CAAC4K,iBAAiB,GAAG,IAAI,CAACJ,aAAa,CACtC7G,eAAe,CAAC3D,QAAQ,EAAE6B,SAAS,CAAC,CACpC4B,IAAI,CAAC1E,IAAI,CAAC,CAAC,CAAC,CAAC,CACbyO,SAAS,CAAC9J,GAAG,IAAI,IAAI,CAACoI,cAAc,CAACpI,GAAG,CAAC,EAAGmB,GAAG,IAAK;QACrD,MAAMC,YAAY,GAAI,yBAAwBjD,SAAU,IAAG7B,QAAS,KAAI6E,GAAG,CAACE,OAAQ,EAAC;QACrF,IAAI,CAAChE,aAAa,CAACiE,WAAW,CAAC,IAAI/E,KAAK,CAAC6E,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;AACJ;AACA2E,OAAO,CAAC/B,IAAI;EAAA,iBAA6F+B,OAAO,EAvXTtM,EAAE,mBAuXyBA,EAAE,CAACsQ,UAAU,GAvXxCtQ,EAAE,mBAuXmDwD,eAAe,GAvXpExD,EAAE,mBAuX+E,aAAa,GAvX9FA,EAAE,mBAuX0HyL,iBAAiB,GAvX7IzL,EAAE,mBAuXwJA,EAAE,CAACM,YAAY,GAvXzKN,EAAE,mBAuXoLwL,wBAAwB;AAAA,CAA4D;AACjXc,OAAO,CAACiE,IAAI,kBAxX2FvQ,EAAE;EAAA,MAwXZsM,OAAO;EAAA;EAAA,oBAAkK,KAAK;EAAA;EAAA;IAAA;MAxXpKtM,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAwX+qB;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EAAk5B;AAC1qD;EAAA,mDAzXuGA,EAAE,mBAyXTsM,OAAO,EAAc,CAAC;IAC1G7B,IAAI,EAAEhK,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAE8F,QAAQ,EAAE,2BAA2B;MAAEC,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE,SAAS;MAAEC,MAAM,EAAE,CAAC,OAAO,CAAC;MAAEC,IAAI,EAAE;QACxG,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,sBAAsB;QAC/B,2BAA2B,EAAE,mCAAmC;QAChE,2BAA2B,EAAE,sBAAsB;QACnD,gCAAgC,EAAE,0BAA0B;QAC5D,iBAAiB,EAAE,oCAAoC;QACvD,yBAAyB,EAAE,QAAQ;QACnC,2BAA2B,EAAE;MACjC,CAAC;MAAEC,aAAa,EAAEnQ,iBAAiB,CAACoQ,IAAI;MAAEC,eAAe,EAAEpQ,uBAAuB,CAACqQ,MAAM;MAAEC,MAAM,EAAE,CAAC,oxBAAoxB;IAAE,CAAC;EACv4B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExG,IAAI,EAAEzK,EAAE,CAACsQ;IAAW,CAAC,EAAE;MAAE7F,IAAI,EAAEjH;IAAgB,CAAC,EAAE;MAAEiH,IAAI,EAAEtI,SAAS;MAAEyI,UAAU,EAAE,CAAC;QAClHH,IAAI,EAAE7J,SAAS;QACf8J,IAAI,EAAE,CAAC,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAEtI,SAAS;MAAEyI,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAErK,MAAM;QACZsK,IAAI,EAAE,CAACe,iBAAiB;MAC5B,CAAC;IAAE,CAAC,EAAE;MAAEhB,IAAI,EAAEzK,EAAE,CAACM;IAAa,CAAC,EAAE;MAAEmK,IAAI,EAAEtI,SAAS;MAAEyI,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEtK;MACV,CAAC,EAAE;QACCsK,IAAI,EAAErK,MAAM;QACZsK,IAAI,EAAE,CAACc,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEe,MAAM,EAAE,CAAC;MACrC9B,IAAI,EAAE5J;IACV,CAAC,CAAC;IAAE4L,OAAO,EAAE,CAAC;MACVhC,IAAI,EAAE5J;IACV,CAAC,CAAC;IAAEgM,OAAO,EAAE,CAAC;MACVpC,IAAI,EAAE5J;IACV,CAAC,CAAC;IAAEqM,QAAQ,EAAE,CAAC;MACXzC,IAAI,EAAE5J;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqQ,aAAa,CAAC;AAEpBA,aAAa,CAAC3G,IAAI;EAAA,iBAA6F2G,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBApaqFnR,EAAE;EAAA,MAoaOkR;AAAa,EAA6F;AAC1NA,aAAa,CAACE,IAAI,kBAraqFpR,EAAE;EAAA,UAqagCgB,eAAe,EAAEA,eAAe;AAAA,EAAI;AAC7K;EAAA,mDAtauGhB,EAAE,mBAsaTkR,aAAa,EAAc,CAAC;IAChHzG,IAAI,EAAE3J,QAAQ;IACd4J,IAAI,EAAE,CAAC;MACC2G,OAAO,EAAE,CAACrQ,eAAe,CAAC;MAC1BsQ,OAAO,EAAE,CAAChF,OAAO,EAAEtL,eAAe,CAAC;MACnCuQ,YAAY,EAAE,CAACjF,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpB,sBAAsB,EAAEL,8BAA8B,EAAEW,wBAAwB,EAAEC,iBAAiB,EAAEE,yBAAyB,EAAEW,OAAO,EAAE4E,aAAa,EAAE1N,eAAe,EAAEN,sCAAsC,EAAEF,kCAAkC,EAAEJ,2BAA2B,EAAEG,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}