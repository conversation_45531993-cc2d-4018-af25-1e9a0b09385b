.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-gradient-auth);
}

.register-card {
  width: 100%;
  max-width: 500px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  overflow: hidden;
}

.register-header {
  background: var(--theme-gradient-auth);
  color: white;
  padding: 24px;
  text-align: center;
}

.register-header .mat-card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 500;
}

.register-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.register-header .mat-card-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

.register-form {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.name-row {
  display: flex;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
}

.terms-section {
  margin: 8px 0;
}

.terms-section .mat-checkbox {
  font-size: 14px;
}

.terms-section a {
  color: #673ab7;
  text-decoration: none;
}

.terms-section a:hover {
  text-decoration: underline;
}

.register-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
}

.register-button .mat-icon {
  margin-right: 8px;
}

.register-actions {
  padding: 0 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.login-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.login-text {
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.mat-form-field {
  margin-bottom: 8px;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border-radius: 8px;
}

/* Error styling */
.mat-error {
  font-size: 12px;
  margin-top: 4px;
}

/* Loading spinner styling */
.mat-spinner {
  margin-right: 8px;
}

/* OAuth Buttons Styling */
.divider-container {
  display: flex;
  align-items: center;
  margin: 24px 0 16px;
  gap: 16px;
}

.divider-text {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  white-space: nowrap;
}

.oauth-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.oauth-button {
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.oauth-icon {
  width: 20px;
  height: 20px;
}

.google-button {
  border-color: #dadce0;
  color: #3c4043;
  background-color: #fff;
}

.google-button:hover:not([disabled]) {
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.facebook-button {
  border-color: #1877f2;
  color: #1877f2;
  background-color: #fff;
}

.facebook-button:hover:not([disabled]) {
  background-color: #f0f2f5;
}

.facebook-icon {
  color: #1877f2;
  font-size: 20px;
}

/* Responsive design */
@media (max-width: 600px) {
  .register-container {
    padding: 16px;
  }

  .register-card {
    max-width: 100%;
  }

  .register-form {
    padding: 20px;
  }

  .register-actions {
    padding: 0 20px 20px;
  }

  .name-row {
    flex-direction: column;
    gap: 0;
  }

  .half-width {
    width: 100%;
  }
}

/* Registration Type Selection */
.registration-type-selection {
  padding: 24px;
  text-align: center;
}

.registration-type-selection h3 {
  margin-bottom: 16px;
  color: var(--theme-text-primary);
  font-weight: 500;
}

.type-buttons-compact {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 12px;
}

.type-button-compact {
  height: 40px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  min-width: 140px;
}

.type-button-compact.selected {
  background-color: var(--theme-primary);
  color: white;
  border-color: var(--theme-primary);
}

.type-button-compact:not(.selected) {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  background-color: transparent;
}

.type-button-compact:not(.selected):hover {
  background-color: var(--theme-primary-light);
}

.type-button-compact .mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.type-description {
  margin: 0;
  font-size: 13px;
  color: var(--theme-text-secondary);
  opacity: 0.8;
  min-height: 20px;
}

/* Legacy styles for backward compatibility */
.type-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.type-button {
  flex: 1;
  max-width: 200px;
  height: auto;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.type-button.selected {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-light);
}

.type-button .mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.button-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.button-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
  text-align: center;
  line-height: 1.3;
}

/* Step Progress */
.step-progress {
  margin-top: 8px;
  height: 4px;
  border-radius: 2px;
}

/* Form Steps */
.form-step {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-step h3 {
  margin: 0 0 16px 0;
  color: var(--theme-text-primary);
  font-weight: 500;
  font-size: 18px;
}

.form-step h4 {
  margin: 16px 0 8px 0;
  color: var(--theme-text-secondary);
  font-weight: 500;
  font-size: 14px;
}

/* Additional row layouts */
.location-row,
.birth-info-row,
.business-address-row,
.rates-row {
  display: flex;
  gap: 16px;
}

/* Time input container */
.time-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.time-input-container input {
  flex: 1;
}

.time-input-container button {
  margin-left: 8px;
}

/* Navigation Buttons */
.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--theme-divider);
}

.spacer {
  flex: 1;
}

/* Skills Section Styling */
.skills-section {
  margin: 16px 0;
}

.skills-section h4 {
  margin: 0 0 12px 0;
  color: var(--theme-text-primary);
  font-weight: 500;
  font-size: 14px;
}

.skills-chips {
  margin-bottom: 12px;
}

.mat-chip-set {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mat-chip {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
  border-radius: 16px;
  font-size: 13px;
}

.mat-chip .mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Custom snackbar styles */
::ng-deep .success-snackbar {
  background-color: #4caf50;
  color: white;
}

::ng-deep .error-snackbar {
  background-color: #f44336;
  color: white;
}

/* Responsive design for oracle registration */
@media (max-width: 600px) {
  .type-buttons-compact {
    flex-direction: column;
    align-items: center;
  }

  .type-button-compact {
    min-width: 200px;
  }

  .type-buttons {
    flex-direction: column;
  }

  .type-button {
    max-width: 100%;
  }

  .location-row,
  .birth-info-row,
  .business-address-row,
  .rates-row {
    flex-direction: column;
    gap: 0;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .spacer {
    display: none;
  }
}
