{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion. */\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n  constructor() {\n    /** Emits when the state of the accordion changes */\n    this._stateChanges = new Subject();\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n    this._openCloseAllActions = new Subject();\n    /** A readonly id value to use for unique selection coordination. */\n    this.id = `cdk-accordion-${nextId$1++}`;\n    this._multi = false;\n  }\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  get multi() {\n    return this._multi;\n  }\n  set multi(multi) {\n    this._multi = coerceBooleanProperty(multi);\n  }\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll() {\n    if (this._multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n  /** Closes all enabled accordion items. */\n  closeAll() {\n    this._openCloseAllActions.next(false);\n  }\n  ngOnChanges(changes) {\n    this._stateChanges.next(changes);\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n}\nCdkAccordion.ɵfac = function CdkAccordion_Factory(t) {\n  return new (t || CdkAccordion)();\n};\nCdkAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAccordion,\n  selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n  inputs: {\n    multi: \"multi\"\n  },\n  exportAs: [\"cdkAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_ACCORDION,\n    useExisting: CdkAccordion\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion, [cdkAccordion]',\n      exportAs: 'cdkAccordion',\n      providers: [{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]\n    }]\n  }], null, {\n    multi: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n  /** Whether the AccordionItem is expanded. */\n  get expanded() {\n    return this._expanded;\n  }\n  set expanded(expanded) {\n    expanded = coerceBooleanProperty(expanded);\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the AccordionItem is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(disabled) {\n    this._disabled = coerceBooleanProperty(disabled);\n  }\n  constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n    this.accordion = accordion;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._expansionDispatcher = _expansionDispatcher;\n    /** Subscription to openAll/closeAll events. */\n    this._openCloseAllSubscription = Subscription.EMPTY;\n    /** Event emitted every time the AccordionItem is closed. */\n    this.closed = new EventEmitter();\n    /** Event emitted every time the AccordionItem is opened. */\n    this.opened = new EventEmitter();\n    /** Event emitted when the AccordionItem is destroyed. */\n    this.destroyed = new EventEmitter();\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n    this.expandedChange = new EventEmitter();\n    /** The unique AccordionItem id. */\n    this.id = `cdk-accordion-child-${nextId++}`;\n    this._expanded = false;\n    this._disabled = false;\n    /** Unregister function for _expansionDispatcher. */\n    this._removeUniqueSelectionListener = () => {};\n    this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n      if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n        this.expanded = false;\n      }\n    });\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n  /** Toggles the expanded state of the accordion item. */\n  toggle() {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n  /** Sets the expanded state of the accordion item to false. */\n  close() {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n  /** Sets the expanded state of the accordion item to true. */\n  open() {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n  _subscribeToOpenCloseAllActions() {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n}\nCdkAccordionItem.ɵfac = function CdkAccordionItem_Factory(t) {\n  return new (t || CdkAccordionItem)(i0.ɵɵdirectiveInject(CDK_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher));\n};\nCdkAccordionItem.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAccordionItem,\n  selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n  inputs: {\n    expanded: \"expanded\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    closed: \"closed\",\n    opened: \"opened\",\n    destroyed: \"destroyed\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"cdkAccordionItem\"],\n  features: [i0.ɵɵProvidersFeature([\n  // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n  // registering to the same accordion.\n  {\n    provide: CDK_ACCORDION,\n    useValue: undefined\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionItem, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion-item, [cdkAccordionItem]',\n      exportAs: 'cdkAccordionItem',\n      providers: [\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }]\n    }]\n  }], function () {\n    return [{\n      type: CdkAccordion,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_ACCORDION]\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.UniqueSelectionDispatcher\n    }];\n  }, {\n    closed: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }],\n    expanded: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkAccordionModule {}\nCdkAccordionModule.ɵfac = function CdkAccordionModule_Factory(t) {\n  return new (t || CdkAccordionModule)();\n};\nCdkAccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CdkAccordionModule\n});\nCdkAccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkAccordion, CdkAccordionItem],\n      declarations: [CdkAccordion, CdkAccordionItem]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkAccordion, CdkAccordionItem, CdkAccordionModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Input", "EventEmitter", "Optional", "Inject", "SkipSelf", "Output", "NgModule", "i1", "coerceBooleanProperty", "Subject", "Subscription", "nextId$1", "CDK_ACCORDION", "CdkAccordion", "constructor", "_stateChanges", "_openCloseAllActions", "id", "_multi", "multi", "openAll", "next", "closeAll", "ngOnChanges", "changes", "ngOnDestroy", "complete", "ɵfac", "ɵdir", "provide", "useExisting", "type", "args", "selector", "exportAs", "providers", "nextId", "CdkAccordionItem", "expanded", "_expanded", "expandedChange", "emit", "opened", "accordionId", "accordion", "_expansionDispatcher", "notify", "closed", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "_disabled", "_openCloseAllSubscription", "EMPTY", "destroyed", "_removeUniqueSelectionListener", "listen", "_subscribeToOpenCloseAllActions", "unsubscribe", "toggle", "close", "open", "subscribe", "ChangeDetectorRef", "UniqueSelectionDispatcher", "useValue", "undefined", "decorators", "CdkAccordionModule", "ɵmod", "ɵinj", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/cdk/fesm2020/accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion. */\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n    constructor() {\n        /** Emits when the state of the accordion changes */\n        this._stateChanges = new Subject();\n        /** Stream that emits true/false when openAll/closeAll is triggered. */\n        this._openCloseAllActions = new Subject();\n        /** A readonly id value to use for unique selection coordination. */\n        this.id = `cdk-accordion-${nextId$1++}`;\n        this._multi = false;\n    }\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    get multi() {\n        return this._multi;\n    }\n    set multi(multi) {\n        this._multi = coerceBooleanProperty(multi);\n    }\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n    openAll() {\n        if (this._multi) {\n            this._openCloseAllActions.next(true);\n        }\n    }\n    /** Closes all enabled accordion items. */\n    closeAll() {\n        this._openCloseAllActions.next(false);\n    }\n    ngOnChanges(changes) {\n        this._stateChanges.next(changes);\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._openCloseAllActions.complete();\n    }\n}\nCdkAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordion, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkAccordion.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkAccordion, selector: \"cdk-accordion, [cdkAccordion]\", inputs: { multi: \"multi\" }, providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }], exportAs: [\"cdkAccordion\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion, [cdkAccordion]',\n                    exportAs: 'cdkAccordion',\n                    providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }],\n                }]\n        }], propDecorators: { multi: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n    /** Whether the AccordionItem is expanded. */\n    get expanded() {\n        return this._expanded;\n    }\n    set expanded(expanded) {\n        expanded = coerceBooleanProperty(expanded);\n        // Only emit events and update the internal value if the value changes.\n        if (this._expanded !== expanded) {\n            this._expanded = expanded;\n            this.expandedChange.emit(expanded);\n            if (expanded) {\n                this.opened.emit();\n                /**\n                 * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n                 * the name value is the id of the accordion.\n                 */\n                const accordionId = this.accordion ? this.accordion.id : this.id;\n                this._expansionDispatcher.notify(this.id, accordionId);\n            }\n            else {\n                this.closed.emit();\n            }\n            // Ensures that the animation will run when the value is set outside of an `@Input`.\n            // This includes cases like the open, close and toggle methods.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the AccordionItem is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        this._disabled = coerceBooleanProperty(disabled);\n    }\n    constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n        this.accordion = accordion;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._expansionDispatcher = _expansionDispatcher;\n        /** Subscription to openAll/closeAll events. */\n        this._openCloseAllSubscription = Subscription.EMPTY;\n        /** Event emitted every time the AccordionItem is closed. */\n        this.closed = new EventEmitter();\n        /** Event emitted every time the AccordionItem is opened. */\n        this.opened = new EventEmitter();\n        /** Event emitted when the AccordionItem is destroyed. */\n        this.destroyed = new EventEmitter();\n        /**\n         * Emits whenever the expanded state of the accordion changes.\n         * Primarily used to facilitate two-way binding.\n         * @docs-private\n         */\n        this.expandedChange = new EventEmitter();\n        /** The unique AccordionItem id. */\n        this.id = `cdk-accordion-child-${nextId++}`;\n        this._expanded = false;\n        this._disabled = false;\n        /** Unregister function for _expansionDispatcher. */\n        this._removeUniqueSelectionListener = () => { };\n        this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n            if (this.accordion &&\n                !this.accordion.multi &&\n                this.accordion.id === accordionId &&\n                this.id !== id) {\n                this.expanded = false;\n            }\n        });\n        // When an accordion item is hosted in an accordion, subscribe to open/close events.\n        if (this.accordion) {\n            this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n        }\n    }\n    /** Emits an event for the accordion item being destroyed. */\n    ngOnDestroy() {\n        this.opened.complete();\n        this.closed.complete();\n        this.destroyed.emit();\n        this.destroyed.complete();\n        this._removeUniqueSelectionListener();\n        this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n    toggle() {\n        if (!this.disabled) {\n            this.expanded = !this.expanded;\n        }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n    close() {\n        if (!this.disabled) {\n            this.expanded = false;\n        }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n    open() {\n        if (!this.disabled) {\n            this.expanded = true;\n        }\n    }\n    _subscribeToOpenCloseAllActions() {\n        return this.accordion._openCloseAllActions.subscribe(expanded => {\n            // Only change expanded state if item is enabled\n            if (!this.disabled) {\n                this.expanded = expanded;\n            }\n        });\n    }\n}\nCdkAccordionItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionItem, deps: [{ token: CDK_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }], target: i0.ɵɵFactoryTarget.Directive });\nCdkAccordionItem.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkAccordionItem, selector: \"cdk-accordion-item, [cdkAccordionItem]\", inputs: { expanded: \"expanded\", disabled: \"disabled\" }, outputs: { closed: \"closed\", opened: \"opened\", destroyed: \"destroyed\", expandedChange: \"expandedChange\" }, providers: [\n        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n        // registering to the same accordion.\n        { provide: CDK_ACCORDION, useValue: undefined },\n    ], exportAs: [\"cdkAccordionItem\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionItem, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion-item, [cdkAccordionItem]',\n                    exportAs: 'cdkAccordionItem',\n                    providers: [\n                        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n                        // registering to the same accordion.\n                        { provide: CDK_ACCORDION, useValue: undefined },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: CdkAccordion, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_ACCORDION]\n                }, {\n                    type: SkipSelf\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }]; }, propDecorators: { closed: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], expandedChange: [{\n                type: Output\n            }], expanded: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkAccordionModule {\n}\nCdkAccordionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCdkAccordionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionModule, declarations: [CdkAccordion, CdkAccordionItem], exports: [CdkAccordion, CdkAccordionItem] });\nCdkAccordionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkAccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkAccordion, CdkAccordionItem],\n                    declarations: [CdkAccordion, CdkAccordionItem],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC5H,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAId,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA,MAAMe,YAAY,CAAC;EACfC,WAAW,GAAG;IACV;IACA,IAAI,CAACC,aAAa,GAAG,IAAIN,OAAO,EAAE;IAClC;IACA,IAAI,CAACO,oBAAoB,GAAG,IAAIP,OAAO,EAAE;IACzC;IACA,IAAI,CAACQ,EAAE,GAAI,iBAAgBN,QAAQ,EAAG,EAAC;IACvC,IAAI,CAACO,MAAM,GAAG,KAAK;EACvB;EACA;EACA,IAAIC,KAAK,GAAG;IACR,OAAO,IAAI,CAACD,MAAM;EACtB;EACA,IAAIC,KAAK,CAACA,KAAK,EAAE;IACb,IAAI,CAACD,MAAM,GAAGV,qBAAqB,CAACW,KAAK,CAAC;EAC9C;EACA;EACAC,OAAO,GAAG;IACN,IAAI,IAAI,CAACF,MAAM,EAAE;MACb,IAAI,CAACF,oBAAoB,CAACK,IAAI,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACAC,QAAQ,GAAG;IACP,IAAI,CAACN,oBAAoB,CAACK,IAAI,CAAC,KAAK,CAAC;EACzC;EACAE,WAAW,CAACC,OAAO,EAAE;IACjB,IAAI,CAACT,aAAa,CAACM,IAAI,CAACG,OAAO,CAAC;EACpC;EACAC,WAAW,GAAG;IACV,IAAI,CAACV,aAAa,CAACW,QAAQ,EAAE;IAC7B,IAAI,CAACV,oBAAoB,CAACU,QAAQ,EAAE;EACxC;AACJ;AACAb,YAAY,CAACc,IAAI;EAAA,iBAA6Fd,YAAY;AAAA,CAAmD;AAC7KA,YAAY,CAACe,IAAI,kBADmF/B,EAAE;EAAA,MACJgB,YAAY;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WADVhB,EAAE,oBAC4F,CAAC;IAAEgC,OAAO,EAAEjB,aAAa;IAAEkB,WAAW,EAAEjB;EAAa,CAAC,CAAC,GADrJhB,EAAE;AAAA,EACqN;AAC3T;EAAA,mDAFoGA,EAAE,mBAENgB,YAAY,EAAc,CAAC;IAC/GkB,IAAI,EAAEhC,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEjB,aAAa;QAAEkB,WAAW,EAAEjB;MAAa,CAAC;IACrE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEM,KAAK,EAAE,CAAC;MACtBY,IAAI,EAAE/B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACA,QAAQ,EAAE;IACnBA,QAAQ,GAAG9B,qBAAqB,CAAC8B,QAAQ,CAAC;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,KAAKD,QAAQ,EAAE;MAC7B,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,IAAI,CAACE,cAAc,CAACC,IAAI,CAACH,QAAQ,CAAC;MAClC,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACI,MAAM,CAACD,IAAI,EAAE;QAClB;AAChB;AACA;AACA;QACgB,MAAME,WAAW,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC3B,EAAE,GAAG,IAAI,CAACA,EAAE;QAChE,IAAI,CAAC4B,oBAAoB,CAACC,MAAM,CAAC,IAAI,CAAC7B,EAAE,EAAE0B,WAAW,CAAC;MAC1D,CAAC,MACI;QACD,IAAI,CAACI,MAAM,CAACN,IAAI,EAAE;MACtB;MACA;MACA;MACA,IAAI,CAACO,kBAAkB,CAACC,YAAY,EAAE;IAC1C;EACJ;EACA;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG3C,qBAAqB,CAAC0C,QAAQ,CAAC;EACpD;EACApC,WAAW,CAAC8B,SAAS,EAAEI,kBAAkB,EAAEH,oBAAoB,EAAE;IAC7D,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACH,oBAAoB,GAAGA,oBAAoB;IAChD;IACA,IAAI,CAACO,yBAAyB,GAAG1C,YAAY,CAAC2C,KAAK;IACnD;IACA,IAAI,CAACN,MAAM,GAAG,IAAI9C,YAAY,EAAE;IAChC;IACA,IAAI,CAACyC,MAAM,GAAG,IAAIzC,YAAY,EAAE;IAChC;IACA,IAAI,CAACqD,SAAS,GAAG,IAAIrD,YAAY,EAAE;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACuC,cAAc,GAAG,IAAIvC,YAAY,EAAE;IACxC;IACA,IAAI,CAACgB,EAAE,GAAI,uBAAsBmB,MAAM,EAAG,EAAC;IAC3C,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACI,8BAA8B,GAAG,MAAM,CAAE,CAAC;IAC/C,IAAI,CAACA,8BAA8B,GAAGV,oBAAoB,CAACW,MAAM,CAAC,CAACvC,EAAE,EAAE0B,WAAW,KAAK;MACnF,IAAI,IAAI,CAACC,SAAS,IACd,CAAC,IAAI,CAACA,SAAS,CAACzB,KAAK,IACrB,IAAI,CAACyB,SAAS,CAAC3B,EAAE,KAAK0B,WAAW,IACjC,IAAI,CAAC1B,EAAE,KAAKA,EAAE,EAAE;QAChB,IAAI,CAACqB,QAAQ,GAAG,KAAK;MACzB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,IAAI,CAACM,SAAS,EAAE;MAChB,IAAI,CAACQ,yBAAyB,GAAG,IAAI,CAACK,+BAA+B,EAAE;IAC3E;EACJ;EACA;EACAhC,WAAW,GAAG;IACV,IAAI,CAACiB,MAAM,CAAChB,QAAQ,EAAE;IACtB,IAAI,CAACqB,MAAM,CAACrB,QAAQ,EAAE;IACtB,IAAI,CAAC4B,SAAS,CAACb,IAAI,EAAE;IACrB,IAAI,CAACa,SAAS,CAAC5B,QAAQ,EAAE;IACzB,IAAI,CAAC6B,8BAA8B,EAAE;IACrC,IAAI,CAACH,yBAAyB,CAACM,WAAW,EAAE;EAChD;EACA;EACAC,MAAM,GAAG;IACL,IAAI,CAAC,IAAI,CAACT,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAClC;EACJ;EACA;EACAsB,KAAK,GAAG;IACJ,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,KAAK;IACzB;EACJ;EACA;EACAuB,IAAI,GAAG;IACH,IAAI,CAAC,IAAI,CAACX,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACxB;EACJ;EACAmB,+BAA+B,GAAG;IAC9B,OAAO,IAAI,CAACb,SAAS,CAAC5B,oBAAoB,CAAC8C,SAAS,CAACxB,QAAQ,IAAI;MAC7D;MACA,IAAI,CAAC,IAAI,CAACY,QAAQ,EAAE;QAChB,IAAI,CAACZ,QAAQ,GAAGA,QAAQ;MAC5B;IACJ,CAAC,CAAC;EACN;AACJ;AACAD,gBAAgB,CAACV,IAAI;EAAA,iBAA6FU,gBAAgB,EAtI9BxC,EAAE,mBAsI8Ce,aAAa,OAtI7Df,EAAE,mBAsIwGA,EAAE,CAACkE,iBAAiB,GAtI9HlE,EAAE,mBAsIyIU,EAAE,CAACyD,yBAAyB;AAAA,CAA4C;AACvT3B,gBAAgB,CAACT,IAAI,kBAvI+E/B,EAAE;EAAA,MAuIAwC,gBAAgB;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAvIlBxC,EAAE,oBAuIoP;EAClV;EACA;EACA;IAAEgC,OAAO,EAAEjB,aAAa;IAAEqD,QAAQ,EAAEC;EAAU,CAAC,CAClD;AAAA,EAAiD;AACtD;EAAA,mDA5IoGrE,EAAE,mBA4INwC,gBAAgB,EAAc,CAAC;IACnHN,IAAI,EAAEhC,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wCAAwC;MAClDC,QAAQ,EAAE,kBAAkB;MAC5BC,SAAS,EAAE;MACP;MACA;MACA;QAAEN,OAAO,EAAEjB,aAAa;QAAEqD,QAAQ,EAAEC;MAAU,CAAC;IAEvD,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnC,IAAI,EAAElB,YAAY;MAAEsD,UAAU,EAAE,CAAC;QACjEpC,IAAI,EAAE7B;MACV,CAAC,EAAE;QACC6B,IAAI,EAAE5B,MAAM;QACZ6B,IAAI,EAAE,CAACpB,aAAa;MACxB,CAAC,EAAE;QACCmB,IAAI,EAAE3B;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2B,IAAI,EAAElC,EAAE,CAACkE;IAAkB,CAAC,EAAE;MAAEhC,IAAI,EAAExB,EAAE,CAACyD;IAA0B,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEjB,MAAM,EAAE,CAAC;MAC7GhB,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEqC,MAAM,EAAE,CAAC;MACTX,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEiD,SAAS,EAAE,CAAC;MACZvB,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEmC,cAAc,EAAE,CAAC;MACjBT,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEiC,QAAQ,EAAE,CAAC;MACXP,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEkD,QAAQ,EAAE,CAAC;MACXnB,IAAI,EAAE/B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,kBAAkB,CAAC;AAEzBA,kBAAkB,CAACzC,IAAI;EAAA,iBAA6FyC,kBAAkB;AAAA,CAAkD;AACxLA,kBAAkB,CAACC,IAAI,kBAtL6ExE,EAAE;EAAA,MAsLeuE;AAAkB,EAA8F;AACrOA,kBAAkB,CAACE,IAAI,kBAvL6EzE,EAAE,qBAuLoC;AAC1I;EAAA,mDAxLoGA,EAAE,mBAwLNuE,kBAAkB,EAAc,CAAC;IACrHrC,IAAI,EAAEzB,QAAQ;IACd0B,IAAI,EAAE,CAAC;MACCuC,OAAO,EAAE,CAAC1D,YAAY,EAAEwB,gBAAgB,CAAC;MACzCmC,YAAY,EAAE,CAAC3D,YAAY,EAAEwB,gBAAgB;IACjD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASxB,YAAY,EAAEwB,gBAAgB,EAAE+B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}