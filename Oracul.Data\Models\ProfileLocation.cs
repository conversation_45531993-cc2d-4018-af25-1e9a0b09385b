using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Profile location entity for user geographical information
    /// </summary>
    public class ProfileLocation : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [MaxLength(100)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? State { get; set; }

        [MaxLength(100)]
        public string? Country { get; set; }

        [MaxLength(200)]
        public string? DisplayLocation { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
    }
}
