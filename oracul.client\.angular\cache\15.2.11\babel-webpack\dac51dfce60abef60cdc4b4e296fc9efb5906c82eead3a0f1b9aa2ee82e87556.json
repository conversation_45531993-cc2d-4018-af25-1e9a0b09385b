{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/theme/theme.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./shared/navigation/navigation.component\";\nexport class AppComponent {\n  constructor(themeService) {\n    this.themeService = themeService;\n    this.title = 'oracul.client';\n  }\n  ngOnInit() {\n    // Initialize theme\n    this.themeService.getCurrentTheme();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"app-container\"], [1, \"content-container\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navigation\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i2.RouterOutlet, i3.NavigationComponent],\n      styles: [\"[_nghost-%COMP%] {\\r\\n  display: block;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.app-container[_ngcontent-%COMP%] {\\r\\n  min-height: 100vh;\\r\\n  background-color: #f5f5f5;\\r\\n}\\r\\n\\r\\n\\r\\n.content-container[_ngcontent-%COMP%] {\\r\\n  padding-top: 64px; \\r\\n}\\r\\n\\r\\n.toolbar-spacer[_ngcontent-%COMP%] {\\r\\n  flex: 1 1 auto;\\r\\n}\\r\\n\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  margin-right: 16px;\\r\\n  font-size: 14px;\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n}\\r\\n\\r\\n\\r\\n.submenu-arrow[_ngcontent-%COMP%] {\\r\\n  margin-left: auto;\\r\\n  font-size: 18px;\\r\\n}\\r\\n\\r\\n.theme-menu-content[_ngcontent-%COMP%] {\\r\\n  min-width: 400px;\\r\\n  max-width: 500px;\\r\\n}\\r\\n\\r\\n\\r\\n  .theme-menu .mat-menu-panel {\\r\\n  max-width: 600px !important;\\r\\n  min-width: 400px !important;\\r\\n}\\r\\n\\r\\n  .user-menu .mat-menu-item {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n  .user-menu .mat-menu-item .mat-icon:first-child {\\r\\n  margin-right: 0;\\r\\n}\\r\\n\\r\\n.content-container[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  margin: 40px 0;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  overflow-x: auto;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n.temperature[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #ff6b35;\\r\\n}\\r\\n\\r\\n.summary[_ngcontent-%COMP%] {\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .content-container[_ngcontent-%COMP%] {\\r\\n    padding: 10px;\\r\\n  }\\r\\n\\r\\n  .weather-table[_ngcontent-%COMP%] {\\r\\n    font-size: 14px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\nmat-card[_ngcontent-%COMP%] {\\r\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\nmat-card[_ngcontent-%COMP%]:hover {\\r\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%] {\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;AAQA,OAAM,MAAOA,YAAY;EAGvBC,YACUC,YAA0B;IAA1B,iBAAY,GAAZA,YAAY;IAHtB,UAAK,GAAG,eAAe;EAIpB;EAEHC,QAAQ;IACN;IACA,IAAI,CAACD,YAAY,CAACE,eAAe,EAAE;EACrC;;;uBAVWJ,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAK;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCPzBC,8BAA2B;UAEzBA,iCAAiC;UAGjCA,8BAA+B;UAE7BA,gCAA+B;UACjCA,iBAAM", "names": ["AppComponent", "constructor", "themeService", "ngOnInit", "getCurrentTheme", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'oracul.client';\r\n\r\n  constructor(\r\n    private themeService: ThemeService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Initialize theme\r\n    this.themeService.getCurrentTheme();\r\n  }\r\n}\r\n", "<!-- Application Layout with Unified Navigation -->\r\n<div class=\"app-container\">\r\n  <!-- Unified Navigation Menu - Always Visible -->\r\n  <app-navigation></app-navigation>\r\n\r\n  <!-- Main Content Area -->\r\n  <div class=\"content-container\">\r\n    <!-- Router Outlet for All Components -->\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}