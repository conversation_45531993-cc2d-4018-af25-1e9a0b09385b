using System.ComponentModel.DataAnnotations;

namespace Oracul.Server.Models
{
    // Authentication Request DTOs
    public class LoginRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    public class RegisterRequest
    {
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        [MaxLength(100)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? PhoneNumber { get; set; }

        public bool AcceptTerms { get; set; } = false;
    }

    public class OracleRegisterRequest : RegisterRequest
    {
        // Professional Information
        [Required]
        [MaxLength(200)]
        public string ProfessionalTitle { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Headline { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Summary { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string PrimarySpecialization { get; set; } = string.Empty;

        [Range(0, 50)]
        public int YearsOfExperience { get; set; }

        // Location Information
        [Required]
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? State { get; set; }

        [Required]
        [MaxLength(100)]
        public string Country { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? DisplayLocation { get; set; }

        // Oracle-Specific Information
        [Required]
        public DateTime BirthDate { get; set; }

        public TimeSpan? BirthTime { get; set; }

        [Required]
        [MaxLength(200)]
        public string BirthLocation { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? AstrologicalSign { get; set; }

        public List<string> OracleTypes { get; set; } = new();
        public List<string> LanguagesSpoken { get; set; } = new();
        public List<string> Skills { get; set; } = new();

        // Contact & Business Information
        [MaxLength(500)]
        public string? Website { get; set; }

        [MaxLength(500)]
        public string? PortfolioUrl { get; set; }

        public BusinessAddressDto? BusinessAddress { get; set; }

        // Consultation Rates
        public ConsultationRatesDto? ConsultationRates { get; set; }
    }



    public class ConsultationRatesDto
    {
        [Range(0, 10000)]
        public decimal? HourlyRate { get; set; }

        [Range(0, 10000)]
        public decimal? SessionRate { get; set; }

        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "BGN";
    }

    public class ForgotPasswordRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
    }

    public class ResetPasswordRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Token { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        [MaxLength(100)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class ChangePasswordRequest
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        [MaxLength(100)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class RefreshTokenRequest
    {
        [Required]
        public string RefreshToken { get; set; } = string.Empty;
    }

    public class SocialLoginRequest
    {
        [Required]
        public string Provider { get; set; } = string.Empty; // "google" or "facebook"

        [Required]
        public string AccessToken { get; set; } = string.Empty;

        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ProfilePictureUrl { get; set; }
        public string? ProviderId { get; set; }
    }

    // Authentication Response DTOs
    public class AuthResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public UserInfo? User { get; set; }
    }

    public class UserInfo
    {
        public int Id { get; set; }
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? ProfilePictureUrl { get; set; }
        public bool EmailConfirmed { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public List<string> Roles { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
    }

    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    // JWT Configuration
    public class JwtSettings
    {
        public string SecretKey { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int AccessTokenExpirationMinutes { get; set; } = 15;
        public int RefreshTokenExpirationDays { get; set; } = 7;
    }

    // Email Configuration
    public class EmailSettings
    {
        public string SmtpHost { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = string.Empty;
        public string SmtpPassword { get; set; } = string.Empty;
        public bool EnableSsl { get; set; } = true;
        public string FromEmail { get; set; } = string.Empty;
        public string FromName { get; set; } = string.Empty;
    }

    // Social Authentication Settings
    public class GoogleAuthSettings
    {
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
    }

    public class FacebookAuthSettings
    {
        public string AppId { get; set; } = string.Empty;
        public string AppSecret { get; set; } = string.Empty;
    }
}
