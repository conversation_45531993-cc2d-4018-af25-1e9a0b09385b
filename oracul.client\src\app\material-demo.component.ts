import { Component } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-material-demo',
  template: `
    <mat-card class="demo-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>palette</mat-icon>
          Material Design Components Demo
        </mat-card-title>
        <mat-card-subtitle>
          Explore various Angular Material components
        </mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <!-- Buttons Section -->
        <div class="demo-section">
          <h3>Buttons</h3>
          <div class="button-row">
            <button mat-button>Basic</button>
            <button mat-raised-button color="primary">Primary</button>
            <button mat-raised-button color="accent">Accent</button>
            <button mat-raised-button color="warn">Warn</button>
            <button mat-fab color="primary">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-mini-fab color="accent">
              <mat-icon>edit</mat-icon>
            </button>
          </div>
        </div>

        <!-- Form Fields Section -->
        <div class="demo-section">
          <h3>Form Fields</h3>
          <div class="form-row">
            <mat-form-field appearance="fill">
              <mat-label>Name</mat-label>
              <input matInput placeholder="Enter your name">
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput placeholder="Enter your email" type="email">
              <mat-icon matSuffix>email</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="fill">
              <mat-label>Choose an option</mat-label>
              <mat-select>
                <mat-option value="option1">Option 1</mat-option>
                <mat-option value="option2">Option 2</mat-option>
                <mat-option value="option3">Option 3</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <!-- Checkboxes and Toggles -->
        <div class="demo-section">
          <h3>Checkboxes & Toggles</h3>
          <div class="checkbox-row">
            <mat-checkbox>Check me!</mat-checkbox>
            <mat-checkbox color="primary" checked>Primary checkbox</mat-checkbox>
            <mat-checkbox color="warn">Warning checkbox</mat-checkbox>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="demo-section">
          <h3>Actions</h3>
          <div class="action-row">
            <button mat-raised-button color="primary" (click)="showSnackbar()">
              <mat-icon>notifications</mat-icon>
              Show Snackbar
            </button>
            <button mat-stroked-button color="accent">
              <mat-icon>download</mat-icon>
              Download
            </button>
            <button mat-flat-button color="warn">
              <mat-icon>delete</mat-icon>
              Delete
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .demo-card {
      margin: 20px 0;
    }
    
    .demo-section {
      margin: 24px 0;
      padding: 16px 0;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .demo-section:last-child {
      border-bottom: none;
    }
    
    .demo-section h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-weight: 500;
    }
    
    .button-row, .form-row, .checkbox-row, .action-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
    }
    
    .form-row {
      flex-direction: column;
      align-items: stretch;
    }
    
    @media (min-width: 768px) {
      .form-row {
        flex-direction: row;
        align-items: center;
      }
      
      .form-row mat-form-field {
        flex: 1;
      }
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class MaterialDemoComponent {
  constructor(private snackBar: MatSnackBar) {}

  showSnackbar() {
    this.snackBar.open('Hello from Angular Material! 🎉', 'Close', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom',
    });
  }
}
