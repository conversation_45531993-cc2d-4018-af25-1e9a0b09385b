{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:7027/api',\n  oauth: {\n    google: {\n      clientId: 'your-google-client-id.apps.googleusercontent.com'\n    },\n    facebook: {\n      appId: 'your-facebook-app-id'\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,4BAA4B;EACpCC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,QAAQ,EAAE;KACX;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE;;;CAGZ", "names": ["environment", "production", "apiUrl", "o<PERSON>h", "google", "clientId", "facebook", "appId"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\r\n  production: false,\r\n  apiUrl: 'https://localhost:7027/api',\r\n  oauth: {\r\n    google: {\r\n      clientId: 'your-google-client-id.apps.googleusercontent.com'\r\n    },\r\n    facebook: {\r\n      appId: 'your-facebook-app-id'\r\n    }\r\n  }\r\n};\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}