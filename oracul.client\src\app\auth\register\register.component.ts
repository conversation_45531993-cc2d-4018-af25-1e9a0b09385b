import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../services/auth.service';
import { OAuthService, OAuthUser } from '../services/oauth.service';
import { RegisterRequest, OracleRegisterRequest, OAuthLoginRequest } from '../models/auth.models';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  hideConfirmPassword = true;
  returnUrl = '/';

  // Registration type
  registrationType: 'general' | 'oracle' = 'general';

  // Time format preference
  timeFormat24h = true;

  // Helper method for template
  isOracleRegistration(): boolean {
    return this.registrationType === 'oracle';
  }
  currentStep = 1;
  totalSteps = 1;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private oauthService: OAuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.registerForm = this.createGeneralForm();
  }

  ngOnInit(): void {
    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';

    // Redirect if already logged in (temporarily disabled for testing)
    // this.authService.isAuthenticated$.subscribe(isAuth => {
    //   if (isAuth) {
    //     this.router.navigate([this.returnUrl]);
    //   }
    // });
  }

  // Form creation methods
  private createGeneralForm(): FormGroup {
    return this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      acceptTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
  }

  private createOracleForm(): FormGroup {
    return this.formBuilder.group({
      // Basic Information (Step 1)
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],

      // Professional Information (Step 2)
      professionalTitle: ['', [Validators.required]],
      headline: ['', [Validators.required, Validators.maxLength(220)]],
      summary: ['', [Validators.required, Validators.maxLength(2000)]],
      primarySpecialization: ['', [Validators.required]],
      yearsOfExperience: [0, [Validators.required, Validators.min(0)]],

      // Location Information (Step 3)
      city: ['', [Validators.required]],
      state: [''],
      country: ['', [Validators.required]],
      displayLocation: [''],

      // Oracle-Specific Information (Step 4)
      birthDate: ['', [Validators.required]],
      birthTime: [''],
      birthLocation: ['', [Validators.required]],
      astrologicalSign: [''],
      oracleTypes: [[], [Validators.required]],
      languagesSpoken: [['Български'], [Validators.required]],
      skills: [[], [Validators.required]],

      // Contact & Business Information (Step 5)
      website: [''],
      portfolioUrl: [''],
      businessStreet: [''],
      businessCity: [''],
      businessState: [''],
      businessPostalCode: [''],
      businessCountry: [''],
      isBusinessAddressPublic: [false],

      // Consultation Rates (Step 6)
      hourlyRate: [0],
      sessionRate: [0],
      currency: ['BGN'],

      acceptTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
  }

  // Registration type switching
  setRegistrationType(type: 'general' | 'oracle'): void {
    this.registrationType = type;
    if (type === 'oracle') {
      this.registerForm = this.createOracleForm();
      this.totalSteps = 6;
      this.currentStep = 1;
    } else {
      this.registerForm = this.createGeneralForm();
      this.totalSteps = 1;
      this.currentStep = 1;
    }
  }

  // Step navigation for oracle registration
  nextStep(): void {
    if (this.currentStep < this.totalSteps && this.isCurrentStepValid()) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  isCurrentStepValid(): boolean {
    if (this.registrationType === 'general') {
      return this.registerForm.valid;
    }

    // Validate current step for oracle registration
    const step1Fields = ['firstName', 'lastName', 'email', 'password', 'confirmPassword'];
    const step2Fields = ['professionalTitle', 'headline', 'summary', 'primarySpecialization', 'yearsOfExperience'];
    const step3Fields = ['city', 'country'];
    const step4Fields = ['birthDate', 'birthLocation', 'oracleTypes', 'languagesSpoken', 'skills'];
    const step5Fields: string[] = []; // All optional
    const step6Fields = ['currency', 'acceptTerms'];

    let fieldsToValidate: string[] = [];
    switch (this.currentStep) {
      case 1: fieldsToValidate = step1Fields; break;
      case 2: fieldsToValidate = step2Fields; break;
      case 3: fieldsToValidate = step3Fields; break;
      case 4: fieldsToValidate = step4Fields; break;
      case 5: fieldsToValidate = step5Fields; break;
      case 6: fieldsToValidate = step6Fields; break;
    }

    return fieldsToValidate.every(field => {
      const control = this.registerForm.get(field);
      return control ? control.valid : true;
    });
  }

  // Get step title in Bulgarian
  getStepTitle(): string {
    if (this.registrationType === 'general') {
      return 'Създаване на акаунт';
    }

    switch (this.currentStep) {
      case 1: return 'Основна информация';
      case 2: return 'Професионална информация';
      case 3: return 'Местоположение';
      case 4: return 'Оракулска информация';
      case 5: return 'Контакти и бизнес';
      case 6: return 'Тарифи и условия';
      default: return 'Регистрация';
    }
  }

  // Helper methods for oracle registration
  getSpecializationOptions(): string[] {
    return [
      'Астрология',
      'Таро',
      'Кристални лечения',
      'Нумерология',
      'Хиромантия',
      'Рунология',
      'Медиумизъм',
      'Енергийно лечение',
      'Аура четене',
      'Духовно консултиране'
    ];
  }

  getOracleTypeOptions(): string[] {
    return [
      'Таро карти',
      'Астрологични карти',
      'Кристали',
      'Руни',
      'Нумерология',
      'Хиромантия',
      'Медитация',
      'Енергийна работа',
      'Аура четене',
      'Духовно водачество'
    ];
  }

  getLanguageOptions(): string[] {
    return [
      'Български',
      'Английски',
      'Руски',
      'Немски',
      'Френски',
      'Испански',
      'Италиански',
      'Турски'
    ];
  }

  getAstrologicalSigns(): string[] {
    return [
      'Овен', 'Телец', 'Близнаци', 'Рак', 'Лъв', 'Дева',
      'Везни', 'Скорпион', 'Стрелец', 'Козирог', 'Водолей', 'Риби'
    ];
  }

  // Time format toggle
  toggleTimeFormat(): void {
    this.timeFormat24h = !this.timeFormat24h;
  }

  // Skill management methods
  addSkill(event: KeyboardEvent, input: HTMLInputElement): void {
    event.preventDefault();
    const value = input.value.trim();

    if (value) {
      const currentSkills = this.registerForm.get('skills')?.value || [];
      if (!currentSkills.includes(value)) {
        const updatedSkills = [...currentSkills, value];
        this.registerForm.get('skills')?.setValue(updatedSkills);
        input.value = '';
      }
    }
  }

  addSkillFromInput(input: HTMLInputElement): void {
    const value = input.value.trim();

    if (value) {
      const currentSkills = this.registerForm.get('skills')?.value || [];
      if (!currentSkills.includes(value)) {
        const updatedSkills = [...currentSkills, value];
        this.registerForm.get('skills')?.setValue(updatedSkills);
        input.value = '';
      }
    }
  }

  removeSkill(index: number): void {
    const currentSkills = this.registerForm.get('skills')?.value || [];
    currentSkills.splice(index, 1);
    this.registerForm.get('skills')?.setValue(currentSkills);
  }

  passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { 'passwordMismatch': true };
    }
    return null;
  }

  onSubmit(): void {
    if (this.registrationType === 'oracle' && this.currentStep < this.totalSteps) {
      // For oracle registration, move to next step if not on final step
      this.nextStep();
      return;
    }

    if (this.registerForm.valid) {
      this.isLoading = true;

      if (this.registrationType === 'general') {
        this.submitGeneralRegistration();
      } else {
        this.submitOracleRegistration();
      }
    } else {
      this.markFormGroupTouched();
      this.snackBar.open('Моля, попълнете всички задължителни полета', 'Затвори', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  private submitGeneralRegistration(): void {
    const registerRequest: RegisterRequest = {
      firstName: this.registerForm.value.firstName,
      lastName: this.registerForm.value.lastName,
      email: this.registerForm.value.email,
      phoneNumber: this.registerForm.value.phoneNumber || undefined,
      password: this.registerForm.value.password,
      confirmPassword: this.registerForm.value.confirmPassword,
      acceptTerms: this.registerForm.value.acceptTerms
    };

    this.authService.register(registerRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.snackBar.open('Регистрацията е успешна! Добре дошли в Оракул!', 'Затвори', {
            duration: 5000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate([this.returnUrl]);
        } else {
          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private submitOracleRegistration(): void {
    const formValue = this.registerForm.value;
    const oracleRequest: OracleRegisterRequest = {
      // Basic information
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      phoneNumber: formValue.phoneNumber || undefined,
      password: formValue.password,
      confirmPassword: formValue.confirmPassword,
      acceptTerms: formValue.acceptTerms,

      // Professional information
      professionalTitle: formValue.professionalTitle,
      headline: formValue.headline,
      summary: formValue.summary,
      primarySpecialization: formValue.primarySpecialization,
      yearsOfExperience: formValue.yearsOfExperience,

      // Location information
      city: formValue.city,
      state: formValue.state,
      country: formValue.country,
      displayLocation: formValue.displayLocation || `${formValue.city}, ${formValue.country}`,

      // Oracle-specific information
      birthDate: formValue.birthDate,
      birthTime: formValue.birthTime,
      birthLocation: formValue.birthLocation,
      astrologicalSign: formValue.astrologicalSign,
      oracleTypes: formValue.oracleTypes,
      languagesSpoken: formValue.languagesSpoken,
      skills: formValue.skills,

      // Contact & business information
      website: formValue.website,
      portfolioUrl: formValue.portfolioUrl,
      businessAddress: {
        street: formValue.businessStreet,
        city: formValue.businessCity,
        state: formValue.businessState,
        postalCode: formValue.businessPostalCode,
        country: formValue.businessCountry,
        isPublic: formValue.isBusinessAddressPublic
      },

      // Consultation rates
      consultationRates: {
        hourlyRate: formValue.hourlyRate,
        sessionRate: formValue.sessionRate,
        currency: formValue.currency
      }
    };

    // TODO: Implement oracle registration API call
    // For now, use the general registration and then create profile
    this.authService.register(oracleRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.snackBar.open('Регистрацията като оракул е успешна! Добре дошли!', 'Затвори', {
            duration: 5000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/profile/edit']); // Redirect to profile completion
        } else {
          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.registerForm.controls).forEach(key => {
      const control = this.registerForm.get(key);
      control?.markAsTouched();
    });
  }

  getFirstNameErrorMessage(): string {
    const control = this.registerForm.get('firstName');
    if (control?.hasError('required')) {
      return 'Първото име е задължително';
    }
    if (control?.hasError('minlength')) {
      return 'Първото име трябва да бъде поне 2 символа';
    }
    return '';
  }

  getLastNameErrorMessage(): string {
    const control = this.registerForm.get('lastName');
    if (control?.hasError('required')) {
      return 'Фамилното име е задължително';
    }
    if (control?.hasError('minlength')) {
      return 'Фамилното име трябва да бъде поне 2 символа';
    }
    return '';
  }

  getEmailErrorMessage(): string {
    const control = this.registerForm.get('email');
    if (control?.hasError('required')) {
      return 'Имейлът е задължителен';
    }
    if (control?.hasError('email')) {
      return 'Моля, въведете валиден имейл адрес';
    }
    return '';
  }

  getPasswordErrorMessage(): string {
    const control = this.registerForm.get('password');
    if (control?.hasError('required')) {
      return 'Паролата е задължителна';
    }
    if (control?.hasError('minlength')) {
      return 'Паролата трябва да бъде поне 6 символа';
    }
    return '';
  }

  getConfirmPasswordErrorMessage(): string {
    const control = this.registerForm.get('confirmPassword');
    if (control?.hasError('required')) {
      return 'Моля, потвърдете паролата си';
    }
    if (this.registerForm.hasError('passwordMismatch')) {
      return 'Паролите не съвпадат';
    }
    return '';
  }

  // Additional error message methods for oracle fields
  getFieldErrorMessage(fieldName: string): string {
    const control = this.registerForm.get(fieldName);
    if (!control) return '';

    if (control.hasError('required')) {
      return 'Това поле е задължително';
    }
    if (control.hasError('minlength')) {
      return `Минимум ${control.errors?.['minlength'].requiredLength} символа`;
    }
    if (control.hasError('maxlength')) {
      return `Максимум ${control.errors?.['maxlength'].requiredLength} символа`;
    }
    if (control.hasError('min')) {
      return `Минимална стойност: ${control.errors?.['min'].min}`;
    }
    if (control.hasError('email')) {
      return 'Моля, въведете валиден имейл адрес';
    }
    return '';
  }

  navigateToLogin(): void {
    this.router.navigate(['/login'], { queryParams: { returnUrl: this.returnUrl } });
  }

  signUpWithGoogle(): void {
    this.isLoading = true;

    this.oauthService.signInWithGooglePopup().subscribe({
      next: (oauthUser: OAuthUser) => {
        this.handleOAuthSignUp(oauthUser);
      },
      error: () => {
        this.isLoading = false;
        this.snackBar.open('Регистрацията с Google неуспешна. Моля, опитайте отново.', 'Затвори', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  signUpWithFacebook(): void {
    this.isLoading = true;

    this.oauthService.signInWithFacebook().subscribe({
      next: (oauthUser: OAuthUser) => {
        this.handleOAuthSignUp(oauthUser);
      },
      error: () => {
        this.isLoading = false;
        this.snackBar.open('Регистрацията с Facebook неуспешна. Моля, опитайте отново.', 'Затвори', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private handleOAuthSignUp(oauthUser: OAuthUser): void {
    const oauthRequest: OAuthLoginRequest = {
      provider: oauthUser.provider,
      accessToken: oauthUser.accessToken,
      email: oauthUser.email,
      firstName: oauthUser.firstName,
      lastName: oauthUser.lastName,
      profilePictureUrl: oauthUser.profilePictureUrl
    };

    this.authService.loginWithOAuth(oauthRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          const providerName = oauthUser.provider === 'google' ? 'Google' : 'Facebook';
          this.snackBar.open(`Добре дошли! Акаунтът е създаден с ${providerName}`, 'Затвори', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate([this.returnUrl]);
        } else {
          this.snackBar.open(response.message || 'OAuth регистрацията неуспешна', 'Затвори', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error || 'OAuth регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }
}
