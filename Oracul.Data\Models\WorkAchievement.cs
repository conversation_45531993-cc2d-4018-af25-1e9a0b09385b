using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Work achievement entity for specific accomplishments in work experience
    /// </summary>
    public class WorkAchievement : BaseEntity
    {
        [Required]
        public int WorkExperienceId { get; set; }

        [Required]
        public string Achievement { get; set; } = string.Empty;

        // Navigation properties
        public virtual WorkExperience WorkExperience { get; set; } = null!;
    }
}
