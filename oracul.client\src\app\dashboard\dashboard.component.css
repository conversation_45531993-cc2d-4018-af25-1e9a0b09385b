.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;
}

.header-card .mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--theme-primary);
}

.header-card .mat-card-subtitle {
  color: rgba(0, 0, 0, 0.6);
  margin-top: 8px;
}

.loading-container {
  margin: 20px 0;
}

.loading-content {
  text-align: center;
  padding: 40px 20px;
}

.loading-content .mat-spinner {
  margin: 0 auto 20px;
}

.loading-content p {
  margin: 10px 0;
  color: rgba(0, 0, 0, 0.6);
}

.data-card {
  margin: 20px 0;
}

.data-card .mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--theme-primary);
}

.table-container {
  overflow-x: auto;
  margin-top: 16px;
}

.weather-table {
  width: 100%;
  min-width: 600px;
}

.weather-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.weather-table th .mat-icon {
  margin-right: 8px;
  font-size: 18px;
  vertical-align: middle;
}

.temperature {
  font-weight: 500;
  color: #ff9800;
}

.summary {
  font-style: italic;
  color: #666;
}

/* Profile Demo Card */
.profile-demo-card {
  background: linear-gradient(135deg, rgba(103, 58, 183, 0.05) 0%, rgba(255, 193, 7, 0.05) 100%);
  border-left: 4px solid var(--theme-primary);
}

.profile-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.feature-tag {
  background-color: var(--theme-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.profile-demo-card .mat-card-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .weather-table {
    min-width: 500px;
  }
}
