{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    this.duration = 0;\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    this.verticalPosition = 'bottom';\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {}\nMatSnackBarLabel.ɵfac = function MatSnackBarLabel_Factory(t) {\n  return new (t || MatSnackBarLabel)();\n};\nMatSnackBarLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSnackBarLabel,\n  selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {}\nMatSnackBarActions.ɵfac = function MatSnackBarActions_Factory(t) {\n  return new (t || MatSnackBarActions)();\n};\nMatSnackBarActions.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSnackBarActions,\n  selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {}\nMatSnackBarAction.ɵfac = function MatSnackBarAction_Factory(t) {\n  return new (t || MatSnackBarAction)();\n};\nMatSnackBarAction.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSnackBarAction,\n  selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SimpleSnackBar {\n  constructor(snackBarRef, data) {\n    this.snackBarRef = snackBarRef;\n    this.data = data;\n  }\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n}\nSimpleSnackBar.ɵfac = function SimpleSnackBar_Factory(t) {\n  return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n};\nSimpleSnackBar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: SimpleSnackBar,\n  selectors: [[\"simple-snack-bar\"]],\n  hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n  exportAs: [\"matSnackBar\"],\n  decls: 3,\n  vars: 2,\n  consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\", 4, \"ngIf\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n  template: function SimpleSnackBar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtext(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(2, SimpleSnackBar_div_2_Template, 3, 1, \"div\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasAction);\n    }\n  },\n  dependencies: [i2.NgIf, i3.MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n  styles: [\".mat-mdc-simple-snack-bar{display:flex}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatSnackBarRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DATA]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: trigger('state', [state('void, hidden', style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), state('visible', style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n    opacity: 0\n  })))])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet uniqueId = 0;\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\nclass _MatSnackBarContainerBase extends BasePortalOutlet {\n  constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, /** The snack bar configuration. */\n  snackBarConfig) {\n    super();\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._platform = _platform;\n    this.snackBarConfig = snackBarConfig;\n    this._document = inject(DOCUMENT);\n    this._trackedModals = new Set();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    this._announceDelay = 150;\n    /** Whether the component has been destroyed. */\n    this._destroyed = false;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    this._onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    this._onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    this._onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    this._animationState = 'void';\n    /** Unique ID of the aria-live element. */\n    this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    };\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n      this._live = 'assertive';\n    } else if (snackBarConfig.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(event) {\n    const {\n      fromState,\n      toState\n    } = event;\n    if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n      this._completeExit();\n    }\n    if (toState === 'visible') {\n      // Note: we shouldn't use `this` inside the zone callback,\n      // because it can cause a memory leak.\n      const onEnter = this._onEnter;\n      this._ngZone.run(() => {\n        onEnter.next();\n        onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  /**\n   * Waits for the zone to settle before removing the element. Helps prevent\n   * errors where we end up removing an element which is in the middle of an animation.\n   */\n  _completeExit() {\n    this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._onExit.next();\n        this._onExit.complete();\n      });\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(crisbeto): consider de-duplicating this with the `LiveAnnouncer`.\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (!this._announceTimeoutId) {\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n          const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement = null;\n            if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n              focusedElement = document.activeElement;\n            }\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n  }\n}\n_MatSnackBarContainerBase.ɵfac = function _MatSnackBarContainerBase_Factory(t) {\n  return new (t || _MatSnackBarContainerBase)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n};\n_MatSnackBarContainerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSnackBarContainerBase,\n  viewQuery: function _MatSnackBarContainerBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSnackBarContainerBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: MatSnackBarConfig\n    }];\n  }, {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends _MatSnackBarContainerBase {\n  /** Applies the correct CSS class to the label based on its content. */\n  _afterPortalAttached() {\n    super._afterPortalAttached();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n}\nMatSnackBarContainer.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSnackBarContainer_BaseFactory;\n  return function MatSnackBarContainer_Factory(t) {\n    return (ɵMatSnackBarContainer_BaseFactory || (ɵMatSnackBarContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSnackBarContainer)))(t || MatSnackBarContainer);\n  };\n}();\nMatSnackBarContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSnackBarContainer,\n  selectors: [[\"mat-snack-bar-container\"]],\n  viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\", \"mdc-snackbar--open\"],\n  hostVars: 1,\n  hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n        return ctx.onAnimationEnd($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 6,\n  vars: 3,\n  consts: [[1, \"mdc-snackbar__surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"label\", \"\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n  template: function MatSnackBarContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1, 2)(3, \"div\", 3);\n      i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(5, \"div\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n    }\n  },\n  dependencies: [i3$1.CdkPortalOutlet],\n  styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss svg,.mat-mdc-snack-bar-container .mdc-snackbar__dismiss img{width:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px));height:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size, inherit);font-family:var(--mdc-snackbar-supporting-text-font, inherit);font-weight:var(--mdc-snackbar-supporting-text-weight, inherit);line-height:var(--mdc-snackbar-supporting-text-line-height, inherit)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-mdc-snack-bar-button-color, transparent);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matSnackBarAnimations.snackBarState]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      animations: [matSnackBarAnimations.snackBarState],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n        '[@state]': '_animationState',\n        '(@state.done)': 'onAnimationEnd($event)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss svg,.mat-mdc-snack-bar-container .mdc-snackbar__dismiss img{width:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px));height:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size, inherit);font-family:var(--mdc-snackbar-supporting-text-font, inherit);font-weight:var(--mdc-snackbar-supporting-text-weight, inherit);line-height:var(--mdc-snackbar-supporting-text-line-height, inherit)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-mdc-snack-bar-button-color, transparent);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"]\n    }]\n  }], null, {\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSnackBarModule {}\nMatSnackBarModule.ɵfac = function MatSnackBarModule_Factory(t) {\n  return new (t || MatSnackBarModule)();\n};\nMatSnackBarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSnackBarModule\n});\nMatSnackBarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule],\n      exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      declarations: [SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\nclass _MatSnackBarBase {\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n    this._overlay = _overlay;\n    this._live = _live;\n    this._injector = _injector;\n    this._breakpointObserver = _breakpointObserver;\n    this._parentSnackBar = _parentSnackBar;\n    this._defaultConfig = _defaultConfig;\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    this._snackBarRefAtThisLevel = null;\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n}\n_MatSnackBarBase.ɵfac = function _MatSnackBarBase_Factory(t) {\n  return new (t || _MatSnackBarBase)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(_MatSnackBarBase, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n};\n_MatSnackBarBase.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatSnackBarBase,\n  factory: _MatSnackBarBase.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSnackBarBase, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i2$1.LiveAnnouncer\n    }, {\n      type: i0.Injector\n    }, {\n      type: i3$2.BreakpointObserver\n    }, {\n      type: _MatSnackBarBase,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: MatSnackBarConfig,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar extends _MatSnackBarBase {\n  constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n    super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n    this.simpleSnackBarComponent = SimpleSnackBar;\n    this.snackBarContainerComponent = MatSnackBarContainer;\n    this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n  }\n}\nMatSnackBar.ɵfac = function MatSnackBar_Factory(t) {\n  return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n};\nMatSnackBar.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatSnackBar,\n  factory: MatSnackBar.ɵfac,\n  providedIn: MatSnackBarModule\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: MatSnackBarModule\n    }]\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i2$1.LiveAnnouncer\n    }, {\n      type: i0.Injector\n    }, {\n      type: i3$2.BreakpointObserver\n    }, {\n      type: MatSnackBar,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: MatSnackBarConfig,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "inject", "ViewChild", "NgModule", "Injector", "TemplateRef", "Injectable", "Optional", "SkipSelf", "Subject", "i2", "DOCUMENT", "CommonModule", "i3", "MatButtonModule", "trigger", "state", "style", "transition", "animate", "i3$1", "BasePortalOutlet", "CdkPortalOutlet", "PortalModule", "ComponentPortal", "TemplatePortal", "i1", "take", "takeUntil", "i2$1", "i3$2", "Breakpoints", "i1$1", "OverlayModule", "OverlayConfig", "MatCommonModule", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "constructor", "containerInstance", "_overlayRef", "_afterDismissed", "_afterOpened", "_onAction", "_dismissedByAction", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "_durationTimeoutId", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "data", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "ɵdir", "type", "args", "selector", "host", "MatSnackBarActions", "MatSnackBarAction", "SimpleSnackBar", "snackBarRef", "action", "hasAction", "ɵcmp", "NgIf", "MatButton", "exportAs", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "undefined", "decorators", "matSnackBarAnimations", "snackBarState", "transform", "opacity", "uniqueId", "_MatSnackBarContainerBase", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "snackBarConfig", "_document", "_trackedModals", "Set", "_announce<PERSON><PERSON>y", "_destroyed", "_onAnnounce", "_animationState", "_liveElementId", "attachDomPortal", "portal", "_assertNotAttached", "result", "_portalOutlet", "_afterPortalAttached", "_live", "FIREFOX", "_role", "attachComponentPortal", "attachTemplatePortal", "onAnimationEnd", "event", "fromState", "toState", "_completeExit", "onEnter", "run", "enter", "detectChanges", "_screenReaderAnnounce", "nativeElement", "setAttribute", "_announceTimeoutId", "ngOnDestroy", "_clearFromModals", "onMicrotaskEmpty", "pipe", "element", "panelClasses", "panelClass", "Array", "isArray", "for<PERSON>ach", "cssClass", "classList", "add", "_exposeToModals", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "ngDevMode", "Error", "runOutsideAngular", "inertElement", "querySelector", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "NgZone", "ElementRef", "ChangeDetectorRef", "Platform", "static", "MatSnackBarContainer", "label", "_label", "labelClass", "toggle", "<PERSON><PERSON><PERSON>", "animations", "MatSnackBarModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "_MatSnackBarBase", "_openedSnackBarRef", "parent", "_parentSnackBar", "_snackBarRefAtThisLevel", "value", "_overlay", "_injector", "_breakpointObserver", "_defaultConfig", "openFromComponent", "component", "config", "_attach", "openFromTemplate", "open", "message", "_config", "simpleSnackBarComponent", "_attachSnackBarContainer", "overlayRef", "userInjector", "viewContainerRef", "injector", "create", "providers", "provide", "useValue", "containerPortal", "snackBarContainerComponent", "containerRef", "attach", "instance", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "detachments", "overlayElement", "handsetCssClass", "matches", "announce", "_animateSnackBar", "overlayConfig", "direction", "positionStrategy", "position", "global", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "Overlay", "LiveAnnouncer", "BreakpointObserver", "ɵprov", "MatSnackBar", "overlay", "live", "breakpointObserver", "parentSnackBar", "defaultConfig"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        /** Subject for notifying the user that the snack bar has been dismissed. */\n        this._afterDismissed = new Subject();\n        /** Subject for notifying the user that the snack bar has opened and appeared. */\n        this._afterOpened = new Subject();\n        /** Subject for notifying the user that the snack bar action was called. */\n        this._onAction = new Subject();\n        /** Whether the snack bar was dismissed using the action button. */\n        this._dismissedByAction = false;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    constructor() {\n        /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n        this.politeness = 'assertive';\n        /**\n         * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n         * component or template, the announcement message will default to the specified message.\n         */\n        this.announcementMessage = '';\n        /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n        this.duration = 0;\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** The horizontal position to place the snack bar. */\n        this.horizontalPosition = 'center';\n        /** The vertical position to place the snack bar. */\n        this.verticalPosition = 'bottom';\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n}\nMatSnackBarLabel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSnackBarLabel.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n}\nMatSnackBarActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSnackBarActions.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n}\nMatSnackBarAction.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSnackBarAction.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SimpleSnackBar {\n    constructor(snackBarRef, data) {\n        this.snackBarRef = snackBarRef;\n        this.data = data;\n    }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n}\nSimpleSnackBar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: SimpleSnackBar, deps: [{ token: MatSnackBarRef }, { token: MAT_SNACK_BAR_DATA }], target: i0.ɵɵFactoryTarget.Component });\nSimpleSnackBar.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: SimpleSnackBar, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"], dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i3.MatButton, selector: \"    button[mat-button], button[mat-raised-button], button[mat-flat-button],    button[mat-stroked-button]  \", inputs: [\"disabled\", \"disableRipple\", \"color\"], exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n<div matSnackBarActions *ngIf=\\\"hasAction\\\">\\n  <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n    {{data.action}}\\n  </button>\\n</div>\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"] }]\n        }], ctorParameters: function () { return [{ type: MatSnackBarRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DATA]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: trigger('state', [\n        state('void, hidden', style({\n            transform: 'scale(0.8)',\n            opacity: 0,\n        })),\n        state('visible', style({\n            transform: 'scale(1)',\n            opacity: 1,\n        })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n            opacity: 0,\n        }))),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet uniqueId = 0;\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\nclass _MatSnackBarContainerBase extends BasePortalOutlet {\n    constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, \n    /** The snack bar configuration. */\n    snackBarConfig) {\n        super();\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._platform = _platform;\n        this.snackBarConfig = snackBarConfig;\n        this._document = inject(DOCUMENT);\n        this._trackedModals = new Set();\n        /** The number of milliseconds to wait before announcing the snack bar's content. */\n        this._announceDelay = 150;\n        /** Whether the component has been destroyed. */\n        this._destroyed = false;\n        /** Subject for notifying that the snack bar has announced to screen readers. */\n        this._onAnnounce = new Subject();\n        /** Subject for notifying that the snack bar has exited from view. */\n        this._onExit = new Subject();\n        /** Subject for notifying that the snack bar has finished entering the view. */\n        this._onEnter = new Subject();\n        /** The state of the snack bar animations. */\n        this._animationState = 'void';\n        /** Unique ID of the aria-live element. */\n        this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n        /**\n         * Attaches a DOM portal to the snack bar container.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            this._assertNotAttached();\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._afterPortalAttached();\n            return result;\n        };\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (snackBarConfig.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(event) {\n        const { fromState, toState } = event;\n        if ((toState === 'void' && fromState !== 'void') || toState === 'hidden') {\n            this._completeExit();\n        }\n        if (toState === 'visible') {\n            // Note: we shouldn't use `this` inside the zone callback,\n            // because it can cause a memory leak.\n            const onEnter = this._onEnter;\n            this._ngZone.run(() => {\n                onEnter.next();\n                onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n    }\n    /**\n     * Waits for the zone to settle before removing the element. Helps prevent\n     * errors where we end up removing an element which is in the middle of an animation.\n     */\n    _completeExit() {\n        this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n            this._ngZone.run(() => {\n                this._onExit.next();\n                this._onExit.complete();\n            });\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(crisbeto): consider de-duplicating this with the `LiveAnnouncer`.\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (!this._announceTimeoutId) {\n            this._ngZone.runOutsideAngular(() => {\n                this._announceTimeoutId = setTimeout(() => {\n                    const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n                    const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n                    if (inertElement && liveElement) {\n                        // If an element in the snack bar content is focused before being moved\n                        // track it and restore focus after moving to the live region.\n                        let focusedElement = null;\n                        if (this._platform.isBrowser &&\n                            document.activeElement instanceof HTMLElement &&\n                            inertElement.contains(document.activeElement)) {\n                            focusedElement = document.activeElement;\n                        }\n                        inertElement.removeAttribute('aria-hidden');\n                        liveElement.appendChild(inertElement);\n                        focusedElement?.focus();\n                        this._onAnnounce.next();\n                        this._onAnnounce.complete();\n                    }\n                }, this._announceDelay);\n            });\n        }\n    }\n}\n_MatSnackBarContainerBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSnackBarContainerBase, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.Platform }, { token: MatSnackBarConfig }], target: i0.ɵɵFactoryTarget.Directive });\n_MatSnackBarContainerBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatSnackBarContainerBase, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSnackBarContainerBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.Platform }, { type: MatSnackBarConfig }]; }, propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends _MatSnackBarContainerBase {\n    /** Applies the correct CSS class to the label based on its content. */\n    _afterPortalAttached() {\n        super._afterPortalAttached();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n}\nMatSnackBarContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSnackBarContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSnackBarContainer, selector: \"mat-snack-bar-container\", host: { listeners: { \"@state.done\": \"onAnimationEnd($event)\" }, properties: { \"@state\": \"_animationState\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open\" }, viewQueries: [{ propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss svg,.mat-mdc-snack-bar-container .mdc-snackbar__dismiss img{width:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px));height:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size, inherit);font-family:var(--mdc-snackbar-supporting-text-font, inherit);font-weight:var(--mdc-snackbar-supporting-text-weight, inherit);line-height:var(--mdc-snackbar-supporting-text-line-height, inherit)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-mdc-snack-bar-button-color, transparent);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"], dependencies: [{ kind: \"directive\", type: i3$1.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matSnackBarAnimations.snackBarState], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, animations: [matSnackBarAnimations.snackBarState], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n                        '[@state]': '_animationState',\n                        '(@state.done)': 'onAnimationEnd($event)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__dismiss svg,.mat-mdc-snack-bar-container .mdc-snackbar__dismiss img{width:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px));height:var(--mdc-icon-button-icon-size, var(--mdc-snackbar-icon-size, 24px))}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color, inherit)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size, inherit);font-family:var(--mdc-snackbar-supporting-text-font, inherit);font-weight:var(--mdc-snackbar-supporting-text-weight, inherit);line-height:var(--mdc-snackbar-supporting-text-line-height, inherit)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-mdc-snack-bar-button-color, transparent);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"] }]\n        }], propDecorators: { _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSnackBarModule {\n}\nMatSnackBarModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSnackBarModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarModule, declarations: [SimpleSnackBar,\n        MatSnackBarContainer,\n        MatSnackBarLabel,\n        MatSnackBarActions,\n        MatSnackBarAction], imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule], exports: [MatCommonModule,\n        MatSnackBarContainer,\n        MatSnackBarLabel,\n        MatSnackBarActions,\n        MatSnackBarAction] });\nMatSnackBarModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule],\n                    exports: [\n                        MatCommonModule,\n                        MatSnackBarContainer,\n                        MatSnackBarLabel,\n                        MatSnackBarActions,\n                        MatSnackBarAction,\n                    ],\n                    declarations: [\n                        SimpleSnackBar,\n                        MatSnackBarContainer,\n                        MatSnackBarLabel,\n                        MatSnackBarActions,\n                        MatSnackBarAction,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\nclass _MatSnackBarBase {\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n        this._overlay = _overlay;\n        this._live = _live;\n        this._injector = _injector;\n        this._breakpointObserver = _breakpointObserver;\n        this._parentSnackBar = _parentSnackBar;\n        this._defaultConfig = _defaultConfig;\n        /**\n         * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n         * If there is a parent snack-bar service, all operations should delegate to that parent\n         * via `_openedSnackBarRef`.\n         */\n        this._snackBarRefAtThisLevel = null;\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        let positionStrategy = this._overlay.position().global();\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n}\n_MatSnackBarBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSnackBarBase, deps: [{ token: i1$1.Overlay }, { token: i2$1.LiveAnnouncer }, { token: i0.Injector }, { token: i3$2.BreakpointObserver }, { token: _MatSnackBarBase, optional: true, skipSelf: true }, { token: MAT_SNACK_BAR_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });\n_MatSnackBarBase.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSnackBarBase });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSnackBarBase, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i2$1.LiveAnnouncer }, { type: i0.Injector }, { type: i3$2.BreakpointObserver }, { type: _MatSnackBarBase, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: MatSnackBarConfig, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar extends _MatSnackBarBase {\n    constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n        super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n        this.simpleSnackBarComponent = SimpleSnackBar;\n        this.snackBarContainerComponent = MatSnackBarContainer;\n        this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n    }\n}\nMatSnackBar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBar, deps: [{ token: i1$1.Overlay }, { token: i2$1.LiveAnnouncer }, { token: i0.Injector }, { token: i3$2.BreakpointObserver }, { token: MatSnackBar, optional: true, skipSelf: true }, { token: MAT_SNACK_BAR_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });\nMatSnackBar.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBar, providedIn: MatSnackBarModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: MatSnackBarModule }]\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i2$1.LiveAnnouncer }, { type: i0.Injector }, { type: i3$2.BreakpointObserver }, { type: MatSnackBar, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: MatSnackBarConfig, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC5M,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,IAAI,MAAM,qBAAqB;AAC3C,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AACtH,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAChD,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,qBAAqB;AAC3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AACnE,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;EAAA;IAAA,YA2HwGzC,EAAE;IAAFA,EAAE,4BA8DoP;IA9DtPA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aA8DuS,eAAQ;IAAA,EAAE;IA9DnTA,EAAE,UA8D2U;IA9D7UA,EAAE,eA8DoV;EAAA;EAAA;IAAA,eA9DtVA,EAAE;IAAFA,EAAE,aA8D2U;IA9D7UA,EAAE,iDA8D2U;EAAA;AAAA;AAAA;AAAA;AAxLrb,MAAM0C,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAW,CAACC,iBAAiB,EAAEC,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAIlC,OAAO,EAAE;IACpC;IACA,IAAI,CAACmC,YAAY,GAAG,IAAInC,OAAO,EAAE;IACjC;IACA,IAAI,CAACoC,SAAS,GAAG,IAAIpC,OAAO,EAAE;IAC9B;IACA,IAAI,CAACqC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACL,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACM,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,EAAE,CAAC;EACpE;EACA;EACAC,OAAO,GAAG;IACN,IAAI,CAAC,IAAI,CAACP,eAAe,CAACQ,MAAM,EAAE;MAC9B,IAAI,CAACV,iBAAiB,CAACW,IAAI,EAAE;IACjC;IACAC,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;EACAC,iBAAiB,GAAG;IAChB,IAAI,CAAC,IAAI,CAACV,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACL,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACD,SAAS,CAACW,IAAI,EAAE;MACrB,IAAI,CAACX,SAAS,CAACY,QAAQ,EAAE;MACzB,IAAI,CAACP,OAAO,EAAE;IAClB;IACAG,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACII,eAAe,GAAG;IACd,IAAI,CAACH,iBAAiB,EAAE;EAC5B;EACA;EACAI,aAAa,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACN,kBAAkB,GAAGO,UAAU,CAAC,MAAM,IAAI,CAACX,OAAO,EAAE,EAAEb,IAAI,CAACyB,GAAG,CAACF,QAAQ,EAAExB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA2B,KAAK,GAAG;IACJ,IAAI,CAAC,IAAI,CAACnB,YAAY,CAACO,MAAM,EAAE;MAC3B,IAAI,CAACP,YAAY,CAACY,IAAI,EAAE;MACxB,IAAI,CAACZ,YAAY,CAACa,QAAQ,EAAE;IAChC;EACJ;EACA;EACAR,cAAc,GAAG;IACb,IAAI,CAACP,WAAW,CAACsB,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACnB,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACN,SAAS,CAACY,QAAQ,EAAE;IAC7B;IACA,IAAI,CAACd,eAAe,CAACa,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACH,eAAe,CAACc,QAAQ,EAAE;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAc,GAAG;IACb,OAAO,IAAI,CAACvB,eAAe;EAC/B;EACA;EACAwB,WAAW,GAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQ,GAAG;IACP,OAAO,IAAI,CAACxB,SAAS;EACzB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,kBAAkB,GAAG,IAAI3E,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM4E,iBAAiB,CAAC;EACpB/B,WAAW,GAAG;IACV;IACA,IAAI,CAACgC,UAAU,GAAG,WAAW;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACb,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACc,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,kBAAkB,GAAG,QAAQ;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAG,QAAQ;EACpC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACC,IAAI;EAAA,iBAA6FD,gBAAgB;AAAA,CAAmD;AACrLA,gBAAgB,CAACE,IAAI,kBADmFrF,EAAE;EAAA,MACJmF,gBAAgB;EAAA;EAAA;AAAA,EAA0H;AAChP;EAAA,mDAFwGnF,EAAE,mBAEVmF,gBAAgB,EAAc,CAAC;IACnHG,IAAI,EAAEpF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,oBAAmB;MAC9BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;AAEzBA,kBAAkB,CAACN,IAAI;EAAA,iBAA6FM,kBAAkB;AAAA,CAAmD;AACzLA,kBAAkB,CAACL,IAAI,kBAfiFrF,EAAE;EAAA,MAeF0F,kBAAkB;EAAA;EAAA;AAAA,EAAgI;AAC1P;EAAA,mDAhBwG1F,EAAE,mBAgBV0F,kBAAkB,EAAc,CAAC;IACrHJ,IAAI,EAAEpF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,sBAAqB;MAChCC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAME,iBAAiB,CAAC;AAExBA,iBAAiB,CAACP,IAAI;EAAA,iBAA6FO,iBAAiB;AAAA,CAAmD;AACvLA,iBAAiB,CAACN,IAAI,kBA7BkFrF,EAAE;EAAA,MA6BH2F,iBAAiB;EAAA;EAAA;AAAA,EAA6H;AACrP;EAAA,mDA9BwG3F,EAAE,mBA8BV2F,iBAAiB,EAAc,CAAC;IACpHL,IAAI,EAAEpF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,qBAAoB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,CAAC;EACjB9C,WAAW,CAAC+C,WAAW,EAAEb,IAAI,EAAE;IAC3B,IAAI,CAACa,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACb,IAAI,GAAGA,IAAI;EACpB;EACA;EACAc,MAAM,GAAG;IACL,IAAI,CAACD,WAAW,CAAChC,iBAAiB,EAAE;EACxC;EACA;EACA,IAAIkC,SAAS,GAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACf,IAAI,CAACc,MAAM;EAC7B;AACJ;AACAF,cAAc,CAACR,IAAI;EAAA,iBAA6FQ,cAAc,EA7DtB5F,EAAE,mBA6DsC6C,cAAc,GA7DtD7C,EAAE,mBA6DiE4E,kBAAkB;AAAA,CAA4C;AACzOgB,cAAc,CAACI,IAAI,kBA9DqFhG,EAAE;EAAA,MA8DN4F,cAAc;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA9DV5F,EAAE,4BA8DwK;MA9D1KA,EAAE,UA8D8L;MA9DhMA,EAAE,eA8DoM;MA9DtMA,EAAE,6DA8D4V;IAAA;IAAA;MA9D9VA,EAAE,aA8D8L;MA9DhMA,EAAE,gDA8D8L;MA9DhMA,EAAE,aA8DiP;MA9DnPA,EAAE,kCA8DiP;IAAA;EAAA;EAAA,eAA+MgB,EAAE,CAACiF,IAAI,EAA6F9E,EAAE,CAAC+E,SAAS,EAAiOf,gBAAgB,EAA+DO,kBAAkB,EAAiEC,iBAAiB;EAAA;EAAA;EAAA;AAAA,EAAqI;AACnrC;EAAA,mDA/DwG3F,EAAE,mBA+DV4F,cAAc,EAAc,CAAC;IACjHN,IAAI,EAAEnF,SAAS;IACfoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEW,QAAQ,EAAE,aAAa;MAAEC,aAAa,EAAEhG,iBAAiB,CAACiG,IAAI;MAAEC,eAAe,EAAEjG,uBAAuB,CAACkG,MAAM;MAAEd,IAAI,EAAE;QAClJ,OAAO,EAAE;MACb,CAAC;MAAEe,QAAQ,EAAE,8MAA8M;MAAEC,MAAM,EAAE,CAAC,yCAAyC;IAAE,CAAC;EAC9R,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnB,IAAI,EAAEzC;IAAe,CAAC,EAAE;MAAEyC,IAAI,EAAEoB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxFrB,IAAI,EAAEhF,MAAM;QACZiF,IAAI,EAAE,CAACX,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgC,qBAAqB,GAAG;EAC1B;EACAC,aAAa,EAAExF,OAAO,CAAC,OAAO,EAAE,CAC5BC,KAAK,CAAC,cAAc,EAAEC,KAAK,CAAC;IACxBuF,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACHzF,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IACnBuF,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACHvF,UAAU,CAAC,cAAc,EAAEC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,UAAU,CAAC,wBAAwB,EAAEC,OAAO,CAAC,mCAAmC,EAAEF,KAAK,CAAC;IACpFwF,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CACP;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,SAAStF,gBAAgB,CAAC;EACrDmB,WAAW,CAACoE,OAAO,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,SAAS,EAC/D;EACAC,cAAc,EAAE;IACZ,KAAK,EAAE;IACP,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGhH,MAAM,CAACU,QAAQ,CAAC;IACjC,IAAI,CAACuG,cAAc,GAAG,IAAIC,GAAG,EAAE;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,GAAG;IACzB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI7G,OAAO,EAAE;IAChC;IACA,IAAI,CAACsC,OAAO,GAAG,IAAItC,OAAO,EAAE;IAC5B;IACA,IAAI,CAAC2D,QAAQ,GAAG,IAAI3D,OAAO,EAAE;IAC7B;IACA,IAAI,CAAC8G,eAAe,GAAG,MAAM;IAC7B;IACA,IAAI,CAACC,cAAc,GAAI,gCAA+Bd,QAAQ,EAAG,EAAC;IAClE;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACe,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,CAACC,kBAAkB,EAAE;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACJ,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACI,oBAAoB,EAAE;MAC3B,OAAOF,MAAM;IACjB,CAAC;IACD;IACA;IACA,IAAIZ,cAAc,CAACxC,UAAU,KAAK,WAAW,IAAI,CAACwC,cAAc,CAACvC,mBAAmB,EAAE;MAClF,IAAI,CAACsD,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIf,cAAc,CAACxC,UAAU,KAAK,KAAK,EAAE;MAC1C,IAAI,CAACuD,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAAChB,SAAS,CAACiB,OAAO,EAAE;MACxB,IAAI,IAAI,CAACD,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;MACxB;IACJ;EACJ;EACA;EACAC,qBAAqB,CAACR,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACK,qBAAqB,CAACR,MAAM,CAAC;IAC/D,IAAI,CAACI,oBAAoB,EAAE;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAO,oBAAoB,CAACT,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACM,oBAAoB,CAACT,MAAM,CAAC;IAC9D,IAAI,CAACI,oBAAoB,EAAE;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAQ,cAAc,CAACC,KAAK,EAAE;IAClB,MAAM;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACpC,IAAKE,OAAO,KAAK,MAAM,IAAID,SAAS,KAAK,MAAM,IAAKC,OAAO,KAAK,QAAQ,EAAE;MACtE,IAAI,CAACC,aAAa,EAAE;IACxB;IACA,IAAID,OAAO,KAAK,SAAS,EAAE;MACvB;MACA;MACA,MAAME,OAAO,GAAG,IAAI,CAACrE,QAAQ;MAC7B,IAAI,CAACwC,OAAO,CAAC8B,GAAG,CAAC,MAAM;QACnBD,OAAO,CAACjF,IAAI,EAAE;QACdiF,OAAO,CAAChF,QAAQ,EAAE;MACtB,CAAC,CAAC;IACN;EACJ;EACA;EACAkF,KAAK,GAAG;IACJ,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB,IAAI,CAACE,eAAe,GAAG,SAAS;MAChC,IAAI,CAACT,kBAAkB,CAAC8B,aAAa,EAAE;MACvC,IAAI,CAACC,qBAAqB,EAAE;IAChC;EACJ;EACA;EACAzF,IAAI,GAAG;IACH;IACA;IACA,IAAI,CAACwD,OAAO,CAAC8B,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAACnB,eAAe,GAAG,QAAQ;MAC/B;MACA;MACA;MACA,IAAI,CAACV,WAAW,CAACiC,aAAa,CAACC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACA1F,YAAY,CAAC,IAAI,CAAC2F,kBAAkB,CAAC;IACzC,CAAC,CAAC;IACF,OAAO,IAAI,CAACjG,OAAO;EACvB;EACA;EACAkG,WAAW,GAAG;IACV,IAAI,CAAC5B,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC6B,gBAAgB,EAAE;IACvB,IAAI,CAACV,aAAa,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIA,aAAa,GAAG;IACZ,IAAI,CAAC5B,OAAO,CAACuC,gBAAgB,CAACC,IAAI,CAACzH,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqB,SAAS,CAAC,MAAM;MACxD,IAAI,CAAC4D,OAAO,CAAC8B,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC3F,OAAO,CAACS,IAAI,EAAE;QACnB,IAAI,CAACT,OAAO,CAACU,QAAQ,EAAE;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIqE,oBAAoB,GAAG;IACnB,MAAMuB,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACiC,aAAa;IAC9C,MAAMQ,YAAY,GAAG,IAAI,CAACtC,cAAc,CAACuC,UAAU;IACnD,IAAID,YAAY,EAAE;MACd,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACI,OAAO,CAACC,QAAQ,IAAIN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACP,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACQ,eAAe,EAAE;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIA,eAAe,GAAG;IACd;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAG,IAAI,CAACvC,cAAc;IAC9B,MAAMwC,MAAM,GAAG,IAAI,CAAC/C,SAAS,CAACgD,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACpD,cAAc,CAAC2C,GAAG,CAACO,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEgB,EAAE,CAAC;MACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCK,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEsB,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAb,gBAAgB,GAAG;IACf,IAAI,CAAChC,cAAc,CAACwC,OAAO,CAACU,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE,CAAC,CAACkD,IAAI,EAAE;QACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAACrB,YAAY,CAAC,WAAW,EAAEyB,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACzD,cAAc,CAAC0D,KAAK,EAAE;EAC/B;EACA;EACAjD,kBAAkB,GAAG;IACjB,IAAI,IAAI,CAACE,aAAa,CAACgD,WAAW,EAAE,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAMC,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACIlC,qBAAqB,GAAG;IACpB,IAAI,CAAC,IAAI,CAACG,kBAAkB,EAAE;MAC1B,IAAI,CAACpC,OAAO,CAACoE,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAChC,kBAAkB,GAAGnF,UAAU,CAAC,MAAM;UACvC,MAAMoH,YAAY,GAAG,IAAI,CAACpE,WAAW,CAACiC,aAAa,CAACoC,aAAa,CAAC,eAAe,CAAC;UAClF,MAAMC,WAAW,GAAG,IAAI,CAACtE,WAAW,CAACiC,aAAa,CAACoC,aAAa,CAAC,aAAa,CAAC;UAC/E,IAAID,YAAY,IAAIE,WAAW,EAAE;YAC7B;YACA;YACA,IAAIC,cAAc,GAAG,IAAI;YACzB,IAAI,IAAI,CAACrE,SAAS,CAACsE,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CP,YAAY,CAACQ,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;cAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;YAC3C;YACAN,YAAY,CAACN,eAAe,CAAC,aAAa,CAAC;YAC3CQ,WAAW,CAACO,WAAW,CAACT,YAAY,CAAC;YACrCG,cAAc,EAAEO,KAAK,EAAE;YACvB,IAAI,CAACrE,WAAW,CAAC9D,IAAI,EAAE;YACvB,IAAI,CAAC8D,WAAW,CAAC7D,QAAQ,EAAE;UAC/B;QACJ,CAAC,EAAE,IAAI,CAAC2D,cAAc,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;AACJ;AACAT,yBAAyB,CAAC7B,IAAI;EAAA,iBAA6F6B,yBAAyB,EAtV5CjH,EAAE,mBAsV4DA,EAAE,CAACkM,MAAM,GAtVvElM,EAAE,mBAsVkFA,EAAE,CAACmM,UAAU,GAtVjGnM,EAAE,mBAsV4GA,EAAE,CAACoM,iBAAiB,GAtVlIpM,EAAE,mBAsV6IgC,EAAE,CAACqK,QAAQ,GAtV1JrM,EAAE,mBAsVqK6E,iBAAiB;AAAA,CAA4C;AAC5UoC,yBAAyB,CAAC5B,IAAI,kBAvV0ErF,EAAE;EAAA,MAuVKiH,yBAAyB;EAAA;IAAA;MAvVhCjH,EAAE,aAuVuG4B,eAAe;IAAA;IAAA;MAAA;MAvVxH5B,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE;AAAA,EAuVkM;AAC5S;EAAA,mDAxVwGA,EAAE,mBAwVViH,yBAAyB,EAAc,CAAC;IAC5H3B,IAAI,EAAEpF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoF,IAAI,EAAEtF,EAAE,CAACkM;IAAO,CAAC,EAAE;MAAE5G,IAAI,EAAEtF,EAAE,CAACmM;IAAW,CAAC,EAAE;MAAE7G,IAAI,EAAEtF,EAAE,CAACoM;IAAkB,CAAC,EAAE;MAAE9G,IAAI,EAAEtD,EAAE,CAACqK;IAAS,CAAC,EAAE;MAAE/G,IAAI,EAAET;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsD,aAAa,EAAE,CAAC;MAC1M7C,IAAI,EAAE9E,SAAS;MACf+E,IAAI,EAAE,CAAC3D,eAAe,EAAE;QAAE0K,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAAStF,yBAAyB,CAAC;EACzD;EACAmB,oBAAoB,GAAG;IACnB,KAAK,CAACA,oBAAoB,EAAE;IAC5B;IACA;IACA;IACA,MAAMoE,KAAK,GAAG,IAAI,CAACC,MAAM,CAACrD,aAAa;IACvC,MAAMsD,UAAU,GAAG,qBAAqB;IACxCF,KAAK,CAACtC,SAAS,CAACyC,MAAM,CAACD,UAAU,EAAE,CAACF,KAAK,CAAChB,aAAa,CAAE,IAAGkB,UAAW,EAAC,CAAC,CAAC;EAC9E;AACJ;AACAH,oBAAoB,CAACnH,IAAI;EAAA;EAAA;IAAA,kFA9W+EpF,EAAE,uBA8WYuM,oBAAoB,SAApBA,oBAAoB;EAAA;AAAA,GAAqD;AAC/LA,oBAAoB,CAACvG,IAAI,kBA/W+EhG,EAAE;EAAA,MA+WAuM,oBAAoB;EAAA;EAAA;IAAA;MA/WtBvM,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;QAAA,OA+WA,0BAAsB;MAAA;IAAA;IAAA;MA/WxBA,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,4BA+W6b;MA/W/bA,EAAE,mFA+Wo0B;MA/Wt0BA,EAAE,eA+Wg1B;MA/Wl1BA,EAAE,oBA+WsiC;MA/WxiCA,EAAE,eA+WgjC;IAAA;IAAA;MA/WljCA,EAAE,aA+W4+B;MA/W9+BA,EAAE,oCA+W4+B;IAAA;EAAA;EAAA,eAA2gI0B,IAAI,CAACE,eAAe;EAAA;EAAA;EAAA;IAAA,WAAmI,CAACgF,qBAAqB,CAACC,aAAa;EAAC;AAAA,EAAkG;AAC/3K;EAAA,mDAhXwG7G,EAAE,mBAgXVuM,oBAAoB,EAAc,CAAC;IACvHjH,IAAI,EAAEnF,SAAS;IACfoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEc,eAAe,EAAEjG,uBAAuB,CAACuM,OAAO;MAAExG,aAAa,EAAEhG,iBAAiB,CAACiG,IAAI;MAAEwG,UAAU,EAAE,CAACjG,qBAAqB,CAACC,aAAa,CAAC;MAAEpB,IAAI,EAAE;QACpL,OAAO,EAAE,6DAA6D;QACtE,UAAU,EAAE,iBAAiB;QAC7B,eAAe,EAAE;MACrB,CAAC;MAAEe,QAAQ,EAAE,oqBAAoqB;MAAEC,MAAM,EAAE,CAAC,o4HAAo4H;IAAE,CAAC;EAC/kJ,CAAC,CAAC,QAAkB;IAAEgG,MAAM,EAAE,CAAC;MACvBnH,IAAI,EAAE9E,SAAS;MACf+E,IAAI,EAAE,CAAC,OAAO,EAAE;QAAE+G,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,iBAAiB,CAAC;AAExBA,iBAAiB,CAAC1H,IAAI;EAAA,iBAA6F0H,iBAAiB;AAAA,CAAkD;AACtLA,iBAAiB,CAACC,IAAI,kBAtYkF/M,EAAE;EAAA,MAsYU8M;AAAiB,EAQxG;AAC7BA,iBAAiB,CAACE,IAAI,kBA/YkFhN,EAAE;EAAA,UA+YuCuC,aAAa,EAAEV,YAAY,EAAEX,YAAY,EAAEE,eAAe,EAAEqB,eAAe,EAAEA,eAAe;AAAA,EAAI;AACjP;EAAA,mDAhZwGzC,EAAE,mBAgZV8M,iBAAiB,EAAc,CAAC;IACpHxH,IAAI,EAAE7E,QAAQ;IACd8E,IAAI,EAAE,CAAC;MACC0H,OAAO,EAAE,CAAC1K,aAAa,EAAEV,YAAY,EAAEX,YAAY,EAAEE,eAAe,EAAEqB,eAAe,CAAC;MACtFyK,OAAO,EAAE,CACLzK,eAAe,EACf8J,oBAAoB,EACpBpH,gBAAgB,EAChBO,kBAAkB,EAClBC,iBAAiB,CACpB;MACDwH,YAAY,EAAE,CACVvH,cAAc,EACd2G,oBAAoB,EACpBpH,gBAAgB,EAChBO,kBAAkB,EAClBC,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyH,qCAAqC,GAAG;EAC7C,OAAO,IAAIvI,iBAAiB,EAAE;AAClC;AACA;AACA,MAAMwI,6BAA6B,GAAG,IAAIpN,cAAc,CAAC,+BAA+B,EAAE;EACtFqN,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF,MAAMI,gBAAgB,CAAC;EACnB;EACA,IAAIC,kBAAkB,GAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe;IACnC,OAAOD,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACG,uBAAuB;EAC5E;EACA,IAAIH,kBAAkB,CAACI,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACF,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACF,kBAAkB,GAAGI,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACD,uBAAuB,GAAGC,KAAK;IACxC;EACJ;EACA/K,WAAW,CAACgL,QAAQ,EAAEzF,KAAK,EAAE0F,SAAS,EAAEC,mBAAmB,EAAEL,eAAe,EAAEM,cAAc,EAAE;IAC1F,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACzF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0F,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACL,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACL,uBAAuB,GAAG,IAAI;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,iBAAiB,CAACC,SAAS,EAAEC,MAAM,EAAE;IACjC,OAAO,IAAI,CAACC,OAAO,CAACF,SAAS,EAAEC,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,gBAAgB,CAAC9H,QAAQ,EAAE4H,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACC,OAAO,CAAC7H,QAAQ,EAAE4H,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,IAAI,CAACC,OAAO,EAAE1I,MAAM,GAAG,EAAE,EAAEsI,MAAM,EAAE;IAC/B,MAAMK,OAAO,GAAG;MAAE,GAAG,IAAI,CAACR,cAAc;MAAE,GAAGG;IAAO,CAAC;IACrD;IACA;IACAK,OAAO,CAACzJ,IAAI,GAAG;MAAEwJ,OAAO;MAAE1I;IAAO,CAAC;IAClC;IACA;IACA,IAAI2I,OAAO,CAAC1J,mBAAmB,KAAKyJ,OAAO,EAAE;MACzCC,OAAO,CAAC1J,mBAAmB,GAAG2B,SAAS;IAC3C;IACA,OAAO,IAAI,CAACwH,iBAAiB,CAAC,IAAI,CAACQ,uBAAuB,EAAED,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACIjL,OAAO,GAAG;IACN,IAAI,IAAI,CAACiK,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACjK,OAAO,EAAE;IACrC;EACJ;EACA+F,WAAW,GAAG;IACV;IACA,IAAI,IAAI,CAACqE,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACpK,OAAO,EAAE;IAC1C;EACJ;EACA;AACJ;AACA;EACImL,wBAAwB,CAACC,UAAU,EAAER,MAAM,EAAE;IACzC,MAAMS,YAAY,GAAGT,MAAM,IAAIA,MAAM,CAACU,gBAAgB,IAAIV,MAAM,CAACU,gBAAgB,CAACC,QAAQ;IAC1F,MAAMA,QAAQ,GAAGrO,QAAQ,CAACsO,MAAM,CAAC;MAC7BtB,MAAM,EAAEmB,YAAY,IAAI,IAAI,CAACd,SAAS;MACtCkB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAErK,iBAAiB;QAAEsK,QAAQ,EAAEf;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAMgB,eAAe,GAAG,IAAItN,eAAe,CAAC,IAAI,CAACuN,0BAA0B,EAAEjB,MAAM,CAACU,gBAAgB,EAAEC,QAAQ,CAAC;IAC/G,MAAMO,YAAY,GAAGV,UAAU,CAACW,MAAM,CAACH,eAAe,CAAC;IACvDE,YAAY,CAACE,QAAQ,CAAClI,cAAc,GAAG8G,MAAM;IAC7C,OAAOkB,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;EACInB,OAAO,CAACoB,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAMtB,MAAM,GAAG;MAAE,GAAG,IAAIvJ,iBAAiB,EAAE;MAAE,GAAG,IAAI,CAACoJ,cAAc;MAAE,GAAGyB;IAAW,CAAC;IACpF,MAAMd,UAAU,GAAG,IAAI,CAACe,cAAc,CAACvB,MAAM,CAAC;IAC9C,MAAMwB,SAAS,GAAG,IAAI,CAACjB,wBAAwB,CAACC,UAAU,EAAER,MAAM,CAAC;IACnE,MAAMvI,WAAW,GAAG,IAAIhD,cAAc,CAAC+M,SAAS,EAAEhB,UAAU,CAAC;IAC7D,IAAIa,OAAO,YAAY9O,WAAW,EAAE;MAChC,MAAMqH,MAAM,GAAG,IAAIjG,cAAc,CAAC0N,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAEzB,MAAM,CAACpJ,IAAI;QACtBa;MACJ,CAAC,CAAC;MACFA,WAAW,CAAC2J,QAAQ,GAAGI,SAAS,CAACnH,oBAAoB,CAACT,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAM+G,QAAQ,GAAG,IAAI,CAACe,eAAe,CAAC1B,MAAM,EAAEvI,WAAW,CAAC;MAC1D,MAAMmC,MAAM,GAAG,IAAIlG,eAAe,CAAC2N,OAAO,EAAE/I,SAAS,EAAEqI,QAAQ,CAAC;MAChE,MAAMgB,UAAU,GAAGH,SAAS,CAACpH,qBAAqB,CAACR,MAAM,CAAC;MAC1D;MACAnC,WAAW,CAAC2J,QAAQ,GAAGO,UAAU,CAACP,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACxB,mBAAmB,CACnBgC,OAAO,CAAC3N,WAAW,CAAC4N,eAAe,CAAC,CACpCvG,IAAI,CAACxH,SAAS,CAAC0M,UAAU,CAACsB,WAAW,EAAE,CAAC,CAAC,CACzC5M,SAAS,CAAChC,KAAK,IAAI;MACpBsN,UAAU,CAACuB,cAAc,CAACjG,SAAS,CAACyC,MAAM,CAAC,IAAI,CAACyD,eAAe,EAAE9O,KAAK,CAAC+O,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAIjC,MAAM,CAACrJ,mBAAmB,EAAE;MAC5B;MACA6K,SAAS,CAAChI,WAAW,CAACtE,SAAS,CAAC,MAAM;QAClC,IAAI,CAAC+E,KAAK,CAACiI,QAAQ,CAAClC,MAAM,CAACrJ,mBAAmB,EAAEqJ,MAAM,CAACtJ,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAACyL,gBAAgB,CAAC1K,WAAW,EAAEuI,MAAM,CAAC;IAC1C,IAAI,CAACX,kBAAkB,GAAG5H,WAAW;IACrC,OAAO,IAAI,CAAC4H,kBAAkB;EAClC;EACA;EACA8C,gBAAgB,CAAC1K,WAAW,EAAEuI,MAAM,EAAE;IAClC;IACAvI,WAAW,CAACrB,cAAc,EAAE,CAAClB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACmK,kBAAkB,IAAI5H,WAAW,EAAE;QACxC,IAAI,CAAC4H,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIW,MAAM,CAACrJ,mBAAmB,EAAE;QAC5B,IAAI,CAACsD,KAAK,CAAC6C,KAAK,EAAE;MACtB;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACuC,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAACjJ,cAAc,EAAE,CAAClB,SAAS,CAAC,MAAM;QACrDuC,WAAW,CAAC9C,iBAAiB,CAACkG,KAAK,EAAE;MACzC,CAAC,CAAC;MACF,IAAI,CAACwE,kBAAkB,CAACjK,OAAO,EAAE;IACrC,CAAC,MACI;MACD;MACAqC,WAAW,CAAC9C,iBAAiB,CAACkG,KAAK,EAAE;IACzC;IACA;IACA,IAAImF,MAAM,CAAClK,QAAQ,IAAIkK,MAAM,CAAClK,QAAQ,GAAG,CAAC,EAAE;MACxC2B,WAAW,CAACpB,WAAW,EAAE,CAACnB,SAAS,CAAC,MAAMuC,WAAW,CAAC5B,aAAa,CAACmK,MAAM,CAAClK,QAAQ,CAAC,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;EACIyL,cAAc,CAACvB,MAAM,EAAE;IACnB,MAAMoC,aAAa,GAAG,IAAIhO,aAAa,EAAE;IACzCgO,aAAa,CAACC,SAAS,GAAGrC,MAAM,CAACqC,SAAS;IAC1C,IAAIC,gBAAgB,GAAG,IAAI,CAAC5C,QAAQ,CAAC6C,QAAQ,EAAE,CAACC,MAAM,EAAE;IACxD;IACA,MAAMC,KAAK,GAAGzC,MAAM,CAACqC,SAAS,KAAK,KAAK;IACxC,MAAMK,MAAM,GAAG1C,MAAM,CAACnJ,kBAAkB,KAAK,MAAM,IAC9CmJ,MAAM,CAACnJ,kBAAkB,KAAK,OAAO,IAAI,CAAC4L,KAAM,IAChDzC,MAAM,CAACnJ,kBAAkB,KAAK,KAAK,IAAI4L,KAAM;IAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAI1C,MAAM,CAACnJ,kBAAkB,KAAK,QAAQ;IACjE,IAAI6L,MAAM,EAAE;MACRJ,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdL,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDP,gBAAgB,CAACQ,kBAAkB,EAAE;IACzC;IACA;IACA,IAAI9C,MAAM,CAAClJ,gBAAgB,KAAK,KAAK,EAAE;MACnCwL,gBAAgB,CAACS,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDT,gBAAgB,CAACU,MAAM,CAAC,GAAG,CAAC;IAChC;IACAZ,aAAa,CAACE,gBAAgB,GAAGA,gBAAgB;IACjD,OAAO,IAAI,CAAC5C,QAAQ,CAACkB,MAAM,CAACwB,aAAa,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIV,eAAe,CAAC1B,MAAM,EAAEvI,WAAW,EAAE;IACjC,MAAMgJ,YAAY,GAAGT,MAAM,IAAIA,MAAM,CAACU,gBAAgB,IAAIV,MAAM,CAACU,gBAAgB,CAACC,QAAQ;IAC1F,OAAOrO,QAAQ,CAACsO,MAAM,CAAC;MACnBtB,MAAM,EAAEmB,YAAY,IAAI,IAAI,CAACd,SAAS;MACtCkB,SAAS,EAAE,CACP;QAAEC,OAAO,EAAErM,cAAc;QAAEsM,QAAQ,EAAEtJ;MAAY,CAAC,EAClD;QAAEqJ,OAAO,EAAEtK,kBAAkB;QAAEuK,QAAQ,EAAEf,MAAM,CAACpJ;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;AACJ;AACAwI,gBAAgB,CAACpI,IAAI;EAAA,iBAA6FoI,gBAAgB,EA1oB1BxN,EAAE,UA0oB0CsC,IAAI,CAAC+O,OAAO,GA1oBxDrR,EAAE,UA0oBmEmC,IAAI,CAACmP,aAAa,GA1oBvFtR,EAAE,UA0oBkGA,EAAE,CAACU,QAAQ,GA1oB/GV,EAAE,UA0oB0HoC,IAAI,CAACmP,kBAAkB,GA1oBnJvR,EAAE,UA0oB8JwN,gBAAgB,OA1oBhLxN,EAAE,UA0oB2NqN,6BAA6B;AAAA,CAA6C;AAC/YG,gBAAgB,CAACgE,KAAK,kBA3oBkFxR,EAAE;EAAA,OA2oBYwN,gBAAgB;EAAA,SAAhBA,gBAAgB;AAAA,EAAG;AACzI;EAAA,mDA5oBwGxN,EAAE,mBA4oBVwN,gBAAgB,EAAc,CAAC;IACnHlI,IAAI,EAAE1E;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE0E,IAAI,EAAEhD,IAAI,CAAC+O;IAAQ,CAAC,EAAE;MAAE/L,IAAI,EAAEnD,IAAI,CAACmP;IAAc,CAAC,EAAE;MAAEhM,IAAI,EAAEtF,EAAE,CAACU;IAAS,CAAC,EAAE;MAAE4E,IAAI,EAAElD,IAAI,CAACmP;IAAmB,CAAC,EAAE;MAAEjM,IAAI,EAAEkI,gBAAgB;MAAE7G,UAAU,EAAE,CAAC;QACrLrB,IAAI,EAAEzE;MACV,CAAC,EAAE;QACCyE,IAAI,EAAExE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwE,IAAI,EAAET,iBAAiB;MAAE8B,UAAU,EAAE,CAAC;QAC1CrB,IAAI,EAAEhF,MAAM;QACZiF,IAAI,EAAE,CAAC8H,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAMoE,WAAW,SAASjE,gBAAgB,CAAC;EACvC1K,WAAW,CAAC4O,OAAO,EAAEC,IAAI,EAAE5C,QAAQ,EAAE6C,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAE;IACpF,KAAK,CAACJ,OAAO,EAAEC,IAAI,EAAE5C,QAAQ,EAAE6C,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,CAAC;IACjF,IAAI,CAACpD,uBAAuB,GAAG9I,cAAc;IAC7C,IAAI,CAACyJ,0BAA0B,GAAG9C,oBAAoB;IACtD,IAAI,CAAC6D,eAAe,GAAG,2BAA2B;EACtD;AACJ;AACAqB,WAAW,CAACrM,IAAI;EAAA,iBAA6FqM,WAAW,EAjqBhBzR,EAAE,UAiqBgCsC,IAAI,CAAC+O,OAAO,GAjqB9CrR,EAAE,UAiqByDmC,IAAI,CAACmP,aAAa,GAjqB7EtR,EAAE,UAiqBwFA,EAAE,CAACU,QAAQ,GAjqBrGV,EAAE,UAiqBgHoC,IAAI,CAACmP,kBAAkB,GAjqBzIvR,EAAE,UAiqBoJyR,WAAW,OAjqBjKzR,EAAE,UAiqB4MqN,6BAA6B;AAAA,CAA6C;AAChYoE,WAAW,CAACD,KAAK,kBAlqBuFxR,EAAE;EAAA,OAkqBOyR,WAAW;EAAA,SAAXA,WAAW;EAAA,YAAc3E;AAAiB,EAAG;AAC9J;EAAA,mDAnqBwG9M,EAAE,mBAmqBVyR,WAAW,EAAc,CAAC;IAC9GnM,IAAI,EAAE1E,UAAU;IAChB2E,IAAI,EAAE,CAAC;MAAE+H,UAAU,EAAER;IAAkB,CAAC;EAC5C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExH,IAAI,EAAEhD,IAAI,CAAC+O;IAAQ,CAAC,EAAE;MAAE/L,IAAI,EAAEnD,IAAI,CAACmP;IAAc,CAAC,EAAE;MAAEhM,IAAI,EAAEtF,EAAE,CAACU;IAAS,CAAC,EAAE;MAAE4E,IAAI,EAAElD,IAAI,CAACmP;IAAmB,CAAC,EAAE;MAAEjM,IAAI,EAAEmM,WAAW;MAAE9K,UAAU,EAAE,CAAC;QAChLrB,IAAI,EAAEzE;MACV,CAAC,EAAE;QACCyE,IAAI,EAAExE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwE,IAAI,EAAET,iBAAiB;MAAE8B,UAAU,EAAE,CAAC;QAC1CrB,IAAI,EAAEhF,MAAM;QACZiF,IAAI,EAAE,CAAC8H,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASzI,kBAAkB,EAAEyI,6BAA6B,EAAED,qCAAqC,EAAEqE,WAAW,EAAE9L,iBAAiB,EAAED,kBAAkB,EAAEb,iBAAiB,EAAE0H,oBAAoB,EAAEpH,gBAAgB,EAAE2H,iBAAiB,EAAEjK,cAAc,EAAE+C,cAAc,EAAE4H,gBAAgB,EAAEvG,yBAAyB,EAAEL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}