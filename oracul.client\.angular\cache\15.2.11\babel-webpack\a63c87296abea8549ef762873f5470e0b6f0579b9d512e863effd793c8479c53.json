{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AppComponent } from './app.component';\nimport { MaterialDemoComponent } from './material-demo.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// Authentication Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\n// Dashboard Component\nimport { DashboardComponent } from './dashboard/dashboard.component';\n// Theme Components\nimport { ThemeSelectorComponent } from './core/theme/theme-selector/theme-selector.component';\n// Profile Module\nimport { ProfileModule } from './profile/profile.module';\n// Profile Demo Component\nimport { ProfileDemoComponent } from './profile-demo/profile-demo.component';\nimport { ProfileCardDemoComponent } from './profile-card-demo/profile-card-demo.component';\n// Home Component\nimport { HomeComponent } from './home/<USER>';\n// Shared Components\nimport { NavigationComponent } from './shared/navigation/navigation.component';\n// Test Component\nimport { TestApiConnectionComponent } from './test-api-connection.component';\n// Services and Guards\nimport { AuthService } from './auth/services/auth.service';\nimport { OAuthService } from './auth/services/oauth.service';\nimport { ThemeService } from './core/theme/theme.service';\nimport { AuthGuard, RoleGuard } from './auth/guards/auth.guard';\nimport { AuthInterceptor } from './auth/interceptors/auth.interceptor';\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, MaterialDemoComponent, LoginComponent, RegisterComponent, DashboardComponent, ThemeSelectorComponent, NavigationComponent, ProfileDemoComponent, ProfileCardDemoComponent, HomeComponent, TestApiConnectionComponent],\n  imports: [BrowserModule, CommonModule, HttpClientModule, BrowserAnimationsModule, ReactiveFormsModule, FormsModule, ProfileModule, RouterModule.forRoot([{\n    path: 'home',\n    component: HomeComponent\n  }, {\n    path: 'login',\n    component: LoginComponent\n  }, {\n    path: 'register',\n    component: RegisterComponent\n  }, {\n    path: 'dashboard',\n    component: DashboardComponent,\n    canActivate: [AuthGuard]\n  }, {\n    path: 'profile-demo',\n    component: ProfileDemoComponent,\n    canActivate: [AuthGuard]\n  }, {\n    path: 'profile-card-demo',\n    component: ProfileCardDemoComponent,\n    canActivate: [AuthGuard]\n  }, {\n    path: 'test-api',\n    component: TestApiConnectionComponent\n  }, {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: '/home'\n  }]),\n  // Material Modules\n  MatToolbarModule, MatButtonModule, MatCardModule, MatTableModule, MatIconModule, MatProgressSpinnerModule, MatProgressBarModule, MatSnackBarModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatSidenavModule, MatListModule, MatMenuModule, MatTabsModule, MatDividerModule, MatChipsModule, MatDatepickerModule, MatNativeDateModule],\n  providers: [AuthService, OAuthService, ThemeService, AuthGuard, RoleGuard, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "mappings": ";AAAA,SAASA,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E;AACA,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,iBAAiB,QAAQ,oCAAoC;AAEtE;AACA,SAASC,kBAAkB,QAAQ,iCAAiC;AAEpE;AACA,SAASC,sBAAsB,QAAQ,sDAAsD;AAE7F;AACA,SAASC,aAAa,QAAQ,0BAA0B;AAExD;AACA,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,wBAAwB,QAAQ,iDAAiD;AAE1F;AACA,SAASC,aAAa,QAAQ,uBAAuB;AAErD;AACA,SAASC,mBAAmB,QAAQ,0CAA0C;AAE9E;AACA,SAASC,0BAA0B,QAAQ,iCAAiC;AAE5E;AACA,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,SAAS,EAAEC,SAAS,QAAQ,0BAA0B;AAC/D,SAASC,eAAe,QAAQ,sCAAsC;AAEtE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAoFrD,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,eAlFrB9C,QAAQ,CAAC;EACR+C,YAAY,EAAE,CACZzC,YAAY,EACZC,qBAAqB,EACrBE,cAAc,EACdC,iBAAiB,EACjBC,kBAAkB,EAClBC,sBAAsB,EACtBK,mBAAmB,EACnBH,oBAAoB,EACpBC,wBAAwB,EACxBC,aAAa,EACbE,0BAA0B,CAC3B;EACD8B,OAAO,EAAE,CACP/C,aAAa,EACbC,YAAY,EACZJ,gBAAgB,EAChBU,uBAAuB,EACvBL,mBAAmB,EACnBC,WAAW,EACXS,aAAa,EACbR,YAAY,CAAC4C,OAAO,CAAC,CACnB;IAAEC,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEnC;EAAa,CAAE,EAC1C;IAAEkC,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAE1C;EAAc,CAAE,EAC5C;IAAEyC,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEzC;EAAiB,CAAE,EAClD;IACEwC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAExC,kBAAkB;IAC7ByC,WAAW,EAAE,CAAC9B,SAAS;GACxB,EACD;IACE4B,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAErC,oBAAoB;IAC/BsC,WAAW,EAAE,CAAC9B,SAAS;GACxB,EACD;IACE4B,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEpC,wBAAwB;IACnCqC,WAAW,EAAE,CAAC9B,SAAS;GACxB,EACD;IAAE4B,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEjC;EAA0B,CAAE,EAC3D;IAAEgC,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,OAAO;IAAEC,SAAS,EAAE;EAAM,CAAE,EACpD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE;EAAO,CAAE,CACpC,CAAC;EACF;EACA5B,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,wBAAwB,EACxBC,oBAAoB,EACpBC,iBAAiB,EACjBC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,CACpB;EACDU,SAAS,EAAE,CACTpC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,SAAS,EACTC,SAAS,EACT;IACEiC,OAAO,EAAEzD,iBAAiB;IAC1B0D,QAAQ,EAAEjC,eAAe;IACzBkC,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAACrD,YAAY;CACzB,CAAC,GACWwC,SAAS,CAAI;SAAbA,SAAS", "names": ["HttpClientModule", "HTTP_INTERCEPTORS", "NgModule", "BrowserModule", "CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "AppComponent", "MaterialDemoComponent", "BrowserAnimationsModule", "LoginComponent", "RegisterComponent", "DashboardComponent", "ThemeSelectorComponent", "ProfileModule", "ProfileDemoComponent", "ProfileCardDemoComponent", "HomeComponent", "NavigationComponent", "TestApiConnectionComponent", "AuthService", "OAuthService", "ThemeService", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "AuthInterceptor", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatTableModule", "MatIconModule", "MatProgressSpinnerModule", "MatProgressBarModule", "MatSnackBarModule", "MatDialogModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatSidenavModule", "MatListModule", "MatMenuModule", "MatTabsModule", "MatDividerModule", "MatChipsModule", "MatDatepickerModule", "MatNativeDateModule", "AppModule", "declarations", "imports", "forRoot", "path", "component", "canActivate", "redirectTo", "pathMatch", "providers", "provide", "useClass", "multi", "bootstrap"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.module.ts"], "sourcesContent": ["import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { AppComponent } from './app.component';\r\nimport { MaterialDemoComponent } from './material-demo.component';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\n\r\n// Authentication Components\r\nimport { LoginComponent } from './auth/login/login.component';\r\nimport { RegisterComponent } from './auth/register/register.component';\r\n\r\n// Dashboard Component\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\n\r\n// Theme Components\r\nimport { ThemeSelectorComponent } from './core/theme/theme-selector/theme-selector.component';\r\n\r\n// Profile Module\r\nimport { ProfileModule } from './profile/profile.module';\r\n\r\n// Profile Demo Component\r\nimport { ProfileDemoComponent } from './profile-demo/profile-demo.component';\r\nimport { ProfileCardDemoComponent } from './profile-card-demo/profile-card-demo.component';\r\n\r\n// Home Component\r\nimport { HomeComponent } from './home/<USER>';\r\n\r\n// Shared Components\r\nimport { NavigationComponent } from './shared/navigation/navigation.component';\r\n\r\n// Test Component\r\nimport { TestApiConnectionComponent } from './test-api-connection.component';\r\n\r\n// Services and Guards\r\nimport { AuthService } from './auth/services/auth.service';\r\nimport { OAuthService } from './auth/services/oauth.service';\r\nimport { ThemeService } from './core/theme/theme.service';\r\nimport { AuthGuard, RoleGuard } from './auth/guards/auth.guard';\r\nimport { AuthInterceptor } from './auth/interceptors/auth.interceptor';\r\n\r\n// Angular Material Modules\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatSidenavModule } from '@angular/material/sidenav';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatDividerModule } from '@angular/material/divider';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    MaterialDemoComponent,\r\n    LoginComponent,\r\n    RegisterComponent,\r\n    DashboardComponent,\r\n    ThemeSelectorComponent,\r\n    NavigationComponent,\r\n    ProfileDemoComponent,\r\n    ProfileCardDemoComponent,\r\n    HomeComponent,\r\n    TestApiConnectionComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    CommonModule,\r\n    HttpClientModule,\r\n    BrowserAnimationsModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    ProfileModule,\r\n    RouterModule.forRoot([\r\n      { path: 'home', component: HomeComponent },\r\n      { path: 'login', component: LoginComponent },\r\n      { path: 'register', component: RegisterComponent },\r\n      {\r\n        path: 'dashboard',\r\n        component: DashboardComponent,\r\n        canActivate: [AuthGuard]\r\n      },\r\n      {\r\n        path: 'profile-demo',\r\n        component: ProfileDemoComponent,\r\n        canActivate: [AuthGuard]\r\n      },\r\n      {\r\n        path: 'profile-card-demo',\r\n        component: ProfileCardDemoComponent,\r\n        canActivate: [AuthGuard]\r\n      },\r\n      { path: 'test-api', component: TestApiConnectionComponent },\r\n      { path: '', redirectTo: '/home', pathMatch: 'full' },\r\n      { path: '**', redirectTo: '/home' }\r\n    ]),\r\n    // Material Modules\r\n    MatToolbarModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatTableModule,\r\n    MatIconModule,\r\n    MatProgressSpinnerModule,\r\n    MatProgressBarModule,\r\n    MatSnackBarModule,\r\n    MatDialogModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatCheckboxModule,\r\n    MatSidenavModule,\r\n    MatListModule,\r\n    MatMenuModule,\r\n    MatTabsModule,\r\n    MatDividerModule,\r\n    MatChipsModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule\r\n  ],\r\n  providers: [\r\n    AuthService,\r\n    OAuthService,\r\n    ThemeService,\r\n    AuthGuard,\r\n    RoleGuard,\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: AuthInterceptor,\r\n      multi: true\r\n    }\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}