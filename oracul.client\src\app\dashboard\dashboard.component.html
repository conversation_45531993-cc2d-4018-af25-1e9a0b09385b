<!-- Dashboard Content -->
<div class="dashboard-container">
  <!-- Header Card -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>wb_sunny</mat-icon>
        {{ t.dashboard.title }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ t.dashboard.weatherForecast }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>Този компонент демонстрира извличане на данни от сървъра с помощта на Angular Material компоненти.</p>
      <button mat-raised-button color="primary" (click)="getForecasts()">
        <mat-icon>refresh</mat-icon>
        Обнови данните
      </button>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="!forecasts" class="loading-container">
    <mat-card>
      <mat-card-content>
        <div class="loading-content">
          <mat-spinner diameter="50"></mat-spinner>
          <p>{{ t.common.loading }}...</p>
          <p><em>Моля, обновете след като ASP.NET backend-ът е стартиран.</em></p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Weather Data Table -->
  <mat-card *ngIf="forecasts" class="data-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>table_chart</mat-icon>
        Данни за прогнозата за времето
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="forecasts" class="weather-table">
          <!-- Date Column -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>calendar_today</mat-icon>
              Дата
            </th>
            <td mat-cell *matCellDef="let forecast">{{ forecast.date | date:'short' }}</td>
          </ng-container>

          <!-- Temperature C Column -->
          <ng-container matColumnDef="temperatureC">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>thermostat</mat-icon>
              Темп. (°C)
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="temperature">{{ forecast.temperatureC }}°</span>
            </td>
          </ng-container>

          <!-- Temperature F Column -->
          <ng-container matColumnDef="temperatureF">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>thermostat</mat-icon>
              Темп. (°F)
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="temperature">{{ forecast.temperatureF }}°</span>
            </td>
          </ng-container>

          <!-- Summary Column -->
          <ng-container matColumnDef="summary">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>description</mat-icon>
              Резюме
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="summary">{{ forecast.summary }}</span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Profile System Demo -->
  <mat-card class="data-card profile-demo-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>person</mat-icon>
        {{ t.dashboard.profileSystem }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ t.dashboard.profileSystemDescription }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>Изследвайте цялостната професионална профилна система с дизайн и функции, вдъхновени от LinkedIn.</p>
      <div class="profile-features">
        <span class="feature-tag">{{ t.dashboard.readingPortfolio }}</span>
        <span class="feature-tag">{{ t.dashboard.astrologicalSkills }}</span>
        <span class="feature-tag">{{ t.dashboard.spiritualJourney }}</span>
        <span class="feature-tag">{{ t.dashboard.cosmicAnalytics }}</span>
      </div>
    </mat-card-content>
    <mat-card-actions>
      <button mat-raised-button color="primary" routerLink="/profile-demo">
        <mat-icon>launch</mat-icon>
        {{ t.dashboard.tryProfileDemo }}
      </button>
      <button mat-stroked-button routerLink="/profile/luna-starweaver">
        <mat-icon>visibility</mat-icon>
        {{ t.dashboard.viewSampleProfile }}
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Material Design Demo -->
  <app-material-demo></app-material-demo>
</div>
