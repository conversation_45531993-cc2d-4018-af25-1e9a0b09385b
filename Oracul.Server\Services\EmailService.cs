using Microsoft.Extensions.Options;
using Oracul.Server.Models;
using System.Net;
using System.Net.Mail;

namespace Oracul.Server.Services
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;
        private readonly IConfiguration _configuration;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger, IConfiguration configuration)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<bool> SendPasswordResetEmailAsync(string email, string resetToken, string userName)
        {
            var resetUrl = $"{_configuration["AppSettings:ClientUrl"]}/reset-password?token={resetToken}&email={Uri.EscapeDataString(email)}";
            
            var subject = "Reset Your Password - Oracul";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #673ab7;'>Password Reset Request</h2>
                        <p>Hello {userName},</p>
                        <p>We received a request to reset your password for your Oracul account.</p>
                        <p>Click the button below to reset your password:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{resetUrl}' style='background-color: #673ab7; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Reset Password</a>
                        </div>
                        <p>If the button doesn't work, copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #666;'>{resetUrl}</p>
                        <p><strong>This link will expire in 1 hour.</strong></p>
                        <p>If you didn't request this password reset, please ignore this email.</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='color: #666; font-size: 12px;'>This is an automated message from Oracul. Please do not reply to this email.</p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, body);
        }

        public async Task<bool> SendEmailConfirmationAsync(string email, string confirmationToken, string userName)
        {
            var confirmUrl = $"{_configuration["AppSettings:ClientUrl"]}/confirm-email?token={confirmationToken}&email={Uri.EscapeDataString(email)}";
            
            var subject = "Confirm Your Email - Oracul";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #673ab7;'>Welcome to Oracul!</h2>
                        <p>Hello {userName},</p>
                        <p>Thank you for creating an account with Oracul. To complete your registration, please confirm your email address.</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{confirmUrl}' style='background-color: #673ab7; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Confirm Email</a>
                        </div>
                        <p>If the button doesn't work, copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #666;'>{confirmUrl}</p>
                        <p>If you didn't create this account, please ignore this email.</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='color: #666; font-size: 12px;'>This is an automated message from Oracul. Please do not reply to this email.</p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, body);
        }

        public async Task<bool> SendWelcomeEmailAsync(string email, string userName)
        {
            var subject = "Welcome to Oracul!";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #673ab7;'>Welcome to Oracul!</h2>
                        <p>Hello {userName},</p>
                        <p>Your account has been successfully created and verified. Welcome to the Oracul community!</p>
                        <p>You can now access all features of our platform.</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{_configuration["AppSettings:ClientUrl"]}/login' style='background-color: #673ab7; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Login to Your Account</a>
                        </div>
                        <p>If you have any questions, feel free to contact our support team.</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='color: #666; font-size: 12px;'>This is an automated message from Oracul. Please do not reply to this email.</p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, body);
        }

        public async Task<bool> SendEmailAsync(string to, string subject, string body)
        {
            try
            {
                using var client = new SmtpClient(_emailSettings.SmtpHost, _emailSettings.SmtpPort);
                client.EnableSsl = _emailSettings.EnableSsl;
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword);

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };

                mailMessage.To.Add(to);

                await client.SendMailAsync(mailMessage);
                _logger.LogInformation("Email sent successfully to {Email}", to);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email}", to);
                return false;
            }
        }
    }
}
