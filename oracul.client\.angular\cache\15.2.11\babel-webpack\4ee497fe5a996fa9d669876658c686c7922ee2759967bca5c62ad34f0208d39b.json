{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet ProfileCardDemoComponent = class ProfileCardDemoComponent {\n  constructor() {\n    this.sampleProfiles = [];\n  }\n  ngOnInit() {\n    this.loadSampleProfiles();\n  }\n  loadSampleProfiles() {\n    this.sampleProfiles = [{\n      id: 1,\n      userId: 1,\n      username: 'luna-starweaver',\n      slug: 'luna-starweaver',\n      isPublic: true,\n      profileCompletionPercentage: 95,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',\n      firstName: 'Luna',\n      lastName: 'Starweaver',\n      professionalTitle: 'Professional Astrologer & Cosmic Guide',\n      headline: 'Illuminating your path through the wisdom of the stars',\n      location: {\n        city: 'Sedona',\n        state: 'Arizona',\n        country: 'USA',\n        displayLocation: 'Sedona, Arizona'\n      },\n      contactInfo: {\n        email: '<EMAIL>',\n        isEmailPublic: true,\n        website: 'https://starweaver.com',\n        phoneNumbers: [{\n          number: '+****************',\n          type: 'business',\n          isPublic: true,\n          isPrimary: true\n        }]\n      },\n      summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling. My approach combines traditional astrological wisdom with modern psychological insights.',\n      skills: [{\n        name: 'Natal Chart Reading',\n        endorsements: 45,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Tarot Reading',\n        endorsements: 38,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Spiritual Counseling',\n        endorsements: 32,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Crystal Healing',\n        endorsements: 28,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Meditation Guidance',\n        endorsements: 25,\n        proficiencyLevel: 'intermediate'\n      }],\n      experiences: [{\n        company: 'Cosmic Wisdom Center',\n        position: 'Senior Astrologer',\n        startDate: new Date('2018-01-01'),\n        isCurrent: true,\n        description: 'Leading astrologer providing personalized readings and workshops',\n        location: 'Sedona, Arizona'\n      }, {\n        company: 'Mystic Moon Studio',\n        position: 'Astrology Consultant',\n        startDate: new Date('2015-01-01'),\n        endDate: new Date('2017-12-31'),\n        isCurrent: false,\n        description: 'Provided astrological consultations and spiritual guidance',\n        location: 'Santa Fe, New Mexico'\n      }],\n      portfolioItems: [{\n        title: 'Cosmic Birth Chart Analysis',\n        description: 'Comprehensive natal chart reading service',\n        imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],\n        technologies: ['Astrology', 'Spiritual Guidance'],\n        completedAt: new Date('2023-01-01'),\n        category: 'Astrology Services'\n      }],\n      blogPosts: [],\n      achievements: [],\n      certifications: [],\n      socialLinks: [],\n      profileViews: 1247,\n      createdAt: new Date('2020-01-01'),\n      updatedAt: new Date('2023-12-01')\n    }, {\n      id: 2,\n      userId: 2,\n      username: 'marcus-celestial',\n      slug: 'marcus-celestial',\n      isPublic: true,\n      profileCompletionPercentage: 88,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',\n      firstName: 'Marcus',\n      lastName: 'Celestial',\n      professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',\n      headline: 'Unlocking the secrets of your cosmic blueprint',\n      location: {\n        city: 'Los Angeles',\n        state: 'California',\n        country: 'USA',\n        displayLocation: 'Los Angeles, California'\n      },\n      contactInfo: {\n        email: '<EMAIL>',\n        isEmailPublic: true,\n        website: 'https://celestialreadings.com',\n        phoneNumbers: [{\n          number: '+****************',\n          type: 'business',\n          isPublic: true,\n          isPrimary: true\n        }]\n      },\n      summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges through astrological insights.',\n      skills: [{\n        name: 'Birth Chart Analysis',\n        endorsements: 52,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Compatibility Reading',\n        endorsements: 41,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Life Transitions',\n        endorsements: 35,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Career Guidance',\n        endorsements: 29,\n        proficiencyLevel: 'advanced'\n      }],\n      experiences: [{\n        company: 'Celestial Insights',\n        position: 'Lead Astrologer',\n        startDate: new Date('2019-01-01'),\n        isCurrent: true,\n        description: 'Providing comprehensive astrological services and mentoring junior astrologers',\n        location: 'Los Angeles, California'\n      }],\n      portfolioItems: [],\n      blogPosts: [],\n      achievements: [],\n      certifications: [],\n      socialLinks: [],\n      profileViews: 892,\n      createdAt: new Date('2021-01-01'),\n      updatedAt: new Date('2023-11-15')\n    }];\n  }\n  onContactProfile(profile) {\n    console.log('Contact profile:', profile.firstName, profile.lastName);\n    if (profile.contactInfo?.email) {\n      window.location.href = `mailto:${profile.contactInfo.email}`;\n    }\n  }\n  onProfileClicked(profile) {\n    console.log('Profile clicked:', profile.firstName, profile.lastName);\n    // In a real app, this would navigate to the profile\n  }\n};\n\nProfileCardDemoComponent = __decorate([Component({\n  selector: 'app-profile-card-demo',\n  templateUrl: './profile-card-demo.component.html',\n  styleUrls: ['./profile-card-demo.component.css']\n})], ProfileCardDemoComponent);\nexport { ProfileCardDemoComponent };", "map": {"version": 3, "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAQ1C,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC;IACL,mBAAc,GAAkB,EAAE;EA2JpC;EAzJEC,QAAQ;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkB;IACxB,IAAI,CAACC,cAAc,GAAG,CACpB;MACEC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE,iBAAiB;MACvBC,QAAQ,EAAE,IAAI;MACdC,2BAA2B,EAAE,EAAE;MAC/BC,eAAe,EAAE,oEAAoE;MACrFC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,YAAY;MACtBC,iBAAiB,EAAE,wCAAwC;MAC3DC,QAAQ,EAAE,wDAAwD;MAClEC,QAAQ,EAAE;QACRC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE,KAAK;QACdC,eAAe,EAAE;OAClB;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,qBAAqB;QAC5BC,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE,wBAAwB;QACjCC,YAAY,EAAE,CACZ;UACEC,MAAM,EAAE,mBAAmB;UAC3BC,IAAI,EAAE,UAAU;UAChBlB,QAAQ,EAAE,IAAI;UACdmB,SAAS,EAAE;SACZ;OAEJ;MACDC,OAAO,EAAE,iQAAiQ;MAC1QC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,qBAAqB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC7E;QAAEF,IAAI,EAAE,eAAe;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACvE;QAAEF,IAAI,EAAE,sBAAsB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAChF;QAAEF,IAAI,EAAE,iBAAiB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC3E;QAAEF,IAAI,EAAE,qBAAqB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAc,CAAE,CACpF;MACDC,WAAW,EAAE,CACX;QACEC,OAAO,EAAE,sBAAsB;QAC/BC,QAAQ,EAAE,mBAAmB;QAC7BC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,kEAAkE;QAC/ExB,QAAQ,EAAE;OACX,EACD;QACEmB,OAAO,EAAE,oBAAoB;QAC7BC,QAAQ,EAAE,sBAAsB;QAChCC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCG,OAAO,EAAE,IAAIH,IAAI,CAAC,YAAY,CAAC;QAC/BC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE,4DAA4D;QACzExB,QAAQ,EAAE;OACX,CACF;MACD0B,cAAc,EAAE,CACd;QACEC,KAAK,EAAE,6BAA6B;QACpCH,WAAW,EAAE,2CAA2C;QACxDI,SAAS,EAAE,CAAC,oEAAoE,CAAC;QACjFC,YAAY,EAAE,CAAC,WAAW,EAAE,oBAAoB,CAAC;QACjDC,WAAW,EAAE,IAAIR,IAAI,CAAC,YAAY,CAAC;QACnCS,QAAQ,EAAE;OACX,CACF;MACDC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAIf,IAAI,CAAC,YAAY,CAAC;MACjCgB,SAAS,EAAE,IAAIhB,IAAI,CAAC,YAAY;KACjC,EACD;MACEjC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE,kBAAkB;MACxBC,QAAQ,EAAE,IAAI;MACdC,2BAA2B,EAAE,EAAE;MAC/BC,eAAe,EAAE,oEAAoE;MACrFC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,WAAW;MACrBC,iBAAiB,EAAE,2CAA2C;MAC9DC,QAAQ,EAAE,gDAAgD;MAC1DC,QAAQ,EAAE;QACRC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE,KAAK;QACdC,eAAe,EAAE;OAClB;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,8BAA8B;QACrCC,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE,+BAA+B;QACxCC,YAAY,EAAE,CACZ;UACEC,MAAM,EAAE,mBAAmB;UAC3BC,IAAI,EAAE,UAAU;UAChBlB,QAAQ,EAAE,IAAI;UACdmB,SAAS,EAAE;SACZ;OAEJ;MACDC,OAAO,EAAE,4LAA4L;MACrMC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,sBAAsB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC9E;QAAEF,IAAI,EAAE,uBAAuB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC/E;QAAEF,IAAI,EAAE,kBAAkB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC5E;QAAEF,IAAI,EAAE,iBAAiB;QAAEC,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAC5E;MACDC,WAAW,EAAE,CACX;QACEC,OAAO,EAAE,oBAAoB;QAC7BC,QAAQ,EAAE,iBAAiB;QAC3BC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,gFAAgF;QAC7FxB,QAAQ,EAAE;OACX,CACF;MACD0B,cAAc,EAAE,EAAE;MAClBM,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,GAAG;MACjBC,SAAS,EAAE,IAAIf,IAAI,CAAC,YAAY,CAAC;MACjCgB,SAAS,EAAE,IAAIhB,IAAI,CAAC,YAAY;KACjC,CACF;EACH;EAEAiB,gBAAgB,CAACC,OAAoB;IACnCC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC5C,SAAS,EAAE4C,OAAO,CAAC3C,QAAQ,CAAC;IACpE,IAAI2C,OAAO,CAACnC,WAAW,EAAEC,KAAK,EAAE;MAC9BqC,MAAM,CAAC3C,QAAQ,CAAC4C,IAAI,GAAG,UAAUJ,OAAO,CAACnC,WAAW,CAACC,KAAK,EAAE;;EAEhE;EAEAuC,gBAAgB,CAACL,OAAoB;IACnCC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC5C,SAAS,EAAE4C,OAAO,CAAC3C,QAAQ,CAAC;IACpE;EACF;CACD;;AA5JYb,wBAAwB,eALpCD,SAAS,CAAC;EACT+D,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,GACWhE,wBAAwB,CA4JpC;SA5JYA,wBAAwB", "names": ["Component", "ProfileCardDemoComponent", "constructor", "ngOnInit", "loadSampleProfiles", "sampleProfiles", "id", "userId", "username", "slug", "isPublic", "profileCompletionPercentage", "profilePhotoUrl", "firstName", "lastName", "professional<PERSON>itle", "headline", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "phoneNumbers", "number", "type", "isPrimary", "summary", "skills", "name", "endorsements", "proficiencyLevel", "experiences", "company", "position", "startDate", "Date", "isCurrent", "description", "endDate", "portfolioItems", "title", "imageUrls", "technologies", "completedAt", "category", "blogPosts", "achievements", "certifications", "socialLinks", "profileViews", "createdAt", "updatedAt", "onContactProfile", "profile", "console", "log", "window", "href", "onProfileClicked", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile-card-demo\\profile-card-demo.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { UserProfile } from '../profile/models/profile.models';\n\n@Component({\n  selector: 'app-profile-card-demo',\n  templateUrl: './profile-card-demo.component.html',\n  styleUrls: ['./profile-card-demo.component.css']\n})\nexport class ProfileCardDemoComponent implements OnInit {\n  sampleProfiles: UserProfile[] = [];\n\n  ngOnInit(): void {\n    this.loadSampleProfiles();\n  }\n\n  private loadSampleProfiles(): void {\n    this.sampleProfiles = [\n      {\n        id: 1,\n        userId: 1,\n        username: 'luna-starweaver',\n        slug: 'luna-starweaver',\n        isPublic: true,\n        profileCompletionPercentage: 95,\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',\n        firstName: 'Luna',\n        lastName: 'Starweaver',\n        professionalTitle: 'Professional Astrologer & Cosmic Guide',\n        headline: 'Illuminating your path through the wisdom of the stars',\n        location: {\n          city: 'Sedona',\n          state: 'Arizona',\n          country: 'USA',\n          displayLocation: 'Sedona, Arizona'\n        },\n        contactInfo: {\n          email: '<EMAIL>',\n          isEmailPublic: true,\n          website: 'https://starweaver.com',\n          phoneNumbers: [\n            {\n              number: '+****************',\n              type: 'business',\n              isPublic: true,\n              isPrimary: true\n            }\n          ]\n        },\n        summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling. My approach combines traditional astrological wisdom with modern psychological insights.',\n        skills: [\n          { name: 'Natal Chart Reading', endorsements: 45, proficiencyLevel: 'expert' },\n          { name: 'Tarot Reading', endorsements: 38, proficiencyLevel: 'expert' },\n          { name: 'Spiritual Counseling', endorsements: 32, proficiencyLevel: 'advanced' },\n          { name: 'Crystal Healing', endorsements: 28, proficiencyLevel: 'advanced' },\n          { name: 'Meditation Guidance', endorsements: 25, proficiencyLevel: 'intermediate' }\n        ],\n        experiences: [\n          {\n            company: 'Cosmic Wisdom Center',\n            position: 'Senior Astrologer',\n            startDate: new Date('2018-01-01'),\n            isCurrent: true,\n            description: 'Leading astrologer providing personalized readings and workshops',\n            location: 'Sedona, Arizona'\n          },\n          {\n            company: 'Mystic Moon Studio',\n            position: 'Astrology Consultant',\n            startDate: new Date('2015-01-01'),\n            endDate: new Date('2017-12-31'),\n            isCurrent: false,\n            description: 'Provided astrological consultations and spiritual guidance',\n            location: 'Santa Fe, New Mexico'\n          }\n        ],\n        portfolioItems: [\n          {\n            title: 'Cosmic Birth Chart Analysis',\n            description: 'Comprehensive natal chart reading service',\n            imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],\n            technologies: ['Astrology', 'Spiritual Guidance'],\n            completedAt: new Date('2023-01-01'),\n            category: 'Astrology Services'\n          }\n        ],\n        blogPosts: [],\n        achievements: [],\n        certifications: [],\n        socialLinks: [],\n        profileViews: 1247,\n        createdAt: new Date('2020-01-01'),\n        updatedAt: new Date('2023-12-01')\n      },\n      {\n        id: 2,\n        userId: 2,\n        username: 'marcus-celestial',\n        slug: 'marcus-celestial',\n        isPublic: true,\n        profileCompletionPercentage: 88,\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',\n        firstName: 'Marcus',\n        lastName: 'Celestial',\n        professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',\n        headline: 'Unlocking the secrets of your cosmic blueprint',\n        location: {\n          city: 'Los Angeles',\n          state: 'California',\n          country: 'USA',\n          displayLocation: 'Los Angeles, California'\n        },\n        contactInfo: {\n          email: '<EMAIL>',\n          isEmailPublic: true,\n          website: 'https://celestialreadings.com',\n          phoneNumbers: [\n            {\n              number: '+****************',\n              type: 'business',\n              isPublic: true,\n              isPrimary: true\n            }\n          ]\n        },\n        summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges through astrological insights.',\n        skills: [\n          { name: 'Birth Chart Analysis', endorsements: 52, proficiencyLevel: 'expert' },\n          { name: 'Compatibility Reading', endorsements: 41, proficiencyLevel: 'expert' },\n          { name: 'Life Transitions', endorsements: 35, proficiencyLevel: 'advanced' },\n          { name: 'Career Guidance', endorsements: 29, proficiencyLevel: 'advanced' }\n        ],\n        experiences: [\n          {\n            company: 'Celestial Insights',\n            position: 'Lead Astrologer',\n            startDate: new Date('2019-01-01'),\n            isCurrent: true,\n            description: 'Providing comprehensive astrological services and mentoring junior astrologers',\n            location: 'Los Angeles, California'\n          }\n        ],\n        portfolioItems: [],\n        blogPosts: [],\n        achievements: [],\n        certifications: [],\n        socialLinks: [],\n        profileViews: 892,\n        createdAt: new Date('2021-01-01'),\n        updatedAt: new Date('2023-11-15')\n      }\n    ];\n  }\n\n  onContactProfile(profile: UserProfile): void {\n    console.log('Contact profile:', profile.firstName, profile.lastName);\n    if (profile.contactInfo?.email) {\n      window.location.href = `mailto:${profile.contactInfo.email}`;\n    }\n  }\n\n  onProfileClicked(profile: UserProfile): void {\n    console.log('Profile clicked:', profile.firstName, profile.lastName);\n    // In a real app, this would navigate to the profile\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}