{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';\nimport * as i0 from \"@angular/core\";\nexport class ThemeService {\n  constructor() {\n    this.THEME_STORAGE_KEY = 'oracul-theme';\n    this.currentThemeSubject = new BehaviorSubject(MYSTICAL_PURPLE_THEME);\n    this.currentTheme$ = this.currentThemeSubject.asObservable();\n    this.loadSavedTheme();\n  }\n  loadSavedTheme() {\n    const savedThemeName = localStorage.getItem(this.THEME_STORAGE_KEY);\n    if (savedThemeName) {\n      const savedTheme = AVAILABLE_THEMES.find(theme => theme.name === savedThemeName);\n      if (savedTheme) {\n        this.setTheme(savedTheme);\n        return;\n      }\n    }\n    // Apply default theme\n    this.applyTheme(MYSTICAL_PURPLE_THEME);\n  }\n  setTheme(theme) {\n    this.currentThemeSubject.next(theme);\n    this.applyTheme(theme);\n    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);\n  }\n  setThemeByName(themeName) {\n    const theme = AVAILABLE_THEMES.find(t => t.name === themeName);\n    if (theme) {\n      this.setTheme(theme);\n    }\n  }\n  getCurrentTheme() {\n    return this.currentThemeSubject.value;\n  }\n  getAvailableThemes() {\n    return AVAILABLE_THEMES;\n  }\n  applyTheme(theme) {\n    const root = document.documentElement;\n    const body = document.body;\n    const colors = theme.colors;\n    // Add transition class to prevent flicker\n    body.classList.add('theme-changing');\n    // Apply CSS custom properties\n    root.style.setProperty('--theme-primary', colors.primary);\n    root.style.setProperty('--theme-primary-light', colors.primaryLight);\n    root.style.setProperty('--theme-primary-dark', colors.primaryDark);\n    root.style.setProperty('--theme-accent', colors.accent);\n    root.style.setProperty('--theme-accent-light', colors.accentLight);\n    root.style.setProperty('--theme-accent-dark', colors.accentDark);\n    root.style.setProperty('--theme-warn', colors.warn);\n    root.style.setProperty('--theme-success', colors.success);\n    root.style.setProperty('--theme-error', colors.error);\n    root.style.setProperty('--theme-background', colors.background);\n    root.style.setProperty('--theme-surface', colors.surface);\n    // Text colors\n    root.style.setProperty('--theme-text-primary', colors.text.primary);\n    root.style.setProperty('--theme-text-secondary', colors.text.secondary);\n    root.style.setProperty('--theme-text-disabled', colors.text.disabled);\n    root.style.setProperty('--theme-text-hint', colors.text.hint);\n    // Gradients\n    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);\n    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);\n    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);\n    // OAuth colors\n    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);\n    root.style.setProperty('--theme-google-border', colors.oauth.google.border);\n    root.style.setProperty('--theme-google-text', colors.oauth.google.text);\n    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);\n    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);\n    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);\n    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);\n    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);\n    // Update body class for theme-specific styling\n    body.className = body.className.replace(/theme-\\w+/g, '');\n    body.classList.add(`theme-${theme.name}`);\n    // Force a repaint to ensure Material components pick up the new colors\n    setTimeout(() => {\n      body.classList.remove('theme-changing');\n      // Force style recalculation for all Material components\n      const materialSelectors = ['.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button', '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle', '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip', '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card'];\n      materialSelectors.forEach(selector => {\n        const elements = document.querySelectorAll(selector);\n        elements.forEach(el => {\n          const element = el;\n          // Force recomputation of styles\n          const computedStyle = window.getComputedStyle(element);\n          element.style.cssText = element.style.cssText;\n        });\n      });\n      // Dispatch a custom event to notify components of theme change\n      window.dispatchEvent(new CustomEvent('themeChanged', {\n        detail: theme\n      }));\n    }, 100);\n  }\n  // Helper method to create custom theme\n  createCustomTheme(name, colors) {\n    const baseTheme = MYSTICAL_PURPLE_THEME;\n    return {\n      name,\n      colors: {\n        ...baseTheme.colors,\n        ...colors,\n        text: {\n          ...baseTheme.colors.text,\n          ...(colors.text || {})\n        },\n        gradient: {\n          ...baseTheme.colors.gradient,\n          ...(colors.gradient || {})\n        },\n        oauth: {\n          google: {\n            ...baseTheme.colors.oauth.google,\n            ...(colors.oauth?.google || {})\n          },\n          facebook: {\n            ...baseTheme.colors.oauth.facebook,\n            ...(colors.oauth?.facebook || {})\n          }\n        }\n      }\n    };\n  }\n  // Export current theme configuration\n  exportTheme() {\n    return JSON.stringify(this.getCurrentTheme(), null, 2);\n  }\n  // Import theme from JSON\n  importTheme(themeJson) {\n    try {\n      const theme = JSON.parse(themeJson);\n      if (this.validateTheme(theme)) {\n        this.setTheme(theme);\n        return true;\n      }\n      return false;\n    } catch {\n      return false;\n    }\n  }\n  validateTheme(theme) {\n    return theme && typeof theme.name === 'string' && theme.colors && typeof theme.colors.primary === 'string' && typeof theme.colors.accent === 'string';\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(t) {\n      return new (t || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAAkDC,qBAAqB,EAAEC,gBAAgB,QAAQ,gBAAgB;;AAKjH,OAAM,MAAOC,YAAY;EAKvBC;IAJiB,sBAAiB,GAAG,cAAc;IAC3C,wBAAmB,GAAG,IAAIJ,eAAe,CAAcC,qBAAqB,CAAC;IAC9E,kBAAa,GAAG,IAAI,CAACI,mBAAmB,CAACC,YAAY,EAAE;IAG5D,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQA,cAAc;IACpB,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,iBAAiB,CAAC;IACnE,IAAIH,cAAc,EAAE;MAClB,MAAMI,UAAU,GAAGV,gBAAgB,CAACW,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAKP,cAAc,CAAC;MAChF,IAAII,UAAU,EAAE;QACd,IAAI,CAACI,QAAQ,CAACJ,UAAU,CAAC;QACzB;;;IAGJ;IACA,IAAI,CAACK,UAAU,CAAChB,qBAAqB,CAAC;EACxC;EAEAe,QAAQ,CAACF,KAAkB;IACzB,IAAI,CAACT,mBAAmB,CAACa,IAAI,CAACJ,KAAK,CAAC;IACpC,IAAI,CAACG,UAAU,CAACH,KAAK,CAAC;IACtBL,YAAY,CAACU,OAAO,CAAC,IAAI,CAACR,iBAAiB,EAAEG,KAAK,CAACC,IAAI,CAAC;EAC1D;EAEAK,cAAc,CAACC,SAAiB;IAC9B,MAAMP,KAAK,GAAGZ,gBAAgB,CAACW,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACP,IAAI,KAAKM,SAAS,CAAC;IAC9D,IAAIP,KAAK,EAAE;MACT,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC;;EAExB;EAEAS,eAAe;IACb,OAAO,IAAI,CAAClB,mBAAmB,CAACmB,KAAK;EACvC;EAEAC,kBAAkB;IAChB,OAAOvB,gBAAgB;EACzB;EAEQe,UAAU,CAACH,KAAkB;IACnC,MAAMY,IAAI,GAAGC,QAAQ,CAACC,eAAe;IACrC,MAAMC,IAAI,GAAGF,QAAQ,CAACE,IAAI;IAC1B,MAAMC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IAE3B;IACAD,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAEpC;IACAN,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACK,OAAO,CAAC;IACzDT,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACM,YAAY,CAAC;IACpEV,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACO,WAAW,CAAC;IAClEX,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAEJ,MAAM,CAACQ,MAAM,CAAC;IACvDZ,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACS,WAAW,CAAC;IAClEb,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACU,UAAU,CAAC;IAChEd,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,cAAc,EAAEJ,MAAM,CAACW,IAAI,CAAC;IACnDf,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACY,OAAO,CAAC;IACzDhB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,eAAe,EAAEJ,MAAM,CAACa,KAAK,CAAC;IACrDjB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAEJ,MAAM,CAACc,UAAU,CAAC;IAC/DlB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAEJ,MAAM,CAACe,OAAO,CAAC;IAEzD;IACAnB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACgB,IAAI,CAACX,OAAO,CAAC;IACnET,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAEJ,MAAM,CAACgB,IAAI,CAACC,SAAS,CAAC;IACvErB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACgB,IAAI,CAACE,QAAQ,CAAC;IACrEtB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAEJ,MAAM,CAACgB,IAAI,CAACG,IAAI,CAAC;IAE7D;IACAvB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,0BAA0B,EAAEJ,MAAM,CAACoB,QAAQ,CAACf,OAAO,CAAC;IAC3ET,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAEJ,MAAM,CAACoB,QAAQ,CAACH,SAAS,CAAC;IAC/ErB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACoB,QAAQ,CAACC,IAAI,CAAC;IAErE;IACAzB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACT,UAAU,CAAC;IAC3ElB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC;IAC3E5B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACP,IAAI,CAAC;IACvEpB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAEJ,MAAM,CAACsB,KAAK,CAACC,MAAM,CAACE,KAAK,CAAC;IAEzE7B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACZ,UAAU,CAAC;IAC/ElB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,yBAAyB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACF,MAAM,CAAC;IAC/E5B,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACV,IAAI,CAAC;IAC3EpB,IAAI,CAACO,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAEJ,MAAM,CAACsB,KAAK,CAACI,QAAQ,CAACD,KAAK,CAAC;IAE7E;IACA1B,IAAI,CAAC4B,SAAS,GAAG5B,IAAI,CAAC4B,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IACzD7B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,SAASlB,KAAK,CAACC,IAAI,EAAE,CAAC;IAEzC;IACA4C,UAAU,CAAC,MAAK;MACd9B,IAAI,CAACE,SAAS,CAAC6B,MAAM,CAAC,gBAAgB,CAAC;MAEvC;MACA,MAAMC,iBAAiB,GAAG,CACxB,wBAAwB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,iBAAiB,EACrG,qBAAqB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,uBAAuB,EAC5F,uBAAuB,EAAE,2BAA2B,EAAE,cAAc,EAAE,eAAe,EACrF,cAAc,EAAE,qBAAqB,EAAE,8BAA8B,EAAE,WAAW,CACnF;MAEDA,iBAAiB,CAACC,OAAO,CAACC,QAAQ,IAAG;QACnC,MAAMC,QAAQ,GAAGrC,QAAQ,CAACsC,gBAAgB,CAACF,QAAQ,CAAC;QACpDC,QAAQ,CAACF,OAAO,CAACI,EAAE,IAAG;UACpB,MAAMC,OAAO,GAAGD,EAAiB;UACjC;UACA,MAAME,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACH,OAAO,CAAC;UACtDA,OAAO,CAAClC,KAAK,CAACsC,OAAO,GAAGJ,OAAO,CAAClC,KAAK,CAACsC,OAAO;QAC/C,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF;MACAF,MAAM,CAACG,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAE;QAAEC,MAAM,EAAE5D;MAAK,CAAE,CAAC,CAAC;IAC1E,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA6D,iBAAiB,CAAC5D,IAAY,EAAEe,MAA4B;IAC1D,MAAM8C,SAAS,GAAG3E,qBAAqB;IACvC,OAAO;MACLc,IAAI;MACJe,MAAM,EAAE;QACN,GAAG8C,SAAS,CAAC9C,MAAM;QACnB,GAAGA,MAAM;QACTgB,IAAI,EAAE;UACJ,GAAG8B,SAAS,CAAC9C,MAAM,CAACgB,IAAI;UACxB,IAAIhB,MAAM,CAACgB,IAAI,IAAI,EAAE;SACtB;QACDI,QAAQ,EAAE;UACR,GAAG0B,SAAS,CAAC9C,MAAM,CAACoB,QAAQ;UAC5B,IAAIpB,MAAM,CAACoB,QAAQ,IAAI,EAAE;SAC1B;QACDE,KAAK,EAAE;UACLC,MAAM,EAAE;YACN,GAAGuB,SAAS,CAAC9C,MAAM,CAACsB,KAAK,CAACC,MAAM;YAChC,IAAIvB,MAAM,CAACsB,KAAK,EAAEC,MAAM,IAAI,EAAE;WAC/B;UACDG,QAAQ,EAAE;YACR,GAAGoB,SAAS,CAAC9C,MAAM,CAACsB,KAAK,CAACI,QAAQ;YAClC,IAAI1B,MAAM,CAACsB,KAAK,EAAEI,QAAQ,IAAI,EAAE;;;;KAIvC;EACH;EAEA;EACAqB,WAAW;IACT,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxD,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;EACxD;EAEA;EACAyD,WAAW,CAACC,SAAiB;IAC3B,IAAI;MACF,MAAMnE,KAAK,GAAgBgE,IAAI,CAACI,KAAK,CAACD,SAAS,CAAC;MAChD,IAAI,IAAI,CAACE,aAAa,CAACrE,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC;QACpB,OAAO,IAAI;;MAEb,OAAO,KAAK;KACb,CAAC,MAAM;MACN,OAAO,KAAK;;EAEhB;EAEQqE,aAAa,CAACrE,KAAU;IAC9B,OAAOA,KAAK,IACL,OAAOA,KAAK,CAACC,IAAI,KAAK,QAAQ,IAC9BD,KAAK,CAACgB,MAAM,IACZ,OAAOhB,KAAK,CAACgB,MAAM,CAACK,OAAO,KAAK,QAAQ,IACxC,OAAOrB,KAAK,CAACgB,MAAM,CAACQ,MAAM,KAAK,QAAQ;EAChD;;;uBA5KWnC,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAiF,SAAZjF,YAAY;MAAAkF,YAFX;IAAM;EAAA", "names": ["BehaviorSubject", "MYSTICAL_PURPLE_THEME", "AVAILABLE_THEMES", "ThemeService", "constructor", "currentThemeSubject", "asObservable", "loadSavedTheme", "savedThemeName", "localStorage", "getItem", "THEME_STORAGE_KEY", "savedTheme", "find", "theme", "name", "setTheme", "applyTheme", "next", "setItem", "setThemeByName", "themeName", "t", "getCurrentTheme", "value", "getAvailableThemes", "root", "document", "documentElement", "body", "colors", "classList", "add", "style", "setProperty", "primary", "primaryLight", "primaryDark", "accent", "accentLight", "accentDark", "warn", "success", "error", "background", "surface", "text", "secondary", "disabled", "hint", "gradient", "auth", "o<PERSON>h", "google", "border", "hover", "facebook", "className", "replace", "setTimeout", "remove", "materialSelectors", "for<PERSON>ach", "selector", "elements", "querySelectorAll", "el", "element", "computedStyle", "window", "getComputedStyle", "cssText", "dispatchEvent", "CustomEvent", "detail", "createCustomTheme", "baseTheme", "exportTheme", "JSON", "stringify", "importTheme", "themeJson", "parse", "validateTheme", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\theme\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { ThemeConfig, ThemeColors, DEFAULT_THEME, MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ThemeService {\r\n  private readonly THEME_STORAGE_KEY = 'oracul-theme';\r\n  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(MYSTICAL_PURPLE_THEME);\r\n  public currentTheme$ = this.currentThemeSubject.asObservable();\r\n\r\n  constructor() {\r\n    this.loadSavedTheme();\r\n  }\r\n\r\n  private loadSavedTheme(): void {\r\n    const savedThemeName = localStorage.getItem(this.THEME_STORAGE_KEY);\r\n    if (savedThemeName) {\r\n      const savedTheme = AVAILABLE_THEMES.find(theme => theme.name === savedThemeName);\r\n      if (savedTheme) {\r\n        this.setTheme(savedTheme);\r\n        return;\r\n      }\r\n    }\r\n    // Apply default theme\r\n    this.applyTheme(MYSTICAL_PURPLE_THEME);\r\n  }\r\n\r\n  setTheme(theme: ThemeConfig): void {\r\n    this.currentThemeSubject.next(theme);\r\n    this.applyTheme(theme);\r\n    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);\r\n  }\r\n\r\n  setThemeByName(themeName: string): void {\r\n    const theme = AVAILABLE_THEMES.find(t => t.name === themeName);\r\n    if (theme) {\r\n      this.setTheme(theme);\r\n    }\r\n  }\r\n\r\n  getCurrentTheme(): ThemeConfig {\r\n    return this.currentThemeSubject.value;\r\n  }\r\n\r\n  getAvailableThemes(): ThemeConfig[] {\r\n    return AVAILABLE_THEMES;\r\n  }\r\n\r\n  private applyTheme(theme: ThemeConfig): void {\r\n    const root = document.documentElement;\r\n    const body = document.body;\r\n    const colors = theme.colors;\r\n\r\n    // Add transition class to prevent flicker\r\n    body.classList.add('theme-changing');\r\n\r\n    // Apply CSS custom properties\r\n    root.style.setProperty('--theme-primary', colors.primary);\r\n    root.style.setProperty('--theme-primary-light', colors.primaryLight);\r\n    root.style.setProperty('--theme-primary-dark', colors.primaryDark);\r\n    root.style.setProperty('--theme-accent', colors.accent);\r\n    root.style.setProperty('--theme-accent-light', colors.accentLight);\r\n    root.style.setProperty('--theme-accent-dark', colors.accentDark);\r\n    root.style.setProperty('--theme-warn', colors.warn);\r\n    root.style.setProperty('--theme-success', colors.success);\r\n    root.style.setProperty('--theme-error', colors.error);\r\n    root.style.setProperty('--theme-background', colors.background);\r\n    root.style.setProperty('--theme-surface', colors.surface);\r\n\r\n    // Text colors\r\n    root.style.setProperty('--theme-text-primary', colors.text.primary);\r\n    root.style.setProperty('--theme-text-secondary', colors.text.secondary);\r\n    root.style.setProperty('--theme-text-disabled', colors.text.disabled);\r\n    root.style.setProperty('--theme-text-hint', colors.text.hint);\r\n\r\n    // Gradients\r\n    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);\r\n    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);\r\n    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);\r\n\r\n    // OAuth colors\r\n    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);\r\n    root.style.setProperty('--theme-google-border', colors.oauth.google.border);\r\n    root.style.setProperty('--theme-google-text', colors.oauth.google.text);\r\n    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);\r\n\r\n    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);\r\n    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);\r\n    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);\r\n    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);\r\n\r\n    // Update body class for theme-specific styling\r\n    body.className = body.className.replace(/theme-\\w+/g, '');\r\n    body.classList.add(`theme-${theme.name}`);\r\n\r\n    // Force a repaint to ensure Material components pick up the new colors\r\n    setTimeout(() => {\r\n      body.classList.remove('theme-changing');\r\n\r\n      // Force style recalculation for all Material components\r\n      const materialSelectors = [\r\n        '.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button',\r\n        '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle',\r\n        '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip',\r\n        '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card'\r\n      ];\r\n\r\n      materialSelectors.forEach(selector => {\r\n        const elements = document.querySelectorAll(selector);\r\n        elements.forEach(el => {\r\n          const element = el as HTMLElement;\r\n          // Force recomputation of styles\r\n          const computedStyle = window.getComputedStyle(element);\r\n          element.style.cssText = element.style.cssText;\r\n        });\r\n      });\r\n\r\n      // Dispatch a custom event to notify components of theme change\r\n      window.dispatchEvent(new CustomEvent('themeChanged', { detail: theme }));\r\n    }, 100);\r\n  }\r\n\r\n  // Helper method to create custom theme\r\n  createCustomTheme(name: string, colors: Partial<ThemeColors>): ThemeConfig {\r\n    const baseTheme = MYSTICAL_PURPLE_THEME;\r\n    return {\r\n      name,\r\n      colors: {\r\n        ...baseTheme.colors,\r\n        ...colors,\r\n        text: {\r\n          ...baseTheme.colors.text,\r\n          ...(colors.text || {})\r\n        },\r\n        gradient: {\r\n          ...baseTheme.colors.gradient,\r\n          ...(colors.gradient || {})\r\n        },\r\n        oauth: {\r\n          google: {\r\n            ...baseTheme.colors.oauth.google,\r\n            ...(colors.oauth?.google || {})\r\n          },\r\n          facebook: {\r\n            ...baseTheme.colors.oauth.facebook,\r\n            ...(colors.oauth?.facebook || {})\r\n          }\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  // Export current theme configuration\r\n  exportTheme(): string {\r\n    return JSON.stringify(this.getCurrentTheme(), null, 2);\r\n  }\r\n\r\n  // Import theme from JSON\r\n  importTheme(themeJson: string): boolean {\r\n    try {\r\n      const theme: ThemeConfig = JSON.parse(themeJson);\r\n      if (this.validateTheme(theme)) {\r\n        this.setTheme(theme);\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private validateTheme(theme: any): theme is ThemeConfig {\r\n    return theme &&\r\n           typeof theme.name === 'string' &&\r\n           theme.colors &&\r\n           typeof theme.colors.primary === 'string' &&\r\n           typeof theme.colors.accent === 'string';\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}