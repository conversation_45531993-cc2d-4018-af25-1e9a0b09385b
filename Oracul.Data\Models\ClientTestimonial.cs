using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Client testimonial entity for portfolio item reviews
    /// </summary>
    public class ClientTestimonial : BaseEntity
    {
        [Required]
        public int PortfolioItemId { get; set; }

        [Required]
        [MaxLength(200)]
        public string ClientName { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? ClientTitle { get; set; }

        [MaxLength(200)]
        public string? ClientCompany { get; set; }

        [Required]
        public string TestimonialText { get; set; } = string.Empty;

        public int? Rating { get; set; }

        [MaxLength(500)]
        public string? ClientPhotoUrl { get; set; }

        [Required]
        public DateTime GivenAt { get; set; }

        // Navigation properties
        public virtual PortfolioItem PortfolioItem { get; set; } = null!;
    }
}
