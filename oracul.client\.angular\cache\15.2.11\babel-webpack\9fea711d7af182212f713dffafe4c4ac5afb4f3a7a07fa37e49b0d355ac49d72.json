{"ast": null, "code": "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nfunction MatExpansionPanel_ng_template_5_Template(rf, ctx) {}\nconst _c1 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c2 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanelHeader_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r0._getExpandedState());\n  }\n}\nconst _c3 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c4 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: trigger('indicatorRotate', [state('collapsed, void', style({\n    transform: 'rotate(0deg)'\n  })), state('expanded', style({\n    transform: 'rotate(180deg)'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))]),\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: trigger('bodyExpansion', [state('collapsed, void', style({\n    height: '0px',\n    visibility: 'hidden'\n  })), state('expanded', style({\n    height: '*',\n    visibility: 'visible'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n  constructor(_template, _expansionPanel) {\n    this._template = _template;\n    this._expansionPanel = _expansionPanel;\n  }\n}\nMatExpansionPanelContent.ɵfac = function MatExpansionPanelContent_Factory(t) {\n  return new (t || MatExpansionPanelContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL, 8));\n};\nMatExpansionPanelContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelContent,\n  selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n  set hideToggle(value) {\n    this._hideToggle = coerceBooleanProperty(value);\n  }\n  /** The position of the expansion indicator. */\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n    super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n    this._viewContainerRef = _viewContainerRef;\n    this._animationMode = _animationMode;\n    this._hideToggle = false;\n    /** An event emitted after the body's expansion animation happens. */\n    this.afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    this.afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    this._inputChanges = new Subject();\n    /** ID for the associated header element. Used for a11y labelling. */\n    this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n    /** Stream of body animation done events. */\n    this._bodyAnimationDone = new Subject();\n    this.accordion = accordion;\n    this._document = _document;\n    // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n    this._bodyAnimationDone.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      if (event.fromState !== 'void') {\n        if (event.toState === 'expanded') {\n          this.afterExpand.emit();\n        } else if (event.toState === 'collapsed') {\n          this.afterCollapse.emit();\n        }\n      }\n    });\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n  /** Gets the expanded state string. */\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n  open() {\n    this.expanded = true;\n  }\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._bodyAnimationDone.complete();\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n    return false;\n  }\n}\nMatExpansionPanel.ɵfac = function MatExpansionPanel_Factory(t) {\n  return new (t || MatExpansionPanel)(i0.ɵɵdirectiveInject(MAT_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8));\n};\nMatExpansionPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatExpansionPanel,\n  selectors: [[\"mat-expansion-panel\"]],\n  contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n    }\n  },\n  viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-expansion-panel\"],\n  hostVars: 6,\n  hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    expanded: \"expanded\",\n    hideToggle: \"hideToggle\",\n    togglePosition: \"togglePosition\"\n  },\n  outputs: {\n    opened: \"opened\",\n    closed: \"closed\",\n    expandedChange: \"expandedChange\",\n    afterExpand: \"afterExpand\",\n    afterCollapse: \"afterCollapse\"\n  },\n  exportAs: [\"matExpansionPanel\"],\n  features: [i0.ɵɵProvidersFeature([\n  // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n  // to the same accordion.\n  {\n    provide: MAT_ACCORDION,\n    useValue: undefined\n  }, {\n    provide: MAT_EXPANSION_PANEL,\n    useExisting: MatExpansionPanel\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c2,\n  decls: 7,\n  vars: 4,\n  consts: [[\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [\"body\", \"\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n  template: function MatExpansionPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 0, 1);\n      i0.ɵɵlistener(\"@bodyExpansion.done\", function MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener($event) {\n        return ctx._bodyAnimationDone.next($event);\n      });\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵprojection(4, 1);\n      i0.ɵɵtemplate(5, MatExpansionPanel_ng_template_5_Template, 0, 0, \"ng-template\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(6, 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"@bodyExpansion\", ctx._getExpandedState())(\"id\", ctx.id);\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n    }\n  },\n  dependencies: [i2.CdkPortalOutlet],\n  styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matExpansionAnimations.bodyExpansion]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled', 'expanded'],\n      outputs: ['opened', 'closed', 'expandedChange'],\n      animations: [matExpansionAnimations.bodyExpansion],\n      providers: [\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [MAT_ACCORDION]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.UniqueSelectionDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    hideToggle: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }]\n  });\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {}\nMatExpansionPanelActionRow.ɵfac = function MatExpansionPanelActionRow_Factory(t) {\n  return new (t || MatExpansionPanelActionRow)();\n};\nMatExpansionPanelActionRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelActionRow,\n  selectors: [[\"mat-action-row\"]],\n  hostAttrs: [1, \"mat-action-row\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatExpansionPanelHeader.\n/** @docs-private */\nclass MatExpansionPanelHeaderBase {}\nconst _MatExpansionPanelHeaderMixinBase = mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n  constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n    super();\n    this.panel = panel;\n    this._element = _element;\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    this._parentChangeSubscription = Subscription.EMPTY;\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck());\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n}\nMatExpansionPanelHeader.ɵfac = function MatExpansionPanelHeader_Factory(t) {\n  return new (t || MatExpansionPanelHeader)(i0.ɵɵdirectiveInject(MatExpansionPanel, 1), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2$1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n};\nMatExpansionPanelHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatExpansionPanelHeader,\n  selectors: [[\"mat-expansion-panel-header\"]],\n  hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n  hostVars: 15,\n  hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n        return ctx._toggle();\n      })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n      i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n      i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    tabIndex: \"tabIndex\",\n    expandedHeight: \"expandedHeight\",\n    collapsedHeight: \"collapsedHeight\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c4,\n  decls: 5,\n  vars: 3,\n  consts: [[1, \"mat-content\"], [\"class\", \"mat-expansion-indicator\", 4, \"ngIf\"], [1, \"mat-expansion-indicator\"]],\n  template: function MatExpansionPanelHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, MatExpansionPanelHeader_span_4_Template, 1, 1, \"span\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx._showToggle());\n    }\n  },\n  dependencies: [i3.NgIf],\n  styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matExpansionAnimations.indicatorRotate]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['tabIndex'],\n      animations: [matExpansionAnimations.indicatorRotate],\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatExpansionPanel,\n      decorators: [{\n        type: Host\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2$1.FocusMonitor\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }];\n  }, {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {}\nMatExpansionPanelDescription.ɵfac = function MatExpansionPanelDescription_Factory(t) {\n  return new (t || MatExpansionPanelDescription)();\n};\nMatExpansionPanelDescription.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelDescription,\n  selectors: [[\"mat-panel-description\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {}\nMatExpansionPanelTitle.ɵfac = function MatExpansionPanelTitle_Factory(t) {\n  return new (t || MatExpansionPanelTitle)();\n};\nMatExpansionPanelTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelTitle,\n  selectors: [[\"mat-panel-title\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n  constructor() {\n    super(...arguments);\n    /** Headers belonging to this accordion. */\n    this._ownHeaders = new QueryList();\n    this._hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    this.displayMode = 'default';\n    /** The position of the expansion indicator. */\n    this.togglePosition = 'after';\n  }\n  /** Whether the expansion indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle;\n  }\n  set hideToggle(show) {\n    this._hideToggle = coerceBooleanProperty(show);\n  }\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n      this._ownHeaders.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n}\nMatAccordion.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAccordion_BaseFactory;\n  return function MatAccordion_Factory(t) {\n    return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(t || MatAccordion);\n  };\n}();\nMatAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAccordion,\n  selectors: [[\"mat-accordion\"]],\n  contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-accordion\"],\n  hostVars: 2,\n  hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n    }\n  },\n  inputs: {\n    multi: \"multi\",\n    hideToggle: \"hideToggle\",\n    displayMode: \"displayMode\",\n    togglePosition: \"togglePosition\"\n  },\n  exportAs: [\"matAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_ACCORDION,\n    useExisting: MatAccordion\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      inputs: ['multi'],\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      }\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatExpansionModule {}\nMatExpansionModule.ɵfac = function MatExpansionModule_Factory(t) {\n  return new (t || MatExpansionModule)();\n};\nMatExpansionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatExpansionModule\n});\nMatExpansionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      declarations: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };", "map": {"version": 3, "names": ["CdkAccordionItem", "CdkAccordion", "CdkAccordionModule", "i2", "TemplatePortal", "PortalModule", "i3", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Directive", "Inject", "Optional", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "SkipSelf", "Input", "Output", "ContentChild", "ViewChild", "Host", "Attribute", "QueryList", "ContentChildren", "NgModule", "mixinTabIndex", "MatCommonModule", "coerceBooleanProperty", "i2$1", "FocusKeyManager", "distinctUntilChanged", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "ANIMATION_MODULE_TYPE", "Subject", "Subscription", "EMPTY", "merge", "trigger", "state", "style", "transition", "animate", "i1", "MAT_ACCORDION", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "transform", "bodyExpansion", "height", "visibility", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "constructor", "_template", "_expansionPanel", "ɵfac", "TemplateRef", "ɵdir", "type", "args", "selector", "undefined", "decorators", "uniqueId", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "hideToggle", "_hideToggle", "accordion", "value", "togglePosition", "_togglePosition", "_changeDetectorRef", "_uniqueSelectionDispatcher", "_viewContainerRef", "_document", "_animationMode", "defaultOptions", "afterExpand", "afterCollapse", "_inputChanges", "_headerId", "_bodyAnimationDone", "pipe", "x", "y", "fromState", "toState", "subscribe", "event", "emit", "_hasSpacing", "expanded", "displayMode", "_getExpandedState", "toggle", "close", "open", "ngAfterContentInit", "_lazyContent", "opened", "_portal", "ngOnChanges", "changes", "next", "ngOnDestroy", "complete", "_containsFocus", "_body", "focusedElement", "activeElement", "bodyElement", "nativeElement", "contains", "ChangeDetectorRef", "UniqueSelectionDispatcher", "ViewContainerRef", "ɵcmp", "provide", "useValue", "useExisting", "CdkPortalOutlet", "exportAs", "encapsulation", "None", "changeDetection", "OnPush", "inputs", "outputs", "animations", "providers", "host", "template", "styles", "MatExpansionPanelActionRow", "class", "MatExpansionPanelHeaderBase", "_MatExpansionPanelHeaderMixinBase", "MatExpansionPanelHeader", "panel", "_element", "_focusMonitor", "tabIndex", "_parentChangeSubscription", "accordionHideToggleChange", "_stateChanges", "parseInt", "closed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusVia", "expandedHeight", "collapsedHeight", "disabled", "_toggle", "_isExpanded", "_getPanelId", "id", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "unsubscribe", "stopMonitoring", "ElementRef", "FocusMonitor", "NgIf", "MatExpansionPanelDescription", "MatExpansionPanelTitle", "Mat<PERSON><PERSON>rdi<PERSON>", "arguments", "_ownHeaders", "show", "_headers", "headers", "reset", "header", "notifyOn<PERSON><PERSON>es", "_keyManager", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "destroy", "descendants", "MatExpansionModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/expansion.mjs"], "sourcesContent": ["import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: trigger('indicatorRotate', [\n        state('collapsed, void', style({ transform: 'rotate(0deg)' })),\n        state('expanded', style({ transform: 'rotate(180deg)' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: trigger('bodyExpansion', [\n        state('collapsed, void', style({ height: '0px', visibility: 'hidden' })),\n        state('expanded', style({ height: '*', visibility: 'visible' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    constructor(_template, _expansionPanel) {\n        this._template = _template;\n        this._expansionPanel = _expansionPanel;\n    }\n}\nMatExpansionPanelContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelContent, deps: [{ token: i0.TemplateRef }, { token: MAT_EXPANSION_PANEL, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanelContent, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = coerceBooleanProperty(value);\n    }\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n        super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n        this._viewContainerRef = _viewContainerRef;\n        this._animationMode = _animationMode;\n        this._hideToggle = false;\n        /** An event emitted after the body's expansion animation happens. */\n        this.afterExpand = new EventEmitter();\n        /** An event emitted after the body's collapse animation happens. */\n        this.afterCollapse = new EventEmitter();\n        /** Stream that emits for changes in `@Input` properties. */\n        this._inputChanges = new Subject();\n        /** ID for the associated header element. Used for a11y labelling. */\n        this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n        /** Stream of body animation done events. */\n        this._bodyAnimationDone = new Subject();\n        this.accordion = accordion;\n        this._document = _document;\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._bodyAnimationDone\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            if (event.fromState !== 'void') {\n                if (event.toState === 'expanded') {\n                    this.afterExpand.emit();\n                }\n                else if (event.toState === 'collapsed') {\n                    this.afterCollapse.emit();\n                }\n            }\n        });\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._bodyAnimationDone.complete();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n}\nMatExpansionPanel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanel, deps: [{ token: MAT_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatExpansionPanel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanel, selector: \"mat-expansion-panel\", inputs: { disabled: \"disabled\", expanded: \"expanded\", hideToggle: \"hideToggle\", togglePosition: \"togglePosition\" }, outputs: { opened: \"opened\", closed: \"closed\", expandedChange: \"expandedChange\", afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n        // to the same accordion.\n        { provide: MAT_ACCORDION, useValue: undefined },\n        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n    ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matExpansionAnimations.bodyExpansion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled', 'expanded'], outputs: ['opened', 'closed', 'expandedChange'], animations: [matExpansionAnimations.bodyExpansion], providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [MAT_ACCORDION]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { hideToggle: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }] } });\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n}\nMatExpansionPanelActionRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelActionRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanelActionRow, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatExpansionPanelHeader.\n/** @docs-private */\nclass MatExpansionPanelHeaderBase {\n}\nconst _MatExpansionPanelHeaderMixinBase = mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n    constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n        super();\n        this.panel = panel;\n        this._element = _element;\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        this._parentChangeSubscription = Subscription.EMPTY;\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n}\nMatExpansionPanelHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelHeader, deps: [{ token: MatExpansionPanel, host: true }, { token: i0.ElementRef }, { token: i2$1.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component });\nMatExpansionPanelHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanelHeader, selector: \"mat-expansion-panel-header\", inputs: { tabIndex: \"tabIndex\", expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\" }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"], dependencies: [{ kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [matExpansionAnimations.indicatorRotate], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['tabIndex'], animations: [matExpansionAnimations.indicatorRotate], host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"] }]\n        }], ctorParameters: function () { return [{ type: MatExpansionPanel, decorators: [{\n                    type: Host\n                }] }, { type: i0.ElementRef }, { type: i2$1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; }, propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n}\nMatExpansionPanelDescription.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelDescription.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanelDescription, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n}\nMatExpansionPanelTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelTitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatExpansionPanelTitle, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    constructor() {\n        super(...arguments);\n        /** Headers belonging to this accordion. */\n        this._ownHeaders = new QueryList();\n        this._hideToggle = false;\n        /**\n         * Display mode used for all expansion panels in the accordion. Currently two display\n         * modes exist:\n         *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n         *     panel at a different elevation from the rest of the accordion.\n         *  flat - no spacing is placed around expanded panels, showing all panels at the same\n         *     elevation.\n         */\n        this.displayMode = 'default';\n        /** The position of the expansion indicator. */\n        this.togglePosition = 'after';\n    }\n    /** Whether the expansion indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle;\n    }\n    set hideToggle(show) {\n        this._hideToggle = coerceBooleanProperty(show);\n    }\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._keyManager?.destroy();\n        this._ownHeaders.destroy();\n    }\n}\nMatAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAccordion.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatAccordion, selector: \"mat-accordion\", inputs: { multi: \"multi\", hideToggle: \"hideToggle\", displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n        {\n            provide: MAT_ACCORDION,\n            useExisting: MatAccordion,\n        },\n    ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    inputs: ['multi'],\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatExpansionModule {\n}\nMatExpansionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatExpansionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionModule, declarations: [MatAccordion,\n        MatExpansionPanel,\n        MatExpansionPanelActionRow,\n        MatExpansionPanelHeader,\n        MatExpansionPanelTitle,\n        MatExpansionPanelDescription,\n        MatExpansionPanelContent], imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule], exports: [MatAccordion,\n        MatExpansionPanel,\n        MatExpansionPanelActionRow,\n        MatExpansionPanelHeader,\n        MatExpansionPanelTitle,\n        MatExpansionPanelDescription,\n        MatExpansionPanelContent] });\nMatExpansionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionModule, imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    declarations: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3F,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AAClE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzP,SAASC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AACvE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AAC9E,SAASC,KAAK,EAAEC,cAAc,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC1D,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,0BAA0B;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAAA;AAAA;AAAA;AAAA;EAAA;IAmFgHzC,EAAE,wBA6U0uC;EAAA;EAAA;IAAA,eA7U5uCA,EAAE;IAAFA,EAAE,2DA6UiqC;EAAA;AAAA;AAAA;AAAA;AA5ZnxC,MAAM0C,aAAa,GAAG,IAAIzC,cAAc,CAAC,eAAe,CAAC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0C,gCAAgC,GAAG,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,eAAe,EAAET,OAAO,CAAC,iBAAiB,EAAE,CACxCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEQ,SAAS,EAAE;EAAe,CAAC,CAAC,CAAC,EAC9DT,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;IAAEQ,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EACzDP,UAAU,CAAC,2CAA2C,EAAEC,OAAO,CAACG,gCAAgC,CAAC,CAAC,CACrG,CAAC;EACF;EACAI,aAAa,EAAEX,OAAO,CAAC,eAAe,EAAE,CACpCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEU,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAS,CAAC,CAAC,CAAC,EACxEZ,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;IAAEU,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAU,CAAC,CAAC,CAAC,EAChEV,UAAU,CAAC,2CAA2C,EAAEC,OAAO,CAACG,gCAAgC,CAAC,CAAC,CACrG;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,mBAAmB,GAAG,IAAIjD,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkD,wBAAwB,CAAC;EAC3BC,WAAW,CAACC,SAAS,EAAEC,eAAe,EAAE;IACpC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;AACJ;AACAH,wBAAwB,CAACI,IAAI;EAAA,iBAA6FJ,wBAAwB,EAAlCnD,EAAE,mBAAkDA,EAAE,CAACwD,WAAW,GAAlExD,EAAE,mBAA6EkD,mBAAmB;AAAA,CAA4D;AAC9QC,wBAAwB,CAACM,IAAI,kBADmFzD,EAAE;EAAA,MACJmD,wBAAwB;EAAA;AAAA,EAAoE;AAC1M;EAAA,mDAFgHnD,EAAE,mBAElBmD,wBAAwB,EAAc,CAAC;IAC3HO,IAAI,EAAExD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE1D,EAAE,CAACwD;IAAY,CAAC,EAAE;MAAEE,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxFJ,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAACT,mBAAmB;MAC9B,CAAC,EAAE;QACCQ,IAAI,EAAEtD;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,IAAI2D,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAI/D,cAAc,CAAC,qCAAqC,CAAC;AACrG;AACA;AACA;AACA;AACA,MAAMgE,iBAAiB,SAAS1E,gBAAgB,CAAC;EAC7C;EACA,IAAI2E,UAAU,GAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAK,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACF,UAAW;EAC5E;EACA,IAAIA,UAAU,CAACG,KAAK,EAAE;IAClB,IAAI,CAACF,WAAW,GAAG9C,qBAAqB,CAACgD,KAAK,CAAC;EACnD;EACA;EACA,IAAIC,cAAc,GAAG;IACjB,OAAO,IAAI,CAACC,eAAe,IAAK,IAAI,CAACH,SAAS,IAAI,IAAI,CAACA,SAAS,CAACE,cAAe;EACpF;EACA,IAAIA,cAAc,CAACD,KAAK,EAAE;IACtB,IAAI,CAACE,eAAe,GAAGF,KAAK;EAChC;EACAjB,WAAW,CAACgB,SAAS,EAAEI,kBAAkB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAE;IACjI,KAAK,CAACT,SAAS,EAAEI,kBAAkB,EAAEC,0BAA0B,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACT,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACW,WAAW,GAAG,IAAIzE,YAAY,EAAE;IACrC;IACA,IAAI,CAAC0E,aAAa,GAAG,IAAI1E,YAAY,EAAE;IACvC;IACA,IAAI,CAAC2E,aAAa,GAAG,IAAIhD,OAAO,EAAE;IAClC;IACA,IAAI,CAACiD,SAAS,GAAI,8BAA6BlB,QAAQ,EAAG,EAAC;IAC3D;IACA,IAAI,CAACmB,kBAAkB,GAAG,IAAIlD,OAAO,EAAE;IACvC,IAAI,CAACoC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACO,SAAS,GAAGA,SAAS;IAC1B;IACA;IACA,IAAI,CAACO,kBAAkB,CAClBC,IAAI,CAAC3D,oBAAoB,CAAC,CAAC4D,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACEC,SAAS,CAACC,KAAK,IAAI;MACpB,IAAIA,KAAK,CAACH,SAAS,KAAK,MAAM,EAAE;QAC5B,IAAIG,KAAK,CAACF,OAAO,KAAK,UAAU,EAAE;UAC9B,IAAI,CAACT,WAAW,CAACY,IAAI,EAAE;QAC3B,CAAC,MACI,IAAID,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;UACpC,IAAI,CAACR,aAAa,CAACW,IAAI,EAAE;QAC7B;MACJ;IACJ,CAAC,CAAC;IACF,IAAIb,cAAc,EAAE;MAChB,IAAI,CAACX,UAAU,GAAGW,cAAc,CAACX,UAAU;IAC/C;EACJ;EACA;EACAyB,WAAW,GAAG;IACV,IAAI,IAAI,CAACvB,SAAS,EAAE;MAChB,OAAO,IAAI,CAACwB,QAAQ,IAAI,IAAI,CAACxB,SAAS,CAACyB,WAAW,KAAK,SAAS;IACpE;IACA,OAAO,KAAK;EAChB;EACA;EACAC,iBAAiB,GAAG;IAChB,OAAO,IAAI,CAACF,QAAQ,GAAG,UAAU,GAAG,WAAW;EACnD;EACA;EACAG,MAAM,GAAG;IACL,IAAI,CAACH,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAClC;EACA;EACAI,KAAK,GAAG;IACJ,IAAI,CAACJ,QAAQ,GAAG,KAAK;EACzB;EACA;EACAK,IAAI,GAAG;IACH,IAAI,CAACL,QAAQ,GAAG,IAAI;EACxB;EACAM,kBAAkB,GAAG;IACjB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC7C,eAAe,KAAK,IAAI,EAAE;MACjE;MACA,IAAI,CAAC8C,MAAM,CACNjB,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC,EAAEC,MAAM,CAAC,MAAM,IAAI,CAACkE,QAAQ,IAAI,CAAC,IAAI,CAACS,OAAO,CAAC,EAAE1E,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5E6D,SAAS,CAAC,MAAM;QACjB,IAAI,CAACa,OAAO,GAAG,IAAI1G,cAAc,CAAC,IAAI,CAACwG,YAAY,CAAC9C,SAAS,EAAE,IAAI,CAACqB,iBAAiB,CAAC;MAC1F,CAAC,CAAC;IACN;EACJ;EACA4B,WAAW,CAACC,OAAO,EAAE;IACjB,IAAI,CAACvB,aAAa,CAACwB,IAAI,CAACD,OAAO,CAAC;EACpC;EACAE,WAAW,GAAG;IACV,KAAK,CAACA,WAAW,EAAE;IACnB,IAAI,CAACvB,kBAAkB,CAACwB,QAAQ,EAAE;IAClC,IAAI,CAAC1B,aAAa,CAAC0B,QAAQ,EAAE;EACjC;EACA;EACAC,cAAc,GAAG;IACb,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,MAAMC,cAAc,GAAG,IAAI,CAAClC,SAAS,CAACmC,aAAa;MACnD,MAAMC,WAAW,GAAG,IAAI,CAACH,KAAK,CAACI,aAAa;MAC5C,OAAOH,cAAc,KAAKE,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAACJ,cAAc,CAAC;IACjF;IACA,OAAO,KAAK;EAChB;AACJ;AACA5C,iBAAiB,CAACV,IAAI;EAAA,iBAA6FU,iBAAiB,EAhIpBjE,EAAE,mBAgIoC0C,aAAa,OAhInD1C,EAAE,mBAgI8FA,EAAE,CAACkH,iBAAiB,GAhIpHlH,EAAE,mBAgI+HyC,EAAE,CAAC0E,yBAAyB,GAhI7JnH,EAAE,mBAgIwKA,EAAE,CAACoH,gBAAgB,GAhI7LpH,EAAE,mBAgIwMF,QAAQ,GAhIlNE,EAAE,mBAgI6N+B,qBAAqB,MAhIpP/B,EAAE,mBAgI+QgE,mCAAmC;AAAA,CAA4D;AACheC,iBAAiB,CAACoD,IAAI,kBAjI0FrH,EAAE;EAAA,MAiIXiE,iBAAiB;EAAA;EAAA;IAAA;MAjIRjE,EAAE,0BAsIzCmD,wBAAwB;IAAA;IAAA;MAAA;MAtIenD,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAiI0hB;EACpoB;EACA;EACA;IAAEsH,OAAO,EAAE5E,aAAa;IAAE6E,QAAQ,EAAE1D;EAAU,CAAC,EAC/C;IAAEyD,OAAO,EAAEpE,mBAAmB;IAAEsE,WAAW,EAAEvD;EAAkB,CAAC,CACnE,GAtI2GjE,EAAE,6BAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAsI2Q;MAtI7QA,EAAE,+BAsIsgB;MAtIxgBA,EAAE;QAAA,OAsI2Z,mCAA+B;MAAA,EAAE;MAtI9bA,EAAE,4BAsIkjB;MAtIpjBA,EAAE,mBAsIilB;MAtInlBA,EAAE,gFAsIgpB;MAtIlpBA,EAAE,eAsI0pB;MAtI5pBA,EAAE,mBAsIitB;MAtIntBA,EAAE,eAsIytB;IAAA;IAAA;MAtI3tBA,EAAE,aAsI4X;MAtI9XA,EAAE,sDAsI4X;MAtI9XA,EAAE,8CAsIue;MAtIzeA,EAAE,aAsIioB;MAtInoBA,EAAE,2CAsIioB;IAAA;EAAA;EAAA,eAA2zDN,EAAE,CAAC+H,eAAe;EAAA;EAAA;EAAA;IAAA,WAAmI,CAAC7E,sBAAsB,CAACG,aAAa;EAAC;EAAA;AAAA,EAAiG;AAC10F;EAAA,mDAvIgH/C,EAAE,mBAuIlBiE,iBAAiB,EAAc,CAAC;IACpHP,IAAI,EAAEpD,SAAS;IACfqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAE8D,QAAQ,EAAE,mBAAmB;MAAEC,aAAa,EAAEpH,iBAAiB,CAACqH,IAAI;MAAEC,eAAe,EAAErH,uBAAuB,CAACsH,MAAM;MAAEC,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;MAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,CAAC;MAAEC,UAAU,EAAE,CAACrF,sBAAsB,CAACG,aAAa,CAAC;MAAEmF,SAAS,EAAE;MACvS;MACA;MACA;QAAEZ,OAAO,EAAE5E,aAAa;QAAE6E,QAAQ,EAAE1D;MAAU,CAAC,EAC/C;QAAEyD,OAAO,EAAEpE,mBAAmB;QAAEsE,WAAW,EAAEvD;MAAkB,CAAC,CACnE;MAAEkE,IAAI,EAAE;QACL,OAAO,EAAE,qBAAqB;QAC9B,sBAAsB,EAAE,UAAU;QAClC,iCAAiC,EAAE,qCAAqC;QACxE,qCAAqC,EAAE;MAC3C,CAAC;MAAEC,QAAQ,EAAE,ihBAAihB;MAAEC,MAAM,EAAE,CAAC,wqDAAwqD;IAAE,CAAC;EAChuE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEtD;MACV,CAAC,EAAE;QACCsD,IAAI,EAAEjD;MACV,CAAC,EAAE;QACCiD,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAACjB,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAEgB,IAAI,EAAE1D,EAAE,CAACkH;IAAkB,CAAC,EAAE;MAAExD,IAAI,EAAEjB,EAAE,CAAC0E;IAA0B,CAAC,EAAE;MAAEzD,IAAI,EAAE1D,EAAE,CAACoH;IAAiB,CAAC,EAAE;MAAE1D,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzIJ,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAAC7D,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE4D,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEtD;MACV,CAAC,EAAE;QACCsD,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAAC5B,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE2B,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAACK,mCAAmC;MAC9C,CAAC,EAAE;QACCN,IAAI,EAAEtD;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8D,UAAU,EAAE,CAAC;MACzCR,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAE4D,cAAc,EAAE,CAAC;MACjBZ,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAEoE,WAAW,EAAE,CAAC;MACdpB,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEoE,aAAa,EAAE,CAAC;MAChBrB,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEwF,YAAY,EAAE,CAAC;MACfzC,IAAI,EAAE9C,YAAY;MAClB+C,IAAI,EAAE,CAACR,wBAAwB;IACnC,CAAC,CAAC;IAAEyD,KAAK,EAAE,CAAC;MACRlD,IAAI,EAAE7C,SAAS;MACf8C,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM2E,0BAA0B,CAAC;AAEjCA,0BAA0B,CAAC/E,IAAI;EAAA,iBAA6F+E,0BAA0B;AAAA,CAAmD;AACzMA,0BAA0B,CAAC7E,IAAI,kBA7LiFzD,EAAE;EAAA,MA6LFsI,0BAA0B;EAAA;EAAA;AAAA,EAAyF;AACnO;EAAA,mDA9LgHtI,EAAE,mBA8LlBsI,0BAA0B,EAAc,CAAC;IAC7H5E,IAAI,EAAExD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BuE,IAAI,EAAE;QACFI,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,CAAC;AAElC,MAAMC,iCAAiC,GAAGtH,aAAa,CAACqH,2BAA2B,CAAC;AACpF;AACA;AACA;AACA,MAAME,uBAAuB,SAASD,iCAAiC,CAAC;EACpErF,WAAW,CAACuF,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAErE,kBAAkB,EAAEK,cAAc,EAAED,cAAc,EAAEkE,QAAQ,EAAE;IACtG,KAAK,EAAE;IACP,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACrE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACmE,yBAAyB,GAAG9G,YAAY,CAACC,KAAK;IACnD,MAAM8G,yBAAyB,GAAGL,KAAK,CAACvE,SAAS,GAC3CuE,KAAK,CAACvE,SAAS,CAAC6E,aAAa,CAAC9D,IAAI,CAACzD,MAAM,CAAC6E,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC7GrE,KAAK;IACX,IAAI,CAAC4G,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;IAC7C;IACA;IACA,IAAI,CAACC,yBAAyB,GAAG5G,KAAK,CAACwG,KAAK,CAACvC,MAAM,EAAEuC,KAAK,CAACQ,MAAM,EAAEH,yBAAyB,EAAEL,KAAK,CAAC3D,aAAa,CAACG,IAAI,CAACzD,MAAM,CAAC6E,OAAO,IAAI;MACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAACf,SAAS,CAAC,MAAM,IAAI,CAAChB,kBAAkB,CAAC4E,YAAY,EAAE,CAAC;IAC5D;IACAT,KAAK,CAACQ,MAAM,CACPhE,IAAI,CAACzD,MAAM,CAAC,MAAMiH,KAAK,CAAChC,cAAc,EAAE,CAAC,CAAC,CAC1CnB,SAAS,CAAC,MAAMqD,aAAa,CAACQ,QAAQ,CAACT,QAAQ,EAAE,SAAS,CAAC,CAAC;IACjE,IAAI/D,cAAc,EAAE;MAChB,IAAI,CAACyE,cAAc,GAAGzE,cAAc,CAACyE,cAAc;MACnD,IAAI,CAACC,eAAe,GAAG1E,cAAc,CAAC0E,eAAe;IACzD;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACb,KAAK,CAACa,QAAQ;EAC9B;EACA;EACAC,OAAO,GAAG;IACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAChB,IAAI,CAACb,KAAK,CAAC5C,MAAM,EAAE;IACvB;EACJ;EACA;EACA2D,WAAW,GAAG;IACV,OAAO,IAAI,CAACf,KAAK,CAAC/C,QAAQ;EAC9B;EACA;EACAE,iBAAiB,GAAG;IAChB,OAAO,IAAI,CAAC6C,KAAK,CAAC7C,iBAAiB,EAAE;EACzC;EACA;EACA6D,WAAW,GAAG;IACV,OAAO,IAAI,CAAChB,KAAK,CAACiB,EAAE;EACxB;EACA;EACAC,kBAAkB,GAAG;IACjB,OAAO,IAAI,CAAClB,KAAK,CAACrE,cAAc;EACpC;EACA;EACAwF,WAAW,GAAG;IACV,OAAO,CAAC,IAAI,CAACnB,KAAK,CAACzE,UAAU,IAAI,CAAC,IAAI,CAACyE,KAAK,CAACa,QAAQ;EACzD;EACA;AACJ;AACA;AACA;EACIO,gBAAgB,GAAG;IACf,MAAMC,UAAU,GAAG,IAAI,CAACN,WAAW,EAAE;IACrC,IAAIM,UAAU,IAAI,IAAI,CAACV,cAAc,EAAE;MACnC,OAAO,IAAI,CAACA,cAAc;IAC9B,CAAC,MACI,IAAI,CAACU,UAAU,IAAI,IAAI,CAACT,eAAe,EAAE;MAC1C,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI;EACf;EACA;EACAU,QAAQ,CAACxE,KAAK,EAAE;IACZ,QAAQA,KAAK,CAACyE,OAAO;MACjB;MACA,KAAKpI,KAAK;MACV,KAAKF,KAAK;QACN,IAAI,CAACC,cAAc,CAAC4D,KAAK,CAAC,EAAE;UACxBA,KAAK,CAAC0E,cAAc,EAAE;UACtB,IAAI,CAACV,OAAO,EAAE;QAClB;QACA;MACJ;QACI,IAAI,IAAI,CAACd,KAAK,CAACvE,SAAS,EAAE;UACtB,IAAI,CAACuE,KAAK,CAACvE,SAAS,CAACgG,oBAAoB,CAAC3E,KAAK,CAAC;QACpD;QACA;IAAO;EAEnB;EACA;AACJ;AACA;AACA;AACA;EACI4E,KAAK,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAID,MAAM,EAAE;MACR,IAAI,CAACzB,aAAa,CAACQ,QAAQ,CAAC,IAAI,CAACT,QAAQ,EAAE0B,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAC3B,QAAQ,CAAC5B,aAAa,CAACqD,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACAC,eAAe,GAAG;IACd,IAAI,CAAC3B,aAAa,CAAC4B,OAAO,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAACpD,SAAS,CAAC8E,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI,IAAI,CAAC3B,KAAK,CAACvE,SAAS,EAAE;QAChC,IAAI,CAACuE,KAAK,CAACvE,SAAS,CAACsG,kBAAkB,CAAC,IAAI,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACAjE,WAAW,GAAG;IACV,IAAI,CAACsC,yBAAyB,CAAC4B,WAAW,EAAE;IAC5C,IAAI,CAAC9B,aAAa,CAAC+B,cAAc,CAAC,IAAI,CAAChC,QAAQ,CAAC;EACpD;AACJ;AACAF,uBAAuB,CAACnF,IAAI;EAAA,iBAA6FmF,uBAAuB,EA5UhC1I,EAAE,mBA4UgDiE,iBAAiB,MA5UnEjE,EAAE,mBA4U0FA,EAAE,CAAC6K,UAAU,GA5UzG7K,EAAE,mBA4UoHsB,IAAI,CAACwJ,YAAY,GA5UvI9K,EAAE,mBA4UkJA,EAAE,CAACkH,iBAAiB,GA5UxKlH,EAAE,mBA4UmLgE,mCAAmC,MA5UxNhE,EAAE,mBA4UmP+B,qBAAqB,MA5U1Q/B,EAAE,mBA4UqS,UAAU;AAAA,CAA6D;AAC9d0I,uBAAuB,CAACrB,IAAI,kBA7UoFrH,EAAE;EAAA,MA6UL0I,uBAAuB;EAAA;EAAA,oBAAgL,QAAQ;EAAA;EAAA;IAAA;MA7U5M1I,EAAE;QAAA,OA6UL,aAAS;MAAA;QAAA,OAAT,oBAAgB;MAAA;IAAA;IAAA;MA7UbA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,6BA6Um9B;MA7Ur9BA,EAAE,gBA6U2gC;MA7U7gCA,EAAE,mBA6UykC;MA7U3kCA,EAAE,mBA6UsmC;MA7UxmCA,EAAE,eA6U+mC;MA7UjnCA,EAAE,wEA6U0uC;IAAA;IAAA;MA7U5uCA,EAAE,2DA6Uk9B;MA7Up9BA,EAAE,aA6UurC;MA7UzrCA,EAAE,sCA6UurC;IAAA;EAAA;EAAA,eAA67DH,EAAE,CAACkL,IAAI;EAAA;EAAA;EAAA;IAAA,WAA+E,CAACnI,sBAAsB,CAACC,eAAe;EAAC;EAAA;AAAA,EAAiG;AACr8G;EAAA,mDA9UgH7C,EAAE,mBA8UlB0I,uBAAuB,EAAc,CAAC;IAC1HhF,IAAI,EAAEpD,SAAS;IACfqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAE+D,aAAa,EAAEpH,iBAAiB,CAACqH,IAAI;MAAEC,eAAe,EAAErH,uBAAuB,CAACsH,MAAM;MAAEC,MAAM,EAAE,CAAC,UAAU,CAAC;MAAEE,UAAU,EAAE,CAACrF,sBAAsB,CAACC,eAAe,CAAC;MAAEsF,IAAI,EAAE;QAC/M,OAAO,EAAE,gDAAgD;QACzD,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,iBAAiB;QAC9B,iBAAiB,EAAE,UAAU;QAC7B,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,gBAAgB;QACxC,sBAAsB,EAAE,eAAe;QACvC,8CAA8C,EAAG,kCAAiC;QAClF,+CAA+C,EAAG,mCAAkC;QACpF,iCAAiC,EAAE,qCAAqC;QACxE,gBAAgB,EAAE,oBAAoB;QACtC,SAAS,EAAE,WAAW;QACtB,WAAW,EAAE;MACjB,CAAC;MAAEC,QAAQ,EAAE,0WAA0W;MAAEC,MAAM,EAAE,CAAC,+0DAA+0D;IAAE,CAAC;EAChuE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEO,iBAAiB;MAAEH,UAAU,EAAE,CAAC;QACtEJ,IAAI,EAAE5C;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4C,IAAI,EAAE1D,EAAE,CAAC6K;IAAW,CAAC,EAAE;MAAEnH,IAAI,EAAEpC,IAAI,CAACwJ;IAAa,CAAC,EAAE;MAAEpH,IAAI,EAAE1D,EAAE,CAACkH;IAAkB,CAAC,EAAE;MAAExD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxHJ,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAACK,mCAAmC;MAC9C,CAAC,EAAE;QACCN,IAAI,EAAEtD;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEtD;MACV,CAAC,EAAE;QACCsD,IAAI,EAAEvD,MAAM;QACZwD,IAAI,EAAE,CAAC5B,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE2B,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAE3C,SAAS;QACf4C,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2F,cAAc,EAAE,CAAC;MAC7C5F,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAE6I,eAAe,EAAE,CAAC;MAClB7F,IAAI,EAAEhD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMsK,4BAA4B,CAAC;AAEnCA,4BAA4B,CAACzH,IAAI;EAAA,iBAA6FyH,4BAA4B;AAAA,CAAmD;AAC7MA,4BAA4B,CAACvH,IAAI,kBA1X+EzD,EAAE;EAAA,MA0XAgL,4BAA4B;EAAA;EAAA;AAAA,EAAwH;AACtQ;EAAA,mDA3XgHhL,EAAE,mBA2XlBgL,4BAA4B,EAAc,CAAC;IAC/HtH,IAAI,EAAExD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCuE,IAAI,EAAE;QACFI,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAM0C,sBAAsB,CAAC;AAE7BA,sBAAsB,CAAC1H,IAAI;EAAA,iBAA6F0H,sBAAsB;AAAA,CAAmD;AACjMA,sBAAsB,CAACxH,IAAI,kBA1YqFzD,EAAE;EAAA,MA0YNiL,sBAAsB;EAAA;EAAA;AAAA,EAA4G;AAC9O;EAAA,mDA3YgHjL,EAAE,mBA2YlBiL,sBAAsB,EAAc,CAAC;IACzHvH,IAAI,EAAExD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BuE,IAAI,EAAE;QACFI,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,YAAY,SAAS1L,YAAY,CAAC;EACpC4D,WAAW,GAAG;IACV,KAAK,CAAC,GAAG+H,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,WAAW,GAAG,IAAIpK,SAAS,EAAE;IAClC,IAAI,CAACmD,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC0B,WAAW,GAAG,SAAS;IAC5B;IACA,IAAI,CAACvB,cAAc,GAAG,OAAO;EACjC;EACA;EACA,IAAIJ,UAAU,GAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAU,CAACmH,IAAI,EAAE;IACjB,IAAI,CAAClH,WAAW,GAAG9C,qBAAqB,CAACgK,IAAI,CAAC;EAClD;EACAnF,kBAAkB,GAAG;IACjB,IAAI,CAACoF,QAAQ,CAAC/E,OAAO,CAChBpB,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC6J,QAAQ,CAAC,CAAC,CAC9B9F,SAAS,CAAE+F,OAAO,IAAK;MACxB,IAAI,CAACH,WAAW,CAACI,KAAK,CAACD,OAAO,CAAC7J,MAAM,CAAC+J,MAAM,IAAIA,MAAM,CAAC9C,KAAK,CAACvE,SAAS,KAAK,IAAI,CAAC,CAAC;MACjF,IAAI,CAACgH,WAAW,CAACM,eAAe,EAAE;IACtC,CAAC,CAAC;IACF,IAAI,CAACC,WAAW,GAAG,IAAIpK,eAAe,CAAC,IAAI,CAAC6J,WAAW,CAAC,CAACQ,QAAQ,EAAE,CAACC,cAAc,EAAE;EACxF;EACA;EACAzB,oBAAoB,CAAC3E,KAAK,EAAE;IACxB,IAAI,CAACkG,WAAW,CAACG,SAAS,CAACrG,KAAK,CAAC;EACrC;EACAiF,kBAAkB,CAACe,MAAM,EAAE;IACvB,IAAI,CAACE,WAAW,CAACI,gBAAgB,CAACN,MAAM,CAAC;EAC7C;EACAhF,WAAW,GAAG;IACV,KAAK,CAACA,WAAW,EAAE;IACnB,IAAI,CAACkF,WAAW,EAAEK,OAAO,EAAE;IAC3B,IAAI,CAACZ,WAAW,CAACY,OAAO,EAAE;EAC9B;AACJ;AACAd,YAAY,CAAC3H,IAAI;EAAA;EAAA;IAAA,kEA9c+FvD,EAAE,uBA8cJkL,YAAY,SAAZA,YAAY;EAAA;AAAA,GAAqD;AAC/KA,YAAY,CAACzH,IAAI,kBA/c+FzD,EAAE;EAAA,MA+chBkL,YAAY;EAAA;EAAA;IAAA;MA/cElL,EAAE,0BAod1D0I,uBAAuB;IAAA;IAAA;MAAA;MApdiC1I,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA+c8P,CACxW;IACIsH,OAAO,EAAE5E,aAAa;IACtB8E,WAAW,EAAE0D;EACjB,CAAC,CACJ,GApd2GlL,EAAE;AAAA,EAoduD;AACzK;EAAA,mDArdgHA,EAAE,mBAqdlBkL,YAAY,EAAc,CAAC;IAC/GxH,IAAI,EAAExD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzB8D,QAAQ,EAAE,cAAc;MACxBK,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBG,SAAS,EAAE,CACP;QACIZ,OAAO,EAAE5E,aAAa;QACtB8E,WAAW,EAAE0D;MACjB,CAAC,CACJ;MACD/C,IAAI,EAAE;QACFI,KAAK,EAAE,eAAe;QACtB;QACA;QACA,6BAA6B,EAAE;MACnC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE+C,QAAQ,EAAE,CAAC;MACzB5H,IAAI,EAAEzC,eAAe;MACrB0C,IAAI,EAAE,CAAC+E,uBAAuB,EAAE;QAAEuD,WAAW,EAAE;MAAK,CAAC;IACzD,CAAC,CAAC;IAAE/H,UAAU,EAAE,CAAC;MACbR,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAEmF,WAAW,EAAE,CAAC;MACdnC,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAE4D,cAAc,EAAE,CAAC;MACjBZ,IAAI,EAAEhD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwL,kBAAkB,CAAC;AAEzBA,kBAAkB,CAAC3I,IAAI;EAAA,iBAA6F2I,kBAAkB;AAAA,CAAkD;AACxLA,kBAAkB,CAACC,IAAI,kBA7fyFnM,EAAE;EAAA,MA6fGkM;AAAkB,EAYnG;AACpCA,kBAAkB,CAACE,IAAI,kBA1gByFpM,EAAE;EAAA,UA0gBiCD,YAAY,EAAEqB,eAAe,EAAE3B,kBAAkB,EAAEG,YAAY;AAAA,EAAI;AACtN;EAAA,mDA3gBgHI,EAAE,mBA2gBlBkM,kBAAkB,EAAc,CAAC;IACrHxI,IAAI,EAAExC,QAAQ;IACdyC,IAAI,EAAE,CAAC;MACC0I,OAAO,EAAE,CAACtM,YAAY,EAAEqB,eAAe,EAAE3B,kBAAkB,EAAEG,YAAY,CAAC;MAC1E0M,OAAO,EAAE,CACLpB,YAAY,EACZjH,iBAAiB,EACjBqE,0BAA0B,EAC1BI,uBAAuB,EACvBuC,sBAAsB,EACtBD,4BAA4B,EAC5B7H,wBAAwB,CAC3B;MACDoJ,YAAY,EAAE,CACVrB,YAAY,EACZjH,iBAAiB,EACjBqE,0BAA0B,EAC1BI,uBAAuB,EACvBuC,sBAAsB,EACtBD,4BAA4B,EAC5B7H,wBAAwB;IAEhC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASR,gCAAgC,EAAED,aAAa,EAAEQ,mBAAmB,EAAEc,mCAAmC,EAAEkH,YAAY,EAAEgB,kBAAkB,EAAEjI,iBAAiB,EAAEqE,0BAA0B,EAAEnF,wBAAwB,EAAE6H,4BAA4B,EAAEtC,uBAAuB,EAAEuC,sBAAsB,EAAErI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}