{"ast": null, "code": "import * as i8 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Self, Attribute, Input, ViewChild, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nfunction MatSelect_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.placeholder);\n  }\n}\nfunction MatSelect_span_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.triggerValue);\n  }\n}\nfunction MatSelect_span_5_ng_content_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 0, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction MatSelect_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtemplate(1, MatSelect_span_5_span_1_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(2, MatSelect_span_5_ng_content_2_Template, 1, 0, \"ng-content\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", !!ctx_r3.customTrigger);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", true);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 15, 16);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8._panelDoneAnimatingStream.next($event.toState));\n    })(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r4._getPanelTheme(), \"\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.panelClass)(\"@transformPanel\", \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r4.id + \"-panel\")(\"aria-multiselectable\", ctx_r4.multiple)(\"aria-label\", ctx_r4.ariaLabel || null)(\"aria-labelledby\", ctx_r4._getPanelAriaLabelledby());\n  }\n}\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [transition('* => void', query('@transformPanel', [animateChild()], {\n    optional: true\n  }))]),\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: trigger('transformPanel', [state('void', style({\n    opacity: 0,\n    transform: 'scale(1, 0.8)'\n  })), transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  }))), transition('* => void', animate('100ms linear', style({\n    opacity: 0\n  })))])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor( /** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n  constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    this._elementRef = _elementRef;\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n  }\n}))));\n/** Base class with all of the `MatSelect` functionality. */\nclass _MatSelectBase extends _MatSelectMixinBase {\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = coerceBooleanProperty(value);\n  }\n  /** Whether to center the active option over the trigger. */\n  get disableOptionCentering() {\n    return this._disableOptionCentering;\n  }\n  set disableOptionCentering(value) {\n    this._disableOptionCentering = coerceBooleanProperty(value);\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  get typeaheadDebounceInterval() {\n    return this._typeaheadDebounceInterval;\n  }\n  set typeaheadDebounceInterval(value) {\n    this._typeaheadDebounceInterval = coerceNumberProperty(value);\n  }\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n    super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._viewportRuler = _viewportRuler;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._ngZone = _ngZone;\n    this._dir = _dir;\n    this._parentFormField = _parentFormField;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._defaultOptions = _defaultOptions;\n    /** Whether or not the overlay panel is open. */\n    this._panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    this._compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    this._uid = `mat-select-${nextUniqueId++}`;\n    /** Current `aria-labelledby` value for the select trigger. */\n    this._triggerAriaLabelledBy = null;\n    /** Emits whenever the component is destroyed. */\n    this._destroy = new Subject();\n    /** `View -> model callback called when value changes` */\n    this._onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n    this._onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n    this._valueId = `mat-select-value-${nextUniqueId++}`;\n    /** Emits when the panel element is finished transforming in. */\n    this._panelDoneAnimatingStream = new Subject();\n    this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    this._focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    this.controlType = 'mat-select';\n    this._multiple = false;\n    this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /** Aria label of the select. */\n    this.ariaLabel = '';\n    /** Combined stream of all of the child options' change events. */\n    this.optionSelectionChanges = defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    this.openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n    this.selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n    this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n  }\n  ngAfterContentInit() {\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (this._canOpen()) {\n      this._panelOpen = true;\n      this._keyManager.withHorizontalOrientation(null);\n      this._highlightCorrectOption();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n    }\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n  _onAttached() {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this._typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit = null;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first item instead.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        this._keyManager.setFirstItemActive();\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    return value;\n  }\n  /** Called when the overlay panel is done animating. */\n  _panelDoneAnimating(isOpen) {\n    this.openedChange.emit(isOpen);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    return this._panelOpen || !this.empty || this._focused && !!this._placeholder;\n  }\n}\n_MatSelectBase.ɵfac = function _MatSelectBase_Factory(t) {\n  return new (t || _MatSelectBase)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n};\n_MatSelectBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSelectBase,\n  viewQuery: function _MatSelectBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n    }\n  },\n  inputs: {\n    userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n    panelClass: \"panelClass\",\n    placeholder: \"placeholder\",\n    required: \"required\",\n    multiple: \"multiple\",\n    disableOptionCentering: \"disableOptionCentering\",\n    compareWith: \"compareWith\",\n    value: \"value\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    errorStateMatcher: \"errorStateMatcher\",\n    typeaheadDebounceInterval: \"typeaheadDebounceInterval\",\n    sortComparator: \"sortComparator\",\n    id: \"id\"\n  },\n  outputs: {\n    openedChange: \"openedChange\",\n    _openedStream: \"opened\",\n    _closedStream: \"closed\",\n    selectionChange: \"selectionChange\",\n    valueChange: \"valueChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSelectBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.ErrorStateMatcher\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i6.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }]\n    }, {\n      type: i4.NgControl,\n      decorators: [{\n        type: Self\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SELECT_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.LiveAnnouncer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SELECT_CONFIG]\n      }]\n    }];\n  }, {\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    disableOptionCentering: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {}\nMatSelectTrigger.ɵfac = function MatSelectTrigger_Factory(t) {\n  return new (t || MatSelectTrigger)();\n};\nMatSelectTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSelectTrigger,\n  selectors: [[\"mat-select-trigger\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_SELECT_TRIGGER,\n    useExisting: MatSelectTrigger\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSelect extends _MatSelectBase {\n  constructor() {\n    super(...arguments);\n    this._positions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }];\n    this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  }\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth();\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Note that it's important that we read this in `ngAfterViewInit`, because\n    // reading it earlier will cause the form field to return a different element.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n  }\n  open() {\n    this._overlayWidth = this._getOverlayWidth();\n    super.open();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n  }\n  close() {\n    super.close();\n    // Required for the MDC form field to pick up when the overlay has been closed.\n    this.stateChanges.next();\n  }\n  /** Scrolls the active option into view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth() {\n    const refToMeasure = this._preferredOverlayOrigin instanceof CdkOverlayOrigin ? this._preferredOverlayOrigin.elementRef : this._preferredOverlayOrigin || this._elementRef;\n    return refToMeasure.nativeElement.getBoundingClientRect().width;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n    this._syncParentProperties();\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n}\nMatSelect.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSelect_BaseFactory;\n  return function MatSelect_Factory(t) {\n    return (ɵMatSelect_BaseFactory || (ɵMatSelect_BaseFactory = i0.ɵɵgetInheritedFactory(MatSelect)))(t || MatSelect);\n  };\n}();\nMatSelect.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSelect,\n  selectors: [[\"mat-select\"]],\n  contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n    }\n  },\n  hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n  hostVars: 19,\n  hostBindings: function MatSelect_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n        return ctx._onFocus();\n      })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n        return ctx._onBlur();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n      i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\",\n    hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\"\n  },\n  exportAs: [\"matSelect\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: MatSelect\n  }, {\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: MatSelect\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c3,\n  decls: 11,\n  vars: 11,\n  consts: [[\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [1, \"mat-mdc-select-value\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-placeholder mat-mdc-select-min-line\", 4, \"ngSwitchCase\"], [\"class\", \"mat-mdc-select-value-text\", 3, \"ngSwitch\", 4, \"ngSwitchCase\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"backdropClick\", \"attach\", \"detach\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-min-line\", 4, \"ngSwitchDefault\"], [4, \"ngSwitchCase\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"ngClass\", \"keydown\"], [\"panel\", \"\"]],\n  template: function MatSelect_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n        return ctx.toggle();\n      });\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵtemplate(4, MatSelect_span_4_Template, 2, 1, \"span\", 3);\n      i0.ɵɵtemplate(5, MatSelect_span_5_Template, 3, 2, \"span\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(8, \"svg\", 7);\n      i0.ɵɵelement(9, \"path\", 8);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 9, \"ng-template\", 9);\n      i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n        return ctx.close();\n      })(\"attach\", function MatSelect_Template_ng_template_attach_10_listener() {\n        return ctx._onAttached();\n      })(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n        return ctx.close();\n      });\n    }\n    if (rf & 2) {\n      const _r0 = i0.ɵɵreference(1);\n      i0.ɵɵattribute(\"aria-owns\", ctx.panelOpen ? ctx.id + \"-panel\" : null);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngSwitch\", ctx.empty);\n      i0.ɵɵattribute(\"id\", ctx._valueId);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", false);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || _r0)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth);\n    }\n  },\n  dependencies: [i7.NgClass, i7.NgSwitch, i7.NgSwitchCase, i7.NgSwitchDefault, i8.CdkConnectedOverlay, i8.CdkOverlayOrigin],\n  styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:calc(100vw - 32px);max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:calc(100vh - 32px);max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:4px;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}.mat-mdc-select{display:inline-block;width:100%;outline:none}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}.mdc-menu-surface.mat-mdc-select-panel{width:100%;max-height:275px;position:static;outline:0;margin:0;padding:8px 0;list-style-type:none}.mdc-menu-surface.mat-mdc-select-panel:focus{outline:none}.cdk-high-contrast-active .mdc-menu-surface.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) .mdc-menu-surface.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above .mdc-menu-surface.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matSelectAnimations.transformPanel]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      inputs: ['disabled', 'disableRipple', 'tabIndex'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-autocomplete': 'none',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      animations: [matSelectAnimations.transformPanel],\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:calc(100vw - 32px);max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:calc(100vh - 32px);max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:4px;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}.mat-mdc-select{display:inline-block;width:100%;outline:none}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}.mdc-menu-surface.mat-mdc-select-panel{width:100%;max-height:275px;position:static;outline:0;margin:0;padding:8px 0;list-style-type:none}.mdc-menu-surface.mat-mdc-select-panel:focus{outline:none}.cdk-high-contrast-active .mdc-menu-surface.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) .mdc-menu-surface.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above .mdc-menu-surface.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"]\n    }]\n  }], null, {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSelectModule {}\nMatSelectModule.ɵfac = function MatSelectModule_Factory(t) {\n  return new (t || MatSelectModule)();\n};\nMatSelectModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSelectModule\n});\nMatSelectModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n  imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      declarations: [MatSelect, MatSelectTrigger],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };", "map": {"version": 3, "names": ["i8", "Overlay", "CdkConnectedOverlay", "CdkOverlayOrigin", "OverlayModule", "i7", "CommonModule", "i0", "InjectionToken", "EventEmitter", "Directive", "Optional", "Inject", "Self", "Attribute", "Input", "ViewChild", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "NgModule", "i2", "mixinDisableRipple", "mixinTabIndex", "mixinDisabled", "mixinErrorState", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MAT_OPTION_PARENT_COMPONENT", "MatOption", "MAT_OPTGROUP", "MatOptionModule", "MatCommonModule", "i6", "MAT_FORM_FIELD", "MatFormFieldControl", "MatFormFieldModule", "i1", "CdkScrollableModule", "i5", "ActiveDescendantKeyManager", "i3", "coerceBooleanProperty", "coerceNumberProperty", "SelectionModel", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "ENTER", "SPACE", "hasModifierKey", "A", "i4", "Validators", "Subject", "defer", "merge", "startWith", "switchMap", "take", "filter", "map", "distinctUntilChanged", "takeUntil", "trigger", "transition", "query", "animate<PERSON><PERSON><PERSON>", "state", "style", "animate", "matSelectAnimations", "transformPanelWrap", "optional", "transformPanel", "opacity", "transform", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "nextUniqueId", "MAT_SELECT_SCROLL_STRATEGY", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "constructor", "source", "value", "_MatSelectMixinBase", "_elementRef", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "stateChanges", "_MatSelectBase", "focused", "_focused", "_panelOpen", "placeholder", "_placeholder", "next", "required", "_required", "control", "hasValidator", "multiple", "_multiple", "_selectionModel", "ngDevMode", "disableOptionCentering", "_disableOptionCentering", "compareWith", "_compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "_onChange", "typeaheadDebounceInterval", "_typeaheadDebounceInterval", "id", "_id", "_uid", "_viewportRuler", "_changeDetectorRef", "_ngZone", "elementRef", "_dir", "_parentFormField", "tabIndex", "scrollStrategyFactory", "_liveAnnouncer", "_defaultOptions", "o1", "o2", "_triggerAriaLabelledBy", "_destroy", "_onTouched", "_valueId", "_panelDoneAnimatingStream", "_overlayPanelClass", "overlayPanelClass", "controlType", "aria<PERSON><PERSON><PERSON>", "optionSelectionChanges", "options", "changes", "pipe", "option", "onSelectionChange", "onStable", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "valueAccessor", "_scrollStrategyFactory", "_scrollStrategy", "parseInt", "ngOnInit", "subscribe", "_panelDoneAnimating", "panelOpen", "ngAfterContentInit", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "element", "nativeElement", "setAttribute", "removeAttribute", "_previousControl", "undefined", "disabled", "updateErrorState", "ngOnChanges", "_keyManager", "withTypeAhead", "ngOnDestroy", "destroy", "complete", "toggle", "close", "open", "_canOpen", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isRtl", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "triggerValue", "empty", "selectedOptions", "viewValue", "reverse", "join", "_handleKeydown", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "activeItemIndex", "shift<PERSON>ey", "_onFocus", "_onBlur", "cancelTypeahead", "_onAttached", "_overlayDir", "positionChange", "detectChanges", "_positioningSettled", "_getPanelTheme", "color", "isEmpty", "Promise", "resolve", "then", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "tabOut", "focus", "change", "panel", "_scrollOptionIntoView", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "toArray", "sort", "a", "b", "sortComparator", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "emit", "_getChangeEvent", "setFirstItemActive", "length", "_getPanelAriaLabe<PERSON>by", "labelId", "getLabelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "isOpen", "setDescribedByIds", "ids", "onContainerClick", "shouldLabelFloat", "ɵfac", "ViewportRuler", "ChangeDetectorRef", "NgZone", "ErrorStateMatcher", "ElementRef", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "LiveAnnouncer", "ɵdir", "type", "decorators", "MatFormField", "args", "userAriaDescribedBy", "panelClass", "errorStateMatcher", "MatSelectTrigger", "useExisting", "selector", "providers", "MatSelect", "arguments", "_positions", "originX", "originY", "overlayX", "overlayY", "_hideSingleSelectionIndicator", "hideSingleSelectionIndicator", "_overlayWidth", "_getOverlayWidth", "ngAfterViewInit", "_preferredOverlayOrigin", "getConnectedOverlayOrigin", "index", "labelCount", "optionGroups", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "refToMeasure", "getBoundingClientRect", "width", "_syncParentProperties", "ɵcmp", "Ng<PERSON><PERSON>", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "exportAs", "inputs", "encapsulation", "None", "changeDetection", "OnPush", "host", "animations", "template", "styles", "descendants", "customTrigger", "MatSelectModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/select.mjs"], "sourcesContent": ["import * as i8 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Self, Attribute, Input, ViewChild, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst matSelectAnimations = {\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: trigger('transformPanelWrap', [\n        transition('* => void', query('@transformPanel', [animateChild()], { optional: true })),\n    ]),\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: trigger('transformPanel', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(1, 0.8)',\n        })),\n        transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1, 1)',\n        }))),\n        transition('* => void', animate('100ms linear', style({ opacity: 0 }))),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n    constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, \n    /**\n     * Form control bound to the component.\n     * Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    ngControl) {\n        this._elementRef = _elementRef;\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n        /**\n         * Emits whenever the component state changes and should cause the parent\n         * form-field to update. Implemented as part of `MatFormFieldControl`.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n    }\n}))));\n/** Base class with all of the `MatSelect` functionality. */\nclass _MatSelectBase extends _MatSelectMixinBase {\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = coerceBooleanProperty(value);\n    }\n    /** Whether to center the active option over the trigger. */\n    get disableOptionCentering() {\n        return this._disableOptionCentering;\n    }\n    set disableOptionCentering(value) {\n        this._disableOptionCentering = coerceBooleanProperty(value);\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    get typeaheadDebounceInterval() {\n        return this._typeaheadDebounceInterval;\n    }\n    set typeaheadDebounceInterval(value) {\n        this._typeaheadDebounceInterval = coerceNumberProperty(value);\n    }\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n        super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._viewportRuler = _viewportRuler;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._dir = _dir;\n        this._parentFormField = _parentFormField;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._defaultOptions = _defaultOptions;\n        /** Whether or not the overlay panel is open. */\n        this._panelOpen = false;\n        /** Comparison function to specify which option is displayed. Defaults to object equality. */\n        this._compareWith = (o1, o2) => o1 === o2;\n        /** Unique id for this input. */\n        this._uid = `mat-select-${nextUniqueId++}`;\n        /** Current `aria-labelledby` value for the select trigger. */\n        this._triggerAriaLabelledBy = null;\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when select has been touched` */\n        this._onTouched = () => { };\n        /** ID for the DOM node containing the select's value. */\n        this._valueId = `mat-select-value-${nextUniqueId++}`;\n        /** Emits when the panel element is finished transforming in. */\n        this._panelDoneAnimatingStream = new Subject();\n        this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n        this._focused = false;\n        /** A name for this control that can be used by `mat-form-field`. */\n        this.controlType = 'mat-select';\n        this._multiple = false;\n        this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n        /** Aria label of the select. */\n        this.ariaLabel = '';\n        /** Combined stream of all of the child options' change events. */\n        this.optionSelectionChanges = defer(() => {\n            const options = this.options;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n        });\n        /** Event emitted when the select panel has been toggled. */\n        this.openedChange = new EventEmitter();\n        /** Event emitted when the select has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the select has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the selected value has been changed by the user. */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the select changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (_defaultOptions?.typeaheadDebounceInterval != null) {\n            this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n        }\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        // We need `distinctUntilChanged` here, because some browsers will\n        // fire the animation end event twice for the same animation. See:\n        // https://github.com/angular/angular/issues/24084\n        this._panelDoneAnimatingStream\n            .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n            .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    }\n    ngAfterContentInit() {\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (this._canOpen()) {\n            this._panelOpen = true;\n            this._keyManager.withHorizontalOrientation(null);\n            this._highlightCorrectOption();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n        }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return option.value != null && this._compareWith(option.value, value);\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this._typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey']);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit = null;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first item instead.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                this._keyManager.setFirstItemActive();\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        let value = (labelId ? labelId + ' ' : '') + this._valueId;\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n        this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return this._panelOpen || !this.empty || (this._focused && !!this._placeholder);\n    }\n}\n_MatSelectBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSelectBase, deps: [{ token: i1.ViewportRuler }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.ErrorStateMatcher }, { token: i0.ElementRef }, { token: i3.Directionality, optional: true }, { token: i4.NgForm, optional: true }, { token: i4.FormGroupDirective, optional: true }, { token: MAT_FORM_FIELD, optional: true }, { token: i4.NgControl, optional: true, self: true }, { token: 'tabindex', attribute: true }, { token: MAT_SELECT_SCROLL_STRATEGY }, { token: i5.LiveAnnouncer }, { token: MAT_SELECT_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatSelectBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatSelectBase, inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", placeholder: \"placeholder\", required: \"required\", multiple: \"multiple\", disableOptionCentering: \"disableOptionCentering\", compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: \"typeaheadDebounceInterval\", sortComparator: \"sortComparator\", id: \"id\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatSelectBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.ErrorStateMatcher }, { type: i0.ElementRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i4.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i6.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }, { type: i4.NgControl, decorators: [{\n                    type: Self\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SELECT_SCROLL_STRATEGY]\n                }] }, { type: i5.LiveAnnouncer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SELECT_CONFIG]\n                }] }]; }, propDecorators: { userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], disableOptionCentering: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n}\nMatSelectTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSelectTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSelectTrigger, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                }]\n        }] });\nclass MatSelect extends _MatSelectBase {\n    constructor() {\n        super(...arguments);\n        this._positions = [\n            {\n                originX: 'start',\n                originY: 'bottom',\n                overlayX: 'start',\n                overlayY: 'top',\n            },\n            {\n                originX: 'start',\n                originY: 'top',\n                overlayX: 'start',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n        ];\n        this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    }\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth();\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    ngAfterViewInit() {\n        // Note that it's important that we read this in `ngAfterViewInit`, because\n        // reading it earlier will cause the form field to return a different element.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n    }\n    open() {\n        this._overlayWidth = this._getOverlayWidth();\n        super.open();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n    }\n    close() {\n        super.close();\n        // Required for the MDC form field to pick up when the overlay has been closed.\n        this.stateChanges.next();\n    }\n    /** Scrolls the active option into view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth() {\n        const refToMeasure = this._preferredOverlayOrigin instanceof CdkOverlayOrigin\n            ? this._preferredOverlayOrigin.elementRef\n            : this._preferredOverlayOrigin || this._elementRef;\n        return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n        this._syncParentProperties();\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n}\nMatSelect.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelect, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSelect.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSelect, selector: \"mat-select\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\" }, host: { attributes: { \"role\": \"combobox\", \"aria-autocomplete\": \"none\", \"aria-haspopup\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n        { provide: MatFormFieldControl, useExisting: MatSelect },\n        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n    ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], exportAs: [\"matSelect\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:calc(100vw - 32px);max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:calc(100vh - 32px);max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:4px;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}.mat-mdc-select{display:inline-block;width:100%;outline:none}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}.mdc-menu-surface.mat-mdc-select-panel{width:100%;max-height:275px;position:static;outline:0;margin:0;padding:8px 0;list-style-type:none}.mdc-menu-surface.mat-mdc-select-panel:focus{outline:none}.cdk-high-contrast-active .mdc-menu-surface.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) .mdc-menu-surface.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above .mdc-menu-surface.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"], dependencies: [{ kind: \"directive\", type: i7.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i7.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i7.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"directive\", type: i7.NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { kind: \"directive\", type: i8.CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: i8.CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }], animations: [matSelectAnimations.transformPanel], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', inputs: ['disabled', 'disableRipple', 'tabIndex'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-autocomplete': 'none',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, animations: [matSelectAnimations.transformPanel], providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:calc(100vw - 32px);max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:calc(100vh - 32px);max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:4px;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}.mat-mdc-select{display:inline-block;width:100%;outline:none}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}.mdc-menu-surface.mat-mdc-select-panel{width:100%;max-height:275px;position:static;outline:0;margin:0;padding:8px 0;list-style-type:none}.mdc-menu-surface.mat-mdc-select-panel:focus{outline:none}.cdk-high-contrast-active .mdc-menu-surface.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) .mdc-menu-surface.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above .mdc-menu-surface.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"] }]\n        }], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], hideSingleSelectionIndicator: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSelectModule {\n}\nMatSelectModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSelectModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectModule, declarations: [MatSelect, MatSelectTrigger], imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule], exports: [CdkScrollableModule,\n        MatFormFieldModule,\n        MatSelect,\n        MatSelectTrigger,\n        MatOptionModule,\n        MatCommonModule] });\nMatSelectModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule,\n        MatFormFieldModule,\n        MatOptionModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    declarations: [MatSelect, MatSelectTrigger],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,sBAAsB;AAC1C,SAASC,OAAO,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACpG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACpO,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,eAAe,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC3P,OAAO,KAAKC,EAAE,MAAM,8BAA8B;AAClD,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,8BAA8B;AACtG,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,0BAA0B,QAAQ,mBAAmB;AAC9D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,CAAC,QAAQ,uBAAuB;AACtH,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AACzG,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;AAErG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAAA;AAAA;EAAA;IAwzBsGnE,EAAE,8BA4MysC;IA5M3sCA,EAAE,UA4MwtC;IA5M1tCA,EAAE,eA4M+tC;EAAA;EAAA;IAAA,eA5MjuCA,EAAE;IAAFA,EAAE,aA4MwtC;IA5M1tCA,EAAE,sCA4MwtC;EAAA;AAAA;AAAA;EAAA;IA5M1tCA,EAAE,8BA4Mu4C;IA5Mz4CA,EAAE,UA4Mu5C;IA5Mz5CA,EAAE,eA4M85C;EAAA;EAAA;IAAA,eA5Mh6CA,EAAE;IAAFA,EAAE,aA4Mu5C;IA5Mz5CA,EAAE,uCA4Mu5C;EAAA;AAAA;AAAA;EAAA;IA5Mz5CA,EAAE,8CA4Mo/C;EAAA;AAAA;AAAA;EAAA;IA5Mt/CA,EAAE,8BA4Ms0C;IA5Mx0CA,EAAE,kEA4M85C;IA5Mh6CA,EAAE,8EA4Mo/C;IA5Mt/CA,EAAE,eA4MigD;EAAA;EAAA;IAAA,eA5MngDA,EAAE;IAAFA,EAAE,+CA4Mq0C;IA5Mv0CA,EAAE,aA4Mo+C;IA5Mt+CA,EAAE,iCA4Mo+C;EAAA;AAAA;AAAA;EAAA;IAAA,YA5Mt+CA,EAAE;IAAFA,EAAE;IAAFA,EAAE,kBA4M4+F;IA5M9+FA,EAAE,iCA4M4+F;IA5M9+FA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aA4Mi5F,qDAA8C;IAAA,EAAE;MA5Mn8FA,EAAE;MAAA,gBAAFA,EAAE;MAAA,OAAFA,EAAE,aA4Mm9F,8BAAsB;IAAA,EAAxC;IA5Mn8FA,EAAE,mBA4M2gG;IA5M7gGA,EAAE,eA4MqhG;EAAA;EAAA;IAAA,eA5MvhGA,EAAE;IAAFA,EAAE,qHA4MynF;IA5M3nFA,EAAE,yCA4M60F;IA5M/0FA,EAAE,wCA4M0pF;EAAA;AAAA;AAAA;AAAA;AA7/BlwF,MAAMoE,mBAAmB,GAAG;EACxB;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAER,OAAO,CAAC,oBAAoB,EAAE,CAC9CC,UAAU,CAAC,WAAW,EAAEC,KAAK,CAAC,iBAAiB,EAAE,CAACC,YAAY,EAAE,CAAC,EAAE;IAAEM,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAC1F,CAAC;EACF;EACAC,cAAc,EAAEV,OAAO,CAAC,gBAAgB,EAAE,CACtCI,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChBM,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHX,UAAU,CAAC,iBAAiB,EAAEK,OAAO,CAAC,kCAAkC,EAAED,KAAK,CAAC;IAC5EM,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJX,UAAU,CAAC,WAAW,EAAEK,OAAO,CAAC,cAAc,EAAED,KAAK,CAAC;IAAEM,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC1E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgC,GAAG;EACxC,OAAOC,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8B,GAAG;EACtC,OAAOD,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iCAAiC,GAAG;EACzC,OAAOF,KAAK,CAAC,mCAAmC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,0BAA0B,GAAG,IAAI9E,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA,SAAS+E,2CAA2C,CAACC,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,EAAE;AACtD;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAInF,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA,MAAMoF,mCAAmC,GAAG;EACxCC,OAAO,EAAEP,0BAA0B;EACnCQ,IAAI,EAAE,CAAC7F,OAAO,CAAC;EACf8F,UAAU,EAAER;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,kBAAkB,GAAG,IAAIxF,cAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAMyF,eAAe,CAAC;EAClBC,WAAW,EACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG5E,kBAAkB,CAACC,aAAa,CAACC,aAAa,CAACC,eAAe,CAAC,MAAM;EAC7FsE,WAAW,CAACI,WAAW,EAAEC,yBAAyB,EAAEC,WAAW,EAAEC,gBAAgB;EACjF;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE;IACP,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAIjD,OAAO,EAAE;EACrC;AACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACL;AACA,MAAMkD,cAAc,SAASP,mBAAmB,CAAC;EAC7C;EACA,IAAIQ,OAAO,GAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,UAAU;EAC3C;EACA;EACA,IAAIC,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAW,CAACZ,KAAK,EAAE;IACnB,IAAI,CAACa,YAAY,GAAGb,KAAK;IACzB,IAAI,CAACO,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACV,SAAS,EAAEW,OAAO,EAAEC,YAAY,CAAC7D,UAAU,CAAC0D,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQ,CAACf,KAAK,EAAE;IAChB,IAAI,CAACgB,SAAS,GAAGvE,qBAAqB,CAACuD,KAAK,CAAC;IAC7C,IAAI,CAACO,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA;EACA,IAAIK,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACnB,KAAK,EAAE;IAChB,IAAI,IAAI,CAACqB,eAAe,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMzC,gCAAgC,EAAE;IAC5C;IACA,IAAI,CAACuC,SAAS,GAAG3E,qBAAqB,CAACuD,KAAK,CAAC;EACjD;EACA;EACA,IAAIuB,sBAAsB,GAAG;IACzB,OAAO,IAAI,CAACC,uBAAuB;EACvC;EACA,IAAID,sBAAsB,CAACvB,KAAK,EAAE;IAC9B,IAAI,CAACwB,uBAAuB,GAAG/E,qBAAqB,CAACuD,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIyB,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAW,CAACE,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOL,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMtC,iCAAiC,EAAE;IAC7C;IACA,IAAI,CAAC0C,YAAY,GAAGC,EAAE;IACtB,IAAI,IAAI,CAACN,eAAe,EAAE;MACtB;MACA,IAAI,CAACO,oBAAoB,EAAE;IAC/B;EACJ;EACA;EACA,IAAI5B,KAAK,GAAG;IACR,OAAO,IAAI,CAAC6B,MAAM;EACtB;EACA,IAAI7B,KAAK,CAAC8B,QAAQ,EAAE;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;IAC/C,IAAIC,WAAW,EAAE;MACb,IAAI,CAACE,SAAS,CAACH,QAAQ,CAAC;IAC5B;EACJ;EACA;EACA,IAAII,yBAAyB,GAAG;IAC5B,OAAO,IAAI,CAACC,0BAA0B;EAC1C;EACA,IAAID,yBAAyB,CAAClC,KAAK,EAAE;IACjC,IAAI,CAACmC,0BAA0B,GAAGzF,oBAAoB,CAACsD,KAAK,CAAC;EACjE;EACA;EACA,IAAIoC,EAAE,GAAG;IACL,OAAO,IAAI,CAACC,GAAG;EACnB;EACA,IAAID,EAAE,CAACpC,KAAK,EAAE;IACV,IAAI,CAACqC,GAAG,GAAGrC,KAAK,IAAI,IAAI,CAACsC,IAAI;IAC7B,IAAI,CAAC/B,YAAY,CAACO,IAAI,EAAE;EAC5B;EACAhB,WAAW,CAACyC,cAAc,EAAEC,kBAAkB,EAAEC,OAAO,EAAEtC,yBAAyB,EAAEuC,UAAU,EAAEC,IAAI,EAAEvC,WAAW,EAAEC,gBAAgB,EAAEuC,gBAAgB,EAAEtC,SAAS,EAAEuC,QAAQ,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAE;IAChO,KAAK,CAACN,UAAU,EAAEvC,yBAAyB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,CAAC;IACtF,IAAI,CAACiC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACrC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACe,YAAY,GAAG,CAACuB,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACzC;IACA,IAAI,CAACZ,IAAI,GAAI,cAAarD,YAAY,EAAG,EAAC;IAC1C;IACA,IAAI,CAACkE,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI9F,OAAO,EAAE;IAC7B;IACA,IAAI,CAAC2E,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;IACA,IAAI,CAACoB,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B;IACA,IAAI,CAACC,QAAQ,GAAI,oBAAmBrE,YAAY,EAAG,EAAC;IACpD;IACA,IAAI,CAACsE,yBAAyB,GAAG,IAAIjG,OAAO,EAAE;IAC9C,IAAI,CAACkG,kBAAkB,GAAG,IAAI,CAACR,eAAe,EAAES,iBAAiB,IAAI,EAAE;IACvE,IAAI,CAAC/C,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACgD,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACtC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,uBAAuB,GAAG,IAAI,CAACwB,eAAe,EAAEzB,sBAAsB,IAAI,KAAK;IACpF;IACA,IAAI,CAACoC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,sBAAsB,GAAGrG,KAAK,CAAC,MAAM;MACtC,MAAMsG,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACC,OAAO,CAACC,IAAI,CAACtG,SAAS,CAACoG,OAAO,CAAC,EAAEnG,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAGqG,OAAO,CAAChG,GAAG,CAACmG,MAAM,IAAIA,MAAM,CAACC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA,OAAO,IAAI,CAACxB,OAAO,CAACyB,QAAQ,CAACH,IAAI,CAACpG,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,MAAM,IAAI,CAACkG,sBAAsB,CAAC,CAAC;IAC5F,CAAC,CAAC;IACF;IACA,IAAI,CAACO,YAAY,GAAG,IAAI9J,YAAY,EAAE;IACtC;IACA,IAAI,CAAC+J,aAAa,GAAG,IAAI,CAACD,YAAY,CAACJ,IAAI,CAACnG,MAAM,CAACyG,CAAC,IAAIA,CAAC,CAAC,EAAExG,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAACyG,aAAa,GAAG,IAAI,CAACH,YAAY,CAACJ,IAAI,CAACnG,MAAM,CAACyG,CAAC,IAAI,CAACA,CAAC,CAAC,EAAExG,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAAC0G,eAAe,GAAG,IAAIlK,YAAY,EAAE;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmK,WAAW,GAAG,IAAInK,YAAY,EAAE;IACrC,IAAI,IAAI,CAACiG,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAACmE,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAIzB,eAAe,EAAEd,yBAAyB,IAAI,IAAI,EAAE;MACpD,IAAI,CAACC,0BAA0B,GAAGa,eAAe,CAACd,yBAAyB;IAC/E;IACA,IAAI,CAACwC,sBAAsB,GAAG5B,qBAAqB;IACnD,IAAI,CAAC6B,eAAe,GAAG,IAAI,CAACD,sBAAsB,EAAE;IACpD,IAAI,CAAC7B,QAAQ,GAAG+B,QAAQ,CAAC/B,QAAQ,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACT,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACAyC,QAAQ,GAAG;IACP,IAAI,CAACxD,eAAe,GAAG,IAAI1E,cAAc,CAAC,IAAI,CAACwE,QAAQ,CAAC;IACxD,IAAI,CAACZ,YAAY,CAACO,IAAI,EAAE;IACxB;IACA;IACA;IACA,IAAI,CAACyC,yBAAyB,CACzBQ,IAAI,CAACjG,oBAAoB,EAAE,EAAEC,SAAS,CAAC,IAAI,CAACqF,QAAQ,CAAC,CAAC,CACtD0B,SAAS,CAAC,MAAM,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC;EAClE;EACAC,kBAAkB,GAAG;IACjB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAC7D,eAAe,CAAC8D,OAAO,CAACpB,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACqF,QAAQ,CAAC,CAAC,CAAC0B,SAAS,CAACM,KAAK,IAAI;MAC3EA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACtB,MAAM,IAAIA,MAAM,CAACuB,MAAM,EAAE,CAAC;MAC9CH,KAAK,CAACI,OAAO,CAACF,OAAO,CAACtB,MAAM,IAAIA,MAAM,CAACyB,QAAQ,EAAE,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAAC5B,OAAO,CAACC,OAAO,CAACC,IAAI,CAACtG,SAAS,CAAC,IAAI,CAAC,EAAEM,SAAS,CAAC,IAAI,CAACqF,QAAQ,CAAC,CAAC,CAAC0B,SAAS,CAAC,MAAM;MACjF,IAAI,CAACY,aAAa,EAAE;MACpB,IAAI,CAAC9D,oBAAoB,EAAE;IAC/B,CAAC,CAAC;EACN;EACA+D,SAAS,GAAG;IACR,MAAMC,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC1D,MAAMvF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAIsF,iBAAiB,KAAK,IAAI,CAACzC,sBAAsB,EAAE;MACnD,MAAM2C,OAAO,GAAG,IAAI,CAAC5F,WAAW,CAAC6F,aAAa;MAC9C,IAAI,CAAC5C,sBAAsB,GAAGyC,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnBE,OAAO,CAACE,YAAY,CAAC,iBAAiB,EAAEJ,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACDE,OAAO,CAACG,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAI3F,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAAC4F,gBAAgB,KAAK5F,SAAS,CAACW,OAAO,EAAE;QAC7C,IAAI,IAAI,CAACiF,gBAAgB,KAAKC,SAAS,IACnC7F,SAAS,CAAC8F,QAAQ,KAAK,IAAI,IAC3B9F,SAAS,CAAC8F,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAG9F,SAAS,CAAC8F,QAAQ;QACtC;QACA,IAAI,CAACF,gBAAgB,GAAG5F,SAAS,CAACW,OAAO;MAC7C;MACA,IAAI,CAACoF,gBAAgB,EAAE;IAC3B;EACJ;EACAC,WAAW,CAACxC,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAACvD,YAAY,CAACO,IAAI,EAAE;IAC5B;IACA,IAAIgD,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAACyC,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAACC,aAAa,CAAC,IAAI,CAACrE,0BAA0B,CAAC;IACnE;EACJ;EACAsE,WAAW,GAAG;IACV,IAAI,CAACF,WAAW,EAAEG,OAAO,EAAE;IAC3B,IAAI,CAACtD,QAAQ,CAACtC,IAAI,EAAE;IACpB,IAAI,CAACsC,QAAQ,CAACuD,QAAQ,EAAE;IACxB,IAAI,CAACpG,YAAY,CAACoG,QAAQ,EAAE;EAChC;EACA;EACAC,MAAM,GAAG;IACL,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAAC6B,KAAK,EAAE,GAAG,IAAI,CAACC,IAAI,EAAE;EAC/C;EACA;EACAA,IAAI,GAAG;IACH,IAAI,IAAI,CAACC,QAAQ,EAAE,EAAE;MACjB,IAAI,CAACpG,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4F,WAAW,CAACS,yBAAyB,CAAC,IAAI,CAAC;MAChD,IAAI,CAACC,uBAAuB,EAAE;MAC9B,IAAI,CAACzE,kBAAkB,CAAC0E,YAAY,EAAE;IAC1C;EACJ;EACA;EACAL,KAAK,GAAG;IACJ,IAAI,IAAI,CAAClG,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC4F,WAAW,CAACS,yBAAyB,CAAC,IAAI,CAACG,MAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAAC3E,kBAAkB,CAAC0E,YAAY,EAAE;MACtC,IAAI,CAAC7D,UAAU,EAAE;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+D,UAAU,CAACpH,KAAK,EAAE;IACd,IAAI,CAACgC,YAAY,CAAChC,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqH,gBAAgB,CAAC1F,EAAE,EAAE;IACjB,IAAI,CAACM,SAAS,GAAGN,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2F,iBAAiB,CAAC3F,EAAE,EAAE;IAClB,IAAI,CAAC0B,UAAU,GAAG1B,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4F,gBAAgB,CAACC,UAAU,EAAE;IACzB,IAAI,CAACpB,QAAQ,GAAGoB,UAAU;IAC1B,IAAI,CAAChF,kBAAkB,CAAC0E,YAAY,EAAE;IACtC,IAAI,CAAC3G,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA;EACA,IAAIkE,SAAS,GAAG;IACZ,OAAO,IAAI,CAACrE,UAAU;EAC1B;EACA;EACA,IAAI8G,QAAQ,GAAG;IACX,OAAO,IAAI,CAACtG,QAAQ,GAAG,IAAI,CAACE,eAAe,EAAEoG,QAAQ,IAAI,EAAE,GAAG,IAAI,CAACpG,eAAe,EAAEoG,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAIC,YAAY,GAAG;IACf,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACvG,SAAS,EAAE;MAChB,MAAMwG,eAAe,GAAG,IAAI,CAACvG,eAAe,CAACoG,QAAQ,CAAC5J,GAAG,CAACmG,MAAM,IAAIA,MAAM,CAAC6D,SAAS,CAAC;MACrF,IAAI,IAAI,CAACV,MAAM,EAAE,EAAE;QACfS,eAAe,CAACE,OAAO,EAAE;MAC7B;MACA;MACA,OAAOF,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAAC1G,eAAe,CAACoG,QAAQ,CAAC,CAAC,CAAC,CAACI,SAAS;EACrD;EACA;EACAV,MAAM,GAAG;IACL,OAAO,IAAI,CAACxE,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC3C,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACAgI,cAAc,CAAC5C,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACgB,QAAQ,EAAE;MAChB,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACiD,kBAAkB,CAAC7C,KAAK,CAAC,GAAG,IAAI,CAAC8C,oBAAoB,CAAC9C,KAAK,CAAC;IACtF;EACJ;EACA;EACA8C,oBAAoB,CAAC9C,KAAK,EAAE;IACxB,MAAM+C,OAAO,GAAG/C,KAAK,CAAC+C,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKvL,UAAU,IACrCuL,OAAO,KAAKtL,QAAQ,IACpBsL,OAAO,KAAKrL,UAAU,IACtBqL,OAAO,KAAKpL,WAAW;IAC3B,MAAMsL,SAAS,GAAGF,OAAO,KAAKnL,KAAK,IAAImL,OAAO,KAAKlL,KAAK;IACxD,MAAMqL,OAAO,GAAG,IAAI,CAAC/B,WAAW;IAChC;IACA,IAAK,CAAC+B,OAAO,CAACC,QAAQ,EAAE,IAAIF,SAAS,IAAI,CAACnL,cAAc,CAACkI,KAAK,CAAC,IAC1D,CAAC,IAAI,CAACjE,QAAQ,IAAIiE,KAAK,CAACoD,MAAM,KAAKJ,UAAW,EAAE;MACjDhD,KAAK,CAACqD,cAAc,EAAE,CAAC,CAAC;MACxB,IAAI,CAAC3B,IAAI,EAAE;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAAC3F,QAAQ,EAAE;MACrB,MAAMuH,wBAAwB,GAAG,IAAI,CAACjB,QAAQ;MAC9Ca,OAAO,CAACK,SAAS,CAACvD,KAAK,CAAC;MACxB,MAAMwD,cAAc,GAAG,IAAI,CAACnB,QAAQ;MACpC;MACA,IAAImB,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAAC7F,cAAc,CAAC8F,QAAQ,CAACD,cAAc,CAACf,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAI,kBAAkB,CAAC7C,KAAK,EAAE;IACtB,MAAMkD,OAAO,GAAG,IAAI,CAAC/B,WAAW;IAChC,MAAM4B,OAAO,GAAG/C,KAAK,CAAC+C,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKvL,UAAU,IAAIuL,OAAO,KAAKtL,QAAQ;IACjE,MAAM0L,QAAQ,GAAGD,OAAO,CAACC,QAAQ,EAAE;IACnC,IAAIH,UAAU,IAAIhD,KAAK,CAACoD,MAAM,EAAE;MAC5B;MACApD,KAAK,CAACqD,cAAc,EAAE;MACtB,IAAI,CAAC5B,KAAK,EAAE;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAAC0B,QAAQ,KACbJ,OAAO,KAAKnL,KAAK,IAAImL,OAAO,KAAKlL,KAAK,CAAC,IACxCqL,OAAO,CAACQ,UAAU,IAClB,CAAC5L,cAAc,CAACkI,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACqD,cAAc,EAAE;MACtBH,OAAO,CAACQ,UAAU,CAACC,qBAAqB,EAAE;IAC9C,CAAC,MACI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACnH,SAAS,IAAI+G,OAAO,KAAKhL,CAAC,IAAIiI,KAAK,CAAC4D,OAAO,EAAE;MACpE5D,KAAK,CAACqD,cAAc,EAAE;MACtB,MAAMQ,oBAAoB,GAAG,IAAI,CAACpF,OAAO,CAACqF,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAAC/C,QAAQ,IAAI,CAAC+C,GAAG,CAAC1B,QAAQ,CAAC;MACrF,IAAI,CAAC5D,OAAO,CAACyB,OAAO,CAACtB,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAACoC,QAAQ,EAAE;UAClB6C,oBAAoB,GAAGjF,MAAM,CAACuB,MAAM,EAAE,GAAGvB,MAAM,CAACyB,QAAQ,EAAE;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM2D,sBAAsB,GAAGd,OAAO,CAACe,eAAe;MACtDf,OAAO,CAACK,SAAS,CAACvD,KAAK,CAAC;MACxB,IAAI,IAAI,CAAChE,SAAS,IACdgH,UAAU,IACVhD,KAAK,CAACkE,QAAQ,IACdhB,OAAO,CAACQ,UAAU,IAClBR,OAAO,CAACe,eAAe,KAAKD,sBAAsB,EAAE;QACpDd,OAAO,CAACQ,UAAU,CAACC,qBAAqB,EAAE;MAC9C;IACJ;EACJ;EACAQ,QAAQ,GAAG;IACP,IAAI,CAAC,IAAI,CAACnD,QAAQ,EAAE;MAChB,IAAI,CAAC1F,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACH,YAAY,CAACO,IAAI,EAAE;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI0I,OAAO,GAAG;IACN,IAAI,CAAC9I,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC6F,WAAW,EAAEkD,eAAe,EAAE;IACnC,IAAI,CAAC,IAAI,CAACrD,QAAQ,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE;MACnC,IAAI,CAAC3B,UAAU,EAAE;MACjB,IAAI,CAACb,kBAAkB,CAAC0E,YAAY,EAAE;MACtC,IAAI,CAAC3G,YAAY,CAACO,IAAI,EAAE;IAC5B;EACJ;EACA;AACJ;AACA;EACI4I,WAAW,GAAG;IACV,IAAI,CAACC,WAAW,CAACC,cAAc,CAAC7F,IAAI,CAACpG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmH,SAAS,CAAC,MAAM;MAC1D,IAAI,CAACtC,kBAAkB,CAACqH,aAAa,EAAE;MACvC,IAAI,CAACC,mBAAmB,EAAE;IAC9B,CAAC,CAAC;EACN;EACA;EACAC,cAAc,GAAG;IACb,OAAO,IAAI,CAACnH,gBAAgB,GAAI,OAAM,IAAI,CAACA,gBAAgB,CAACoH,KAAM,EAAC,GAAG,EAAE;EAC5E;EACA;EACA,IAAIrC,KAAK,GAAG;IACR,OAAO,CAAC,IAAI,CAACtG,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC4I,OAAO,EAAE;EAClE;EACArI,oBAAoB,GAAG;IACnB;IACA;IACAsI,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAAC9J,SAAS,EAAE;QAChB,IAAI,CAACuB,MAAM,GAAG,IAAI,CAACvB,SAAS,CAACN,KAAK;MACtC;MACA,IAAI,CAACqK,oBAAoB,CAAC,IAAI,CAACxI,MAAM,CAAC;MACtC,IAAI,CAACtB,YAAY,CAACO,IAAI,EAAE;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIuJ,oBAAoB,CAACrK,KAAK,EAAE;IACxB,IAAI,CAAC6D,OAAO,CAACyB,OAAO,CAACtB,MAAM,IAAIA,MAAM,CAACsG,iBAAiB,EAAE,CAAC;IAC1D,IAAI,CAACjJ,eAAe,CAACkJ,KAAK,EAAE;IAC5B,IAAI,IAAI,CAACpJ,QAAQ,IAAInB,KAAK,EAAE;MACxB,IAAI,CAACwK,KAAK,CAACC,OAAO,CAACzK,KAAK,CAAC,KAAK,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMvC,8BAA8B,EAAE;MAC1C;MACAiB,KAAK,CAACsF,OAAO,CAAEoF,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,EAAE;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAAC3K,KAAK,CAAC;MAC5D;MACA;MACA,IAAI6K,mBAAmB,EAAE;QACrB,IAAI,CAACtE,WAAW,CAACuE,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAAC7F,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAACuB,WAAW,CAACuE,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACtI,kBAAkB,CAAC0E,YAAY,EAAE;EAC1C;EACA;AACJ;AACA;AACA;EACIyD,oBAAoB,CAAC3K,KAAK,EAAE;IACxB,MAAM6K,mBAAmB,GAAG,IAAI,CAAChH,OAAO,CAACkH,IAAI,CAAE/G,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAAC3C,eAAe,CAAC2J,UAAU,CAAChH,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAOA,MAAM,CAAChE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC0B,YAAY,CAACsC,MAAM,CAAChE,KAAK,EAAEA,KAAK,CAAC;MACzE,CAAC,CACD,OAAOiL,KAAK,EAAE;QACV,IAAI,OAAO3J,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACA4J,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIJ,mBAAmB,EAAE;MACrB,IAAI,CAACxJ,eAAe,CAACkE,MAAM,CAACsF,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACA7I,YAAY,CAACF,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAACD,MAAM,IAAK,IAAI,CAACT,SAAS,IAAIoJ,KAAK,CAACC,OAAO,CAAC3I,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAAC+B,OAAO,EAAE;QACd,IAAI,CAACwG,oBAAoB,CAACvI,QAAQ,CAAC;MACvC;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;EACAoD,eAAe,GAAG;IACd,IAAI,CAACqB,WAAW,GAAG,IAAIhK,0BAA0B,CAAC,IAAI,CAACsH,OAAO,CAAC,CAC1D2C,aAAa,CAAC,IAAI,CAACrE,0BAA0B,CAAC,CAC9CiJ,uBAAuB,EAAE,CACzBpE,yBAAyB,CAAC,IAAI,CAACG,MAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDkE,cAAc,EAAE,CAChBC,cAAc,EAAE,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC;IAC1C,IAAI,CAAChF,WAAW,CAACiF,MAAM,CAAC1G,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACE,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAAC7D,QAAQ,IAAI,IAAI,CAACoF,WAAW,CAACuC,UAAU,EAAE;UAC/C,IAAI,CAACvC,WAAW,CAACuC,UAAU,CAACC,qBAAqB,EAAE;QACvD;QACA;QACA;QACA,IAAI,CAAC0C,KAAK,EAAE;QACZ,IAAI,CAAC5E,KAAK,EAAE;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,WAAW,CAACmF,MAAM,CAAC5G,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACnE,UAAU,IAAI,IAAI,CAACgL,KAAK,EAAE;QAC/B,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACrF,WAAW,CAAC8C,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAAC1I,UAAU,IAAI,CAAC,IAAI,CAACQ,QAAQ,IAAI,IAAI,CAACoF,WAAW,CAACuC,UAAU,EAAE;QACxE,IAAI,CAACvC,WAAW,CAACuC,UAAU,CAACC,qBAAqB,EAAE;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACArD,aAAa,GAAG;IACZ,MAAMmG,kBAAkB,GAAGrO,KAAK,CAAC,IAAI,CAACqG,OAAO,CAACC,OAAO,EAAE,IAAI,CAACV,QAAQ,CAAC;IACrE,IAAI,CAACQ,sBAAsB,CAACG,IAAI,CAAChG,SAAS,CAAC8N,kBAAkB,CAAC,CAAC,CAAC/G,SAAS,CAACM,KAAK,IAAI;MAC/E,IAAI,CAAC0G,SAAS,CAAC1G,KAAK,CAACrF,MAAM,EAAEqF,KAAK,CAAC2G,WAAW,CAAC;MAC/C,IAAI3G,KAAK,CAAC2G,WAAW,IAAI,CAAC,IAAI,CAAC5K,QAAQ,IAAI,IAAI,CAACR,UAAU,EAAE;QACxD,IAAI,CAACkG,KAAK,EAAE;QACZ,IAAI,CAAC4E,KAAK,EAAE;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACAjO,KAAK,CAAC,GAAG,IAAI,CAACqG,OAAO,CAAChG,GAAG,CAACmG,MAAM,IAAIA,MAAM,CAACgI,aAAa,CAAC,CAAC,CACrDjI,IAAI,CAAChG,SAAS,CAAC8N,kBAAkB,CAAC,CAAC,CACnC/G,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACtC,kBAAkB,CAACqH,aAAa,EAAE;MACvC,IAAI,CAACtJ,YAAY,CAACO,IAAI,EAAE;IAC5B,CAAC,CAAC;EACN;EACA;EACAgL,SAAS,CAAC9H,MAAM,EAAE+H,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAAC5K,eAAe,CAAC2J,UAAU,CAAChH,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAAChE,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACoB,SAAS,EAAE;MACzC4C,MAAM,CAACyB,QAAQ,EAAE;MACjB,IAAI,CAACpE,eAAe,CAACkJ,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACvK,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAACkM,iBAAiB,CAAClI,MAAM,CAAChE,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAIiM,WAAW,KAAKjI,MAAM,CAACyD,QAAQ,EAAE;QACjCzD,MAAM,CAACyD,QAAQ,GACT,IAAI,CAACpG,eAAe,CAACkE,MAAM,CAACvB,MAAM,CAAC,GACnC,IAAI,CAAC3C,eAAe,CAACoE,QAAQ,CAACzB,MAAM,CAAC;MAC/C;MACA,IAAI+H,WAAW,EAAE;QACb,IAAI,CAACxF,WAAW,CAAC4F,aAAa,CAACnI,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAAC7C,QAAQ,EAAE;QACf,IAAI,CAACyJ,WAAW,EAAE;QAClB,IAAImB,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACN,KAAK,EAAE;QAChB;MACJ;IACJ;IACA,IAAIQ,WAAW,KAAK,IAAI,CAAC5K,eAAe,CAAC2J,UAAU,CAAChH,MAAM,CAAC,EAAE;MACzD,IAAI,CAACkI,iBAAiB,EAAE;IAC5B;IACA,IAAI,CAAC3L,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA;EACA8J,WAAW,GAAG;IACV,IAAI,IAAI,CAACzJ,QAAQ,EAAE;MACf,MAAM0C,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuI,OAAO,EAAE;MACtC,IAAI,CAAC/K,eAAe,CAACgL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAChC,OAAO,IAAI,CAACC,cAAc,GACpB,IAAI,CAACA,cAAc,CAACF,CAAC,EAAEC,CAAC,EAAE1I,OAAO,CAAC,GAClCA,OAAO,CAAC4I,OAAO,CAACH,CAAC,CAAC,GAAGzI,OAAO,CAAC4I,OAAO,CAACF,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAChM,YAAY,CAACO,IAAI,EAAE;IAC5B;EACJ;EACA;EACAoL,iBAAiB,CAACQ,aAAa,EAAE;IAC7B,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAI,IAAI,CAACxL,QAAQ,EAAE;MACfwL,WAAW,GAAG,IAAI,CAAClF,QAAQ,CAAC5J,GAAG,CAACmG,MAAM,IAAIA,MAAM,CAAChE,KAAK,CAAC;IAC3D,CAAC,MACI;MACD2M,WAAW,GAAG,IAAI,CAAClF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzH,KAAK,GAAG0M,aAAa;IACrE;IACA,IAAI,CAAC7K,MAAM,GAAG8K,WAAW;IACzB,IAAI,CAACnI,WAAW,CAACoI,IAAI,CAACD,WAAW,CAAC;IAClC,IAAI,CAAC1K,SAAS,CAAC0K,WAAW,CAAC;IAC3B,IAAI,CAACpI,eAAe,CAACqI,IAAI,CAAC,IAAI,CAACC,eAAe,CAACF,WAAW,CAAC,CAAC;IAC5D,IAAI,CAACnK,kBAAkB,CAAC0E,YAAY,EAAE;EAC1C;EACA;AACJ;AACA;AACA;EACID,uBAAuB,GAAG;IACtB,IAAI,IAAI,CAACV,WAAW,EAAE;MAClB,IAAI,IAAI,CAACoB,KAAK,EAAE;QACZ,IAAI,CAACpB,WAAW,CAACuG,kBAAkB,EAAE;MACzC,CAAC,MACI;QACD,IAAI,CAACvG,WAAW,CAAC4F,aAAa,CAAC,IAAI,CAAC9K,eAAe,CAACoG,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAV,QAAQ,GAAG;IACP,OAAO,CAAC,IAAI,CAACpG,UAAU,IAAI,CAAC,IAAI,CAACyF,QAAQ,IAAI,IAAI,CAACvC,OAAO,EAAEkJ,MAAM,GAAG,CAAC;EACzE;EACA;EACAtB,KAAK,CAAC5H,OAAO,EAAE;IACX,IAAI,CAAC3D,WAAW,CAAC6F,aAAa,CAAC0F,KAAK,CAAC5H,OAAO,CAAC;EACjD;EACA;EACAmJ,uBAAuB,GAAG;IACtB,IAAI,IAAI,CAACrJ,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMsJ,OAAO,GAAG,IAAI,CAACrK,gBAAgB,EAAEsK,UAAU,EAAE;IACnD,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACG,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGH,OAAO;EAChF;EACA;EACAI,wBAAwB,GAAG;IACvB,IAAI,IAAI,CAACrI,SAAS,IAAI,IAAI,CAACuB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuC,UAAU,EAAE;MACnE,OAAO,IAAI,CAACvC,WAAW,CAACuC,UAAU,CAAC1G,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACAyD,yBAAyB,GAAG;IACxB,IAAI,IAAI,CAAClC,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMsJ,OAAO,GAAG,IAAI,CAACrK,gBAAgB,EAAEsK,UAAU,EAAE;IACnD,IAAIlN,KAAK,GAAG,CAACiN,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC3J,QAAQ;IAC1D,IAAI,IAAI,CAAC8J,cAAc,EAAE;MACrBpN,KAAK,IAAI,GAAG,GAAG,IAAI,CAACoN,cAAc;IACtC;IACA,OAAOpN,KAAK;EAChB;EACA;EACA+E,mBAAmB,CAACuI,MAAM,EAAE;IACxB,IAAI,CAACnJ,YAAY,CAACyI,IAAI,CAACU,MAAM,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIC,iBAAiB,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACT,MAAM,EAAE;MACZ,IAAI,CAAC7M,WAAW,CAAC6F,aAAa,CAACC,YAAY,CAAC,kBAAkB,EAAEwH,GAAG,CAACzF,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAAC7H,WAAW,CAAC6F,aAAa,CAACE,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIwH,gBAAgB,GAAG;IACf,IAAI,CAAChC,KAAK,EAAE;IACZ,IAAI,CAAC3E,IAAI,EAAE;EACf;EACA;AACJ;AACA;AACA;EACI,IAAI4G,gBAAgB,GAAG;IACnB,OAAO,IAAI,CAAC/M,UAAU,IAAI,CAAC,IAAI,CAACgH,KAAK,IAAK,IAAI,CAACjH,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACG,YAAa;EACnF;AACJ;AACAL,cAAc,CAACmN,IAAI;EAAA,iBAA6FnN,cAAc,EAAxBrG,EAAE,mBAAwCiC,EAAE,CAACwR,aAAa,GAA1DzT,EAAE,mBAAqEA,EAAE,CAAC0T,iBAAiB,GAA3F1T,EAAE,mBAAsGA,EAAE,CAAC2T,MAAM,GAAjH3T,EAAE,mBAA4HiB,EAAE,CAAC2S,iBAAiB,GAAlJ5T,EAAE,mBAA6JA,EAAE,CAAC6T,UAAU,GAA5K7T,EAAE,mBAAuLqC,EAAE,CAACyR,cAAc,MAA1M9T,EAAE,mBAAqOiD,EAAE,CAAC8Q,MAAM,MAAhP/T,EAAE,mBAA2QiD,EAAE,CAAC+Q,kBAAkB,MAAlShU,EAAE,mBAA6T8B,cAAc,MAA7U9B,EAAE,mBAAwWiD,EAAE,CAACgR,SAAS,OAAtXjU,EAAE,mBAA6Z,UAAU,GAAzaA,EAAE,mBAAqc+E,0BAA0B,GAAje/E,EAAE,mBAA4emC,EAAE,CAAC+R,aAAa,GAA9flU,EAAE,mBAAygBoF,iBAAiB;AAAA,CAA4D;AAC9rBiB,cAAc,CAAC8N,IAAI,kBADmFnU,EAAE;EAAA,MACJqG,cAAc;EAAA;IAAA;MADZrG,EAAE;MAAFA,EAAE;MAAFA,EAAE,aAC64BL,mBAAmB;IAAA;IAAA;MAAA;MADl6BK,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAAFA,EAAE,6BAAFA,EAAE;AAAA,EACm/B;AAC3lC;EAAA,mDAFsGA,EAAE,mBAERqG,cAAc,EAAc,CAAC;IACjH+N,IAAI,EAAEjU;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiU,IAAI,EAAEnS,EAAE,CAACwR;IAAc,CAAC,EAAE;MAAEW,IAAI,EAAEpU,EAAE,CAAC0T;IAAkB,CAAC,EAAE;MAAEU,IAAI,EAAEpU,EAAE,CAAC2T;IAAO,CAAC,EAAE;MAAES,IAAI,EAAEnT,EAAE,CAAC2S;IAAkB,CAAC,EAAE;MAAEQ,IAAI,EAAEpU,EAAE,CAAC6T;IAAW,CAAC,EAAE;MAAEO,IAAI,EAAE/R,EAAE,CAACyR,cAAc;MAAEO,UAAU,EAAE,CAAC;QAChND,IAAI,EAAEhU;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgU,IAAI,EAAEnR,EAAE,CAAC8Q,MAAM;MAAEM,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAEhU;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgU,IAAI,EAAEnR,EAAE,CAAC+Q,kBAAkB;MAAEK,UAAU,EAAE,CAAC;QAC9CD,IAAI,EAAEhU;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgU,IAAI,EAAEvS,EAAE,CAACyS,YAAY;MAAED,UAAU,EAAE,CAAC;QACxCD,IAAI,EAAEhU;MACV,CAAC,EAAE;QACCgU,IAAI,EAAE/T,MAAM;QACZkU,IAAI,EAAE,CAACzS,cAAc;MACzB,CAAC;IAAE,CAAC,EAAE;MAAEsS,IAAI,EAAEnR,EAAE,CAACgR,SAAS;MAAEI,UAAU,EAAE,CAAC;QACrCD,IAAI,EAAE9T;MACV,CAAC,EAAE;QACC8T,IAAI,EAAEhU;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgU,IAAI,EAAEpI,SAAS;MAAEqI,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAE7T,SAAS;QACfgU,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEH,IAAI,EAAEpI,SAAS;MAAEqI,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAE/T,MAAM;QACZkU,IAAI,EAAE,CAACxP,0BAA0B;MACrC,CAAC;IAAE,CAAC,EAAE;MAAEqP,IAAI,EAAEjS,EAAE,CAAC+R;IAAc,CAAC,EAAE;MAAEE,IAAI,EAAEpI,SAAS;MAAEqI,UAAU,EAAE,CAAC;QAC9DD,IAAI,EAAEhU;MACV,CAAC,EAAE;QACCgU,IAAI,EAAE/T,MAAM;QACZkU,IAAI,EAAE,CAACnP,iBAAiB;MAC5B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoP,mBAAmB,EAAE,CAAC;MAClDJ,IAAI,EAAE5T,KAAK;MACX+T,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE1Q,OAAO,EAAE,CAAC;MACVuQ,IAAI,EAAE3T,SAAS;MACf8T,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE/C,KAAK,EAAE,CAAC;MACR4C,IAAI,EAAE3T,SAAS;MACf8T,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE/E,WAAW,EAAE,CAAC;MACd4E,IAAI,EAAE3T,SAAS;MACf8T,IAAI,EAAE,CAAC5U,mBAAmB;IAC9B,CAAC,CAAC;IAAE8U,UAAU,EAAE,CAAC;MACbL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiG,WAAW,EAAE,CAAC;MACd2N,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoG,QAAQ,EAAE,CAAC;MACXwN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwG,QAAQ,EAAE,CAAC;MACXoN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE4G,sBAAsB,EAAE,CAAC;MACzBgN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE8G,WAAW,EAAE,CAAC;MACd8M,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqF,KAAK,EAAE,CAAC;MACRuO,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEgJ,SAAS,EAAE,CAAC;MACZ4K,IAAI,EAAE5T,KAAK;MACX+T,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEtB,cAAc,EAAE,CAAC;MACjBmB,IAAI,EAAE5T,KAAK;MACX+T,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEG,iBAAiB,EAAE,CAAC;MACpBN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEuH,yBAAyB,EAAE,CAAC;MAC5BqM,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE6R,cAAc,EAAE,CAAC;MACjB+B,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyH,EAAE,EAAE,CAAC;MACLmM,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwJ,YAAY,EAAE,CAAC;MACfoK,IAAI,EAAE1T;IACV,CAAC,CAAC;IAAEuJ,aAAa,EAAE,CAAC;MAChBmK,IAAI,EAAE1T,MAAM;MACZ6T,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEpK,aAAa,EAAE,CAAC;MAChBiK,IAAI,EAAE1T,MAAM;MACZ6T,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEnK,eAAe,EAAE,CAAC;MAClBgK,IAAI,EAAE1T;IACV,CAAC,CAAC;IAAE2J,WAAW,EAAE,CAAC;MACd+J,IAAI,EAAE1T;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMiU,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACnB,IAAI;EAAA,iBAA6FmB,gBAAgB;AAAA,CAAmD;AACrLA,gBAAgB,CAACR,IAAI,kBAzFiFnU,EAAE;EAAA,MAyFF2U,gBAAgB;EAAA;EAAA,WAzFhB3U,EAAE,oBAyF2D,CAAC;IAAEsF,OAAO,EAAEG,kBAAkB;IAAEmP,WAAW,EAAED;EAAiB,CAAC,CAAC;AAAA,EAAiB;AACpP;EAAA,mDA1FsG3U,EAAE,mBA0FR2U,gBAAgB,EAAc,CAAC;IACnHP,IAAI,EAAEjU,SAAS;IACfoU,IAAI,EAAE,CAAC;MACCM,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAExP,OAAO,EAAEG,kBAAkB;QAAEmP,WAAW,EAAED;MAAiB,CAAC;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMI,SAAS,SAAS1O,cAAc,CAAC;EACnCV,WAAW,GAAG;IACV,KAAK,CAAC,GAAGqP,SAAS,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CACd;MACIC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBZ,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,IAAI,CAACa,6BAA6B,GAAG,IAAI,CAACzM,eAAe,EAAE0M,4BAA4B,IAAI,KAAK;EACpG;EACA,IAAIhC,gBAAgB,GAAG;IACnB;IACA;IACA,OAAO,IAAI,CAAC1I,SAAS,IAAI,CAAC,IAAI,CAAC2C,KAAK,IAAK,IAAI,CAAClH,OAAO,IAAI,CAAC,CAAC,IAAI,CAACG,WAAY;EAChF;EACAiE,QAAQ,GAAG;IACP,KAAK,CAACA,QAAQ,EAAE;IAChB,IAAI,CAACtC,cAAc,CACdmJ,MAAM,EAAE,CACR3H,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACqF,QAAQ,CAAC,CAAC,CAC9B0B,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACE,SAAS,EAAE;QAChB,IAAI,CAAC2K,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;QAC5C,IAAI,CAACpN,kBAAkB,CAACqH,aAAa,EAAE;MAC3C;IACJ,CAAC,CAAC;EACN;EACAgG,eAAe,GAAG;IACd;IACA;IACA,IAAI,IAAI,CAACjN,gBAAgB,EAAE;MACvB,IAAI,CAACkN,uBAAuB,GAAG,IAAI,CAAClN,gBAAgB,CAACmN,yBAAyB,EAAE;IACpF;EACJ;EACAjJ,IAAI,GAAG;IACH,IAAI,CAAC6I,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC5C,KAAK,CAAC9I,IAAI,EAAE;IACZ;IACA,IAAI,CAACvG,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA+F,KAAK,GAAG;IACJ,KAAK,CAACA,KAAK,EAAE;IACb;IACA,IAAI,CAACtG,YAAY,CAACO,IAAI,EAAE;EAC5B;EACA;EACA8K,qBAAqB,CAACoE,KAAK,EAAE;IACzB,MAAMhM,MAAM,GAAG,IAAI,CAACH,OAAO,CAACuI,OAAO,EAAE,CAAC4D,KAAK,CAAC;IAC5C,IAAIhM,MAAM,EAAE;MACR,MAAM2H,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC5F,aAAa;MACtC,MAAMkK,UAAU,GAAGxU,6BAA6B,CAACuU,KAAK,EAAE,IAAI,CAACnM,OAAO,EAAE,IAAI,CAACqM,YAAY,CAAC;MACxF,MAAMpK,OAAO,GAAG9B,MAAM,CAACmM,eAAe,EAAE;MACxC,IAAIH,KAAK,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAtE,KAAK,CAACyE,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACDzE,KAAK,CAACyE,SAAS,GAAG1U,wBAAwB,CAACoK,OAAO,CAACuK,SAAS,EAAEvK,OAAO,CAACwK,YAAY,EAAE3E,KAAK,CAACyE,SAAS,EAAEzE,KAAK,CAAC2E,YAAY,CAAC;MAC5H;IACJ;EACJ;EACAxG,mBAAmB,GAAG;IAClB,IAAI,CAAC8B,qBAAqB,CAAC,IAAI,CAACrF,WAAW,CAAC8C,eAAe,IAAI,CAAC,CAAC;EACrE;EACAwD,eAAe,CAAC7M,KAAK,EAAE;IACnB,OAAO,IAAIH,eAAe,CAAC,IAAI,EAAEG,KAAK,CAAC;EAC3C;EACA;EACA4P,gBAAgB,GAAG;IACf,MAAMW,YAAY,GAAG,IAAI,CAACT,uBAAuB,YAAY/V,gBAAgB,GACvE,IAAI,CAAC+V,uBAAuB,CAACpN,UAAU,GACvC,IAAI,CAACoN,uBAAuB,IAAI,IAAI,CAAC5P,WAAW;IACtD,OAAOqQ,YAAY,CAACxK,aAAa,CAACyK,qBAAqB,EAAE,CAACC,KAAK;EACnE;EACA;EACA,IAAIf,4BAA4B,GAAG;IAC/B,OAAO,IAAI,CAACD,6BAA6B;EAC7C;EACA,IAAIC,4BAA4B,CAAC1P,KAAK,EAAE;IACpC,IAAI,CAACyP,6BAA6B,GAAGhT,qBAAqB,CAACuD,KAAK,CAAC;IACjE,IAAI,CAAC0Q,qBAAqB,EAAE;EAChC;EACA;EACAA,qBAAqB,GAAG;IACpB,IAAI,IAAI,CAAC7M,OAAO,EAAE;MACd,KAAK,MAAMG,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;QAC/BG,MAAM,CAACxB,kBAAkB,CAAC0E,YAAY,EAAE;MAC5C;IACJ;EACJ;AACJ;AACAgI,SAAS,CAACvB,IAAI;EAAA;EAAA;IAAA,4DAxMwFxT,EAAE,uBAwMG+U,SAAS,SAATA,SAAS;EAAA;AAAA,GAAqD;AACzKA,SAAS,CAACyB,IAAI,kBAzMwFxW,EAAE;EAAA,MAyMT+U,SAAS;EAAA;EAAA;IAAA;MAzMF/U,EAAE,0BA4M9ByF,kBAAkB;MA5MUzF,EAAE,0BA4MiDyB,SAAS;MA5M5DzB,EAAE,0BA4M4H0B,YAAY;IAAA;IAAA;MAAA;MA5M1I1B,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA,oBAyM8M,UAAU,uBAAuB,MAAM,mBAAmB,SAAS;EAAA;EAAA;IAAA;MAzMnRA,EAAE;QAAA,OAyMT,0BAAsB;MAAA;QAAA,OAAtB,cAAU;MAAA;QAAA,OAAV,aAAS;MAAA;IAAA;IAAA;MAzMFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAyM8/B,CAC9lC;IAAEsF,OAAO,EAAEvD,mBAAmB;IAAE6S,WAAW,EAAEG;EAAU,CAAC,EACxD;IAAEzP,OAAO,EAAE9D,2BAA2B;IAAEoT,WAAW,EAAEG;EAAU,CAAC,CACnE,GA5MiG/U,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,+BA4MqhC;MA5MvhCA,EAAE;QAAA,OA4My8B,YAAQ;MAAA,EAAE;MA5Mr9BA,EAAE,4BA4MymC;MA5M3mCA,EAAE,0DA4M+tC;MA5MjuCA,EAAE,0DA4MigD;MA5MngDA,EAAE,eA4M2gD;MA5M7gDA,EAAE,4BA4M6jD;MA5M/jDA,EAAE,iBA4MmyD;MA5MryDA,EAAE,4BA4MmyD;MA5MryDA,EAAE,wBA4My0D;MA5M30DA,EAAE,eA4Mu1D;MA5Mz1DA,EAAE,0EA4MqiG;MA5MviGA,EAAE;QAAA,OA4Mu5E,WAAO;MAAA,EAAE;QAAA,OAAe,iBAAa;MAAA,EAA5B;QAAA,OAA6C,WAAO;MAAA,EAApD;IAAA;IAAA;MAAA,YA5Ml6EA,EAAE;MAAFA,EAAE,mEA4Mi5B;MA5Mn5BA,EAAE,aA4MilC;MA5MnlCA,EAAE,kCA4MilC;MA5MnlCA,EAAE,gCA4MwmC;MA5M1mCA,EAAE,aA4MssC;MA5MxsCA,EAAE,iCA4MssC;MA5MxsCA,EAAE,aA4MoyC;MA5MtyCA,EAAE,kCA4MoyC;MA5MtyCA,EAAE,aA4MsmE;MA5MxmEA,EAAE,oEA4MsmE;IAAA;EAAA;EAAA,eAAqmJF,EAAE,CAAC2W,OAAO,EAAoF3W,EAAE,CAAC4W,QAAQ,EAA6E5W,EAAE,CAAC6W,YAAY,EAAqF7W,EAAE,CAAC8W,eAAe,EAA8DnX,EAAE,CAACE,mBAAmB,EAAk8BF,EAAE,CAACG,gBAAgB;EAAA;EAAA;EAAA;IAAA,WAAyH,CAACwE,mBAAmB,CAACG,cAAc;EAAC;EAAA;AAAA,EAAiG;AACt4Q;EAAA,mDA7MsGvE,EAAE,mBA6MR+U,SAAS,EAAc,CAAC;IAC5GX,IAAI,EAAEzT,SAAS;IACf4T,IAAI,EAAE,CAAC;MAAEM,QAAQ,EAAE,YAAY;MAAEgC,QAAQ,EAAE,WAAW;MAAEC,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;MAAEC,aAAa,EAAEnW,iBAAiB,CAACoW,IAAI;MAAEC,eAAe,EAAEpW,uBAAuB,CAACqW,MAAM;MAAEC,IAAI,EAAE;QAC7L,MAAM,EAAE,UAAU;QAClB,mBAAmB,EAAE,MAAM;QAC3B,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,UAAU;QAC7B,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEC,UAAU,EAAE,CAAChT,mBAAmB,CAACG,cAAc,CAAC;MAAEuQ,SAAS,EAAE,CAC5D;QAAExP,OAAO,EAAEvD,mBAAmB;QAAE6S,WAAW,EAAEG;MAAU,CAAC,EACxD;QAAEzP,OAAO,EAAE9D,2BAA2B;QAAEoT,WAAW,EAAEG;MAAU,CAAC,CACnE;MAAEsC,QAAQ,EAAE,g0FAAg0F;MAAEC,MAAM,EAAE,CAAC,2mHAA2mH;IAAE,CAAC;EACl9M,CAAC,CAAC,QAAkB;IAAE5N,OAAO,EAAE,CAAC;MACxB0K,IAAI,EAAEtT,eAAe;MACrByT,IAAI,EAAE,CAAC9S,SAAS,EAAE;QAAE8V,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAExB,YAAY,EAAE,CAAC;MACf3B,IAAI,EAAEtT,eAAe;MACrByT,IAAI,EAAE,CAAC7S,YAAY,EAAE;QAAE6V,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEC,aAAa,EAAE,CAAC;MAChBpD,IAAI,EAAErT,YAAY;MAClBwT,IAAI,EAAE,CAAC9O,kBAAkB;IAC7B,CAAC,CAAC;IAAE8P,4BAA4B,EAAE,CAAC;MAC/BnB,IAAI,EAAE5T;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiX,eAAe,CAAC;AAEtBA,eAAe,CAACjE,IAAI;EAAA,iBAA6FiE,eAAe;AAAA,CAAkD;AAClLA,eAAe,CAACC,IAAI,kBAhQkF1X,EAAE;EAAA,MAgQUyX;AAAe,EAKtG;AAC3BA,eAAe,CAACE,IAAI,kBAtQkF3X,EAAE;EAAA,WAsQsC,CAACqF,mCAAmC,CAAC;EAAA,UAAYtF,YAAY,EAAEF,aAAa,EAAE8B,eAAe,EAAEC,eAAe,EAAEM,mBAAmB,EACzQF,kBAAkB,EAClBL,eAAe,EACfC,eAAe;AAAA,EAAI;AAC3B;EAAA,mDA1QsG5B,EAAE,mBA0QRyX,eAAe,EAAc,CAAC;IAClHrD,IAAI,EAAEpT,QAAQ;IACduT,IAAI,EAAE,CAAC;MACCqD,OAAO,EAAE,CAAC7X,YAAY,EAAEF,aAAa,EAAE8B,eAAe,EAAEC,eAAe,CAAC;MACxEiW,OAAO,EAAE,CACL3V,mBAAmB,EACnBF,kBAAkB,EAClB+S,SAAS,EACTJ,gBAAgB,EAChBhT,eAAe,EACfC,eAAe,CAClB;MACDkW,YAAY,EAAE,CAAC/C,SAAS,EAAEJ,gBAAgB,CAAC;MAC3CG,SAAS,EAAE,CAACzP,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASD,iBAAiB,EAAEL,0BAA0B,EAAEM,mCAAmC,EAAEL,2CAA2C,EAAES,kBAAkB,EAAEsP,SAAS,EAAErP,eAAe,EAAE+R,eAAe,EAAE9C,gBAAgB,EAAEtO,cAAc,EAAEjC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}