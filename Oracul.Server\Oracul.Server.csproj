<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\oracul.client</SpaRoot>
    <SpaProxyLaunchCommand>npm start</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:53792</SpaProxyServerUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.*-*</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\oracul.client\oracul.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\Oracul.Data\Oracul.Data.csproj" />
  </ItemGroup>

</Project>
