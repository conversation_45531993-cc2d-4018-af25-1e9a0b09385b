using Microsoft.EntityFrameworkCore;
using Oracul.Data.Models;

namespace Oracul.Data.Data
{
    /// <summary>
    /// Main database context for the Oracul application
    /// </summary>
    public class OraculDbContext : DbContext
    {
        public OraculDbContext(DbContextOptions<OraculDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }

        // Profile DbSets
        public DbSet<UserProfile> UserProfiles { get; set; }
        public DbSet<ProfileLocation> ProfileLocations { get; set; }
        public DbSet<ContactInformation> ContactInformations { get; set; }
        public DbSet<BusinessAddress> BusinessAddresses { get; set; }
        public DbSet<PhoneNumber> PhoneNumbers { get; set; }
        public DbSet<ProfileSkill> ProfileSkills { get; set; }
        public DbSet<SkillEndorsement> SkillEndorsements { get; set; }
        public DbSet<BlogPost> BlogPosts { get; set; }
        public DbSet<BlogPostTag> BlogPostTags { get; set; }
        public DbSet<Achievement> Achievements { get; set; }
        public DbSet<Certification> Certifications { get; set; }
        public DbSet<WorkExperience> WorkExperiences { get; set; }
        public DbSet<WorkAchievement> WorkAchievements { get; set; }
        public DbSet<PortfolioItem> PortfolioItems { get; set; }
        public DbSet<PortfolioImage> PortfolioImages { get; set; }
        public DbSet<PortfolioTechnology> PortfolioTechnologies { get; set; }
        public DbSet<ClientTestimonial> ClientTestimonials { get; set; }
        public DbSet<SocialLink> SocialLinks { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            ConfigureUserEntity(modelBuilder);
            ConfigureRoleEntity(modelBuilder);
            ConfigurePermissionEntity(modelBuilder);
            ConfigureUserRoleEntity(modelBuilder);
            ConfigureRolePermissionEntity(modelBuilder);

            // Configure profile entities
            ConfigureProfileEntities(modelBuilder);

            // Configure global query filters for soft delete
            ConfigureGlobalQueryFilters(modelBuilder);

            // Seed initial data
            SeedData(modelBuilder);
        }

        private static void ConfigureUserEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Email).IsRequired();
                entity.Property(e => e.FirstName).IsRequired();
                entity.Property(e => e.LastName).IsRequired();

                // Social authentication indexes
                entity.HasIndex(e => e.GoogleId).IsUnique().HasFilter("[GoogleId] IS NOT NULL");
                entity.HasIndex(e => e.FacebookId).IsUnique().HasFilter("[FacebookId] IS NOT NULL");

                // Token fields
                entity.Property(e => e.EmailConfirmationToken).HasMaxLength(500);
                entity.Property(e => e.PasswordResetToken).HasMaxLength(500);
                entity.Property(e => e.RefreshToken).HasMaxLength(500);
                entity.Property(e => e.GoogleId).HasMaxLength(100);
                entity.Property(e => e.FacebookId).HasMaxLength(100);
                entity.Property(e => e.ProfilePictureUrl).HasMaxLength(500);
            });
        }

        private static void ConfigureRoleEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
            });
        }

        private static void ConfigurePermissionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
            });
        }

        private static void ConfigureUserRoleEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique();
                
                entity.HasOne(ur => ur.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ur => ur.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureRolePermissionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.HasIndex(e => new { e.RoleId, e.PermissionId }).IsUnique();
                
                entity.HasOne(rp => rp.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(rp => rp.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(rp => rp.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(rp => rp.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureProfileEntities(ModelBuilder modelBuilder)
        {
            // UserProfile configuration
            modelBuilder.Entity<UserProfile>(entity =>
            {
                entity.HasIndex(e => e.UserId).IsUnique();
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.Property(e => e.Username).IsRequired();
                entity.Property(e => e.Slug).IsRequired();
                entity.Property(e => e.FirstName).IsRequired();
                entity.Property(e => e.LastName).IsRequired();

                entity.HasOne(p => p.User)
                    .WithOne()
                    .HasForeignKey<UserProfile>(p => p.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ProfileLocation configuration
            modelBuilder.Entity<ProfileLocation>(entity =>
            {
                entity.HasOne(pl => pl.UserProfile)
                    .WithOne(p => p.Location)
                    .HasForeignKey<ProfileLocation>(pl => pl.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ContactInformation configuration
            modelBuilder.Entity<ContactInformation>(entity =>
            {
                entity.HasOne(ci => ci.UserProfile)
                    .WithOne(p => p.ContactInfo)
                    .HasForeignKey<ContactInformation>(ci => ci.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // BusinessAddress configuration
            modelBuilder.Entity<BusinessAddress>(entity =>
            {
                entity.HasOne(ba => ba.ContactInformation)
                    .WithOne(ci => ci.BusinessAddress)
                    .HasForeignKey<BusinessAddress>(ba => ba.ContactInformationId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PhoneNumber configuration
            modelBuilder.Entity<PhoneNumber>(entity =>
            {
                entity.HasOne(pn => pn.ContactInformation)
                    .WithMany(ci => ci.PhoneNumbers)
                    .HasForeignKey(pn => pn.ContactInformationId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ProfileSkill configuration
            modelBuilder.Entity<ProfileSkill>(entity =>
            {
                entity.HasIndex(e => new { e.UserProfileId, e.Name }).IsUnique();
                entity.Property(e => e.Name).IsRequired();

                entity.HasOne(ps => ps.UserProfile)
                    .WithMany(p => p.Skills)
                    .HasForeignKey(ps => ps.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SkillEndorsement configuration
            modelBuilder.Entity<SkillEndorsement>(entity =>
            {
                entity.HasIndex(e => new { e.ProfileSkillId, e.EndorserUserId }).IsUnique();

                entity.HasOne(se => se.ProfileSkill)
                    .WithMany(ps => ps.SkillEndorsements)
                    .HasForeignKey(se => se.ProfileSkillId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(se => se.EndorserUser)
                    .WithMany()
                    .HasForeignKey(se => se.EndorserUserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // BlogPost configuration
            modelBuilder.Entity<BlogPost>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.Excerpt).IsRequired();
                entity.Property(e => e.Slug).IsRequired();

                entity.HasOne(bp => bp.UserProfile)
                    .WithMany(p => p.BlogPosts)
                    .HasForeignKey(bp => bp.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // BlogPostTag configuration
            modelBuilder.Entity<BlogPostTag>(entity =>
            {
                entity.HasIndex(e => new { e.BlogPostId, e.Tag }).IsUnique();
                entity.Property(e => e.Tag).IsRequired();

                entity.HasOne(bpt => bpt.BlogPost)
                    .WithMany(bp => bp.BlogPostTags)
                    .HasForeignKey(bpt => bpt.BlogPostId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Achievement configuration
            modelBuilder.Entity<Achievement>(entity =>
            {
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.Description).IsRequired();

                entity.HasOne(a => a.UserProfile)
                    .WithMany(p => p.Achievements)
                    .HasForeignKey(a => a.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Certification configuration
            modelBuilder.Entity<Certification>(entity =>
            {
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IssuingOrganization).IsRequired();

                entity.HasOne(c => c.UserProfile)
                    .WithMany(p => p.Certifications)
                    .HasForeignKey(c => c.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // WorkExperience configuration
            modelBuilder.Entity<WorkExperience>(entity =>
            {
                entity.Property(e => e.Company).IsRequired();
                entity.Property(e => e.Position).IsRequired();
                entity.Property(e => e.Description).IsRequired();

                entity.HasOne(we => we.UserProfile)
                    .WithMany(p => p.Experiences)
                    .HasForeignKey(we => we.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // WorkAchievement configuration
            modelBuilder.Entity<WorkAchievement>(entity =>
            {
                entity.Property(e => e.Achievement).IsRequired();

                entity.HasOne(wa => wa.WorkExperience)
                    .WithMany(we => we.WorkAchievements)
                    .HasForeignKey(wa => wa.WorkExperienceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PortfolioItem configuration
            modelBuilder.Entity<PortfolioItem>(entity =>
            {
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.Description).IsRequired();

                entity.HasOne(pi => pi.UserProfile)
                    .WithMany(p => p.PortfolioItems)
                    .HasForeignKey(pi => pi.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PortfolioImage configuration
            modelBuilder.Entity<PortfolioImage>(entity =>
            {
                entity.Property(e => e.ImageUrl).IsRequired();

                entity.HasOne(pi => pi.PortfolioItem)
                    .WithMany(p => p.PortfolioImages)
                    .HasForeignKey(pi => pi.PortfolioItemId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PortfolioTechnology configuration
            modelBuilder.Entity<PortfolioTechnology>(entity =>
            {
                entity.Property(e => e.Technology).IsRequired();

                entity.HasOne(pt => pt.PortfolioItem)
                    .WithMany(p => p.PortfolioTechnologies)
                    .HasForeignKey(pt => pt.PortfolioItemId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ClientTestimonial configuration
            modelBuilder.Entity<ClientTestimonial>(entity =>
            {
                entity.Property(e => e.ClientName).IsRequired();
                entity.Property(e => e.TestimonialText).IsRequired();

                entity.HasOne(ct => ct.PortfolioItem)
                    .WithOne(pi => pi.ClientTestimonial)
                    .HasForeignKey<ClientTestimonial>(ct => ct.PortfolioItemId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SocialLink configuration
            modelBuilder.Entity<SocialLink>(entity =>
            {
                entity.HasIndex(e => new { e.UserProfileId, e.Platform }).IsUnique();
                entity.Property(e => e.Platform).IsRequired();
                entity.Property(e => e.Url).IsRequired();

                entity.HasOne(sl => sl.UserProfile)
                    .WithMany(p => p.SocialLinks)
                    .HasForeignKey(sl => sl.UserProfileId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureGlobalQueryFilters(ModelBuilder modelBuilder)
        {
            // Apply global query filter for soft delete
            modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Role>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Permission>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<UserRole>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<RolePermission>().HasQueryFilter(e => !e.IsDeleted);

            // Apply global query filter for profile entities
            modelBuilder.Entity<UserProfile>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ProfileLocation>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ContactInformation>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BusinessAddress>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PhoneNumber>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ProfileSkill>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<SkillEndorsement>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BlogPost>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BlogPostTag>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Achievement>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Certification>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<WorkExperience>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<WorkAchievement>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PortfolioItem>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PortfolioImage>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PortfolioTechnology>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ClientTestimonial>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<SocialLink>().HasQueryFilter(e => !e.IsDeleted);
        }

        private static void SeedData(ModelBuilder modelBuilder)
        {
            // Use a fixed date for seed data to avoid model changes
            var seedDate = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Seed default roles
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 1, Name = "Administrator", Description = "Full system access", CreatedAt = seedDate },
                new Role { Id = 2, Name = "User", Description = "Standard user access", CreatedAt = seedDate },
                new Role { Id = 3, Name = "Oracle", Description = "Oracle practitioner access", CreatedAt = seedDate }
            );

            // Seed default permissions
            modelBuilder.Entity<Permission>().HasData(
                new Permission { Id = 1, Name = "Users.Read", Description = "Read user information", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 2, Name = "Users.Write", Description = "Create and update users", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 3, Name = "Users.Delete", Description = "Delete users", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 4, Name = "Roles.Read", Description = "Read role information", Category = "Roles", CreatedAt = seedDate },
                new Permission { Id = 5, Name = "Roles.Write", Description = "Create and update roles", Category = "Roles", CreatedAt = seedDate },
                new Permission { Id = 6, Name = "Roles.Delete", Description = "Delete roles", Category = "Roles", CreatedAt = seedDate },
                new Permission { Id = 7, Name = "Oracle.Services", Description = "Provide oracle services", Category = "Oracle", CreatedAt = seedDate },
                new Permission { Id = 8, Name = "Oracle.Content", Description = "Publish oracle content", Category = "Oracle", CreatedAt = seedDate }
            );

            // Seed role permissions (Administrator gets all permissions)
            modelBuilder.Entity<RolePermission>().HasData(
                new RolePermission { Id = 1, RoleId = 1, PermissionId = 1, CreatedAt = seedDate },
                new RolePermission { Id = 2, RoleId = 1, PermissionId = 2, CreatedAt = seedDate },
                new RolePermission { Id = 3, RoleId = 1, PermissionId = 3, CreatedAt = seedDate },
                new RolePermission { Id = 4, RoleId = 1, PermissionId = 4, CreatedAt = seedDate },
                new RolePermission { Id = 5, RoleId = 1, PermissionId = 5, CreatedAt = seedDate },
                new RolePermission { Id = 6, RoleId = 1, PermissionId = 6, CreatedAt = seedDate },
                new RolePermission { Id = 7, RoleId = 1, PermissionId = 7, CreatedAt = seedDate },
                new RolePermission { Id = 8, RoleId = 1, PermissionId = 8, CreatedAt = seedDate },
                // User role gets read permissions only
                new RolePermission { Id = 9, RoleId = 2, PermissionId = 1, CreatedAt = seedDate },
                new RolePermission { Id = 10, RoleId = 2, PermissionId = 4, CreatedAt = seedDate },
                // Oracle role gets oracle permissions
                new RolePermission { Id = 11, RoleId = 3, PermissionId = 1, CreatedAt = seedDate },
                new RolePermission { Id = 12, RoleId = 3, PermissionId = 7, CreatedAt = seedDate },
                new RolePermission { Id = 13, RoleId = 3, PermissionId = 8, CreatedAt = seedDate }
            );

            // Seed admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$OVClPP2sY/W8JElGWyxJUOW.qdYKJA.VyzOhFI27qnVoYkXRGDPM2", // hashed password: Admin123!
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedAt = seedDate
                }
            );

            // Assign admin role to admin user
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole
                {
                    Id = 1,
                    UserId = 1,
                    RoleId = 1,
                    CreatedAt = seedDate
                }
            );

            // Seed oracle users
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 2,
                    FirstName = "Luna",
                    LastName = "Starweaver",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$G9R.dG3z8PyMEcHgh4Yt1.NHpMpQNu/hEEjEXbb5Kj5pl0xUu7jxe", // hashed password: Oracle123!
                    PhoneNumber = "+****************",
                    IsActive = true,
                    EmailConfirmed = true,
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop",
                    CreatedAt = seedDate
                },
                new User
                {
                    Id = 3,
                    FirstName = "Orion",
                    LastName = "Celestia",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$G9R.dG3z8PyMEcHgh4Yt1.NHpMpQNu/hEEjEXbb5Kj5pl0xUu7jxe", // hashed password: Oracle123!
                    PhoneNumber = "+****************",
                    IsActive = true,
                    EmailConfirmed = true,
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop",
                    CreatedAt = seedDate
                },
                new User
                {
                    Id = 4,
                    FirstName = "Athena",
                    LastName = "Moonshadow",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$G9R.dG3z8PyMEcHgh4Yt1.NHpMpQNu/hEEjEXbb5Kj5pl0xUu7jxe", // hashed password: Oracle123!
                    PhoneNumber = "+****************",
                    IsActive = true,
                    EmailConfirmed = true,
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop",
                    CreatedAt = seedDate
                },
                new User
                {
                    Id = 5,
                    FirstName = "Phoenix",
                    LastName = "Starlight",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$G9R.dG3z8PyMEcHgh4Yt1.NHpMpQNu/hEEjEXbb5Kj5pl0xUu7jxe", // hashed password: Oracle123!
                    PhoneNumber = "+****************",
                    IsActive = true,
                    EmailConfirmed = true,
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=150&h=150&fit=crop",
                    CreatedAt = seedDate
                },
                new User
                {
                    Id = 6,
                    FirstName = "Zephyr",
                    LastName = "Dreamweaver",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$G9R.dG3z8PyMEcHgh4Yt1.NHpMpQNu/hEEjEXbb5Kj5pl0xUu7jxe", // hashed password: Oracle123!
                    PhoneNumber = "+****************",
                    IsActive = true,
                    EmailConfirmed = true,
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop",
                    CreatedAt = seedDate
                }
            );

            // Assign oracle role to oracle users
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole { Id = 2, UserId = 2, RoleId = 3, CreatedAt = seedDate },
                new UserRole { Id = 3, UserId = 3, RoleId = 3, CreatedAt = seedDate },
                new UserRole { Id = 4, UserId = 4, RoleId = 3, CreatedAt = seedDate },
                new UserRole { Id = 5, UserId = 5, RoleId = 3, CreatedAt = seedDate },
                new UserRole { Id = 6, UserId = 6, RoleId = 3, CreatedAt = seedDate }
            );

            // Create user profiles for oracle users
            modelBuilder.Entity<UserProfile>().HasData(
                new UserProfile
                {
                    Id = 1,
                    UserId = 2,
                    Username = "luna-starweaver",
                    Slug = "luna-starweaver",
                    FirstName = "Luna",
                    LastName = "Starweaver",
                    ProfilePhotoUrl = "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop",
                    CoverPhotoUrl = "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=800&h=300&fit=crop",
                    IsPublic = true,
                    ProfessionalTitle = "Astrologer & Cosmic Guide",
                    Headline = "Revealing celestial patterns to guide your journey. Astrology that illuminates your path and connects you with the cosmos.",
                    Summary = "Professional astrologer with over 10 years of experience reading natal charts and providing cosmic guidance. Specializing in birth chart analysis, compatibility readings, and astrological forecasting to help you navigate life's journey with wisdom from the stars.",
                    ProfileCompletionPercentage = 95,
                    ProfileViews = 3247,
                    CreatedAt = seedDate
                },
                new UserProfile
                {
                    Id = 2,
                    UserId = 3,
                    Username = "orion-celestia",
                    Slug = "orion-celestia",
                    FirstName = "Orion",
                    LastName = "Celestia",
                    ProfilePhotoUrl = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop",
                    CoverPhotoUrl = "https://images.unsplash.com/photo-1532012197267-da84d127e765?w=800&h=300&fit=crop",
                    IsPublic = true,
                    ProfessionalTitle = "Tarot Master & Intuitive Reader",
                    Headline = "Channeling ancient wisdom through cards. Tarot readings that provide clarity, guidance, and spiritual insight.",
                    Summary = "Master tarot reader with a gift for intuitive interpretation and spiritual guidance. With 15 years of experience, I offer in-depth tarot readings that illuminate your path, reveal hidden influences, and help you make empowered choices aligned with your highest good.",
                    ProfileCompletionPercentage = 90,
                    ProfileViews = 2876,
                    CreatedAt = seedDate
                },
                new UserProfile
                {
                    Id = 3,
                    UserId = 4,
                    Username = "athena-moonshadow",
                    Slug = "athena-moonshadow",
                    FirstName = "Athena",
                    LastName = "Moonshadow",
                    ProfilePhotoUrl = "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop",
                    CoverPhotoUrl = "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=800&h=300&fit=crop",
                    IsPublic = true,
                    ProfessionalTitle = "Psychic Medium & Energy Healer",
                    Headline = "Bridging the physical and spiritual realms. Connecting you with guidance from beyond and healing energy from within.",
                    Summary = "Gifted psychic medium and energy healer with the ability to connect with spiritual guides and loved ones who have passed. My readings provide validation, closure, and guidance while my energy work helps clear blockages and restore your natural flow of vitality.",
                    ProfileCompletionPercentage = 85,
                    ProfileViews = 2453,
                    CreatedAt = seedDate
                },
                new UserProfile
                {
                    Id = 4,
                    UserId = 5,
                    Username = "phoenix-starlight",
                    Slug = "phoenix-starlight",
                    FirstName = "Phoenix",
                    LastName = "Starlight",
                    ProfilePhotoUrl = "https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=150&h=150&fit=crop",
                    CoverPhotoUrl = "https://images.unsplash.com/photo-1420593248178-d88870618ca0?w=800&h=300&fit=crop",
                    IsPublic = true,
                    ProfessionalTitle = "Shamanic Practitioner & Spiritual Guide",
                    Headline = "Walking between worlds to bring back healing and wisdom. Ancient shamanic techniques for modern spiritual transformation.",
                    Summary = "Experienced shamanic practitioner trained in ancestral healing traditions from around the world. I facilitate soul retrievals, power animal journeys, and ceremonial rituals that help you reconnect with your authentic self and the natural world.",
                    ProfileCompletionPercentage = 80,
                    ProfileViews = 1987,
                    CreatedAt = seedDate
                },
                new UserProfile
                {
                    Id = 5,
                    UserId = 6,
                    Username = "zephyr-dreamweaver",
                    Slug = "zephyr-dreamweaver",
                    FirstName = "Zephyr",
                    LastName = "Dreamweaver",
                    ProfilePhotoUrl = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop",
                    CoverPhotoUrl = "https://images.unsplash.com/photo-1470813740244-df37b8c1edcb?w=800&h=300&fit=crop",
                    IsPublic = true,
                    ProfessionalTitle = "Dream Analyst & Intuitive Coach",
                    Headline = "Decoding the language of your dreams. Revealing subconscious wisdom to guide your conscious journey.",
                    Summary = "Specialized dream analyst and intuitive coach who helps you understand the powerful messages your subconscious communicates through dreams. Through dream interpretation and intuitive coaching, I help you gain clarity, overcome challenges, and align with your soul's purpose.",
                    ProfileCompletionPercentage = 75,
                    ProfileViews = 1654,
                    CreatedAt = seedDate
                }
            );

            // Add profile locations
            modelBuilder.Entity<ProfileLocation>().HasData(
                new ProfileLocation { Id = 1, UserProfileId = 1, City = "Sedona", State = "AZ", Country = "USA", DisplayLocation = "Sedona, Arizona", CreatedAt = seedDate },
                new ProfileLocation { Id = 2, UserProfileId = 2, City = "Asheville", State = "NC", Country = "USA", DisplayLocation = "Asheville, North Carolina", CreatedAt = seedDate },
                new ProfileLocation { Id = 3, UserProfileId = 3, City = "Salem", State = "MA", Country = "USA", DisplayLocation = "Salem, Massachusetts", CreatedAt = seedDate },
                new ProfileLocation { Id = 4, UserProfileId = 4, City = "Boulder", State = "CO", Country = "USA", DisplayLocation = "Boulder, Colorado", CreatedAt = seedDate },
                new ProfileLocation { Id = 5, UserProfileId = 5, City = "Portland", State = "OR", Country = "USA", DisplayLocation = "Portland, Oregon", CreatedAt = seedDate }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                }
            }
        }
    }
}
