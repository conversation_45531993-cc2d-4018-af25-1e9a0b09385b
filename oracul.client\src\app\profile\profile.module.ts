import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatPaginatorModule } from '@angular/material/paginator';

// Profile Components
import { ProfileViewComponent } from './components/profile-view/profile-view.component';
import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';
import { ProfileSearchComponent } from './components/profile-search/profile-search.component';
import { ProfileCardComponent } from './components/profile-card/profile-card.component';
import { SkillsManagementComponent } from './components/skills-management/skills-management.component';
import { ExperienceManagementComponent } from './components/experience-management/experience-management.component';
import { PortfolioManagementComponent } from './components/portfolio-management/portfolio-management.component';
import { ProfileAnalyticsComponent } from './components/profile-analytics/profile-analytics.component';

// Services
import { ProfileService } from './services/profile.service';

// Guards
import { ProfileOwnerGuard } from './guards/profile-owner.guard';
import { AuthGuard } from '../auth/guards/auth.guard';

const routes = [
  {
    path: 'profile/edit',
    component: ProfileEditComponent,
    canActivate: [ProfileOwnerGuard]
  },
  {
    path: 'profiles/search',
    component: ProfileSearchComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'profile/manage/skills',
    component: SkillsManagementComponent,
    canActivate: [ProfileOwnerGuard]
  },
  {
    path: 'profile/manage/experience',
    component: ExperienceManagementComponent,
    canActivate: [ProfileOwnerGuard]
  },
  {
    path: 'profile/manage/portfolio',
    component: PortfolioManagementComponent,
    canActivate: [ProfileOwnerGuard]
  },
  {
    path: 'profile/analytics',
    component: ProfileAnalyticsComponent,
    canActivate: [ProfileOwnerGuard]
  },
  {
    path: 'profile/:identifier',
    component: ProfileViewComponent
  }
];

@NgModule({
  declarations: [
    ProfileViewComponent,
    ProfileEditComponent,
    ProfileSearchComponent,
    ProfileCardComponent,
    SkillsManagementComponent,
    ExperienceManagementComponent,
    PortfolioManagementComponent,
    ProfileAnalyticsComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule.forChild(routes),

    // Material Modules
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatTabsModule,
    MatExpansionModule,
    MatSlideToggleModule,
    MatDividerModule,
    MatButtonToggleModule,
    MatPaginatorModule
  ],
  providers: [
    // Use real ProfileService connected to backend API
    ProfileService,
    ProfileOwnerGuard
  ],
  exports: [
    ProfileViewComponent,
    ProfileEditComponent,
    ProfileSearchComponent,
    ProfileCardComponent
  ]
})
export class ProfileModule { }
