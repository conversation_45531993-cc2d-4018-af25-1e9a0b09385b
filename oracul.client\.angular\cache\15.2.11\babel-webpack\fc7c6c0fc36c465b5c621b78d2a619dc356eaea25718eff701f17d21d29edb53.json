{"ast": null, "code": "import getCurrentScriptSource from \"./getCurrentScriptSource.js\";\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\n\nfunction parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var options = {};\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      options[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {// URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n    if (scriptSourceURL) {\n      options = scriptSourceURL;\n      options.fromCurrentScript = true;\n    }\n  }\n  return options;\n}\nexport default parseURL;", "map": {"version": 3, "names": ["getCurrentScriptSource", "parseURL", "resourceQuery", "options", "searchParams", "slice", "split", "i", "length", "pair", "decodeURIComponent", "scriptSource", "scriptSourceURL", "URL", "self", "location", "href", "error", "fromCurrentScript"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/webpack-dev-server/client/utils/parseURL.js"], "sourcesContent": ["import getCurrentScriptSource from \"./getCurrentScriptSource.js\";\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\n\nfunction parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var options = {};\n\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      options[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {// URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n\n    if (scriptSourceURL) {\n      options = scriptSourceURL;\n      options.fromCurrentScript = true;\n    }\n  }\n\n  return options;\n}\n\nexport default parseURL;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,6BAA6B;AAChE;AACA;AACA;AACA;;AAEA,SAASC,QAAQ,CAACC,aAAa,EAAE;EAC/B;EACA,IAAIC,OAAO,GAAG,CAAC,CAAC;EAEhB,IAAI,OAAOD,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,EAAE,EAAE;IAC7D,IAAIE,YAAY,GAAGF,aAAa,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAEpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIE,IAAI,GAAGL,YAAY,CAACG,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC;MACrCH,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;IAChD;EACF,CAAC,MAAM;IACL;IACA,IAAIE,YAAY,GAAGX,sBAAsB,EAAE;IAC3C,IAAIY,eAAe;IAEnB,IAAI;MACF;MACA;MACA;MACAA,eAAe,GAAG,IAAIC,GAAG,CAACF,YAAY,EAAEG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC7D,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;MACf;IAAA;IAGF,IAAIL,eAAe,EAAE;MACnBT,OAAO,GAAGS,eAAe;MACzBT,OAAO,CAACe,iBAAiB,GAAG,IAAI;IAClC;EACF;EAEA,OAAOf,OAAO;AAChB;AAEA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}