{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/divider\";\nexport class ProfileDemoComponent {\n  static {\n    this.ɵfac = function ProfileDemoComponent_Factory(t) {\n      return new (t || ProfileDemoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileDemoComponent,\n      selectors: [[\"app-profile-demo\"]],\n      decls: 128,\n      vars: 0,\n      consts: [[1, \"demo-container\"], [1, \"demo-card\"], [1, \"demo-section\"], [1, \"demo-links\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/profile/luna-starweaver\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile/edit\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/profiles/search\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/profile-card-demo\"], [1, \"features-grid\"], [1, \"feature-item\"], [1, \"url-list\"], [1, \"url-item\"], [1, \"url-description\"], [1, \"info-box\"], [1, \"info-content\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/dashboard\"]],\n      template: function ProfileDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Profile System Demo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Test the professional profile system with mockup data \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 2)(11, \"h3\");\n          i0.ɵɵtext(12, \"\\uD83C\\uDFAF Available Profile Pages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"a\", 4)(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" View Astrologer Profile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"a\", 5)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Edit Profile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"a\", 6)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25, \" Search Professionals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"a\", 7)(27, \"mat-icon\");\n          i0.ɵɵtext(28, \"view_module\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(29, \" Profile Cards Demo \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(30, \"mat-divider\");\n          i0.ɵɵelementStart(31, \"div\", 2)(32, \"h3\");\n          i0.ɵɵtext(33, \"\\uD83D\\uDCCB Sample Profile Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 8)(35, \"div\", 9)(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"photo_camera\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"span\");\n          i0.ɵɵtext(39, \"Mystical Profile Photos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 9)(41, \"mat-icon\");\n          i0.ɵɵtext(42, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\");\n          i0.ɵɵtext(44, \"Spiritual Journey Timeline\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 9)(46, \"mat-icon\");\n          i0.ɵɵtext(47, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\");\n          i0.ɵɵtext(49, \"Astrological Skills & Endorsements\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 9)(51, \"mat-icon\");\n          i0.ɵɵtext(52, \"work_outline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\");\n          i0.ɵɵtext(54, \"Reading Portfolio & Testimonials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 9)(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\");\n          i0.ɵɵtext(59, \"Cosmic Wisdom Blog Posts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"emoji_events\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\");\n          i0.ɵɵtext(64, \"Certifications & Achievements\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 9)(66, \"mat-icon\");\n          i0.ɵɵtext(67, \"contact_mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\");\n          i0.ɵɵtext(69, \"Sacred Contact Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 9)(71, \"mat-icon\");\n          i0.ɵɵtext(72, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\");\n          i0.ɵɵtext(74, \"Spiritual Impact Analytics\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(75, \"mat-divider\");\n          i0.ɵɵelementStart(76, \"div\", 2)(77, \"h3\");\n          i0.ɵɵtext(78, \"\\uD83D\\uDD17 Direct URLs for Testing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 10)(80, \"div\", 11)(81, \"code\");\n          i0.ɵɵtext(82, \"http://localhost:4201/profile/luna-starweaver\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\", 12);\n          i0.ɵɵtext(84, \"Professional astrologer profile with cosmic data\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 11)(86, \"code\");\n          i0.ɵɵtext(87, \"http://localhost:4201/profile/edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"span\", 12);\n          i0.ɵɵtext(89, \"Profile editing form\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"div\", 11)(91, \"code\");\n          i0.ɵɵtext(92, \"http://localhost:4201/profiles/search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"span\", 12);\n          i0.ɵɵtext(94, \"Professional search page\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(95, \"mat-divider\");\n          i0.ɵɵelementStart(96, \"div\", 2)(97, \"h3\");\n          i0.ɵɵtext(98, \"\\uD83D\\uDCA1 Mockup Data Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 13)(100, \"mat-icon\");\n          i0.ɵɵtext(101, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"div\", 14)(103, \"p\")(104, \"strong\");\n          i0.ɵɵtext(105, \"Sample Profile:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(106, \" Luna Starweaver - Professional Astrologer & Cosmic Guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"p\")(108, \"strong\");\n          i0.ɵɵtext(109, \"Features:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(110, \" Complete mystical profile with celestial photos, spiritual journey, astrological skills, reading portfolio, cosmic blog posts, and spiritual analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"p\")(112, \"strong\");\n          i0.ɵɵtext(113, \"Data Source:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(114, \" MockProfileService with realistic astrology-focused sample data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"p\")(116, \"strong\");\n          i0.ɵɵtext(117, \"Images:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(118, \" High-quality celestial and mystical stock photos from Unsplash\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(119, \"mat-card-actions\")(120, \"button\", 4)(121, \"mat-icon\");\n          i0.ɵɵtext(122, \"launch\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(123, \" Start Astrology Profile Demo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"button\", 15)(125, \"mat-icon\");\n          i0.ɵɵtext(126, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(127, \" Back to Dashboard \");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i2.MatAnchor, i2.MatButton, i3.MatCard, i3.MatCardActions, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, i4.MatIcon, i5.MatDivider],\n      styles: [\".demo-container[_ngcontent-%COMP%] {\\n      padding: 20px;\\n      max-width: 800px;\\n      margin: 0 auto;\\n    }\\n\\n    .demo-card[_ngcontent-%COMP%] {\\n      margin-bottom: 20px;\\n    }\\n\\n    .demo-section[_ngcontent-%COMP%] {\\n      margin: 24px 0;\\n    }\\n\\n    .demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      color: var(--theme-primary);\\n      margin-bottom: 16px;\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n    }\\n\\n    .demo-links[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 12px;\\n      flex-wrap: wrap;\\n    }\\n\\n    .features-grid[_ngcontent-%COMP%] {\\n      display: grid;\\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n      gap: 16px;\\n    }\\n\\n    .feature-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n      padding: 12px;\\n      background-color: rgba(0,0,0,0.02);\\n      border-radius: 8px;\\n      border-left: 3px solid var(--theme-primary);\\n    }\\n\\n    .feature-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n      color: var(--theme-primary);\\n    }\\n\\n    .url-list[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 12px;\\n    }\\n\\n    .url-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 4px;\\n      padding: 12px;\\n      background-color: rgba(0,0,0,0.02);\\n      border-radius: 8px;\\n    }\\n\\n    .url-item[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n      background-color: var(--theme-primary);\\n      color: white;\\n      padding: 4px 8px;\\n      border-radius: 4px;\\n      font-family: 'Courier New', monospace;\\n      font-size: 0.9rem;\\n    }\\n\\n    .url-description[_ngcontent-%COMP%] {\\n      color: var(--theme-text-secondary);\\n      font-size: 0.9rem;\\n    }\\n\\n    .info-box[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 12px;\\n      padding: 16px;\\n      background-color: rgba(103, 58, 183, 0.1);\\n      border-radius: 8px;\\n      border-left: 4px solid var(--theme-primary);\\n    }\\n\\n    .info-box[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n      color: var(--theme-primary);\\n      margin-top: 2px;\\n    }\\n\\n    .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 4px 0;\\n      line-height: 1.4;\\n    }\\n\\n    .info-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n      color: var(--theme-primary);\\n    }\\n\\n    mat-divider[_ngcontent-%COMP%] {\\n      margin: 20px 0;\\n    }\\n\\n    @media (max-width: 600px) {\\n      .demo-container[_ngcontent-%COMP%] {\\n        padding: 10px;\\n      }\\n\\n      .demo-links[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n      }\\n\\n      .features-grid[_ngcontent-%COMP%] {\\n        grid-template-columns: 1fr;\\n      }\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;AA4PA,OAAM,MAAOA,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAvP7BC,8BAA4B;UAIVA,sBAAM;UAAAA,iBAAW;UAC3BA,qCACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,uEACF;UAAAA,iBAAoB;UAGtBA,wCAAkB;UAEVA,qDAA0B;UAAAA,iBAAK;UACnCA,+BAAwB;UAEVA,2BAAU;UAAAA,iBAAW;UAC/BA,0CACF;UAAAA,iBAAI;UAEJA,6BAA+D;UACnDA,qBAAI;UAAAA,iBAAW;UACzBA,+BACF;UAAAA,iBAAI;UAEJA,6BAAoD;UACxCA,uBAAM;UAAAA,iBAAW;UAC3BA,uCACF;UAAAA,iBAAI;UAEJA,6BAAsD;UAC1CA,4BAAW;UAAAA,iBAAW;UAChCA,qCACF;UAAAA,iBAAI;UAIRA,+BAA2B;UAE3BA,+BAA0B;UACpBA,qDAA0B;UAAAA,iBAAK;UACnCA,+BAA2B;UAEbA,6BAAY;UAAAA,iBAAW;UACjCA,6BAAM;UAAAA,wCAAuB;UAAAA,iBAAO;UAEtCA,+BAA0B;UACdA,qBAAI;UAAAA,iBAAW;UACzBA,6BAAM;UAAAA,2CAA0B;UAAAA,iBAAO;UAEzCA,+BAA0B;UACdA,2BAAU;UAAAA,iBAAW;UAC/BA,6BAAM;UAAAA,mDAAkC;UAAAA,iBAAO;UAEjDA,+BAA0B;UACdA,6BAAY;UAAAA,iBAAW;UACjCA,6BAAM;UAAAA,iDAAgC;UAAAA,iBAAO;UAE/CA,+BAA0B;UACdA,wBAAO;UAAAA,iBAAW;UAC5BA,6BAAM;UAAAA,yCAAwB;UAAAA,iBAAO;UAEvCA,+BAA0B;UACdA,6BAAY;UAAAA,iBAAW;UACjCA,6BAAM;UAAAA,8CAA6B;UAAAA,iBAAO;UAE5CA,+BAA0B;UACdA,6BAAY;UAAAA,iBAAW;UACjCA,6BAAM;UAAAA,2CAA0B;UAAAA,iBAAO;UAEzCA,+BAA0B;UACdA,0BAAS;UAAAA,iBAAW;UAC9BA,6BAAM;UAAAA,2CAA0B;UAAAA,iBAAO;UAK7CA,+BAA2B;UAE3BA,+BAA0B;UACpBA,qDAA0B;UAAAA,iBAAK;UACnCA,gCAAsB;UAEZA,8DAA6C;UAAAA,iBAAO;UAC1DA,iCAA8B;UAAAA,iEAAgD;UAAAA,iBAAO;UAEvFA,gCAAsB;UACdA,mDAAkC;UAAAA,iBAAO;UAC/CA,iCAA8B;UAAAA,qCAAoB;UAAAA,iBAAO;UAE3DA,gCAAsB;UACdA,sDAAqC;UAAAA,iBAAO;UAClDA,iCAA8B;UAAAA,yCAAwB;UAAAA,iBAAO;UAKnEA,+BAA2B;UAE3BA,+BAA0B;UACpBA,8CAAmB;UAAAA,iBAAK;UAC5BA,gCAAsB;UACVA,sBAAI;UAAAA,iBAAW;UACzBA,iCAA0B;UACbA,iCAAe;UAAAA,iBAAS;UAACA,2EAAwD;UAAAA,iBAAI;UAChGA,2BAAG;UAAQA,2BAAS;UAAAA,iBAAS;UAACA,yKAAsJ;UAAAA,iBAAI;UACxLA,2BAAG;UAAQA,8BAAY;UAAAA,iBAAS;UAACA,kFAA+D;UAAAA,iBAAI;UACpGA,2BAAG;UAAQA,yBAAO;UAAAA,iBAAS;UAACA,iFAA8D;UAAAA,iBAAI;UAMtGA,0CAAkB;UAEJA,wBAAM;UAAAA,iBAAW;UAC3BA,gDACF;UAAAA,iBAAS;UACTA,oCAAmD;UACvCA,2BAAS;UAAAA,iBAAW;UAC9BA,qCACF;UAAAA,iBAAS", "names": ["ProfileDemoComponent", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile-demo\\profile-demo.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-profile-demo',\r\n  template: `\r\n    <div class=\"demo-container\">\r\n      <mat-card class=\"demo-card\">\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>person</mat-icon>\r\n            Profile System Demo\r\n          </mat-card-title>\r\n          <mat-card-subtitle>\r\n            Test the professional profile system with mockup data\r\n          </mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <div class=\"demo-section\">\r\n            <h3>🎯 Available Profile Pages</h3>\r\n            <div class=\"demo-links\">\r\n              <a mat-raised-button color=\"primary\" routerLink=\"/profile/luna-starweaver\">\r\n                <mat-icon>visibility</mat-icon>\r\n                View Astrologer Profile\r\n              </a>\r\n\r\n              <a mat-raised-button color=\"accent\" routerLink=\"/profile/edit\">\r\n                <mat-icon>edit</mat-icon>\r\n                Edit Profile\r\n              </a>\r\n\r\n              <a mat-stroked-button routerLink=\"/profiles/search\">\r\n                <mat-icon>search</mat-icon>\r\n                Search Professionals\r\n              </a>\r\n\r\n              <a mat-stroked-button routerLink=\"/profile-card-demo\">\r\n                <mat-icon>view_module</mat-icon>\r\n                Profile Cards Demo\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <mat-divider></mat-divider>\r\n\r\n          <div class=\"demo-section\">\r\n            <h3>📋 Sample Profile Features</h3>\r\n            <div class=\"features-grid\">\r\n              <div class=\"feature-item\">\r\n                <mat-icon>photo_camera</mat-icon>\r\n                <span>Mystical Profile Photos</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>work</mat-icon>\r\n                <span>Spiritual Journey Timeline</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>psychology</mat-icon>\r\n                <span>Astrological Skills & Endorsements</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>work_outline</mat-icon>\r\n                <span>Reading Portfolio & Testimonials</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>article</mat-icon>\r\n                <span>Cosmic Wisdom Blog Posts</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>emoji_events</mat-icon>\r\n                <span>Certifications & Achievements</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>contact_mail</mat-icon>\r\n                <span>Sacred Contact Information</span>\r\n              </div>\r\n              <div class=\"feature-item\">\r\n                <mat-icon>analytics</mat-icon>\r\n                <span>Spiritual Impact Analytics</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <mat-divider></mat-divider>\r\n\r\n          <div class=\"demo-section\">\r\n            <h3>🔗 Direct URLs for Testing</h3>\r\n            <div class=\"url-list\">\r\n              <div class=\"url-item\">\r\n                <code>http://localhost:4201/profile/luna-starweaver</code>\r\n                <span class=\"url-description\">Professional astrologer profile with cosmic data</span>\r\n              </div>\r\n              <div class=\"url-item\">\r\n                <code>http://localhost:4201/profile/edit</code>\r\n                <span class=\"url-description\">Profile editing form</span>\r\n              </div>\r\n              <div class=\"url-item\">\r\n                <code>http://localhost:4201/profiles/search</code>\r\n                <span class=\"url-description\">Professional search page</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <mat-divider></mat-divider>\r\n\r\n          <div class=\"demo-section\">\r\n            <h3>💡 Mockup Data Info</h3>\r\n            <div class=\"info-box\">\r\n              <mat-icon>info</mat-icon>\r\n              <div class=\"info-content\">\r\n                <p><strong>Sample Profile:</strong> Luna Starweaver - Professional Astrologer & Cosmic Guide</p>\r\n                <p><strong>Features:</strong> Complete mystical profile with celestial photos, spiritual journey, astrological skills, reading portfolio, cosmic blog posts, and spiritual analytics</p>\r\n                <p><strong>Data Source:</strong> MockProfileService with realistic astrology-focused sample data</p>\r\n                <p><strong>Images:</strong> High-quality celestial and mystical stock photos from Unsplash</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-raised-button color=\"primary\" routerLink=\"/profile/luna-starweaver\">\r\n            <mat-icon>launch</mat-icon>\r\n            Start Astrology Profile Demo\r\n          </button>\r\n          <button mat-stroked-button routerLink=\"/dashboard\">\r\n            <mat-icon>dashboard</mat-icon>\r\n            Back to Dashboard\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .demo-container {\r\n      padding: 20px;\r\n      max-width: 800px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    .demo-card {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .demo-section {\r\n      margin: 24px 0;\r\n    }\r\n\r\n    .demo-section h3 {\r\n      color: var(--theme-primary);\r\n      margin-bottom: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n    }\r\n\r\n    .demo-links {\r\n      display: flex;\r\n      gap: 12px;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .features-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n      gap: 16px;\r\n    }\r\n\r\n    .feature-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 12px;\r\n      background-color: rgba(0,0,0,0.02);\r\n      border-radius: 8px;\r\n      border-left: 3px solid var(--theme-primary);\r\n    }\r\n\r\n    .feature-item mat-icon {\r\n      color: var(--theme-primary);\r\n    }\r\n\r\n    .url-list {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 12px;\r\n    }\r\n\r\n    .url-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 4px;\r\n      padding: 12px;\r\n      background-color: rgba(0,0,0,0.02);\r\n      border-radius: 8px;\r\n    }\r\n\r\n    .url-item code {\r\n      background-color: var(--theme-primary);\r\n      color: white;\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-family: 'Courier New', monospace;\r\n      font-size: 0.9rem;\r\n    }\r\n\r\n    .url-description {\r\n      color: var(--theme-text-secondary);\r\n      font-size: 0.9rem;\r\n    }\r\n\r\n    .info-box {\r\n      display: flex;\r\n      gap: 12px;\r\n      padding: 16px;\r\n      background-color: rgba(103, 58, 183, 0.1);\r\n      border-radius: 8px;\r\n      border-left: 4px solid var(--theme-primary);\r\n    }\r\n\r\n    .info-box mat-icon {\r\n      color: var(--theme-primary);\r\n      margin-top: 2px;\r\n    }\r\n\r\n    .info-content p {\r\n      margin: 4px 0;\r\n      line-height: 1.4;\r\n    }\r\n\r\n    .info-content strong {\r\n      color: var(--theme-primary);\r\n    }\r\n\r\n    mat-divider {\r\n      margin: 20px 0;\r\n    }\r\n\r\n    @media (max-width: 600px) {\r\n      .demo-container {\r\n        padding: 10px;\r\n      }\r\n\r\n      .demo-links {\r\n        flex-direction: column;\r\n      }\r\n\r\n      .features-grid {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class ProfileDemoComponent {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}