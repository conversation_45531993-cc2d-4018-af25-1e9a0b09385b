{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, catchError, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../auth/services/auth.service\";\nimport * as i2 from \"../services/profile.service\";\nimport * as i3 from \"@angular/router\";\nexport class ProfileOwnerGuard {\n  constructor(authService, profileService, router) {\n    this.authService = authService;\n    this.profileService = profileService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      if (!isAuthenticated) {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: state.url\n          }\n        });\n        return false;\n      }\n      return true;\n    }), catchError(() => {\n      this.router.navigate(['/login']);\n      return of(false);\n    }));\n  }\n  static {\n    this.ɵfac = function ProfileOwnerGuard_Factory(t) {\n      return new (t || ProfileOwnerGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.ProfileService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileOwnerGuard,\n      factory: ProfileOwnerGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,QAAQ,gBAAgB;;;;;AAOtD,OAAM,MAAOC,iBAAiB;EAC5BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,MAAc;IAFd,gBAAW,GAAXF,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,WAAM,GAANC,MAAM;EACb;EAEHC,WAAW,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACL,WAAW,CAACM,gBAAgB,CAACC,IAAI,CAC3CV,IAAI,CAAC,CAAC,CAAC,EACPF,GAAG,CAACa,eAAe,IAAG;MACpB,IAAI,CAACA,eAAe,EAAE;QACpB,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAAEC,WAAW,EAAE;YAAEC,SAAS,EAAEN,KAAK,CAACO;UAAG;QAAE,CAAE,CAAC;QAC3E,OAAO,KAAK;;MAEd,OAAO,IAAI;IACb,CAAC,CAAC,EACFhB,UAAU,CAAC,MAAK;MACd,IAAI,CAACM,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,OAAOf,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;;;uBAzBWI,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAe,SAAjBf,iBAAiB;MAAAgB,YAFhB;IAAM;EAAA", "names": ["of", "map", "catchError", "take", "ProfileOwnerGuard", "constructor", "authService", "profileService", "router", "canActivate", "route", "state", "isAuthenticated$", "pipe", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\guards\\profile-owner.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, catchError, take } from 'rxjs/operators';\r\nimport { AuthService } from '../../auth/services/auth.service';\r\nimport { ProfileService } from '../services/profile.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProfileOwnerGuard implements CanActivate {\r\n  constructor(\r\n    private authService: AuthService,\r\n    private profileService: ProfileService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Observable<boolean> | Promise<boolean> | boolean {\r\n    return this.authService.isAuthenticated$.pipe(\r\n      take(1),\r\n      map(isAuthenticated => {\r\n        if (!isAuthenticated) {\r\n          this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });\r\n          return false;\r\n        }\r\n        return true;\r\n      }),\r\n      catchError(() => {\r\n        this.router.navigate(['/login']);\r\n        return of(false);\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}