{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth/services/auth.service\";\nimport * as i2 from \"./core/theme/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/divider\";\nimport * as i10 from \"@angular/material/menu\";\nimport * as i11 from \"./core/theme/theme-selector/theme-selector.component\";\nfunction AppComponent_mat_toolbar_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r4 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Welcome, \", user_r4.firstName, \"! \");\n  }\n}\nfunction AppComponent_mat_toolbar_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-toolbar\", 3)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 4);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Oracul App\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"span\", 4);\n    i0.ɵɵtemplate(7, AppComponent_mat_toolbar_1_span_7_Template, 2, 1, \"span\", 5);\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵelementStart(9, \"button\", 6)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"mat-menu\", 7, 8)(14, \"button\", 9)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"My Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 10)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Find Professionals\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 11)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Themes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-icon\", 12);\n    i0.ɵɵtext(30, \"chevron_right\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"button\", 13)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(36, \"mat-divider\");\n    i0.ɵɵelementStart(37, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_toolbar_1_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵelementStart(38, \"mat-icon\");\n    i0.ɵɵtext(39, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41, \"Logout\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"mat-menu\", 15, 16)(44, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_toolbar_1_Template_div_click_44_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelement(45, \"app-theme-selector\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(13);\n    const _r3 = i0.ɵɵreference(43);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(8, 3, ctx_r0.authService.currentUser$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n  }\n}\nexport class AppComponent {\n  constructor(authService, themeService, router, snackBar) {\n    this.authService = authService;\n    this.themeService = themeService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.title = 'oracul.client';\n  }\n  ngOnInit() {\n    // Initialize authentication state and theme\n    // Force theme initialization\n    this.themeService.getCurrentTheme();\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.snackBar.open('Logged out successfully', 'Close', {\n          duration: 3000,\n          panelClass: ['success-snackbar']\n        });\n        this.router.navigate(['/login']);\n      },\n      error: error => {\n        this.snackBar.open('Logout failed', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        // Still navigate to login even if logout fails\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"app-container\"], [\"color\", \"primary\", 4, \"ngIf\"], [1, \"content-container\"], [\"color\", \"primary\"], [1, \"toolbar-spacer\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"user-menu\"], [\"menu\", \"matMenu\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile/edit\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profiles/search\"], [\"mat-menu-item\", \"\", 3, \"matMenuTriggerFor\"], [1, \"submenu-arrow\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"theme-menu\"], [\"themeMenu\", \"matMenu\"], [1, \"theme-menu-content\", 3, \"click\"], [1, \"user-info\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_mat_toolbar_1_Template, 46, 5, \"mat-toolbar\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx.authService.isAuthenticated$));\n        }\n      },\n      dependencies: [i5.NgIf, i3.RouterOutlet, i3.RouterLink, i6.MatToolbar, i7.MatIconButton, i8.MatIcon, i9.MatDivider, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, i11.ThemeSelectorComponent, i5.AsyncPipe],\n      styles: [\"[_nghost-%COMP%] {\\r\\n  display: block;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.app-container[_ngcontent-%COMP%] {\\r\\n  min-height: 100vh;\\r\\n  background-color: #f5f5f5;\\r\\n}\\r\\n\\r\\n.toolbar-spacer[_ngcontent-%COMP%] {\\r\\n  flex: 1 1 auto;\\r\\n}\\r\\n\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  margin-right: 16px;\\r\\n  font-size: 14px;\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n}\\r\\n\\r\\n\\r\\n.submenu-arrow[_ngcontent-%COMP%] {\\r\\n  margin-left: auto;\\r\\n  font-size: 18px;\\r\\n}\\r\\n\\r\\n.theme-menu-content[_ngcontent-%COMP%] {\\r\\n  min-width: 400px;\\r\\n  max-width: 500px;\\r\\n}\\r\\n\\r\\n\\r\\n  .theme-menu .mat-menu-panel {\\r\\n  max-width: 600px !important;\\r\\n  min-width: 400px !important;\\r\\n}\\r\\n\\r\\n  .user-menu .mat-menu-item {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n  .user-menu .mat-menu-item .mat-icon:first-child {\\r\\n  margin-right: 0;\\r\\n}\\r\\n\\r\\n.content-container[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  margin: 40px 0;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  overflow-x: auto;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n.temperature[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #ff6b35;\\r\\n}\\r\\n\\r\\n.summary[_ngcontent-%COMP%] {\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .content-container[_ngcontent-%COMP%] {\\r\\n    padding: 10px;\\r\\n  }\\r\\n\\r\\n  .weather-table[_ngcontent-%COMP%] {\\r\\n    font-size: 14px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\nmat-card[_ngcontent-%COMP%] {\\r\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\nmat-card[_ngcontent-%COMP%]:hover {\\r\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%] {\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;IAUIA,gCAAyE;IACvEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,4DACF;;;;;;IATFA,sCAA0E;IAC9DA,qBAAK;IAAAA,iBAAW;IAC1BA,0BAAoC;IACpCA,4BAAM;IAAAA,0BAAU;IAAAA,iBAAO;IACvBA,0BAAoC;IAGpCA,6EAEO;;IAEPA,iCAAmD;IACvCA,+BAAc;IAAAA,iBAAW;IAErCA,uCAA4C;IAE9BA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,2BAAU;IAAAA,iBAAO;IAEzBA,mCAAoD;IACxCA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,mCAAkB;IAAAA,iBAAO;IAEjCA,mCAAsD;IAC1CA,wBAAO;IAAAA,iBAAW;IAC5BA,6BAAM;IAAAA,uBAAM;IAAAA,iBAAO;IACnBA,qCAAgC;IAAAA,8BAAa;IAAAA,iBAAW;IAE1DA,mCAAsB;IACVA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,yBAAQ;IAAAA,iBAAO;IAEvBA,+BAA2B;IAC3BA,mCAAyC;IAAnBA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IACtCA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,uBAAM;IAAAA,iBAAO;IAKvBA,yCAAkD;IAChBA;MAAA,OAASC,wBAAwB;IAAA,EAAC;IAChED,sCAAyC;IAC3CA,iBAAM;;;;;;IApCiBA,eAAuC;IAAvCA,4EAAuC;IAIxCA,eAA0B;IAA1BA,uCAA0B;IAY1BA,gBAA+B;IAA/BA,uCAA+B;;;ACf3D,OAAM,MAAOE,YAAY;EAGvBC,YACSC,WAAwB,EACvBC,YAA0B,EAC1BC,MAAc,EACdC,QAAqB;IAHtB,gBAAW,GAAXH,WAAW;IACV,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IANlB,UAAK,GAAG,eAAe;EAOpB;EAEHC,QAAQ;IACN;IACA;IACA,IAAI,CAACH,YAAY,CAACI,eAAe,EAAE;EACrC;EAEAC,MAAM;IACJ,IAAI,CAACN,WAAW,CAACM,MAAM,EAAE,CAACC,SAAS,CAAC;MAClCC,IAAI,EAAE,MAAK;QACT,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UACrDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACV,QAAQ,CAACM,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE;UAC3CC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF;QACA,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;;;uBAlCWd,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAgB;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDVzBtB,8BAA2B;UAEzBA,8EA6Cc;;UAGdA,8BAA+B;UAE7BA,gCAA+B;UACjCA,iBAAM;;;UAnDwBA,eAA0C;UAA1CA,6EAA0C", "names": ["i0", "$event", "AppComponent", "constructor", "authService", "themeService", "router", "snackBar", "ngOnInit", "getCurrentTheme", "logout", "subscribe", "next", "open", "duration", "panelClass", "navigate", "error", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.ts"], "sourcesContent": ["<!-- Authentication-aware Layout -->\r\n<div class=\"app-container\">\r\n  <!-- Top Toolbar - Only show when authenticated -->\r\n  <mat-toolbar color=\"primary\" *ngIf=\"authService.isAuthenticated$ | async\">\r\n    <mat-icon>cloud</mat-icon>\r\n    <span class=\"toolbar-spacer\"></span>\r\n    <span>Oracul App</span>\r\n    <span class=\"toolbar-spacer\"></span>\r\n\r\n    <!-- User Info -->\r\n    <span class=\"user-info\" *ngIf=\"authService.currentUser$ | async as user\">\r\n      Welcome, {{ user.firstName }}!\r\n    </span>\r\n\r\n    <button mat-icon-button [matMenuTriggerFor]=\"menu\">\r\n      <mat-icon>account_circle</mat-icon>\r\n    </button>\r\n    <mat-menu #menu=\"matMenu\" class=\"user-menu\">\r\n      <button mat-menu-item routerLink=\"/profile/edit\">\r\n        <mat-icon>person</mat-icon>\r\n        <span>My Profile</span>\r\n      </button>\r\n      <button mat-menu-item routerLink=\"/profiles/search\">\r\n        <mat-icon>search</mat-icon>\r\n        <span>Find Professionals</span>\r\n      </button>\r\n      <button mat-menu-item [matMenuTriggerFor]=\"themeMenu\">\r\n        <mat-icon>palette</mat-icon>\r\n        <span>Themes</span>\r\n        <mat-icon class=\"submenu-arrow\">chevron_right</mat-icon>\r\n      </button>\r\n      <button mat-menu-item>\r\n        <mat-icon>settings</mat-icon>\r\n        <span>Settings</span>\r\n      </button>\r\n      <mat-divider></mat-divider>\r\n      <button mat-menu-item (click)=\"logout()\">\r\n        <mat-icon>logout</mat-icon>\r\n        <span>Logout</span>\r\n      </button>\r\n    </mat-menu>\r\n\r\n    <!-- Theme submenu -->\r\n    <mat-menu #themeMenu=\"matMenu\" class=\"theme-menu\">\r\n      <div class=\"theme-menu-content\" (click)=\"$event.stopPropagation()\">\r\n        <app-theme-selector></app-theme-selector>\r\n      </div>\r\n    </mat-menu>\r\n  </mat-toolbar>\r\n\r\n  <!-- Main Content Area -->\r\n  <div class=\"content-container\">\r\n    <!-- Router Outlet for All Components -->\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from './auth/services/auth.service';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'oracul.client';\r\n\r\n  constructor(\r\n    public authService: AuthService,\r\n    private themeService: ThemeService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Initialize authentication state and theme\r\n    // Force theme initialization\r\n    this.themeService.getCurrentTheme();\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout().subscribe({\r\n      next: () => {\r\n        this.snackBar.open('Logged out successfully', 'Close', {\r\n          duration: 3000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n        this.router.navigate(['/login']);\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open('Logout failed', 'Close', {\r\n          duration: 3000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        // Still navigate to login even if logout fails\r\n        this.router.navigate(['/login']);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}