@echo off
setlocal

set DATA_PROJECT=Oracul.Data
set STARTUP_PROJECT=Oracul.Server

echo 🚀 Entity Framework Migration Helper
echo ====================================
echo Data Project: %DATA_PROJECT%
echo Startup Project: %STARTUP_PROJECT%
echo.

if "%1"=="" goto update
if "%1"=="add" goto add
if "%1"=="update" goto update
if "%1"=="remove" goto remove
if "%1"=="list" goto list
if "%1"=="script" goto script
if "%1"=="drop" goto drop
if "%1"=="help" goto help
goto help

:add
if "%2"=="" (
    echo ❌ Error: Migration name is required for 'add' action
    echo 💡 Usage: migrate.bat add YourMigrationName
    echo 📖 See MIGRATION-README.md for detailed instructions
    goto end
)
echo 📝 Creating new migration: %2
dotnet ef migrations add %2 --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT%
if %ERRORLEVEL% EQU 0 (
    echo ✅ Migration '%2' created successfully!
    echo 💡 Next step: Run 'migrate.bat' to apply the migration
)
goto end

:update
echo 🔄 Updating database with latest migrations...
dotnet ef database update --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT%
if %ERRORLEVEL% EQU 0 (
    echo ✅ Database updated successfully!
) else (
    echo ❌ Database update failed. Check connection string and ensure SQL Server is running.
)
goto end

:remove
echo Removing last migration...
dotnet ef migrations remove --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT%
goto end

:list
echo Listing all migrations...
dotnet ef migrations list --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT%
goto end

:script
echo Generating SQL script for all migrations...
dotnet ef migrations script --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT%
goto end

:drop
echo WARNING: This will drop the entire database!
set /p confirmation=Are you sure you want to drop the database? (yes/no): 
if "%confirmation%"=="yes" (
    dotnet ef database drop --project %DATA_PROJECT% --startup-project %STARTUP_PROJECT% --force
) else (
    echo Database drop cancelled.
)
goto end

:help
echo Available actions:
echo   add ^<name^>  - Create a new migration
echo   update      - Apply migrations to database (default)
echo   remove      - Remove the last migration
echo   list        - List all migrations
echo   script      - Generate SQL script
echo   drop        - Drop the database
echo   help        - Show this help
echo.
echo Examples:
echo   migrate.bat
echo   migrate.bat add AddUserTable
echo   migrate.bat update
echo   migrate.bat list

:end
endlocal
