{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../profile/services/profile.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/i18n/translation.service\";\nimport * as i4 from \"../shared/services/avatar.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/chips\";\nfunction HomeComponent_div_31_mat_card_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r7.name, \" \");\n  }\n}\nfunction HomeComponent_div_31_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_31_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const profile_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.viewProfile(profile_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34)(2, \"img\", 35);\n    i0.ɵɵlistener(\"error\", function HomeComponent_div_31_mat_card_1_Template_img_error_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const profile_r5 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.avatarService.onImageError($event, profile_r5.firstName, profile_r5.lastName));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h3\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"p\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 42)(17, \"div\", 43);\n    i0.ɵɵtemplate(18, HomeComponent_div_31_mat_card_1_span_18_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 45)(20, \"div\", 46)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 46)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"mat-card-actions\")(31, \"button\", 47)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 48)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const profile_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.avatarService.getAvatarUrl(profile_r5.profilePhotoUrl, profile_r5.firstName, profile_r5.lastName), i0.ɵɵsanitizeUrl)(\"alt\", profile_r5.firstName + \" \" + profile_r5.lastName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.firstName, \" \", profile_r5.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(profile_r5.professionalTitle);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(profile_r5.location == null ? null : profile_r5.location.displayLocation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(profile_r5.headline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", profile_r5.skills.slice(0, 3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.profileViews, \" \", ctx_r4.t.home.views, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getTotalEndorsements(profile_r5), \" \", ctx_r4.t.home.endorsements, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.home.viewProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.common.contact, \" \");\n  }\n}\nfunction HomeComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeComponent_div_31_mat_card_1_Template, 39, 14, \"mat-card\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProfiles);\n  }\n}\nfunction HomeComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.t.common.loading, \"...\");\n  }\n}\nfunction HomeComponent_mat_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_43_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const article_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.readArticle(article_r11));\n    });\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 53)(7, \"span\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"p\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-chip\", 58);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 47)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 59)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"bookmark_border\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const article_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r11.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r11.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r11.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"By \", article_r11.author, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, article_r11.publishedAt, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", article_r11.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r11.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r11.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nfunction HomeComponent_mat_card_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_54_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const sign_r14 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.viewHoroscope(sign_r14));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 64)(13, \"div\", 65)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"local_fire_department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"casino\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 65)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"mat-card-actions\")(29, \"button\", 47)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const sign_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(sign_r14.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r14.dateRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(sign_r14.todayPrediction);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(sign_r14.element);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.t.home.luckyNumber, \": \", sign_r14.luckyNumber, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(sign_r14.luckyColor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.home.fullReading, \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(profileService, router, t, avatarService) {\n    this.profileService = profileService;\n    this.router = router;\n    this.t = t;\n    this.avatarService = avatarService;\n    this.featuredProfiles = [];\n    this.featuredArticles = [];\n    this.horoscopeSigns = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n  }\n  loadHomeData() {\n    this.isLoading = true;\n    // Load featured profiles from backend\n    this.profileService.getPublicProfiles(1, 6).subscribe({\n      next: result => {\n        this.featuredProfiles = result.profiles;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading featured profiles:', error);\n        // Fallback to single profile if public profiles fail\n        this.profileService.getProfile('luna-starweaver').subscribe({\n          next: profile => {\n            this.featuredProfiles = [profile];\n            this.isLoading = false;\n          },\n          error: err => {\n            console.error('Error loading profile:', err);\n            this.featuredProfiles = [];\n            this.isLoading = false;\n          }\n        });\n      }\n    });\n    // Load featured articles (still using mock data for now)\n    this.featuredArticles = this.getMockArticles();\n    // Load horoscope data (still using mock data for now)\n    this.horoscopeSigns = this.getMockHoroscope();\n  }\n  getMockArticles() {\n    return [{\n      id: 1,\n      title: 'Разбиране на вашата натална карта: Ръководство за начинаещи',\n      excerpt: 'Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.',\n      author: 'Луна Звездоплетка',\n      publishedAt: new Date('2024-01-15'),\n      readTime: 8,\n      category: 'Основи на Астрологията',\n      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',\n      slug: 'understanding-birth-chart-beginners-guide'\n    }, {\n      id: 2,\n      title: 'Ръководство за оцеляване при Меркурий ретроград 2024',\n      excerpt: 'Навигирайте предизвикателствата на Меркурий ретроград с увереност. Научете практически съвети за превръщане на космическия хаос в възможности за растеж.',\n      author: 'Космически Мъдрец',\n      publishedAt: new Date('2024-02-01'),\n      readTime: 6,\n      category: 'Планетарни Транзити',\n      imageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop',\n      slug: 'mercury-retrograde-survival-guide-2024'\n    }, {\n      id: 3,\n      title: 'Ритуали на пълнолунието за манифестация',\n      excerpt: 'Използвайте мощната енергия на пълнолунието, за да манифестирате най-дълбоките си желания и да освободите това, което вече не ви служи.',\n      author: 'Лунна Мистичка',\n      publishedAt: new Date('2024-02-10'),\n      readTime: 10,\n      category: 'Лунна Магия',\n      imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop',\n      slug: 'full-moon-rituals-manifestation'\n    }, {\n      id: 4,\n      title: 'Кристално лечение: Избор на правилните камъни',\n      excerpt: 'Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.',\n      author: 'Пазител на Кристали',\n      publishedAt: new Date('2024-02-20'),\n      readTime: 7,\n      category: 'Кристално Лечение',\n      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop',\n      slug: 'crystal-healing-choosing-right-stones'\n    }];\n  }\n  getMockHoroscope() {\n    return [{\n      id: 1,\n      name: 'Овен',\n      symbol: '♈',\n      element: 'Огън',\n      dateRange: '21 март - 19 април',\n      todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\n      luckyNumber: 7,\n      luckyColor: 'Червен',\n      compatibility: ['Лъв', 'Стрелец', 'Близнаци']\n    }, {\n      id: 2,\n      name: 'Телец',\n      symbol: '♉',\n      element: 'Земя',\n      dateRange: '20 април - 20 май',\n      todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\n      luckyNumber: 3,\n      luckyColor: 'Зелен',\n      compatibility: ['Дева', 'Козирог', 'Рак']\n    }, {\n      id: 3,\n      name: 'Близнаци',\n      symbol: '♊',\n      element: 'Въздух',\n      dateRange: '21 май - 20 юни',\n      todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\n      luckyNumber: 5,\n      luckyColor: 'Жълт',\n      compatibility: ['Везни', 'Водолей', 'Овен']\n    }, {\n      id: 4,\n      name: 'Рак',\n      symbol: '♋',\n      element: 'Вода',\n      dateRange: '21 юни - 22 юли',\n      todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\n      luckyNumber: 2,\n      luckyColor: 'Сребърен',\n      compatibility: ['Скорпион', 'Риби', 'Телец']\n    }, {\n      id: 5,\n      name: 'Leo',\n      symbol: '♌',\n      element: 'Fire',\n      dateRange: 'Jul 23 - Aug 22',\n      todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\n      luckyNumber: 1,\n      luckyColor: 'Gold',\n      compatibility: ['Aries', 'Sagittarius', 'Gemini']\n    }, {\n      id: 6,\n      name: 'Virgo',\n      symbol: '♍',\n      element: 'Earth',\n      dateRange: 'Aug 23 - Sep 22',\n      todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\n      luckyNumber: 6,\n      luckyColor: 'Navy Blue',\n      compatibility: ['Taurus', 'Capricorn', 'Cancer']\n    }];\n  }\n  navigateToLogin() {\n    console.log('Navigating to login...');\n    this.router.navigate(['/login']);\n  }\n  navigateToRegister() {\n    console.log('Navigating to register...');\n    this.router.navigate(['/register']);\n  }\n  viewProfile(profile) {\n    this.router.navigate(['/profile', profile.slug]);\n  }\n  readArticle(article) {\n    // For now, just navigate to login to read full article\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: `/articles/${article.slug}`\n      }\n    });\n  }\n  viewHoroscope(sign) {\n    // For now, just navigate to login to view detailed horoscope\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: `/horoscope/${sign.name.toLowerCase()}`\n      }\n    });\n  }\n  searchProfiles() {\n    this.router.navigate(['/profiles/search']);\n  }\n  getTotalEndorsements(profile) {\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProfileService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TranslationService), i0.ɵɵdirectiveInject(i4.AvatarService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 70,\n      vars: 19,\n      consts: [[\"id\", \"hero\", 1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"secondary-button\", 3, \"click\"], [1, \"hero-image\"], [\"src\", \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\", \"alt\", \"Mystical cosmic background\", 1, \"hero-img\"], [\"id\", \"astrologers\", 1, \"featured-section\"], [1, \"section-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"view-all-btn\", 3, \"click\"], [\"class\", \"profiles-grid\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"id\", \"articles\", 1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"horoscope\", 1, \"horoscope-section\"], [1, \"horoscope-grid\"], [\"class\", \"horoscope-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"cta-section\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-subtitle\"], [1, \"cta-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"cta-secondary\", 3, \"click\"], [1, \"profiles-grid\"], [\"class\", \"profile-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-card\", 3, \"click\"], [1, \"profile-header\"], [1, \"profile-avatar\", 3, \"src\", \"alt\", \"error\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-title\"], [1, \"profile-location\"], [1, \"profile-content\"], [1, \"profile-headline\"], [1, \"profile-skills\"], [1, \"skills-container\"], [\"class\", \"skill-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-stats\"], [1, \"stat\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [1, \"skill-chip\"], [1, \"loading-container\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"date\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-icon-button\", \"\"], [1, \"horoscope-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"sign-avatar\"], [1, \"sign-symbol\"], [1, \"prediction\"], [1, \"sign-details\"], [1, \"detail\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_12_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵelement(17, \"img\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"section\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"h2\", 13)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 14);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_27_listener() {\n            return ctx.searchProfiles();\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, HomeComponent_div_31_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(32, HomeComponent_div_32_Template, 4, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"section\", 18)(34, \"div\", 11)(35, \"div\", 12)(36, \"h2\", 13)(37, \"mat-icon\");\n          i0.ɵɵtext(38, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 14);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵtemplate(43, HomeComponent_mat_card_43_Template, 30, 13, \"mat-card\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"section\", 21)(45, \"div\", 11)(46, \"div\", 12)(47, \"h2\", 13)(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"brightness_7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\", 14);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 22);\n          i0.ɵɵtemplate(54, HomeComponent_mat_card_54_Template, 33, 9, \"mat-card\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"section\", 24)(56, \"div\", 25)(57, \"h2\", 26);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\", 27);\n          i0.ɵɵtext(60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_62_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_66_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.heroTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.heroSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.startJourney, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.common.login, \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.featuredAstrologers, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.featuredAstrologersSubtitle);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.browseAllAstrologers, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.cosmicWisdomArticles, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.cosmicWisdomSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredArticles);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.dailyHoroscope, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.dailyHoroscopeSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.horoscopeSigns);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.ctaTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.ctaSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.createFreeAccount, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.alreadyMember, \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardAvatar, i7.MatCardContent, i7.MatCardHeader, i7.MatCardImage, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatProgressSpinner, i10.MatChip, i5.DatePipe],\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  padding: 80px 20px; \\r\\n  min-height: 500px;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-content[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  width: 100%;\\r\\n  display: grid;\\r\\n  grid-template-columns: 1fr 1fr;\\r\\n  gap: 60px;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.hero-text[_ngcontent-%COMP%] {\\r\\n  max-width: 500px;\\r\\n}\\r\\n\\r\\n.hero-title[_ngcontent-%COMP%] {\\r\\n  font-size: 3.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n\\r\\n.hero-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.hero-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-button[_ngcontent-%COMP%], .secondary-button[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.hero-image[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-img[_ngcontent-%COMP%] {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  border-radius: 16px;\\r\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\r\\n}\\r\\n\\r\\n\\r\\n.featured-section[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%], .horoscope-section[_ngcontent-%COMP%] {\\r\\n  padding: 80px 20px;\\r\\n  background: var(--theme-background);\\r\\n}\\r\\n\\r\\n.section-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.section-header[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  margin-bottom: 60px;\\r\\n}\\r\\n\\r\\n.section-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.section-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin-bottom: 30px;\\r\\n}\\r\\n\\r\\n.view-all-btn[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n.profiles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\r\\n  gap: 30px;\\r\\n  margin-bottom: 40px;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-8px);\\r\\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.profile-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.profile-avatar[_ngcontent-%COMP%] {\\r\\n  width: 60px;\\r\\n  height: 60px;\\r\\n  border-radius: 50%;\\r\\n  object-fit: cover;\\r\\n  border: 3px solid var(--theme-accent);\\r\\n}\\r\\n\\r\\n.profile-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.profile-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.3rem;\\r\\n  font-weight: 600;\\r\\n  margin: 0 0 4px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.profile-title[_ngcontent-%COMP%] {\\r\\n  font-size: 1rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin: 0 0 8px 0;\\r\\n}\\r\\n\\r\\n.profile-location[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.profile-content[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.profile-headline[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-skills[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.skills-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.skill-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-accent);\\r\\n  color: var(--theme-text-primary);\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 0.85rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.profile-stats[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n}\\r\\n\\r\\n\\r\\n.articles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\r\\n  gap: 30px;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n}\\r\\n\\r\\n.article-image[_ngcontent-%COMP%] {\\r\\n  height: 200px;\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.article-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n  font-size: 0.85rem;\\r\\n}\\r\\n\\r\\n.author[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.date[_ngcontent-%COMP%], .read-time[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.6;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.category-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-primary);\\r\\n  color: white;\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n.horoscope-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  border: 2px solid var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n  border-color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n.sign-avatar[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  width: 50px;\\r\\n  height: 50px;\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.sign-symbol[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: bold;\\r\\n}\\r\\n\\r\\n.prediction[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.sign-details[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.85rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n.cta-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-secondary);\\r\\n  color: white;\\r\\n  padding: 80px 20px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.cta-content[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.cta-title[_ngcontent-%COMP%] {\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.cta-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.cta-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-primary[_ngcontent-%COMP%], .cta-secondary[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 20px;\\r\\n  padding: 60px 20px;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  \\r\\n  .nav-links[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .nav-actions[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .mobile-menu-toggle[_ngcontent-%COMP%] {\\r\\n    display: flex;\\r\\n  }\\r\\n\\r\\n  .nav-container[_ngcontent-%COMP%] {\\r\\n    padding: 0 16px;\\r\\n  }\\r\\n\\r\\n  .hero-section[_ngcontent-%COMP%] {\\r\\n    padding: 120px 20px 80px; \\r\\n  }\\r\\n\\r\\n  .hero-content[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n    gap: 40px;\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  .hero-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2.5rem;\\r\\n  }\\r\\n\\r\\n  .profiles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .articles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .horoscope-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\r\\n  }\\r\\n\\r\\n  .section-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .cta-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .hero-actions[_ngcontent-%COMP%], .cta-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;IA+DcA,gCAA0E;IACxEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,8CACF;;;;;;IAvBRA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAoB;IAAA,EAAC;IACpGA,+BAA4B;IAIrBA;MAAA;MAAA;MAAA;MAAA,OAASA,oGAAuE;IAAA,EAAC;IAHtFA,iBAGuF;IACvFA,+BAA0B;IACCA,YAA8C;IAAAA,iBAAK;IAC5EA,6BAAyB;IAAAA,YAA+B;IAAAA,iBAAI;IAC5DA,+BAA8B;IAClBA,4BAAW;IAAAA,iBAAW;IAChCA,6BAAM;IAAAA,aAAuC;IAAAA,iBAAO;IAK1DA,gCAA6B;IACCA,aAAsB;IAAAA,iBAAI;IAEtDA,gCAA4B;IAExBA,qFAEO;IACTA,iBAAM;IAGRA,gCAA2B;IAEbA,2BAAU;IAAAA,iBAAW;IAC/BA,6BAAM;IAAAA,aAA6C;IAAAA,iBAAO;IAE5DA,gCAAkB;IACNA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,aAA6D;IAAAA,iBAAO;IAKhFA,yCAAkB;IAEJA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;IACTA,mCAAkC;IACtBA,wBAAO;IAAAA,iBAAW;IAC5BA,aACF;IAAAA,iBAAS;;;;;IA7CJA,eAAgG;IAAhGA,gJAAgG;IAK1EA,eAA8C;IAA9CA,6EAA8C;IAC9CA,eAA+B;IAA/BA,kDAA+B;IAGhDA,eAAuC;IAAvCA,8FAAuC;IAMrBA,eAAsB;IAAtBA,yCAAsB;IAItBA,eAA6B;IAA7BA,uDAA6B;IAS/CA,eAA6C;IAA7CA,gFAA6C;IAI7CA,eAA6D;IAA7DA,uGAA6D;IAQrEA,eACF;IADEA,0DACF;IAGEA,eACF;IADEA,wDACF;;;;;IAhDNA,+BAA8C;IAC5CA,kFAiDW;IACbA,iBAAM;;;;IAlD+CA,eAAmB;IAAnBA,iDAAmB;;;;;IAqDxEA,+BAAiD;IAC/CA,8BAA2B;IAC3BA,yBAAG;IAAAA,YAAyB;IAAAA,iBAAI;;;;IAA7BA,eAAyB;IAAzBA,yDAAyB;;;;;;IAiB5BA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACpGA,0BAAyF;IAEzFA,uCAAiB;IACCA,YAAmB;IAAAA,iBAAiB;IACpDA,yCAAmB;IAEMA,YAAuB;IAAAA,iBAAO;IACnDA,gCAAmB;IAAAA,aAA2C;;IAAAA,iBAAO;IACrEA,iCAAwB;IAAAA,aAA2C;IAAAA,iBAAO;IAKhFA,yCAAkB;IACWA,aAAqB;IAAAA,iBAAI;IACpDA,qCAAgC;IAAAA,aAAsB;IAAAA,iBAAW;IAGnEA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;IACTA,mCAAwB;IACZA,sBAAK;IAAAA,iBAAW;IAE5BA,mCAAwB;IACZA,gCAAe;IAAAA,iBAAW;;;;;IA3BpBA,eAAwB;IAAxBA,4DAAwB;IAG1BA,eAAmB;IAAnBA,uCAAmB;IAGVA,eAAuB;IAAvBA,oDAAuB;IACzBA,eAA2C;IAA3CA,iFAA2C;IACtCA,eAA2C;IAA3CA,+EAA2C;IAM5CA,eAAqB;IAArBA,yCAAqB;IAChBA,eAAsB;IAAtBA,0CAAsB;IAMpDA,eACF;IADEA,0DACF;;;;;;IAyBJA,oCAAmG;IAA9BA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChGA,uCAAiB;IAEaA,YAAiB;IAAAA,iBAAO;IAEpDA,sCAAgB;IAAAA,YAAe;IAAAA,iBAAiB;IAChDA,yCAAmB;IAAAA,YAAoB;IAAAA,iBAAoB;IAG7DA,wCAAkB;IACMA,aAA0B;IAAAA,iBAAI;IAEpDA,gCAA0B;IAEZA,sCAAqB;IAAAA,iBAAW;IAC1CA,6BAAM;IAAAA,aAAkB;IAAAA,iBAAO;IAEjCA,gCAAoB;IACRA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAAgD;IAAAA,iBAAO;IAE/DA,gCAAoB;IACRA,wBAAO;IAAAA,iBAAW;IAC5BA,6BAAM;IAAAA,aAAqB;IAAAA,iBAAO;IAKxCA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;;;;;IA7BmBA,eAAiB;IAAjBA,qCAAiB;IAE7BA,eAAe;IAAfA,mCAAe;IACZA,eAAoB;IAApBA,wCAAoB;IAIjBA,eAA0B;IAA1BA,8CAA0B;IAKtCA,eAAkB;IAAlBA,sCAAkB;IAIlBA,eAAgD;IAAhDA,oFAAgD;IAIhDA,eAAqB;IAArBA,yCAAqB;IAQ7BA,eACF;IADEA,0DACF;;;AC7JV,OAAM,MAAOC,aAAa;EAMxBC,YACUC,cAA8B,EAC9BC,MAAc,EACfC,CAAqB,EACrBC,aAA4B;IAH3B,mBAAc,GAAdH,cAAc;IACd,WAAM,GAANC,MAAM;IACP,MAAC,GAADC,CAAC;IACD,kBAAa,GAAbC,aAAa;IATtB,qBAAgB,GAAkB,EAAE;IACpC,qBAAgB,GAAc,EAAE;IAChC,mBAAc,GAAoB,EAAE;IACpC,cAAS,GAAG,IAAI;EAOb;EAEHC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAY;IAClB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACN,cAAc,CAACO,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACC,gBAAgB,GAAGD,MAAM,CAACE,QAAQ;QACvC,IAAI,CAACN,SAAS,GAAG,KAAK;MACxB,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;QACA,IAAI,CAACb,cAAc,CAACe,UAAU,CAAC,iBAAiB,CAAC,CAACP,SAAS,CAAC;UAC1DC,IAAI,EAAGO,OAAO,IAAI;YAChB,IAAI,CAACL,gBAAgB,GAAG,CAACK,OAAO,CAAC;YACjC,IAAI,CAACV,SAAS,GAAG,KAAK;UACxB,CAAC;UACDO,KAAK,EAAGI,GAAG,IAAI;YACbH,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;YAC5C,IAAI,CAACN,gBAAgB,GAAG,EAAE;YAC1B,IAAI,CAACL,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;KACD,CAAC;IAEF;IACA,IAAI,CAACY,gBAAgB,GAAG,IAAI,CAACC,eAAe,EAAE;IAE9C;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,gBAAgB,EAAE;EAC/C;EAEQF,eAAe;IACrB,OAAO,CACL;MACEG,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,6DAA6D;MACpEC,OAAO,EAAE,8IAA8I;MACvJC,MAAM,EAAE,mBAAmB;MAC3BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE,mFAAmF;MAC7FC,IAAI,EAAE;KACP,EACD;MACET,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,0JAA0J;MACnKC,MAAM,EAAE,mBAAmB;MAC3BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE,mFAAmF;MAC7FC,IAAI,EAAE;KACP,EACD;MACET,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,yCAAyC;MAChDC,OAAO,EAAE,yIAAyI;MAClJC,MAAM,EAAE,gBAAgB;MACxBC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE,mFAAmF;MAC7FC,IAAI,EAAE;KACP,EACD;MACET,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,+CAA+C;MACtDC,OAAO,EAAE,8HAA8H;MACvIC,MAAM,EAAE,qBAAqB;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,mFAAmF;MAC7FC,IAAI,EAAE;KACP,CACF;EACH;EAEQV,gBAAgB;IACtB,OAAO,CACL;MACEC,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,oBAAoB;MAC/BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU;KAC7C,EACD;MACEjB,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,mBAAmB;MAC9BC,eAAe,EAAE,qHAAqH;MACtIC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,OAAO;MACnBC,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK;KACzC,EACD;MACEjB,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,+FAA+F;MAChHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM;KAC3C,EACD;MACEjB,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,4HAA4H;MAC7IC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,UAAU;MACtBC,aAAa,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO;KAC5C,EACD;MACEjB,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,yGAAyG;MAC1HC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ;KACjD,EACD;MACEjB,EAAE,EAAE,CAAC;MACLU,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,WAAW;MACvBC,aAAa,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ;KAChD,CACF;EACH;EAEAC,eAAe;IACb1B,OAAO,CAAC2B,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,kBAAkB;IAChB7B,OAAO,CAAC2B,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAE,WAAW,CAAC5B,OAAoB;IAC9B,IAAI,CAACf,MAAM,CAACyC,QAAQ,CAAC,CAAC,UAAU,EAAE1B,OAAO,CAACe,IAAI,CAAC,CAAC;EAClD;EAEAc,WAAW,CAACC,OAAgB;IAC1B;IACA,IAAI,CAAC7C,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BK,WAAW,EAAE;QAAEC,SAAS,EAAE,aAAaF,OAAO,CAACf,IAAI;MAAE;KACtD,CAAC;EACJ;EAEAkB,aAAa,CAACC,IAAmB;IAC/B;IACA,IAAI,CAACjD,MAAM,CAACyC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BK,WAAW,EAAE;QAAEC,SAAS,EAAE,cAAcE,IAAI,CAAClB,IAAI,CAACmB,WAAW,EAAE;MAAE;KAClE,CAAC;EACJ;EAEAC,cAAc;IACZ,IAAI,CAACnD,MAAM,CAACyC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAW,oBAAoB,CAACrC,OAAoB;IACvC,OAAOA,OAAO,CAACsC,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,YAAY,EAAE,CAAC,CAAC;EAC3E;;;uBA5MW5D,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAA6D;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDpC1BlE,kCAAwC;UAGXA,YAAsB;UAAAA,iBAAK;UAClDA,4BAAyB;UACvBA,YACF;UAAAA,iBAAI;UACJA,8BAA0B;UACqCA;YAAA,OAASmE,wBAAoB;UAAA,EAAC;UACzFnE,gCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,kCAAgG;UAA5BA;YAAA,OAASmE,qBAAiB;UAAA,EAAC;UAC7FnE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;UAGbA,+BAAwB;UACtBA,0BACuD;UACzDA,iBAAM;UAKVA,oCAAmD;UAIjCA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAwC;UAAAA,iBAAI;UACxEA,mCAA2F;UAAhDA;YAAA,OAASmE,oBAAgB;UAAA,EAAC;UACnEnE,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UAGXA,iEAmDM;UAGNA,iEAGM;UACRA,iBAAM;UAIRA,oCAAgD;UAI9BA,wBAAO;UAAAA,iBAAW;UAC5BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAiC;UAAAA,iBAAI;UAGnEA,gCAA2B;UACzBA,6EA+BW;UACbA,iBAAM;UAKVA,oCAAkD;UAIhCA,6BAAY;UAAAA,iBAAW;UACjCA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAmC;UAAAA,iBAAI;UAGrEA,gCAA4B;UAC1BA,4EAkCW;UACbA,iBAAM;UAKVA,oCAA6B;UAEHA,aAAqB;UAAAA,iBAAK;UAChDA,8BAAwB;UACtBA,aACF;UAAAA,iBAAI;UACJA,gCAAyB;UACuCA;YAAA,OAASmE,wBAAoB;UAAA,EAAC;UAC1FnE,iCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,mCAA6F;UAA5BA;YAAA,OAASmE,qBAAiB;UAAA,EAAC;UAC1FnE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;;;UApNcA,eAAsB;UAAtBA,0CAAsB;UAE3CA,eACF;UADEA,wDACF;UAIIA,eACF;UADEA,wDACF;UAGEA,eACF;UADEA,mDACF;UAgBAA,eACF;UADEA,+DACF;UAC4BA,eAAwC;UAAxCA,4DAAwC;UAGlEA,eACF;UADEA,gEACF;UAG0BA,eAAgB;UAAhBA,qCAAgB;UAsDZA,eAAe;UAAfA,oCAAe;UAa3CA,eACF;UADEA,gEACF;UAC4BA,eAAiC;UAAjCA,qDAAiC;UAIVA,eAAmB;UAAnBA,8CAAmB;UA0CpEA,eACF;UADEA,0DACF;UAC4BA,eAAmC;UAAnCA,uDAAmC;UAIbA,eAAiB;UAAjBA,4CAAiB;UA0C/CA,eAAqB;UAArBA,yCAAqB;UAEzCA,eACF;UADEA,uDACF;UAIIA,eACF;UADEA,6DACF;UAGEA,eACF;UADEA,yDACF", "names": ["i0", "HomeComponent", "constructor", "profileService", "router", "t", "avatarService", "ngOnInit", "loadHomeData", "isLoading", "getPublicProfiles", "subscribe", "next", "result", "featuredPro<PERSON>les", "profiles", "error", "console", "getProfile", "profile", "err", "featuredArticles", "getMockArticles", "horoscopeSigns", "getMockHoroscope", "id", "title", "excerpt", "author", "publishedAt", "Date", "readTime", "category", "imageUrl", "slug", "name", "symbol", "element", "date<PERSON><PERSON><PERSON>", "todayPrediction", "<PERSON><PERSON><PERSON><PERSON>", "luckyColor", "compatibility", "navigateToLogin", "log", "navigate", "navigateToRegister", "viewProfile", "readArticle", "article", "queryParams", "returnUrl", "viewHoroscope", "sign", "toLowerCase", "searchProfiles", "getTotalEndorsements", "skills", "reduce", "sum", "skill", "endorsements", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.ts"], "sourcesContent": ["<!-- Hero Section -->\r\n<section id=\"hero\" class=\"hero-section\">\r\n  <div class=\"hero-content\">\r\n    <div class=\"hero-text\">\r\n      <h1 class=\"hero-title\">{{ t.home.heroTitle }}</h1>\r\n      <p class=\"hero-subtitle\">\r\n        {{ t.home.heroSubtitle }}\r\n      </p>\r\n      <div class=\"hero-actions\">\r\n        <button mat-raised-button color=\"primary\" class=\"cta-button\" (click)=\"navigateToRegister()\" type=\"button\">\r\n          <mat-icon>star</mat-icon>\r\n          {{ t.home.startJourney }}\r\n        </button>\r\n        <button mat-stroked-button color=\"primary\" class=\"secondary-button\" (click)=\"navigateToLogin()\">\r\n          <mat-icon>login</mat-icon>\r\n          {{ t.common.login }}\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"hero-image\">\r\n      <img src=\"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\"\r\n           alt=\"Mystical cosmic background\" class=\"hero-img\">\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Astrologers Section -->\r\n<section id=\"astrologers\" class=\"featured-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>people</mat-icon>\r\n        {{ t.home.featuredAstrologers }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.featuredAstrologersSubtitle }}</p>\r\n      <button mat-stroked-button color=\"primary\" (click)=\"searchProfiles()\" class=\"view-all-btn\">\r\n        <mat-icon>search</mat-icon>\r\n        {{ t.home.browseAllAstrologers }}\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"profiles-grid\" *ngIf=\"!isLoading\">\r\n      <mat-card class=\"profile-card\" *ngFor=\"let profile of featuredProfiles\" (click)=\"viewProfile(profile)\">\r\n        <div class=\"profile-header\">\r\n          <img [src]=\"avatarService.getAvatarUrl(profile.profilePhotoUrl, profile.firstName, profile.lastName)\"\r\n               [alt]=\"profile.firstName + ' ' + profile.lastName\"\r\n               class=\"profile-avatar\"\r\n               (error)=\"avatarService.onImageError($event, profile.firstName, profile.lastName)\">\r\n          <div class=\"profile-info\">\r\n            <h3 class=\"profile-name\">{{ profile.firstName }} {{ profile.lastName }}</h3>\r\n            <p class=\"profile-title\">{{ profile.professionalTitle }}</p>\r\n            <div class=\"profile-location\">\r\n              <mat-icon>location_on</mat-icon>\r\n              <span>{{ profile.location?.displayLocation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"profile-content\">\r\n          <p class=\"profile-headline\">{{ profile.headline }}</p>\r\n\r\n          <div class=\"profile-skills\">\r\n            <div class=\"skills-container\">\r\n              <span *ngFor=\"let skill of profile.skills.slice(0, 3)\" class=\"skill-chip\">\r\n                {{ skill.name }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"profile-stats\">\r\n            <div class=\"stat\">\r\n              <mat-icon>visibility</mat-icon>\r\n              <span>{{ profile.profileViews }} {{ t.home.views }}</span>\r\n            </div>\r\n            <div class=\"stat\">\r\n              <mat-icon>thumb_up</mat-icon>\r\n              <span>{{ getTotalEndorsements(profile) }} {{ t.home.endorsements }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>visibility</mat-icon>\r\n            {{ t.home.viewProfile }}\r\n          </button>\r\n          <button mat-button color=\"accent\">\r\n            <mat-icon>message</mat-icon>\r\n            {{ t.common.contact }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\r\n      <mat-spinner></mat-spinner>\r\n      <p>{{ t.common.loading }}...</p>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Articles Section -->\r\n<section id=\"articles\" class=\"articles-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>article</mat-icon>\r\n        {{ t.home.cosmicWisdomArticles }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.cosmicWisdomSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"articles-grid\">\r\n      <mat-card class=\"article-card\" *ngFor=\"let article of featuredArticles\" (click)=\"readArticle(article)\">\r\n        <img mat-card-image [src]=\"article.imageUrl\" [alt]=\"article.title\" class=\"article-image\">\r\n\r\n        <mat-card-header>\r\n          <mat-card-title>{{ article.title }}</mat-card-title>\r\n          <mat-card-subtitle>\r\n            <div class=\"article-meta\">\r\n              <span class=\"author\">By {{ article.author }}</span>\r\n              <span class=\"date\">{{ article.publishedAt | date:'MMM d, y' }}</span>\r\n              <span class=\"read-time\">{{ article.readTime }} {{ t.home.minRead }}</span>\r\n            </div>\r\n          </mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n          <mat-chip class=\"category-chip\">{{ article.category }}</mat-chip>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.readArticle }}\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>share</mat-icon>\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>bookmark_border</mat-icon>\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Daily Horoscope Section -->\r\n<section id=\"horoscope\" class=\"horoscope-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>brightness_7</mat-icon>\r\n        {{ t.home.dailyHoroscope }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.dailyHoroscopeSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"horoscope-grid\">\r\n      <mat-card class=\"horoscope-card\" *ngFor=\"let sign of horoscopeSigns\" (click)=\"viewHoroscope(sign)\">\r\n        <mat-card-header>\r\n          <div mat-card-avatar class=\"sign-avatar\">\r\n            <span class=\"sign-symbol\">{{ sign.symbol }}</span>\r\n          </div>\r\n          <mat-card-title>{{ sign.name }}</mat-card-title>\r\n          <mat-card-subtitle>{{ sign.dateRange }}</mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"prediction\">{{ sign.todayPrediction }}</p>\r\n\r\n          <div class=\"sign-details\">\r\n            <div class=\"detail\">\r\n              <mat-icon>local_fire_department</mat-icon>\r\n              <span>{{ sign.element }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>casino</mat-icon>\r\n              <span>{{ t.home.luckyNumber }}: {{ sign.luckyNumber }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>palette</mat-icon>\r\n              <span>{{ sign.luckyColor }}</span>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.fullReading }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Call to Action Section -->\r\n<section class=\"cta-section\">\r\n  <div class=\"cta-content\">\r\n    <h2 class=\"cta-title\">{{ t.home.ctaTitle }}</h2>\r\n    <p class=\"cta-subtitle\">\r\n      {{ t.home.ctaSubtitle }}\r\n    </p>\r\n    <div class=\"cta-actions\">\r\n      <button mat-raised-button color=\"primary\" class=\"cta-primary\" (click)=\"navigateToRegister()\" type=\"button\">\r\n        <mat-icon>star</mat-icon>\r\n        {{ t.home.createFreeAccount }}\r\n      </button>\r\n      <button mat-stroked-button color=\"primary\" class=\"cta-secondary\" (click)=\"navigateToLogin()\">\r\n        <mat-icon>login</mat-icon>\r\n        {{ t.home.alreadyMember }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</section>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { UserProfile } from '../profile/models/profile.models';\r\nimport { ProfileService } from '../profile/services/profile.service';\r\nimport { TranslationService } from '../core/i18n/translation.service';\r\nimport { AvatarService } from '../shared/services/avatar.service';\r\n\r\nexport interface Article {\r\n  id: number;\r\n  title: string;\r\n  excerpt: string;\r\n  author: string;\r\n  publishedAt: Date;\r\n  readTime: number;\r\n  category: string;\r\n  imageUrl: string;\r\n  slug: string;\r\n}\r\n\r\nexport interface HoroscopeSign {\r\n  id: number;\r\n  name: string;\r\n  symbol: string;\r\n  element: string;\r\n  dateRange: string;\r\n  todayPrediction: string;\r\n  luckyNumber: number;\r\n  luckyColor: string;\r\n  compatibility: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.css']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  featuredProfiles: UserProfile[] = [];\r\n  featuredArticles: Article[] = [];\r\n  horoscopeSigns: HoroscopeSign[] = [];\r\n  isLoading = true;\r\n\r\n  constructor(\r\n    private profileService: ProfileService,\r\n    private router: Router,\r\n    public t: TranslationService,\r\n    public avatarService: AvatarService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadHomeData();\r\n  }\r\n\r\n  private loadHomeData(): void {\r\n    this.isLoading = true;\r\n\r\n    // Load featured profiles from backend\r\n    this.profileService.getPublicProfiles(1, 6).subscribe({\r\n      next: (result) => {\r\n        this.featuredProfiles = result.profiles;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading featured profiles:', error);\r\n        // Fallback to single profile if public profiles fail\r\n        this.profileService.getProfile('luna-starweaver').subscribe({\r\n          next: (profile) => {\r\n            this.featuredProfiles = [profile];\r\n            this.isLoading = false;\r\n          },\r\n          error: (err) => {\r\n            console.error('Error loading profile:', err);\r\n            this.featuredProfiles = [];\r\n            this.isLoading = false;\r\n          }\r\n        });\r\n      }\r\n    });\r\n\r\n    // Load featured articles (still using mock data for now)\r\n    this.featuredArticles = this.getMockArticles();\r\n\r\n    // Load horoscope data (still using mock data for now)\r\n    this.horoscopeSigns = this.getMockHoroscope();\r\n  }\r\n\r\n  private getMockArticles(): Article[] {\r\n    return [\r\n      {\r\n        id: 1,\r\n        title: 'Разбиране на вашата натална карта: Ръководство за начинаещи',\r\n        excerpt: 'Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.',\r\n        author: 'Луна Звездоплетка',\r\n        publishedAt: new Date('2024-01-15'),\r\n        readTime: 8,\r\n        category: 'Основи на Астрологията',\r\n        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',\r\n        slug: 'understanding-birth-chart-beginners-guide'\r\n      },\r\n      {\r\n        id: 2,\r\n        title: 'Ръководство за оцеляване при Меркурий ретроград 2024',\r\n        excerpt: 'Навигирайте предизвикателствата на Меркурий ретроград с увереност. Научете практически съвети за превръщане на космическия хаос в възможности за растеж.',\r\n        author: 'Космически Мъдрец',\r\n        publishedAt: new Date('2024-02-01'),\r\n        readTime: 6,\r\n        category: 'Планетарни Транзити',\r\n        imageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop',\r\n        slug: 'mercury-retrograde-survival-guide-2024'\r\n      },\r\n      {\r\n        id: 3,\r\n        title: 'Ритуали на пълнолунието за манифестация',\r\n        excerpt: 'Използвайте мощната енергия на пълнолунието, за да манифестирате най-дълбоките си желания и да освободите това, което вече не ви служи.',\r\n        author: 'Лунна Мистичка',\r\n        publishedAt: new Date('2024-02-10'),\r\n        readTime: 10,\r\n        category: 'Лунна Магия',\r\n        imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop',\r\n        slug: 'full-moon-rituals-manifestation'\r\n      },\r\n      {\r\n        id: 4,\r\n        title: 'Кристално лечение: Избор на правилните камъни',\r\n        excerpt: 'Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.',\r\n        author: 'Пазител на Кристали',\r\n        publishedAt: new Date('2024-02-20'),\r\n        readTime: 7,\r\n        category: 'Кристално Лечение',\r\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop',\r\n        slug: 'crystal-healing-choosing-right-stones'\r\n      }\r\n    ];\r\n  }\r\n\r\n  private getMockHoroscope(): HoroscopeSign[] {\r\n    return [\r\n      {\r\n        id: 1,\r\n        name: 'Овен',\r\n        symbol: '♈',\r\n        element: 'Огън',\r\n        dateRange: '21 март - 19 април',\r\n        todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\r\n        luckyNumber: 7,\r\n        luckyColor: 'Червен',\r\n        compatibility: ['Лъв', 'Стрелец', 'Близнаци']\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Телец',\r\n        symbol: '♉',\r\n        element: 'Земя',\r\n        dateRange: '20 април - 20 май',\r\n        todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\r\n        luckyNumber: 3,\r\n        luckyColor: 'Зелен',\r\n        compatibility: ['Дева', 'Козирог', 'Рак']\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Близнаци',\r\n        symbol: '♊',\r\n        element: 'Въздух',\r\n        dateRange: '21 май - 20 юни',\r\n        todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\r\n        luckyNumber: 5,\r\n        luckyColor: 'Жълт',\r\n        compatibility: ['Везни', 'Водолей', 'Овен']\r\n      },\r\n      {\r\n        id: 4,\r\n        name: 'Рак',\r\n        symbol: '♋',\r\n        element: 'Вода',\r\n        dateRange: '21 юни - 22 юли',\r\n        todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\r\n        luckyNumber: 2,\r\n        luckyColor: 'Сребърен',\r\n        compatibility: ['Скорпион', 'Риби', 'Телец']\r\n      },\r\n      {\r\n        id: 5,\r\n        name: 'Leo',\r\n        symbol: '♌',\r\n        element: 'Fire',\r\n        dateRange: 'Jul 23 - Aug 22',\r\n        todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\r\n        luckyNumber: 1,\r\n        luckyColor: 'Gold',\r\n        compatibility: ['Aries', 'Sagittarius', 'Gemini']\r\n      },\r\n      {\r\n        id: 6,\r\n        name: 'Virgo',\r\n        symbol: '♍',\r\n        element: 'Earth',\r\n        dateRange: 'Aug 23 - Sep 22',\r\n        todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\r\n        luckyNumber: 6,\r\n        luckyColor: 'Navy Blue',\r\n        compatibility: ['Taurus', 'Capricorn', 'Cancer']\r\n      }\r\n    ];\r\n  }\r\n\r\n  navigateToLogin(): void {\r\n    console.log('Navigating to login...');\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    console.log('Navigating to register...');\r\n    this.router.navigate(['/register']);\r\n  }\r\n\r\n  viewProfile(profile: UserProfile): void {\r\n    this.router.navigate(['/profile', profile.slug]);\r\n  }\r\n\r\n  readArticle(article: Article): void {\r\n    // For now, just navigate to login to read full article\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: `/articles/${article.slug}` }\r\n    });\r\n  }\r\n\r\n  viewHoroscope(sign: HoroscopeSign): void {\r\n    // For now, just navigate to login to view detailed horoscope\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: `/horoscope/${sign.name.toLowerCase()}` }\r\n    });\r\n  }\r\n\r\n  searchProfiles(): void {\r\n    this.router.navigate(['/profiles/search']);\r\n  }\r\n\r\n  getTotalEndorsements(profile: UserProfile): number {\r\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\r\n  }\r\n\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}