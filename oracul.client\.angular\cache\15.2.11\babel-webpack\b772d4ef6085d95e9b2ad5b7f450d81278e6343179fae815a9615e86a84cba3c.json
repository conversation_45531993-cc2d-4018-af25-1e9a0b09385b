{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./profile/services/profile.service\";\nimport * as i2 from \"@angular/common\";\nfunction TestApiConnectionComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h3\", 7);\n    i0.ɵɵtext(2, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nexport class TestApiConnectionComponent {\n  constructor(profileService) {\n    this.profileService = profileService;\n    this.results = null;\n    this.error = '';\n  }\n  ngOnInit() {\n    console.log('TestApiConnectionComponent initialized');\n  }\n  testGetProfile() {\n    this.error = '';\n    this.results = 'Loading...';\n    this.profileService.getProfile('luna-starweaver').subscribe({\n      next: profile => {\n        this.results = {\n          success: true,\n          message: 'Profile loaded successfully!',\n          data: {\n            id: profile.id,\n            username: profile.username,\n            firstName: profile.firstName,\n            lastName: profile.lastName,\n            professionalTitle: profile.professionalTitle,\n            headline: profile.headline,\n            profileViews: profile.profileViews,\n            skillsCount: profile.skills?.length || 0,\n            blogPostsCount: profile.blogPosts?.length || 0,\n            certificationsCount: profile.certifications?.length || 0\n          }\n        };\n        console.log('Profile loaded:', profile);\n      },\n      error: err => {\n        this.error = err.message || 'Unknown error occurred';\n        this.results = null;\n        console.error('Error loading profile:', err);\n      }\n    });\n  }\n  testGetPublicProfiles() {\n    this.error = '';\n    this.results = 'Loading...';\n    this.profileService.getPublicProfiles(1, 5).subscribe({\n      next: result => {\n        this.results = {\n          success: true,\n          message: 'Public profiles loaded successfully!',\n          data: {\n            totalCount: result.totalCount,\n            currentPage: result.currentPage,\n            profilesCount: result.profiles.length,\n            profiles: result.profiles.map(p => ({\n              id: p.id,\n              username: p.username,\n              firstName: p.firstName,\n              lastName: p.lastName,\n              professionalTitle: p.professionalTitle\n            }))\n          }\n        };\n        console.log('Public profiles loaded:', result);\n      },\n      error: err => {\n        this.error = err.message || 'Unknown error occurred';\n        this.results = null;\n        console.error('Error loading public profiles:', err);\n      }\n    });\n  }\n  testSearchProfiles() {\n    this.error = '';\n    this.results = 'Loading...';\n    this.profileService.searchProfilesByTerm('astrology', 1, 5).subscribe({\n      next: result => {\n        this.results = {\n          success: true,\n          message: 'Search completed successfully!',\n          data: {\n            searchTerm: 'astrology',\n            totalCount: result.totalCount,\n            currentPage: result.currentPage,\n            profilesCount: result.profiles.length,\n            profiles: result.profiles.map(p => ({\n              id: p.id,\n              username: p.username,\n              firstName: p.firstName,\n              lastName: p.lastName,\n              professionalTitle: p.professionalTitle\n            }))\n          }\n        };\n        console.log('Search results:', result);\n      },\n      error: err => {\n        this.error = err.message || 'Unknown error occurred';\n        this.results = null;\n        console.error('Error searching profiles:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function TestApiConnectionComponent_Factory(t) {\n      return new (t || TestApiConnectionComponent)(i0.ɵɵdirectiveInject(i1.ProfileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TestApiConnectionComponent,\n      selectors: [[\"app-test-api-connection\"]],\n      decls: 18,\n      vars: 4,\n      consts: [[2, \"padding\", \"20px\", \"margin\", \"20px\", \"border\", \"2px solid #ccc\", \"border-radius\", \"8px\"], [2, \"margin\", \"10px 0\"], [2, \"margin-right\", \"10px\", \"padding\", \"8px 16px\", 3, \"click\"], [2, \"padding\", \"8px 16px\", 3, \"click\"], [2, \"margin-top\", \"20px\"], [2, \"background\", \"#f5f5f5\", \"padding\", \"10px\", \"border-radius\", \"4px\", \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"style\", \"margin-top: 20px;\", 4, \"ngIf\"], [2, \"color\", \"red\"], [2, \"background\", \"#ffe6e6\", \"padding\", \"10px\", \"border-radius\", \"4px\", \"color\", \"red\"]],\n      template: function TestApiConnectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"API Connection Test\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function TestApiConnectionComponent_Template_button_click_4_listener() {\n            return ctx.testGetProfile();\n          });\n          i0.ɵɵtext(5, \" Test Get Profile (luna-starweaver) \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function TestApiConnectionComponent_Template_button_click_6_listener() {\n            return ctx.testGetPublicProfiles();\n          });\n          i0.ɵɵtext(7, \" Test Get Public Profiles \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function TestApiConnectionComponent_Template_button_click_8_listener() {\n            return ctx.testSearchProfiles();\n          });\n          i0.ɵɵtext(9, \" Test Search Profiles \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 4)(11, \"h3\");\n          i0.ɵɵtext(12, \"Results:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"pre\");\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"json\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, TestApiConnectionComponent_div_17_Template, 5, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 2, ctx.results));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [i2.NgIf, i2.JsonPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;IA6BMA,8BAA6C;IACnBA,sBAAM;IAAAA,iBAAK;IACnCA,8BAAiF;IAC/EA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;AAKR,OAAM,MAAOC,0BAA0B;EAIrCC,YAAoBC,cAA8B;IAA9B,mBAAc,GAAdA,cAAc;IAHlC,YAAO,GAAQ,IAAI;IACnB,UAAK,GAAW,EAAE;EAEmC;EAErDC,QAAQ;IACNC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACvD;EAEAC,cAAc;IACZ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,YAAY;IAE3B,IAAI,CAACN,cAAc,CAACO,UAAU,CAAC,iBAAiB,CAAC,CAACC,SAAS,CAAC;MAC1DC,IAAI,EAAGC,OAAoB,IAAI;QAC7B,IAAI,CAACJ,OAAO,GAAG;UACbK,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,8BAA8B;UACvCC,IAAI,EAAE;YACJC,EAAE,EAAEJ,OAAO,CAACI,EAAE;YACdC,QAAQ,EAAEL,OAAO,CAACK,QAAQ;YAC1BC,SAAS,EAAEN,OAAO,CAACM,SAAS;YAC5BC,QAAQ,EAAEP,OAAO,CAACO,QAAQ;YAC1BC,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;YAC5CC,QAAQ,EAAET,OAAO,CAACS,QAAQ;YAC1BC,YAAY,EAAEV,OAAO,CAACU,YAAY;YAClCC,WAAW,EAAEX,OAAO,CAACY,MAAM,EAAEC,MAAM,IAAI,CAAC;YACxCC,cAAc,EAAEd,OAAO,CAACe,SAAS,EAAEF,MAAM,IAAI,CAAC;YAC9CG,mBAAmB,EAAEhB,OAAO,CAACiB,cAAc,EAAEJ,MAAM,IAAI;;SAE1D;QACDrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,OAAO,CAAC;MACzC,CAAC;MACDL,KAAK,EAAGuB,GAAG,IAAI;QACb,IAAI,CAACvB,KAAK,GAAGuB,GAAG,CAAChB,OAAO,IAAI,wBAAwB;QACpD,IAAI,CAACN,OAAO,GAAG,IAAI;QACnBJ,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEuB,GAAG,CAAC;MAC9C;KACD,CAAC;EACJ;EAEAC,qBAAqB;IACnB,IAAI,CAACxB,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,YAAY;IAE3B,IAAI,CAACN,cAAc,CAAC8B,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtB,SAAS,CAAC;MACpDC,IAAI,EAAGsB,MAAM,IAAI;QACf,IAAI,CAACzB,OAAO,GAAG;UACbK,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,sCAAsC;UAC/CC,IAAI,EAAE;YACJmB,UAAU,EAAED,MAAM,CAACC,UAAU;YAC7BC,WAAW,EAAEF,MAAM,CAACE,WAAW;YAC/BC,aAAa,EAAEH,MAAM,CAACI,QAAQ,CAACZ,MAAM;YACrCY,QAAQ,EAAEJ,MAAM,CAACI,QAAQ,CAACC,GAAG,CAACC,CAAC,KAAK;cAClCvB,EAAE,EAAEuB,CAAC,CAACvB,EAAE;cACRC,QAAQ,EAAEsB,CAAC,CAACtB,QAAQ;cACpBC,SAAS,EAAEqB,CAAC,CAACrB,SAAS;cACtBC,QAAQ,EAAEoB,CAAC,CAACpB,QAAQ;cACpBC,iBAAiB,EAAEmB,CAAC,CAACnB;aACtB,CAAC;;SAEL;QACDhB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4B,MAAM,CAAC;MAChD,CAAC;MACD1B,KAAK,EAAGuB,GAAG,IAAI;QACb,IAAI,CAACvB,KAAK,GAAGuB,GAAG,CAAChB,OAAO,IAAI,wBAAwB;QACpD,IAAI,CAACN,OAAO,GAAG,IAAI;QACnBJ,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEuB,GAAG,CAAC;MACtD;KACD,CAAC;EACJ;EAEAU,kBAAkB;IAChB,IAAI,CAACjC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,YAAY;IAE3B,IAAI,CAACN,cAAc,CAACuC,oBAAoB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC/B,SAAS,CAAC;MACpEC,IAAI,EAAGsB,MAAM,IAAI;QACf,IAAI,CAACzB,OAAO,GAAG;UACbK,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,gCAAgC;UACzCC,IAAI,EAAE;YACJ2B,UAAU,EAAE,WAAW;YACvBR,UAAU,EAAED,MAAM,CAACC,UAAU;YAC7BC,WAAW,EAAEF,MAAM,CAACE,WAAW;YAC/BC,aAAa,EAAEH,MAAM,CAACI,QAAQ,CAACZ,MAAM;YACrCY,QAAQ,EAAEJ,MAAM,CAACI,QAAQ,CAACC,GAAG,CAACC,CAAC,KAAK;cAClCvB,EAAE,EAAEuB,CAAC,CAACvB,EAAE;cACRC,QAAQ,EAAEsB,CAAC,CAACtB,QAAQ;cACpBC,SAAS,EAAEqB,CAAC,CAACrB,SAAS;cACtBC,QAAQ,EAAEoB,CAAC,CAACpB,QAAQ;cACpBC,iBAAiB,EAAEmB,CAAC,CAACnB;aACtB,CAAC;;SAEL;QACDhB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4B,MAAM,CAAC;MACxC,CAAC;MACD1B,KAAK,EAAGuB,GAAG,IAAI;QACb,IAAI,CAACvB,KAAK,GAAGuB,GAAG,CAAChB,OAAO,IAAI,wBAAwB;QACpD,IAAI,CAACN,OAAO,GAAG,IAAI;QACnBJ,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEuB,GAAG,CAAC;MACjD;KACD,CAAC;EACJ;;;uBAzGW9B,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAA2C;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UA/BnChD,8BAAsF;UAChFA,mCAAmB;UAAAA,iBAAK;UAE5BA,8BAA6B;UACnBA;YAAA,OAASiD,oBAAgB;UAAA,EAAC;UAChCjD,oDACF;UAAAA,iBAAS;UACTA,iCAAyF;UAAjFA;YAAA,OAASiD,2BAAuB;UAAA,EAAC;UACvCjD,0CACF;UAAAA,iBAAS;UACTA,iCAAkE;UAA1DA;YAAA,OAASiD,wBAAoB;UAAA,EAAC;UACpCjD,sCACF;UAAAA,iBAAS;UAGXA,+BAA+B;UACzBA,yBAAQ;UAAAA,iBAAK;UACjBA,+BAA0G;UACnGA,aAAoB;;UAAAA,iBAAM;UAInCA,6EAKM;UACRA,iBAAM;;;UAVKA,gBAAoB;UAApBA,wDAAoB;UAIGA,eAAW;UAAXA,gCAAW", "names": ["i0", "TestApiConnectionComponent", "constructor", "profileService", "ngOnInit", "console", "log", "testGetProfile", "error", "results", "getProfile", "subscribe", "next", "profile", "success", "message", "data", "id", "username", "firstName", "lastName", "professional<PERSON>itle", "headline", "profileViews", "skillsCount", "skills", "length", "blogPostsCount", "blogPosts", "certificationsCount", "certifications", "err", "testGetPublicProfiles", "getPublicProfiles", "result", "totalCount", "currentPage", "profilesCount", "profiles", "map", "p", "testSearchProfiles", "searchProfilesByTerm", "searchTerm", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\test-api-connection.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ProfileService } from './profile/services/profile.service';\r\nimport { UserProfile } from './profile/models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-test-api-connection',\r\n  template: `\r\n    <div style=\"padding: 20px; margin: 20px; border: 2px solid #ccc; border-radius: 8px;\">\r\n      <h2>API Connection Test</h2>\r\n      \r\n      <div style=\"margin: 10px 0;\">\r\n        <button (click)=\"testGetProfile()\" style=\"margin-right: 10px; padding: 8px 16px;\">\r\n          Test Get Profile (luna-starweaver)\r\n        </button>\r\n        <button (click)=\"testGetPublicProfiles()\" style=\"margin-right: 10px; padding: 8px 16px;\">\r\n          Test Get Public Profiles\r\n        </button>\r\n        <button (click)=\"testSearchProfiles()\" style=\"padding: 8px 16px;\">\r\n          Test Search Profiles\r\n        </button>\r\n      </div>\r\n\r\n      <div style=\"margin-top: 20px;\">\r\n        <h3>Results:</h3>\r\n        <div style=\"background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;\">\r\n          <pre>{{ results | json }}</pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"margin-top: 20px;\" *ngIf=\"error\">\r\n        <h3 style=\"color: red;\">Error:</h3>\r\n        <div style=\"background: #ffe6e6; padding: 10px; border-radius: 4px; color: red;\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `\r\n})\r\nexport class TestApiConnectionComponent implements OnInit {\r\n  results: any = null;\r\n  error: string = '';\r\n\r\n  constructor(private profileService: ProfileService) {}\r\n\r\n  ngOnInit() {\r\n    console.log('TestApiConnectionComponent initialized');\r\n  }\r\n\r\n  testGetProfile() {\r\n    this.error = '';\r\n    this.results = 'Loading...';\r\n    \r\n    this.profileService.getProfile('luna-starweaver').subscribe({\r\n      next: (profile: UserProfile) => {\r\n        this.results = {\r\n          success: true,\r\n          message: 'Profile loaded successfully!',\r\n          data: {\r\n            id: profile.id,\r\n            username: profile.username,\r\n            firstName: profile.firstName,\r\n            lastName: profile.lastName,\r\n            professionalTitle: profile.professionalTitle,\r\n            headline: profile.headline,\r\n            profileViews: profile.profileViews,\r\n            skillsCount: profile.skills?.length || 0,\r\n            blogPostsCount: profile.blogPosts?.length || 0,\r\n            certificationsCount: profile.certifications?.length || 0\r\n          }\r\n        };\r\n        console.log('Profile loaded:', profile);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.message || 'Unknown error occurred';\r\n        this.results = null;\r\n        console.error('Error loading profile:', err);\r\n      }\r\n    });\r\n  }\r\n\r\n  testGetPublicProfiles() {\r\n    this.error = '';\r\n    this.results = 'Loading...';\r\n    \r\n    this.profileService.getPublicProfiles(1, 5).subscribe({\r\n      next: (result) => {\r\n        this.results = {\r\n          success: true,\r\n          message: 'Public profiles loaded successfully!',\r\n          data: {\r\n            totalCount: result.totalCount,\r\n            currentPage: result.currentPage,\r\n            profilesCount: result.profiles.length,\r\n            profiles: result.profiles.map(p => ({\r\n              id: p.id,\r\n              username: p.username,\r\n              firstName: p.firstName,\r\n              lastName: p.lastName,\r\n              professionalTitle: p.professionalTitle\r\n            }))\r\n          }\r\n        };\r\n        console.log('Public profiles loaded:', result);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.message || 'Unknown error occurred';\r\n        this.results = null;\r\n        console.error('Error loading public profiles:', err);\r\n      }\r\n    });\r\n  }\r\n\r\n  testSearchProfiles() {\r\n    this.error = '';\r\n    this.results = 'Loading...';\r\n    \r\n    this.profileService.searchProfilesByTerm('astrology', 1, 5).subscribe({\r\n      next: (result) => {\r\n        this.results = {\r\n          success: true,\r\n          message: 'Search completed successfully!',\r\n          data: {\r\n            searchTerm: 'astrology',\r\n            totalCount: result.totalCount,\r\n            currentPage: result.currentPage,\r\n            profilesCount: result.profiles.length,\r\n            profiles: result.profiles.map(p => ({\r\n              id: p.id,\r\n              username: p.username,\r\n              firstName: p.firstName,\r\n              lastName: p.lastName,\r\n              professionalTitle: p.professionalTitle\r\n            }))\r\n          }\r\n        };\r\n        console.log('Search results:', result);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.message || 'Unknown error occurred';\r\n        this.results = null;\r\n        console.error('Error searching profiles:', err);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}