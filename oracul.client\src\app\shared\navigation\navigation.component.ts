import { Component, OnInit, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../auth/services/auth.service';
import { TranslationService } from '../../core/i18n/translation.service';

@Component({
  selector: 'app-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.css']
})
export class NavigationComponent implements OnInit, OnDestroy {
  currentRoute = '';
  isMobileMenuOpen = false;
  isHomePage = false;
  activeSection = 'hero';
  private intersectionObserver?: IntersectionObserver;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public authService: AuthService,
    public t: TranslationService
  ) {
    // Track current route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event) => {
      this.currentRoute = (event as NavigationEnd).url;
      const wasHomePage = this.isHomePage;
      this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');
      this.closeMobileMenu();

      // Initialize scroll detection when navigating to home page
      if (this.isHomePage && !wasHomePage) {
        // Clean up previous observer if it exists
        if (this.intersectionObserver) {
          this.intersectionObserver.disconnect();
        }
        this.initializeScrollDetection();
      } else if (!this.isHomePage && this.intersectionObserver) {
        // Clean up observer when leaving home page
        this.intersectionObserver.disconnect();
        this.activeSection = 'hero'; // Reset to default
      }
    });
  }

  ngOnInit(): void {
    // Check initial route
    this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');

    // Handle fragment navigation when arriving at home page
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      // Check for fragment after navigation
      const fragment = this.route.snapshot.fragment;
      if (fragment && this.isHomePage) {
        setTimeout(() => {
          this.scrollToSection(fragment);
        }, 300); // Increased delay to ensure DOM is ready
      }
    });

    // Initialize scroll detection when on home page
    if (this.isHomePage) {
      this.initializeScrollDetection();
    }
  }

  ngOnDestroy(): void {
    // Clean up intersection observer
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  // Navigation methods - adapt behavior based on current page
  navigateToSection(sectionId: string): void {
    this.closeMobileMenu();

    if (this.isHomePage) {
      // If on home page, scroll to section
      this.scrollToSection(sectionId);
      // Update active section immediately for better UX
      this.activeSection = sectionId;
    } else {
      // If on other page, navigate to home with fragment
      this.router.navigate(['/home'], { fragment: sectionId });
    }
  }

  // Smooth scroll to section (only works when on home page)
  private scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  }

  // Authentication navigation
  navigateToLogin(): void {
    this.closeMobileMenu();
    this.router.navigate(['/login']);
  }

  navigateToRegister(): void {
    this.closeMobileMenu();
    this.router.navigate(['/register']);
  }

  // Mobile menu controls
  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
  }

  // Logout functionality
  logout(): void {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/home']);
      },
      error: () => {
        // Still navigate to home even if logout fails
        this.router.navigate(['/home']);
      }
    });
  }

  // Initialize scroll detection using Intersection Observer
  private initializeScrollDetection(): void {
    // Wait for DOM to be ready
    setTimeout(() => {
      const sections = ['hero', 'astrologers', 'articles', 'horoscope'];
      const sectionElements = sections.map(id => document.getElementById(id)).filter(el => el !== null);

      if (sectionElements.length === 0) {
        return; // No sections found, skip initialization
      }

      // Create intersection observer
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // Update active section when section comes into view
              this.activeSection = entry.target.id;
            }
          });
        },
        {
          // Trigger when section is 30% visible
          threshold: 0.3,
          // Offset from top to account for fixed navigation
          rootMargin: '-80px 0px -50% 0px'
        }
      );

      // Observe all sections
      sectionElements.forEach(section => {
        if (section) {
          this.intersectionObserver!.observe(section);
        }
      });
    }, 500);
  }

  // Check if a navigation link is active
  isActiveSection(sectionId: string): boolean {
    return this.isHomePage && this.activeSection === sectionId;
  }
}
