# 🔮 Database Management Scripts

This directory contains scripts for managing the Oracul application database, including seeding with oracle profiles.

## 📋 Available Scripts

### 🖥️ **Windows Scripts**

#### **PowerShell Script (Recommended)**
```powershell
# Full-featured database management
.\Scripts\database-management.ps1 -Action <action>
```

**Available Actions:**
- `status` - Check database status and migrations
- `migrate` - Apply pending migrations
- `seed` - Seed database with 5 oracle profiles
- `recreate` - Drop and recreate empty database
- `reset` - Full reset (drop + migrate + seed)
- `drop` - Drop database (⚠️ deletes all data)

#### **Batch Files (Simple)**
```batch
# Quick database reset
.\Scripts\reset-database.bat

# Seed database only
.\Scripts\seed-database.bat
```

### 🐧 **Linux/Mac Scripts**

#### **Bash Script**
```bash
# Full-featured database management
./Scripts/database-management.sh <action>
```

**Available Actions:** Same as PowerShell script above

## 🚀 **Quick Start Examples**

### **Complete Database Reset**
```powershell
# Windows PowerShell
.\Scripts\database-management.ps1 -Action reset

# Linux/Mac
./Scripts/database-management.sh reset

# Windows Batch (simple)
.\Scripts\reset-database.bat
```

### **Seed Database Only**
```powershell
# Windows PowerShell
.\Scripts\database-management.ps1 -Action seed

# Linux/Mac
./Scripts/database-management.sh seed

# Windows Batch (simple)
.\Scripts\seed-database.bat
```

### **Check Database Status**
```powershell
# Windows PowerShell
.\Scripts\database-management.ps1 -Action status

# Linux/Mac
./Scripts/database-management.sh status
```

## 🔮 **Oracle Profiles Created**

When you run the seeding scripts, the following 5 oracle profiles are created:

1. **Luna Starweaver** (Sedona, AZ) - Professional Astrologer & Cosmic Guide
2. **Sage Moonchild** (Santa Fe, NM) - Crystal Healer & Energy Worker
3. **River Palmistry** (New Orleans, LA) - Third-Generation Palm Reader
4. **Aurora Wisdom** (Portland, OR) - Spiritual Counselor & Intuitive Life Coach
5. **Cosmic Dawn** (Boulder, CO) - Numerologist & Sacred Geometry Expert

Each profile includes:
- ✅ Complete personal and professional information
- ✅ 8 specialized skills with endorsements
- ✅ 2 professional certifications
- ✅ Work experience and company details
- ✅ Specialty-specific blog posts
- ✅ Social media presence
- ✅ Contact information and business address

## 🛠️ **Manual Commands**

If you prefer to run Entity Framework commands manually:

### **Drop Database**
```bash
dotnet ef database drop --project Oracul.Data --startup-project Oracul.Server --force
```

### **Apply Migrations**
```bash
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server
```

### **Seed Database**
```bash
dotnet run --project Oracul.Server --seed-only
```

### **Check Migration Status**
```bash
dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server
```

## 🔧 **Troubleshooting**

### **Common Issues**

1. **"dotnet ef command not found"**
   ```bash
   # Install EF Core tools globally
   dotnet tool install --global dotnet-ef
   ```

2. **Database connection errors**
   - Check that SQL Server LocalDB is running
   - Verify connection string in `appsettings.json`

3. **Migration errors**
   ```bash
   # Check for pending migrations
   dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server
   ```

4. **Seeding fails**
   - Ensure database exists and migrations are applied
   - Check that the ProfileSeedService is registered in DI

### **Script Permissions (Linux/Mac)**

Make the bash script executable:
```bash
chmod +x Scripts/database-management.sh
```

## 📁 **File Structure**

```
Scripts/
├── README.md                    # This file
├── database-management.ps1      # PowerShell script (Windows)
├── database-management.sh       # Bash script (Linux/Mac)
├── reset-database.bat          # Simple reset (Windows)
└── seed-database.bat           # Simple seeding (Windows)
```

## 🎯 **Development Workflow**

### **Starting Fresh**
1. Run `reset` to get a clean database with oracle profiles
2. Start the application: `dotnet run --project Oracul.Server`
3. Access frontend: http://localhost:4200

### **Adding New Data**
1. Run `seed` to add oracle profiles to existing database
2. The seeding service checks for existing profiles and won't duplicate

### **Testing Migrations**
1. Run `recreate` to get empty database with latest schema
2. Run `seed` to populate with test data

### **Important Notes**
- ⚠️ **Stop the backend server** before running seeding scripts to avoid file locking issues
- ✅ **Seeding is automatic** - The backend server automatically seeds the database on startup if needed
- 🔄 **Scripts are for manual control** - Use scripts when you need to reset or reseed manually

## 🌟 **Features**

- ✅ **Cross-platform** - Works on Windows, Linux, and Mac
- ✅ **Safe operations** - Confirmation prompts for destructive actions
- ✅ **Colored output** - Easy to read status messages
- ✅ **Error handling** - Proper error reporting and exit codes
- ✅ **Flexible** - Multiple script options for different preferences
- ✅ **Complete profiles** - Rich oracle data with all relationships

## 🔗 **Related Documentation**

- [Oracle Profiles Seeded](../ORACLE-PROFILES-SEEDED.md) - Complete profile details
- [Frontend-Backend Integration](../FRONTEND-BACKEND-INTEGRATION.md) - API integration
- [Entity Framework Migrations](https://docs.microsoft.com/en-us/ef/core/managing-schemas/migrations/) - Official EF Core docs

---

**Happy coding with the mystical oracle profiles! 🔮✨**
