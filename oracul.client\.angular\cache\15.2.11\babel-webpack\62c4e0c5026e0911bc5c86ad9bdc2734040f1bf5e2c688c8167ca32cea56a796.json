{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, take, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    this._detach = () => {\n      this.disable();\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    this._document = document;\n  }\n}\nScrollStrategyOptions.ɵfac = function ScrollStrategyOptions_Factory(t) {\n  return new (t || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\nScrollStrategyOptions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollStrategyOptions,\n  factory: ScrollStrategyOptions.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ScrollDispatcher\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.disposeOnNavigation = false;\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  constructor( /** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n}\nBaseOverlayDispatcher.ɵfac = function BaseOverlayDispatcher_Factory(t) {\n  return new (t || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n};\nBaseOverlayDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BaseOverlayDispatcher,\n  factory: BaseOverlayDispatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n      this._isAttached = false;\n    }\n  }\n}\nOverlayKeyboardDispatcher.ɵfac = function OverlayKeyboardDispatcher_Factory(t) {\n  return new (t || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n};\nOverlayKeyboardDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayKeyboardDispatcher,\n  factory: OverlayKeyboardDispatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    this._clickListener = event => {\n      const target = _getEventTarget(event);\n      // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n      // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n      this._pointerDownEventTarget = null;\n      // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n      const overlays = this._attachedOverlays.slice();\n      // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        }\n        // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n        if (overlayRef.overlayElement.contains(target) || overlayRef.overlayElement.contains(origin)) {\n          break;\n        }\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      }\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n}\nOverlayOutsideClickDispatcher.ɵfac = function OverlayOutsideClickDispatcher_Factory(t) {\n  return new (t || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n};\nOverlayOutsideClickDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayOutsideClickDispatcher,\n  factory: OverlayOutsideClickDispatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n}\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n  return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\nOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayContainer,\n  factory: OverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    this._outsidePointerEvents = new Subject();\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenStable();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._disposeBackdrop(this._backdropElement);\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._previousHostParent = this._pane = this._host = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropElement, this._host);\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click', this._backdropClickHandler);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n    if (!backdropToDetach) {\n      return;\n    }\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n      return;\n    }\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    });\n    // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n    backdropToDetach.style.pointerEvents = 'none';\n    // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenStable() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._ngZone.onStable.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove();\n      // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollableViewProperties = this._getScrollVisibility();\n      const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n      this._positionChanges.next(changeEvent);\n    }\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the ClientRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._alignItems = '';\n    this._xPosition = '';\n    this._xOffset = '';\n    this._width = '';\n    this._height = '';\n    this._isDisposed = false;\n  }\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n}\nOverlayPositionBuilder.ɵfac = function OverlayPositionBuilder_Factory(t) {\n  return new (t || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n};\nOverlayPositionBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayPositionBuilder,\n  factory: OverlayPositionBuilder.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: OverlayContainer\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  constructor( /** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsModuleType = _animationsModuleType;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n}\nOverlay.ɵfac = function Overlay_Factory(t) {\n  return new (t || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n};\nOverlay.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Overlay,\n  factory: Overlay.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: ScrollStrategyOptions\n    }, {\n      type: OverlayContainer\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: OverlayPositionBuilder\n    }, {\n      type: OverlayKeyboardDispatcher\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i5.Directionality\n    }, {\n      type: i6.Location\n    }, {\n      type: OverlayOutsideClickDispatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  constructor( /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n}\nCdkOverlayOrigin.ɵfac = function CdkOverlayOrigin_Factory(t) {\n  return new (t || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nCdkOverlayOrigin.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkOverlayOrigin,\n  selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n  exportAs: [\"cdkOverlayOrigin\"],\n  standalone: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether or not the overlay should attach a backdrop. */\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /** Whether or not the overlay should be locked when scrolling. */\n  get lockPosition() {\n    return this._lockPosition;\n  }\n  set lockPosition(value) {\n    this._lockPosition = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  get flexibleDimensions() {\n    return this._flexibleDimensions;\n  }\n  set flexibleDimensions(value) {\n    this._flexibleDimensions = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  get growAfterOpen() {\n    return this._growAfterOpen;\n  }\n  set growAfterOpen(value) {\n    this._growAfterOpen = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  get push() {\n    return this._push;\n  }\n  set push(value) {\n    this._push = coerceBooleanProperty(value);\n  }\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._hasBackdrop = false;\n    this._lockPosition = false;\n    this._growAfterOpen = false;\n    this._flexibleDimensions = false;\n    this._push = false;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    /** Margin between the overlay and the viewport edges. */\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    this.disableClose = false;\n    /** Event emitted when the backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this._detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      this.overlayOutsideClick.next(event);\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this.positionChange.emit(position);\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n  }\n}\nCdkConnectedOverlay.ɵfac = function CdkConnectedOverlay_Factory(t) {\n  return new (t || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n};\nCdkConnectedOverlay.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkConnectedOverlay,\n  selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n  inputs: {\n    origin: [\"cdkConnectedOverlayOrigin\", \"origin\"],\n    positions: [\"cdkConnectedOverlayPositions\", \"positions\"],\n    positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n    offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n    offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n    width: [\"cdkConnectedOverlayWidth\", \"width\"],\n    height: [\"cdkConnectedOverlayHeight\", \"height\"],\n    minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n    minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n    backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n    panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n    viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n    scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n    open: [\"cdkConnectedOverlayOpen\", \"open\"],\n    disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n    transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n    hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"],\n    lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"],\n    flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"],\n    growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"],\n    push: [\"cdkConnectedOverlayPush\", \"push\"]\n  },\n  outputs: {\n    backdropClick: \"backdropClick\",\n    positionChange: \"positionChange\",\n    attach: \"attach\",\n    detach: \"detach\",\n    overlayKeydown: \"overlayKeydown\",\n    overlayOutsideClick: \"overlayOutsideClick\"\n  },\n  exportAs: [\"cdkConnectedOverlay\"],\n  standalone: true,\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: Overlay\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHasBackdrop']\n    }],\n    lockPosition: [{\n      type: Input,\n      args: ['cdkConnectedOverlayLockPosition']\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayFlexibleDimensions']\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: ['cdkConnectedOverlayGrowAfterOpen']\n    }],\n    push: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPush']\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass OverlayModule {}\nOverlayModule.ɵfac = function OverlayModule_Factory(t) {\n  return new (t || OverlayModule)();\n};\nOverlayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: OverlayModule\n});\nOverlayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n  imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n  _createContainer() {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n      this._document.addEventListener(eventName, fn);\n      this._fullScreenListener = fn;\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n}\nFullscreenOverlayContainer.ɵfac = function FullscreenOverlayContainer_Factory(t) {\n  return new (t || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\nFullscreenOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FullscreenOverlayContainer,\n  factory: FullscreenOverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };", "map": {"version": 3, "names": ["i1", "ScrollingModule", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "i6", "DOCUMENT", "i0", "Injectable", "Inject", "Optional", "ElementRef", "ApplicationRef", "ANIMATION_MODULE_TYPE", "InjectionToken", "Directive", "EventEmitter", "Input", "Output", "NgModule", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "i1$1", "supportsScrollBehavior", "_getEventTarget", "_isTestEnvironment", "filter", "take", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "i5", "BidiModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "Subject", "Subscription", "merge", "ESCAPE", "hasModifierKey", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_previousHTMLStyles", "top", "left", "_isEnabled", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "NgZone", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "OverlayConfig", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "OverlayKeyboardDispatcher", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "keydownEvents", "next", "_isAttached", "runOutsideAngular", "addEventListener", "removeEventListener", "OverlayOutsideClickDispatcher", "_platform", "_cursorStyleIsSet", "_pointerDownListener", "_pointerDownEventTarget", "_clickListener", "target", "slice", "_outsidePointerEvents", "outsidePointerEvents", "_addEventListeners", "IOS", "_cursorOriginalV<PERSON>ue", "cursor", "Platform", "OverlayContainer", "_containerElement", "getContainerElement", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_backdropElement", "_backdropClick", "_attachments", "_detachments", "_locationChanges", "EMPTY", "_backdropClickHandler", "_backdropTransitionendHandler", "_disposeBackdrop", "_scrollStrategy", "_positionStrategy", "positionStrategy", "backdropElement", "hostElement", "portal", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "onStable", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "dispose", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenStable", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "parentNode", "backdropToDetach", "_backdropTimeout", "setTimeout", "cssClasses", "isAdd", "c", "subscription", "children", "backdrop", "clearTimeout", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "positions", "_preferredPositions", "connectedTo", "_overlayContainer", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_viewportMargin", "_scrollables", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_appliedPanelClasses", "position<PERSON><PERSON>es", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "_getScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "styles", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "for<PERSON>ach", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "parent", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "nextUniqueId", "Overlay", "scrollStrategies", "_componentFactoryResolver", "_positionBuilder", "_injector", "_directionality", "_animationsModuleType", "create", "host", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "id", "_appRef", "get", "ComponentFactoryResolver", "Injector", "Directionality", "Location", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "ɵdir", "exportAs", "standalone", "CdkConnectedOverlay", "_position", "_updatePositionStrategy", "_hasBackdrop", "lockPosition", "_lockPosition", "_flexibleDimensions", "_push", "_overlay", "templateRef", "viewContainerRef", "scrollStrategyFactory", "_dir", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "viewportMargin", "open", "disableClose", "positionChange", "overlayKeydown", "overlayOutsideClick", "_templatePortal", "_scrollStrategyFactory", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_createPositionStrategy", "currentPosition", "_getFlexibleConnectedPositionStrategyOrigin", "transformOriginSelector", "TemplateRef", "ViewContainerRef", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "ɵmod", "ɵinj", "imports", "exports", "providers", "FullscreenOverlayContainer", "platform", "_fullScreenEventName", "_fullScreenListener", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "fullscreenElement", "getFullscreenElement", "fn", "eventName", "_getEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/cdk/fesm2020/overlay.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, take, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._previousHTMLStyles = { top: '', left: '' };\n        this._isEnabled = false;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const body = this._document.body;\n        const viewport = this._viewportRuler.getViewportSize();\n        return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n        this._scrollSubscription = null;\n        /** Detaches the overlay ref and disables the scroll strategy. */\n        this._detach = () => {\n            this.disable();\n            if (this._overlayRef.hasAttached()) {\n                this._ngZone.run(() => this._overlayRef.detach());\n            }\n        };\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n        this._scrollSubscription = null;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        /** Do nothing on scroll. */\n        this.noop = () => new NoopScrollStrategy();\n        /**\n         * Close the overlay as soon as the user scrolls.\n         * @param config Configuration to be used inside the scroll strategy.\n         */\n        this.close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n        /** Block scrolling. */\n        this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n        /**\n         * Update the overlay's position on scroll.\n         * @param config Configuration to be used inside the scroll strategy.\n         * Allows debouncing the reposition calls.\n         */\n        this.reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n        this._document = document;\n    }\n}\nScrollStrategyOptions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollStrategyOptions, deps: [{ token: i1.ScrollDispatcher }, { token: i1.ViewportRuler }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollStrategyOptions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ScrollDispatcher }, { type: i1.ViewportRuler }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    constructor(config) {\n        /** Strategy to be used when handling scroll events while the overlay is open. */\n        this.scrollStrategy = new NoopScrollStrategy();\n        /** Custom class to add to the overlay pane. */\n        this.panelClass = '';\n        /** Whether the overlay has a backdrop. */\n        this.hasBackdrop = false;\n        /** Custom class to add to the backdrop */\n        this.backdropClass = 'cdk-overlay-dark-backdrop';\n        /**\n         * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.disposeOnNavigation = false;\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    constructor(document) {\n        /** Currently attached overlays in the order they were attached. */\n        this._attachedOverlays = [];\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n}\nBaseOverlayDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BaseOverlayDispatcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBaseOverlayDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    constructor(document, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._ngZone = _ngZone;\n        /** Keyboard event listener that will be attached to the body. */\n        this._keydownListener = (event) => {\n            const overlays = this._attachedOverlays;\n            for (let i = overlays.length - 1; i > -1; i--) {\n                // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n                // We want to target the most recent overlay, rather than trying to match where the event came\n                // from, because some components might open an overlay, but keep focus on a trigger element\n                // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n                // because we don't want overlays that don't handle keyboard events to block the ones below\n                // them that do.\n                if (overlays[i]._keydownEvents.observers.length > 0) {\n                    const keydownEvents = overlays[i]._keydownEvents;\n                    /** @breaking-change 14.0.0 _ngZone will be required. */\n                    if (this._ngZone) {\n                        this._ngZone.run(() => keydownEvents.next(event));\n                    }\n                    else {\n                        keydownEvents.next(event);\n                    }\n                    break;\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n            }\n            else {\n                this._document.body.addEventListener('keydown', this._keydownListener);\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._document.body.removeEventListener('keydown', this._keydownListener);\n            this._isAttached = false;\n        }\n    }\n}\nOverlayKeyboardDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: [{ token: DOCUMENT }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayKeyboardDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    constructor(document, _platform, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._cursorStyleIsSet = false;\n        /** Store pointerdown event target to track origin of click. */\n        this._pointerDownListener = (event) => {\n            this._pointerDownEventTarget = _getEventTarget(event);\n        };\n        /** Click event listener that will be attached to the body propagate phase. */\n        this._clickListener = (event) => {\n            const target = _getEventTarget(event);\n            // In case of a click event, we want to check the origin of the click\n            // (e.g. in case where a user starts a click inside the overlay and\n            // releases the click outside of it).\n            // This is done by using the event target of the preceding pointerdown event.\n            // Every click event caused by a pointer device has a preceding pointerdown\n            // event, unless the click was programmatically triggered (e.g. in a unit test).\n            const origin = event.type === 'click' && this._pointerDownEventTarget\n                ? this._pointerDownEventTarget\n                : target;\n            // Reset the stored pointerdown event target, to avoid having it interfere\n            // in subsequent events.\n            this._pointerDownEventTarget = null;\n            // We copy the array because the original may be modified asynchronously if the\n            // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n            // the for loop.\n            const overlays = this._attachedOverlays.slice();\n            // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n            // We want to target all overlays for which the click could be considered as outside click.\n            // As soon as we reach an overlay for which the click is not outside click we break off\n            // the loop.\n            for (let i = overlays.length - 1; i > -1; i--) {\n                const overlayRef = overlays[i];\n                if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                    continue;\n                }\n                // If it's a click inside the overlay, just break - we should do nothing\n                // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n                // and proceed with the next overlay\n                if (overlayRef.overlayElement.contains(target) ||\n                    overlayRef.overlayElement.contains(origin)) {\n                    break;\n                }\n                const outsidePointerEvents = overlayRef._outsidePointerEvents;\n                /** @breaking-change 14.0.0 _ngZone will be required. */\n                if (this._ngZone) {\n                    this._ngZone.run(() => outsidePointerEvents.next(event));\n                }\n                else {\n                    outsidePointerEvents.next(event);\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n            }\n            else {\n                this._addEventListeners(body);\n            }\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            const body = this._document.body;\n            body.removeEventListener('pointerdown', this._pointerDownListener, true);\n            body.removeEventListener('click', this._clickListener, true);\n            body.removeEventListener('auxclick', this._clickListener, true);\n            body.removeEventListener('contextmenu', this._clickListener, true);\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    _addEventListeners(body) {\n        body.addEventListener('pointerdown', this._pointerDownListener, true);\n        body.addEventListener('click', this._clickListener, true);\n        body.addEventListener('auxclick', this._clickListener, true);\n        body.addEventListener('contextmenu', this._clickListener, true);\n    }\n}\nOverlayOutsideClickDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayOutsideClickDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    constructor(document, _platform) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n}\nOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._backdropElement = null;\n        this._backdropClick = new Subject();\n        this._attachments = new Subject();\n        this._detachments = new Subject();\n        this._locationChanges = Subscription.EMPTY;\n        this._backdropClickHandler = (event) => this._backdropClick.next(event);\n        this._backdropTransitionendHandler = (event) => {\n            this._disposeBackdrop(event.target);\n        };\n        /** Stream of keydown events dispatched to this overlay. */\n        this._keydownEvents = new Subject();\n        /** Stream of mouse outside events dispatched to this overlay. */\n        this._outsidePointerEvents = new Subject();\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropElement;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // Update the position once the zone is stable so that the overlay will be fully rendered\n        // before attempting to position it, as the position may depend on the size of the rendered\n        // content.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            // The overlay could've been detached before the zone has stabilized.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenStable();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._disposeBackdrop(this._backdropElement);\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._previousHostParent = this._pane = this._host = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropElement = this._document.createElement('div');\n        this._backdropElement.classList.add('cdk-overlay-backdrop');\n        if (this._animationsDisabled) {\n            this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropElement, this._host);\n        // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n        // action desired when such a click occurs (usually closing the overlay).\n        this._backdropElement.addEventListener('click', this._backdropClickHandler);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    if (this._backdropElement) {\n                        this._backdropElement.classList.add(showingClass);\n                    }\n                });\n            });\n        }\n        else {\n            this._backdropElement.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        const backdropToDetach = this._backdropElement;\n        if (!backdropToDetach) {\n            return;\n        }\n        if (this._animationsDisabled) {\n            this._disposeBackdrop(backdropToDetach);\n            return;\n        }\n        backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n        this._ngZone.runOutsideAngular(() => {\n            backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n        });\n        // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n        // In this case we make it unclickable and we try to remove it after a delay.\n        backdropToDetach.style.pointerEvents = 'none';\n        // Run this outside the Angular zone because there's nothing that Angular cares about.\n        // If it were to run inside the Angular zone, every test that used Overlay would have to be\n        // either async or fakeAsync.\n        this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n            this._disposeBackdrop(backdropToDetach);\n        }, 500));\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenStable() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._ngZone.onStable\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        if (scrollStrategy) {\n            scrollStrategy.disable();\n            if (scrollStrategy.detach) {\n                scrollStrategy.detach();\n            }\n        }\n    }\n    /** Removes a backdrop element from the DOM. */\n    _disposeBackdrop(backdrop) {\n        if (backdrop) {\n            backdrop.removeEventListener('click', this._backdropClickHandler);\n            backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n            backdrop.remove();\n            // It is possible that a new portal has been attached to this overlay since we started\n            // removing the backdrop. If that is the case, only clear the backdrop reference if it\n            // is still the same instance that we started to remove.\n            if (this._backdropElement === backdrop) {\n                this._backdropElement = null;\n            }\n        }\n        if (this._backdropTimeout) {\n            clearTimeout(this._backdropTimeout);\n            this._backdropTimeout = undefined;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n        this._lastBoundingBoxSize = { width: 0, height: 0 };\n        /** Whether the overlay was pushed in a previous positioning. */\n        this._isPushed = false;\n        /** Whether the overlay can be pushed on-screen on the initial open. */\n        this._canPush = true;\n        /** Whether the overlay can grow via flexible width/height after the initial open. */\n        this._growAfterOpen = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this._hasFlexibleDimensions = true;\n        /** Whether the overlay position is locked. */\n        this._positionLocked = false;\n        /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n        this._viewportMargin = 0;\n        /** The Scrollable containers used to check scrollable view properties on position change. */\n        this._scrollables = [];\n        /** Ordered list of preferred positions, from most to least desirable. */\n        this._preferredPositions = [];\n        /** Subject that emits whenever the position changes. */\n        this._positionChanges = new Subject();\n        /** Subscription to viewport size changes. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Default offset for the overlay along the x axis. */\n        this._offsetX = 0;\n        /** Default offset for the overlay along the y axis. */\n        this._offsetY = 0;\n        /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n        this._appliedPanelClasses = [];\n        /** Observable sequence of position changes. */\n        this.positionChanges = this._positionChanges;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollableViewProperties = this._getScrollVisibility();\n            const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n            this._positionChanges.next(changeEvent);\n        }\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the ClientRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    constructor() {\n        this._cssPosition = 'static';\n        this._topOffset = '';\n        this._bottomOffset = '';\n        this._alignItems = '';\n        this._xPosition = '';\n        this._xOffset = '';\n        this._width = '';\n        this._height = '';\n        this._isDisposed = false;\n    }\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n    }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n}\nOverlayPositionBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayPositionBuilder, deps: [{ token: i1.ViewportRuler }, { token: DOCUMENT }, { token: i1$1.Platform }, { token: OverlayContainer }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayPositionBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: OverlayContainer }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n        this.scrollStrategies = scrollStrategies;\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._positionBuilder = _positionBuilder;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._injector = _injector;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._directionality = _directionality;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsModuleType = _animationsModuleType;\n    }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = `cdk-overlay-${nextUniqueId++}`;\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n    }\n}\nOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Overlay, deps: [{ token: ScrollStrategyOptions }, { token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: OverlayPositionBuilder }, { token: OverlayKeyboardDispatcher }, { token: i0.Injector }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i5.Directionality }, { token: i6.Location }, { token: OverlayOutsideClickDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlay.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Overlay, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: ScrollStrategyOptions }, { type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: OverlayPositionBuilder }, { type: OverlayKeyboardDispatcher }, { type: i0.Injector }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i5.Directionality }, { type: i6.Location }, { type: OverlayOutsideClickDispatcher }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n}\nCdkOverlayOrigin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkOverlayOrigin, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkOverlayOrigin.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** Whether or not the overlay should attach a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /** Whether or not the overlay should be locked when scrolling. */\n    get lockPosition() {\n        return this._lockPosition;\n    }\n    set lockPosition(value) {\n        this._lockPosition = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    get flexibleDimensions() {\n        return this._flexibleDimensions;\n    }\n    set flexibleDimensions(value) {\n        this._flexibleDimensions = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    get growAfterOpen() {\n        return this._growAfterOpen;\n    }\n    set growAfterOpen(value) {\n        this._growAfterOpen = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    get push() {\n        return this._push;\n    }\n    set push(value) {\n        this._push = coerceBooleanProperty(value);\n    }\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n        this._overlay = _overlay;\n        this._dir = _dir;\n        this._hasBackdrop = false;\n        this._lockPosition = false;\n        this._growAfterOpen = false;\n        this._flexibleDimensions = false;\n        this._push = false;\n        this._backdropSubscription = Subscription.EMPTY;\n        this._attachSubscription = Subscription.EMPTY;\n        this._detachSubscription = Subscription.EMPTY;\n        this._positionSubscription = Subscription.EMPTY;\n        /** Margin between the overlay and the viewport edges. */\n        this.viewportMargin = 0;\n        /** Whether the overlay is open. */\n        this.open = false;\n        /** Whether the overlay can be closed by user interaction. */\n        this.disableClose = false;\n        /** Event emitted when the backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Event emitted when the position has changed. */\n        this.positionChange = new EventEmitter();\n        /** Event emitted when the overlay has been attached. */\n        this.attach = new EventEmitter();\n        /** Event emitted when the overlay has been detached. */\n        this.detach = new EventEmitter();\n        /** Emits when there are keyboard events that are targeted at the overlay. */\n        this.overlayKeydown = new EventEmitter();\n        /** Emits when there are mouse outside click events that are targeted at the overlay. */\n        this.overlayOutsideClick = new EventEmitter();\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n        }\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this._attachOverlay() : this._detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this._detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            this.overlayOutsideClick.next(event);\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir,\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getFlexibleConnectedPositionStrategyOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getFlexibleConnectedPositionStrategyOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n    _attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this.positionChange.emit(position);\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n    }\n    /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n    _detachOverlay() {\n        if (this._overlayRef) {\n            this._overlayRef.detach();\n        }\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n    }\n}\nCdkConnectedOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkConnectedOverlay, deps: [{ token: Overlay }, { token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkConnectedOverlay.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"], push: [\"cdkConnectedOverlayPush\", \"push\"] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: Overlay }, { type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHasBackdrop']\n            }], lockPosition: [{\n                type: Input,\n                args: ['cdkConnectedOverlayLockPosition']\n            }], flexibleDimensions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayFlexibleDimensions']\n            }], growAfterOpen: [{\n                type: Input,\n                args: ['cdkConnectedOverlayGrowAfterOpen']\n            }], push: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPush']\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass OverlayModule {\n}\nOverlayModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nOverlayModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\nOverlayModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    constructor(_document, platform) {\n        super(_document, platform);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._fullScreenEventName && this._fullScreenListener) {\n            this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n        }\n    }\n    _createContainer() {\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n    }\n    _adjustParentForFullscreenChange() {\n        if (!this._containerElement) {\n            return;\n        }\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n    }\n    _addFullscreenChangeListener(fn) {\n        const eventName = this._getEventName();\n        if (eventName) {\n            if (this._fullScreenListener) {\n                this._document.removeEventListener(eventName, this._fullScreenListener);\n            }\n            this._document.addEventListener(eventName, fn);\n            this._fullScreenListener = fn;\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n}\nFullscreenOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: FullscreenOverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nFullscreenOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,wBAAwB;AACvF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjL,SAASC,mBAAmB,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC/F,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,sBAAsB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,uBAAuB;AACnG,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACnE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGjB,sBAAsB,EAAE;AACxD;AACA;AACA;AACA,MAAMkB,mBAAmB,CAAC;EACtBC,WAAW,CAACC,cAAc,EAAEC,QAAQ,EAAE;IAClC,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,mBAAmB,GAAG;MAAEC,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IAChD,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAGL,QAAQ;EAC7B;EACA;EACAM,MAAM,GAAG,CAAE;EACX;EACAC,MAAM,GAAG;IACL,IAAI,IAAI,CAACC,aAAa,EAAE,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACK,eAAe;MAC3C,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACZ,cAAc,CAACa,yBAAyB,EAAE;MAC9E;MACA,IAAI,CAACX,mBAAmB,CAACE,IAAI,GAAGM,IAAI,CAACI,KAAK,CAACV,IAAI,IAAI,EAAE;MACrD,IAAI,CAACF,mBAAmB,CAACC,GAAG,GAAGO,IAAI,CAACI,KAAK,CAACX,GAAG,IAAI,EAAE;MACnD;MACA;MACAO,IAAI,CAACI,KAAK,CAACV,IAAI,GAAG5B,mBAAmB,CAAC,CAAC,IAAI,CAACoC,uBAAuB,CAACR,IAAI,CAAC;MACzEM,IAAI,CAACI,KAAK,CAACX,GAAG,GAAG3B,mBAAmB,CAAC,CAAC,IAAI,CAACoC,uBAAuB,CAACT,GAAG,CAAC;MACvEO,IAAI,CAACK,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACX,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAY,OAAO,GAAG;IACN,IAAI,IAAI,CAACZ,UAAU,EAAE;MACjB,MAAMa,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;MAC3C,MAAMQ,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAK;MAC5B,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAAClB,UAAU,GAAG,KAAK;MACvBe,SAAS,CAAChB,IAAI,GAAG,IAAI,CAACF,mBAAmB,CAACE,IAAI;MAC9CgB,SAAS,CAACjB,GAAG,GAAG,IAAI,CAACD,mBAAmB,CAACC,GAAG;MAC5Ce,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI5B,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACf,uBAAuB,CAACR,IAAI,EAAE,IAAI,CAACQ,uBAAuB,CAACT,GAAG,CAAC;MAClF,IAAIN,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAf,aAAa,GAAG;IACZ;IACA;IACA;IACA,MAAMS,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;IAC3C,IAAIO,IAAI,CAACH,SAAS,CAACa,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACvB,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMc,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;IAChC,MAAMU,QAAQ,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,eAAe,EAAE;IACtD,OAAOX,IAAI,CAACY,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIb,IAAI,CAACc,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACnF;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wCAAwC,GAAG;EAChD,OAAOC,KAAK,CAAE,4CAA2C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBtC,WAAW,CAACuC,iBAAiB,EAAEC,OAAO,EAAEvC,cAAc,EAAEwC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACvC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACzB,OAAO,EAAE;MACd,IAAI,IAAI,CAAC0B,WAAW,CAACC,WAAW,EAAE,EAAE;QAChC,IAAI,CAACL,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,EAAE,CAAC;MACrD;IACJ,CAAC;EACL;EACA;EACAvC,MAAM,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,EAAE;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAM,GAAG;IACL,IAAI,IAAI,CAACiC,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACX,iBAAiB,CAACY,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAACpE,MAAM,CAACqE,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACT,WAAW,CAACU,cAAc,CAACzB,QAAQ,CAACwB,UAAU,CAACE,aAAa,EAAE,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACf,OAAO,IAAI,IAAI,CAACA,OAAO,CAACgB,SAAS,IAAI,IAAI,CAAChB,OAAO,CAACgB,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACzD,cAAc,CAACa,yBAAyB,EAAE,CAACV,GAAG;MACjF,IAAI,CAACsC,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,EAAE,CAACV,GAAG;QAC1E,IAAIyD,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACF,sBAAsB,CAAC,GAAG,IAAI,CAACjB,OAAO,CAACgB,SAAS,EAAE;UACjF,IAAI,CAACd,OAAO,EAAE;QAClB,CAAC,MACI;UACD,IAAI,CAACC,WAAW,CAACmB,cAAc,EAAE;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACrB,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,IAAI,CAAChB,OAAO,CAAC;IAC7D;EACJ;EACA;EACAzB,OAAO,GAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,EAAE;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAM,GAAG;IACL,IAAI,CAAC7B,OAAO,EAAE;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,kBAAkB,CAAC;EACrB;EACAxD,MAAM,GAAG,CAAE;EACX;EACAS,OAAO,GAAG,CAAE;EACZ;EACAV,MAAM,GAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,4BAA4B,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAAClE,GAAG;IACzD,MAAMqE,YAAY,GAAGN,OAAO,CAAC/D,GAAG,GAAGkE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACjE,IAAI;IACxD,MAAMuE,YAAY,GAAGT,OAAO,CAAC9D,IAAI,GAAGiE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2B,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAAC/D,GAAG,GAAG0E,mBAAmB,CAAC1E,GAAG;IAC1D,MAAM4E,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAAC9D,IAAI,GAAGyE,mBAAmB,CAACzE,IAAI;IAC3D,MAAM6E,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BnF,WAAW,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACnC;EACA;EACAlC,MAAM,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,EAAE;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAM,GAAG;IACL,IAAI,CAAC,IAAI,CAACiC,mBAAmB,EAAE;MAC3B,MAAM0C,QAAQ,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAAC3C,mBAAmB,GAAG,IAAI,CAACH,iBAAiB,CAACY,QAAQ,CAACiC,QAAQ,CAAC,CAACzB,SAAS,CAAC,MAAM;QACjF,IAAI,CAACf,WAAW,CAACmB,cAAc,EAAE;QACjC;QACA,IAAI,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC3C,WAAW,CAACU,cAAc,CAACkC,qBAAqB,EAAE;UAC3E,MAAM;YAAErD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAChC,cAAc,CAAC8B,eAAe,EAAE;UAC/D;UACA;UACA,MAAM0D,WAAW,GAAG,CAAC;YAAEtD,KAAK;YAAEF,MAAM;YAAEuC,MAAM,EAAEvC,MAAM;YAAE0C,KAAK,EAAExC,KAAK;YAAE/B,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAI6D,4BAA4B,CAACqB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAACvE,OAAO,EAAE;YACd,IAAI,CAACsB,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,EAAE,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA7B,OAAO,GAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,EAAE;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAM,GAAG;IACL,IAAI,CAAC7B,OAAO,EAAE;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,qBAAqB,CAAC;EACxB1F,WAAW,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEtC,QAAQ,EAAE;IAC9D,IAAI,CAACqC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACmD,IAAI,GAAG,MAAM,IAAI1B,kBAAkB,EAAE;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC2B,KAAK,GAAIC,MAAM,IAAK,IAAIvD,mBAAmB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACvC,cAAc,EAAE4F,MAAM,CAAC;IACnH;IACA,IAAI,CAACC,KAAK,GAAG,MAAM,IAAI/F,mBAAmB,CAAC,IAAI,CAACE,cAAc,EAAE,IAAI,CAACM,SAAS,CAAC;IAC/E;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwF,UAAU,GAAIF,MAAM,IAAK,IAAIV,wBAAwB,CAAC,IAAI,CAAC5C,iBAAiB,EAAE,IAAI,CAACtC,cAAc,EAAE,IAAI,CAACuC,OAAO,EAAEqD,MAAM,CAAC;IAC7H,IAAI,CAACtF,SAAS,GAAGL,QAAQ;EAC7B;AACJ;AACAwF,qBAAqB,CAACM,IAAI;EAAA,iBAA6FN,qBAAqB,EAA/B9H,EAAE,UAA+CP,EAAE,CAACG,gBAAgB,GAApEI,EAAE,UAA+EP,EAAE,CAACI,aAAa,GAAjGG,EAAE,UAA4GA,EAAE,CAACqI,MAAM,GAAvHrI,EAAE,UAAkID,QAAQ;AAAA,CAA6C;AACtS+H,qBAAqB,CAACQ,KAAK,kBADkFtI,EAAE;EAAA,OACY8H,qBAAqB;EAAA,SAArBA,qBAAqB;EAAA,YAAc;AAAM,EAAG;AACvK;EAAA,mDAF6G9H,EAAE,mBAEf8H,qBAAqB,EAAc,CAAC;IACxHS,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE9I,EAAE,CAACG;IAAiB,CAAC,EAAE;MAAE2I,IAAI,EAAE9I,EAAE,CAACI;IAAc,CAAC,EAAE;MAAE0I,IAAI,EAAEvI,EAAE,CAACqI;IAAO,CAAC,EAAE;MAAEE,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9IJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6I,aAAa,CAAC;EAChBxG,WAAW,CAAC6F,MAAM,EAAE;IAChB;IACA,IAAI,CAACY,cAAc,GAAG,IAAIxC,kBAAkB,EAAE;IAC9C;IACA,IAAI,CAACyC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,aAAa,GAAG,2BAA2B;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAIhB,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAMiB,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACnB,MAAM,CAAC;MACtC,KAAK,MAAMoB,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAIjB,MAAM,CAACoB,GAAG,CAAC,KAAKX,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACW,GAAG,CAAC,GAAGpB,MAAM,CAACoB,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBlH,WAAW,CAACmH,MAAM,EAAEC,OAAO,EAC3B;EACAC,OAAO,EACP;EACAC,OAAO,EACP;EACAZ,UAAU,EAAE;IACR,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACZ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,OAAO,GAAGJ,MAAM,CAACI,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACK,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACK,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;AAE1B;AACA,MAAMC,8BAA8B,CAAC;EACjC5H,WAAW,EACX;EACA6H,cAAc,EACd;EACAC,wBAAwB,EAAE;IACtB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwB,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAM5F,KAAK,CAAE,8BAA6B2F,QAAS,KAAIC,KAAM,KAAI,GAC5D,uCAAsC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0B,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAM5F,KAAK,CAAE,8BAA6B2F,QAAS,KAAIC,KAAM,KAAI,GAC5D,sCAAqC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxBnI,WAAW,CAACE,QAAQ,EAAE;IAClB;IACA,IAAI,CAACkI,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC7H,SAAS,GAAGL,QAAQ;EAC7B;EACAmI,WAAW,GAAG;IACV,IAAI,CAACtF,MAAM,EAAE;EACjB;EACA;EACA9B,GAAG,CAAC+B,UAAU,EAAE;IACZ;IACA,IAAI,CAACtB,MAAM,CAACsB,UAAU,CAAC;IACvB,IAAI,CAACoF,iBAAiB,CAACE,IAAI,CAACtF,UAAU,CAAC;EAC3C;EACA;EACAtB,MAAM,CAACsB,UAAU,EAAE;IACf,MAAMuF,KAAK,GAAG,IAAI,CAACH,iBAAiB,CAACI,OAAO,CAACxF,UAAU,CAAC;IACxD,IAAIuF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACH,iBAAiB,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACH,iBAAiB,CAACM,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC3F,MAAM,EAAE;IACjB;EACJ;AACJ;AACAoF,qBAAqB,CAACnC,IAAI;EAAA,iBAA6FmC,qBAAqB,EA7L/BvK,EAAE,UA6L+CD,QAAQ;AAAA,CAA6C;AACnNwK,qBAAqB,CAACjC,KAAK,kBA9LkFtI,EAAE;EAAA,OA8LYuK,qBAAqB;EAAA,SAArBA,qBAAqB;EAAA,YAAc;AAAM,EAAG;AACvK;EAAA,mDA/L6GvK,EAAE,mBA+LfuK,qBAAqB,EAAc,CAAC;IACxHhC,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgL,yBAAyB,SAASR,qBAAqB,CAAC;EAC1DnI,WAAW,CAACE,QAAQ,EACpB;EACAsC,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAACsC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACoG,gBAAgB,GAAIC,KAAK,IAAK;MAC/B,MAAMC,QAAQ,GAAG,IAAI,CAACV,iBAAiB;MACvC,KAAK,IAAIW,CAAC,GAAGD,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAEK,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACP,MAAM,GAAG,CAAC,EAAE;UACjD,MAAMQ,aAAa,GAAGJ,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc;UAChD;UACA,IAAI,IAAI,CAACxG,OAAO,EAAE;YACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAMoG,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC,CAAC;UACrD,CAAC,MACI;YACDK,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC;UAC7B;UACA;QACJ;MACJ;IACJ,CAAC;EACL;EACA;EACA5H,GAAG,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACoG,WAAW,EAAE;MACnB;MACA,IAAI,IAAI,CAAC5G,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC6G,iBAAiB,CAAC,MAAM,IAAI,CAAC9I,SAAS,CAACa,IAAI,CAACkI,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC,CAAC;MAChH,CAAC,MACI;QACD,IAAI,CAACrI,SAAS,CAACa,IAAI,CAACkI,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC;MAC1E;MACA,IAAI,CAACQ,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACArG,MAAM,GAAG;IACL,IAAI,IAAI,CAACqG,WAAW,EAAE;MAClB,IAAI,CAAC7I,SAAS,CAACa,IAAI,CAACmI,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACX,gBAAgB,CAAC;MACzE,IAAI,CAACQ,WAAW,GAAG,KAAK;IAC5B;EACJ;AACJ;AACAT,yBAAyB,CAAC3C,IAAI;EAAA,iBAA6F2C,yBAAyB,EAxQvC/K,EAAE,UAwQuDD,QAAQ,GAxQjEC,EAAE,UAwQ4EA,EAAE,CAACqI,MAAM;AAAA,CAA6D;AACjQ0C,yBAAyB,CAACzC,KAAK,kBAzQ8EtI,EAAE;EAAA,OAyQgB+K,yBAAyB;EAAA,SAAzBA,yBAAyB;EAAA,YAAc;AAAM,EAAG;AAC/K;EAAA,mDA1Q6G/K,EAAE,mBA0Qf+K,yBAAyB,EAAc,CAAC;IAC5HxC,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEvI,EAAE,CAACqI,MAAM;MAAEM,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEpI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyL,6BAA6B,SAASrB,qBAAqB,CAAC;EAC9DnI,WAAW,CAACE,QAAQ,EAAEuJ,SAAS,EAC/B;EACAjH,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAACuJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACjH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkH,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACC,oBAAoB,GAAId,KAAK,IAAK;MACnC,IAAI,CAACe,uBAAuB,GAAG9K,eAAe,CAAC+J,KAAK,CAAC;IACzD,CAAC;IACD;IACA,IAAI,CAACgB,cAAc,GAAIhB,KAAK,IAAK;MAC7B,MAAMiB,MAAM,GAAGhL,eAAe,CAAC+J,KAAK,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA,MAAM1B,MAAM,GAAG0B,KAAK,CAAC1C,IAAI,KAAK,OAAO,IAAI,IAAI,CAACyD,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BE,MAAM;MACZ;MACA;MACA,IAAI,CAACF,uBAAuB,GAAG,IAAI;MACnC;MACA;MACA;MACA,MAAMd,QAAQ,GAAG,IAAI,CAACV,iBAAiB,CAAC2B,KAAK,EAAE;MAC/C;MACA;MACA;MACA;MACA,KAAK,IAAIhB,CAAC,GAAGD,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAEK,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAM/F,UAAU,GAAG8F,QAAQ,CAACC,CAAC,CAAC;QAC9B,IAAI/F,UAAU,CAACgH,qBAAqB,CAACf,SAAS,CAACP,MAAM,GAAG,CAAC,IAAI,CAAC1F,UAAU,CAACH,WAAW,EAAE,EAAE;UACpF;QACJ;QACA;QACA;QACA;QACA,IAAIG,UAAU,CAACM,cAAc,CAACzB,QAAQ,CAACiI,MAAM,CAAC,IAC1C9G,UAAU,CAACM,cAAc,CAACzB,QAAQ,CAACsF,MAAM,CAAC,EAAE;UAC5C;QACJ;QACA,MAAM8C,oBAAoB,GAAGjH,UAAU,CAACgH,qBAAqB;QAC7D;QACA,IAAI,IAAI,CAACxH,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAMmH,oBAAoB,CAACd,IAAI,CAACN,KAAK,CAAC,CAAC;QAC5D,CAAC,MACI;UACDoB,oBAAoB,CAACd,IAAI,CAACN,KAAK,CAAC;QACpC;MACJ;IACJ,CAAC;EACL;EACA;EACA5H,GAAG,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACoG,WAAW,EAAE;MACnB,MAAMhI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC;MACA,IAAI,IAAI,CAACoB,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC6G,iBAAiB,CAAC,MAAM,IAAI,CAACa,kBAAkB,CAAC9I,IAAI,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAAC8I,kBAAkB,CAAC9I,IAAI,CAAC;MACjC;MACA;MACA;MACA,IAAI,IAAI,CAACqI,SAAS,CAACU,GAAG,IAAI,CAAC,IAAI,CAACT,iBAAiB,EAAE;QAC/C,IAAI,CAACU,oBAAoB,GAAGhJ,IAAI,CAACL,KAAK,CAACsJ,MAAM;QAC7CjJ,IAAI,CAACL,KAAK,CAACsJ,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACX,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAACN,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACArG,MAAM,GAAG;IACL,IAAI,IAAI,CAACqG,WAAW,EAAE;MAClB,MAAMhI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChCA,IAAI,CAACmI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACI,oBAAoB,EAAE,IAAI,CAAC;MACxEvI,IAAI,CAACmI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACM,cAAc,EAAE,IAAI,CAAC;MAC5DzI,IAAI,CAACmI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACM,cAAc,EAAE,IAAI,CAAC;MAC/DzI,IAAI,CAACmI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACM,cAAc,EAAE,IAAI,CAAC;MAClE,IAAI,IAAI,CAACJ,SAAS,CAACU,GAAG,IAAI,IAAI,CAACT,iBAAiB,EAAE;QAC9CtI,IAAI,CAACL,KAAK,CAACsJ,MAAM,GAAG,IAAI,CAACD,oBAAoB;QAC7C,IAAI,CAACV,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAACN,WAAW,GAAG,KAAK;IAC5B;EACJ;EACAc,kBAAkB,CAAC9I,IAAI,EAAE;IACrBA,IAAI,CAACkI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACK,oBAAoB,EAAE,IAAI,CAAC;IACrEvI,IAAI,CAACkI,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;IACzDzI,IAAI,CAACkI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;IAC5DzI,IAAI,CAACkI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;EACnE;AACJ;AACAL,6BAA6B,CAACxD,IAAI;EAAA,iBAA6FwD,6BAA6B,EA5Y/C5L,EAAE,UA4Y+DD,QAAQ,GA5YzEC,EAAE,UA4YoFgB,IAAI,CAAC0L,QAAQ,GA5YnG1M,EAAE,UA4Y8GA,EAAE,CAACqI,MAAM;AAAA,CAA6D;AACnSuD,6BAA6B,CAACtD,KAAK,kBA7Y0EtI,EAAE;EAAA,OA6YoB4L,6BAA6B;EAAA,SAA7BA,6BAA6B;EAAA,YAAc;AAAM,EAAG;AACvL;EAAA,mDA9Y6G5L,EAAE,mBA8Yf4L,6BAA6B,EAAc,CAAC;IAChIrD,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEvH,IAAI,CAAC0L;IAAS,CAAC,EAAE;MAAEnE,IAAI,EAAEvI,EAAE,CAACqI,MAAM;MAAEM,UAAU,EAAE,CAAC;QAC3DJ,IAAI,EAAEpI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwM,gBAAgB,CAAC;EACnBvK,WAAW,CAACE,QAAQ,EAAEuJ,SAAS,EAAE;IAC7B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAClJ,SAAS,GAAGL,QAAQ;EAC7B;EACAmI,WAAW,GAAG;IACV,IAAI,CAACmC,iBAAiB,EAAE9I,MAAM,EAAE;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+I,mBAAmB,GAAG;IAClB,IAAI,CAAC,IAAI,CAACD,iBAAiB,EAAE;MACzB,IAAI,CAACE,gBAAgB,EAAE;IAC3B;IACA,OAAO,IAAI,CAACF,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACIE,gBAAgB,GAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAAClB,SAAS,CAACmB,SAAS,IAAI7L,kBAAkB,EAAE,EAAE;MAClD,MAAM8L,0BAA0B,GAAG,IAAI,CAACtK,SAAS,CAACuK,gBAAgB,CAAE,IAAGH,cAAe,uBAAsB,GAAI,IAAGA,cAAe,mBAAkB,CAAC;MACrJ;MACA;MACA,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,0BAA0B,CAACnC,MAAM,EAAEK,CAAC,EAAE,EAAE;QACxD8B,0BAA0B,CAAC9B,CAAC,CAAC,CAACrH,MAAM,EAAE;MAC1C;IACJ;IACA,MAAMqJ,SAAS,GAAG,IAAI,CAACxK,SAAS,CAACyK,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAAC/J,SAAS,CAACC,GAAG,CAAC0J,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI5L,kBAAkB,EAAE,EAAE;MACtBgM,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAACxB,SAAS,CAACmB,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAAC1K,SAAS,CAACa,IAAI,CAAC8J,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACP,iBAAiB,GAAGO,SAAS;EACtC;AACJ;AACAR,gBAAgB,CAACvE,IAAI;EAAA,iBAA6FuE,gBAAgB,EA1drB3M,EAAE,UA0dqCD,QAAQ,GA1d/CC,EAAE,UA0d0DgB,IAAI,CAAC0L,QAAQ;AAAA,CAA6C;AACnOC,gBAAgB,CAACrE,KAAK,kBA3duFtI,EAAE;EAAA,OA2dO2M,gBAAgB;EAAA,SAAhBA,gBAAgB;EAAA,YAAc;AAAM,EAAG;AAC7J;EAAA,mDA5d6G3M,EAAE,mBA4df2M,gBAAgB,EAAc,CAAC;IACnHpE,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEvH,IAAI,CAAC0L;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,UAAU,CAAC;EACbnL,WAAW,CAACoL,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAE7I,OAAO,EAAED,OAAO,EAAE+I,mBAAmB,EAAEhL,SAAS,EAAEiL,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAE;IACxJ,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC7I,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+I,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAChL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAInM,OAAO,EAAE;IACnC,IAAI,CAACoM,YAAY,GAAG,IAAIpM,OAAO,EAAE;IACjC,IAAI,CAACqM,YAAY,GAAG,IAAIrM,OAAO,EAAE;IACjC,IAAI,CAACsM,gBAAgB,GAAGrM,YAAY,CAACsM,KAAK;IAC1C,IAAI,CAACC,qBAAqB,GAAIpD,KAAK,IAAK,IAAI,CAAC+C,cAAc,CAACzC,IAAI,CAACN,KAAK,CAAC;IACvE,IAAI,CAACqD,6BAA6B,GAAIrD,KAAK,IAAK;MAC5C,IAAI,CAACsD,gBAAgB,CAACtD,KAAK,CAACiB,MAAM,CAAC;IACvC,CAAC;IACD;IACA,IAAI,CAACd,cAAc,GAAG,IAAIvJ,OAAO,EAAE;IACnC;IACA,IAAI,CAACuK,qBAAqB,GAAG,IAAIvK,OAAO,EAAE;IAC1C,IAAIgD,OAAO,CAACgE,cAAc,EAAE;MACxB,IAAI,CAAC2F,eAAe,GAAG3J,OAAO,CAACgE,cAAc;MAC7C,IAAI,CAAC2F,eAAe,CAAC5L,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAAC6L,iBAAiB,GAAG5J,OAAO,CAAC6J,gBAAgB;EACrD;EACA;EACA,IAAIhJ,cAAc,GAAG;IACjB,OAAO,IAAI,CAACgI,KAAK;EACrB;EACA;EACA,IAAIiB,eAAe,GAAG;IAClB,OAAO,IAAI,CAACZ,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIa,WAAW,GAAG;IACd,OAAO,IAAI,CAACnB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7K,MAAM,CAACiM,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACqB,aAAa,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAACzB,WAAW,CAAC,IAAI,CAACG,KAAK,CAAC;IACpD;IACA,MAAMuB,YAAY,GAAG,IAAI,CAACxB,aAAa,CAAC5K,MAAM,CAACiM,MAAM,CAAC;IACtD,IAAI,IAAI,CAACJ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC7L,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACqM,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,IAAI,CAACX,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC3L,MAAM,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAI,CAAC+B,OAAO,CAACwK,QAAQ,CAAC5J,IAAI,CAACnE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0E,SAAS,CAAC,MAAM;MAChD;MACA,IAAI,IAAI,CAACd,WAAW,EAAE,EAAE;QACpB,IAAI,CAACkB,cAAc,EAAE;MACzB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACkJ,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACxK,OAAO,CAACkE,WAAW,EAAE;MAC1B,IAAI,CAACuG,eAAe,EAAE;IAC1B;IACA,IAAI,IAAI,CAACzK,OAAO,CAACiE,UAAU,EAAE;MACzB,IAAI,CAACyG,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAE,IAAI,CAAC7I,OAAO,CAACiE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAACmF,YAAY,CAAC1C,IAAI,EAAE;IACxB;IACA,IAAI,CAACoC,mBAAmB,CAACtK,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAACwB,OAAO,CAACoE,mBAAmB,EAAE;MAClC,IAAI,CAACkF,gBAAgB,GAAG,IAAI,CAACP,SAAS,CAAC7H,SAAS,CAAC,MAAM,IAAI,CAACyJ,OAAO,EAAE,CAAC;IAC1E;IACA,IAAI,CAAC3B,uBAAuB,CAACxK,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,OAAO2L,YAAY,EAAES,SAAS,KAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAT,YAAY,CAACS,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAACxK,WAAW,EAAE,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACL,OAAO,CAAC6G,iBAAiB,CAAC,MAAMiE,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM,IAAI,CAACzK,MAAM,EAAE,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAO6J,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACI7J,MAAM,GAAG;IACL,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE,EAAE;MACrB;IACJ;IACA,IAAI,CAAC4K,cAAc,EAAE;IACrB;IACA;IACA;IACA,IAAI,CAACR,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAACZ,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACtJ,MAAM,EAAE;MACzD,IAAI,CAACsJ,iBAAiB,CAACtJ,MAAM,EAAE;IACnC;IACA,IAAI,IAAI,CAACqJ,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAClL,OAAO,EAAE;IAClC;IACA,MAAMwM,gBAAgB,GAAG,IAAI,CAACtC,aAAa,CAACrI,MAAM,EAAE;IACpD;IACA,IAAI,CAAC+I,YAAY,CAAC3C,IAAI,EAAE;IACxB;IACA,IAAI,CAACoC,mBAAmB,CAAC7J,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAACiM,wBAAwB,EAAE;IAC/B,IAAI,CAAC5B,gBAAgB,CAAC/H,WAAW,EAAE;IACnC,IAAI,CAACyH,uBAAuB,CAAC/J,MAAM,CAAC,IAAI,CAAC;IACzC,OAAOgM,gBAAgB;EAC3B;EACA;EACAN,OAAO,GAAG;IACN,MAAMQ,UAAU,GAAG,IAAI,CAAC/K,WAAW,EAAE;IACrC,IAAI,IAAI,CAACwJ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACe,OAAO,EAAE;IACpC;IACA,IAAI,CAACS,sBAAsB,EAAE;IAC7B,IAAI,CAAC1B,gBAAgB,CAAC,IAAI,CAACR,gBAAgB,CAAC;IAC5C,IAAI,CAACI,gBAAgB,CAAC/H,WAAW,EAAE;IACnC,IAAI,CAACuH,mBAAmB,CAAC7J,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC0J,aAAa,CAACgC,OAAO,EAAE;IAC5B,IAAI,CAACvB,YAAY,CAACiC,QAAQ,EAAE;IAC5B,IAAI,CAAClC,cAAc,CAACkC,QAAQ,EAAE;IAC9B,IAAI,CAAC9E,cAAc,CAAC8E,QAAQ,EAAE;IAC9B,IAAI,CAAC9D,qBAAqB,CAAC8D,QAAQ,EAAE;IACrC,IAAI,CAACrC,uBAAuB,CAAC/J,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI,CAAC2J,KAAK,EAAE3J,MAAM,EAAE;IACpB,IAAI,CAACiL,mBAAmB,GAAG,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI;IACzD,IAAIuC,UAAU,EAAE;MACZ,IAAI,CAAC9B,YAAY,CAAC3C,IAAI,EAAE;IAC5B;IACA,IAAI,CAAC2C,YAAY,CAACgC,QAAQ,EAAE;EAChC;EACA;EACAjL,WAAW,GAAG;IACV,OAAO,IAAI,CAACuI,aAAa,CAACvI,WAAW,EAAE;EAC3C;EACA;EACAkL,aAAa,GAAG;IACZ,OAAO,IAAI,CAACnC,cAAc;EAC9B;EACA;EACAoC,WAAW,GAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,WAAW,GAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACA5C,aAAa,GAAG;IACZ,OAAO,IAAI,CAACF,cAAc;EAC9B;EACA;EACAiB,oBAAoB,GAAG;IACnB,OAAO,IAAI,CAACD,qBAAqB;EACrC;EACA;EACAkE,SAAS,GAAG;IACR,OAAO,IAAI,CAACzL,OAAO;EACvB;EACA;EACAsB,cAAc,GAAG;IACb,IAAI,IAAI,CAACsI,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC8B,KAAK,EAAE;IAClC;EACJ;EACA;EACAC,sBAAsB,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAAChC,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACe,OAAO,EAAE;IACpC;IACA,IAAI,CAACf,iBAAiB,GAAGgC,QAAQ;IACjC,IAAI,IAAI,CAACxL,WAAW,EAAE,EAAE;MACpBwL,QAAQ,CAAC7N,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAACuD,cAAc,EAAE;IACzB;EACJ;EACA;EACAuK,UAAU,CAACC,UAAU,EAAE;IACnB,IAAI,CAAC9L,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAG8L;IAAW,CAAC;IACjD,IAAI,CAACzB,kBAAkB,EAAE;EAC7B;EACA;EACA0B,YAAY,CAACC,GAAG,EAAE;IACd,IAAI,CAAChM,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAEiM,SAAS,EAAED;IAAI,CAAC;IAClD,IAAI,CAAC1B,uBAAuB,EAAE;EAClC;EACA;EACA4B,aAAa,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAEsD,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgB,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAEsD,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAY,GAAG;IACX,MAAMJ,SAAS,GAAG,IAAI,CAACjM,OAAO,CAACiM,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAACzG,KAAK;EACtE;EACA;EACA8G,oBAAoB,CAACV,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACjC,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAACyB,sBAAsB,EAAE;IAC7B,IAAI,CAACzB,eAAe,GAAGiC,QAAQ;IAC/B,IAAI,IAAI,CAACxL,WAAW,EAAE,EAAE;MACpBwL,QAAQ,CAAC7N,MAAM,CAAC,IAAI,CAAC;MACrB6N,QAAQ,CAAC5N,MAAM,EAAE;IACrB;EACJ;EACA;EACAsM,uBAAuB,GAAG;IACtB,IAAI,CAAC1B,KAAK,CAACJ,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC6D,YAAY,EAAE,CAAC;EACvD;EACA;EACAhC,kBAAkB,GAAG;IACjB,IAAI,CAAC,IAAI,CAACxB,KAAK,EAAE;MACb;IACJ;IACA,MAAMvK,KAAK,GAAG,IAAI,CAACuK,KAAK,CAACvK,KAAK;IAC9BA,KAAK,CAACoB,KAAK,GAAG1D,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACN,KAAK,CAAC;IACrDpB,KAAK,CAACkB,MAAM,GAAGxD,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACR,MAAM,CAAC;IACvDlB,KAAK,CAACiO,QAAQ,GAAGvQ,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACuM,QAAQ,CAAC;IAC3DjO,KAAK,CAACkO,SAAS,GAAGxQ,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACwM,SAAS,CAAC;IAC7DlO,KAAK,CAACmO,QAAQ,GAAGzQ,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACyM,QAAQ,CAAC;IAC3DnO,KAAK,CAACoO,SAAS,GAAG1Q,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAAC0M,SAAS,CAAC;EACjE;EACA;EACAlC,oBAAoB,CAACmC,aAAa,EAAE;IAChC,IAAI,CAAC9D,KAAK,CAACvK,KAAK,CAACsO,aAAa,GAAGD,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACAlC,eAAe,GAAG;IACd,MAAMoC,YAAY,GAAG,8BAA8B;IACnD,IAAI,CAAC3D,gBAAgB,GAAG,IAAI,CAACpL,SAAS,CAACyK,aAAa,CAAC,KAAK,CAAC;IAC3D,IAAI,CAACW,gBAAgB,CAAC3K,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAC3D,IAAI,IAAI,CAACyK,mBAAmB,EAAE;MAC1B,IAAI,CAACC,gBAAgB,CAAC3K,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAC9E;IACA,IAAI,IAAI,CAACwB,OAAO,CAACmE,aAAa,EAAE;MAC5B,IAAI,CAACuG,cAAc,CAAC,IAAI,CAACxB,gBAAgB,EAAE,IAAI,CAAClJ,OAAO,CAACmE,aAAa,EAAE,IAAI,CAAC;IAChF;IACA;IACA;IACA,IAAI,CAACyE,KAAK,CAACqB,aAAa,CAAC6C,YAAY,CAAC,IAAI,CAAC5D,gBAAgB,EAAE,IAAI,CAACN,KAAK,CAAC;IACxE;IACA;IACA,IAAI,CAACM,gBAAgB,CAACrC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC2C,qBAAqB,CAAC;IAC3E;IACA,IAAI,CAAC,IAAI,CAACP,mBAAmB,IAAI,OAAO8D,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAAChN,OAAO,CAAC6G,iBAAiB,CAAC,MAAM;QACjCmG,qBAAqB,CAAC,MAAM;UACxB,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;YACvB,IAAI,CAACA,gBAAgB,CAAC3K,SAAS,CAACC,GAAG,CAACqO,YAAY,CAAC;UACrD;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC3D,gBAAgB,CAAC3K,SAAS,CAACC,GAAG,CAACqO,YAAY,CAAC;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIzC,oBAAoB,GAAG;IACnB,IAAI,IAAI,CAACxB,KAAK,CAACoE,WAAW,EAAE;MACxB,IAAI,CAACpE,KAAK,CAACqE,UAAU,CAACxE,WAAW,CAAC,IAAI,CAACG,KAAK,CAAC;IACjD;EACJ;EACA;EACAoC,cAAc,GAAG;IACb,MAAMkC,gBAAgB,GAAG,IAAI,CAAChE,gBAAgB;IAC9C,IAAI,CAACgE,gBAAgB,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACjE,mBAAmB,EAAE;MAC1B,IAAI,CAACS,gBAAgB,CAACwD,gBAAgB,CAAC;MACvC;IACJ;IACAA,gBAAgB,CAAC3O,SAAS,CAACU,MAAM,CAAC,8BAA8B,CAAC;IACjE,IAAI,CAACc,OAAO,CAAC6G,iBAAiB,CAAC,MAAM;MACjCsG,gBAAgB,CAACrG,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC4C,6BAA6B,CAAC;IAC1F,CAAC,CAAC;IACF;IACA;IACAyD,gBAAgB,CAAC5O,KAAK,CAACsO,aAAa,GAAG,MAAM;IAC7C;IACA;IACA;IACA,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAACpN,OAAO,CAAC6G,iBAAiB,CAAC,MAAMwG,UAAU,CAAC,MAAM;MAC1E,IAAI,CAAC1D,gBAAgB,CAACwD,gBAAgB,CAAC;IAC3C,CAAC,EAAE,GAAG,CAAC,CAAC;EACZ;EACA;EACAxC,cAAc,CAAChJ,OAAO,EAAE2L,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMnB,OAAO,GAAGlQ,WAAW,CAACoR,UAAU,IAAI,EAAE,CAAC,CAAC9Q,MAAM,CAACgR,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAIpB,OAAO,CAAClG,MAAM,EAAE;MAChBqH,KAAK,GAAG5L,OAAO,CAACnD,SAAS,CAACC,GAAG,CAAC,GAAG2N,OAAO,CAAC,GAAGzK,OAAO,CAACnD,SAAS,CAACU,MAAM,CAAC,GAAGkN,OAAO,CAAC;IACpF;EACJ;EACA;EACAjB,wBAAwB,GAAG;IACvB;IACA;IACA;IACA,IAAI,CAACnL,OAAO,CAAC6G,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAM4G,YAAY,GAAG,IAAI,CAACzN,OAAO,CAACwK,QAAQ,CACrC5J,IAAI,CAAClE,SAAS,CAACS,KAAK,CAAC,IAAI,CAACkM,YAAY,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAC5DnI,SAAS,CAAC,MAAM;QACjB;QACA;QACA,IAAI,CAAC,IAAI,CAAC2H,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAAC4E,QAAQ,CAACxH,MAAM,KAAK,CAAC,EAAE;UAChE,IAAI,IAAI,CAAC4C,KAAK,IAAI,IAAI,CAAC7I,OAAO,CAACiE,UAAU,EAAE;YACvC,IAAI,CAACyG,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAE,IAAI,CAAC7I,OAAO,CAACiE,UAAU,EAAE,KAAK,CAAC;UACnE;UACA,IAAI,IAAI,CAAC2E,KAAK,IAAI,IAAI,CAACA,KAAK,CAACqB,aAAa,EAAE;YACxC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACtB,KAAK,CAACqB,aAAa;YACnD,IAAI,CAACrB,KAAK,CAAC3J,MAAM,EAAE;UACvB;UACAuO,YAAY,CAACjM,WAAW,EAAE;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA6J,sBAAsB,GAAG;IACrB,MAAMpH,cAAc,GAAG,IAAI,CAAC2F,eAAe;IAC3C,IAAI3F,cAAc,EAAE;MAChBA,cAAc,CAACvF,OAAO,EAAE;MACxB,IAAIuF,cAAc,CAAC1D,MAAM,EAAE;QACvB0D,cAAc,CAAC1D,MAAM,EAAE;MAC3B;IACJ;EACJ;EACA;EACAoJ,gBAAgB,CAACgE,QAAQ,EAAE;IACvB,IAAIA,QAAQ,EAAE;MACVA,QAAQ,CAAC5G,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC0C,qBAAqB,CAAC;MACjEkE,QAAQ,CAAC5G,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC2C,6BAA6B,CAAC;MACjFiE,QAAQ,CAACzO,MAAM,EAAE;MACjB;MACA;MACA;MACA,IAAI,IAAI,CAACiK,gBAAgB,KAAKwE,QAAQ,EAAE;QACpC,IAAI,CAACxE,gBAAgB,GAAG,IAAI;MAChC;IACJ;IACA,IAAI,IAAI,CAACiE,gBAAgB,EAAE;MACvBQ,YAAY,CAAC,IAAI,CAACR,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGtJ,SAAS;IACrC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+J,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EACpC;EACA,IAAIC,SAAS,GAAG;IACZ,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACAzQ,WAAW,CAAC0Q,WAAW,EAAEzQ,cAAc,EAAEM,SAAS,EAAEkJ,SAAS,EAAEkH,iBAAiB,EAAE;IAC9E,IAAI,CAAC1Q,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkH,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,oBAAoB,GAAG;MAAEzO,KAAK,EAAE,CAAC;MAAEF,MAAM,EAAE;IAAE,CAAC;IACnD;IACA,IAAI,CAAC4O,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB;IACA,IAAI,CAACV,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACW,gBAAgB,GAAG,IAAI3R,OAAO,EAAE;IACrC;IACA,IAAI,CAAC4R,mBAAmB,GAAG3R,YAAY,CAACsM,KAAK;IAC7C;IACA,IAAI,CAACsF,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACL,gBAAgB;IAC5C,IAAI,CAACM,SAAS,CAAChB,WAAW,CAAC;EAC/B;EACA;EACAlQ,MAAM,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,IAChBI,UAAU,KAAK,IAAI,CAACJ,WAAW,KAC9B,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMZ,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACsP,kBAAkB,EAAE;IACzB3O,UAAU,CAACwJ,WAAW,CAACxL,SAAS,CAACC,GAAG,CAACoP,gBAAgB,CAAC;IACtD,IAAI,CAACzN,WAAW,GAAGI,UAAU;IAC7B,IAAI,CAAC4O,YAAY,GAAG5O,UAAU,CAACwJ,WAAW;IAC1C,IAAI,CAAClB,KAAK,GAAGtI,UAAU,CAACM,cAAc;IACtC,IAAI,CAACuO,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACV,mBAAmB,CAACrN,WAAW,EAAE;IACtC,IAAI,CAACqN,mBAAmB,GAAG,IAAI,CAACpR,cAAc,CAAC+R,MAAM,EAAE,CAACrO,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAACmO,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC3D,KAAK,EAAE;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAK,GAAG;IACJ;IACA,IAAI,IAAI,CAAC0D,WAAW,IAAI,CAAC,IAAI,CAACpI,SAAS,CAACmB,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACkH,gBAAgB,IAAI,IAAI,CAACb,eAAe,IAAI,IAAI,CAACc,aAAa,EAAE;MACtE,IAAI,CAACE,mBAAmB,EAAE;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,uBAAuB,EAAE;IAC9B;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,wBAAwB,EAAE;IACpD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnH,KAAK,CAAC9F,qBAAqB,EAAE;IACtD,IAAI,CAACkN,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAAClG,mBAAmB,EAAE,CAACjF,qBAAqB,EAAE;IAC1F,MAAMmN,UAAU,GAAG,IAAI,CAACJ,WAAW;IACnC,MAAMhN,WAAW,GAAG,IAAI,CAACkN,YAAY;IACrC,MAAMG,YAAY,GAAG,IAAI,CAACP,aAAa;IACvC,MAAMQ,aAAa,GAAG,IAAI,CAACH,cAAc;IACzC;IACA,MAAMI,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACvC,mBAAmB,EAAE;MACtC;MACA,IAAIwC,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE1N,WAAW,EAAEyN,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAE5N,WAAW,EAAEqN,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAAC1C,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2C,cAAc,CAACR,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACQ,6BAA6B,CAACJ,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAACxK,IAAI,CAAC;UACdoL,QAAQ,EAAEV,GAAG;UACb7L,MAAM,EAAE8L,WAAW;UACnB1N,WAAW;UACXoO,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACX,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,EAAE;QACvEd,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAES,QAAQ,EAAEV,GAAG;UAAEzN;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAIuN,YAAY,CAACpK,MAAM,EAAE;MACrB,IAAIoL,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAIlB,YAAY,EAAE;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAACxR,KAAK,GAAG6R,GAAG,CAACL,eAAe,CAAC1R,MAAM,IAAI+R,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACnD,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC2C,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAAC3M,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC2J,QAAQ,EAAE;MACf;MACA,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC2C,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACO,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;EAChE;EACAlQ,MAAM,GAAG;IACL,IAAI,CAACmP,kBAAkB,EAAE;IACzB,IAAI,CAACH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACoC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC9C,mBAAmB,CAACrN,WAAW,EAAE;EAC1C;EACA;EACAoJ,OAAO,GAAG;IACN,IAAI,IAAI,CAACyE,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACD,YAAY,EAAE;MACnBwC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAAC7Q,KAAK,EAAE;QAClCX,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRsE,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACVvC,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACTkS,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAChJ,KAAK,EAAE;MACZ,IAAI,CAAC6G,0BAA0B,EAAE;IACrC;IACA,IAAI,IAAI,CAACvP,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC4J,WAAW,CAACxL,SAAS,CAACU,MAAM,CAAC2O,gBAAgB,CAAC;IACnE;IACA,IAAI,CAACtN,MAAM,EAAE;IACb,IAAI,CAACqO,gBAAgB,CAACtD,QAAQ,EAAE;IAChC,IAAI,CAAClL,WAAW,GAAG,IAAI,CAACgP,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACII,mBAAmB,GAAG;IAClB,IAAI,IAAI,CAACJ,WAAW,IAAI,CAAC,IAAI,CAACpI,SAAS,CAACmB,SAAS,EAAE;MAC/C;IACJ;IACA,MAAM2J,YAAY,GAAG,IAAI,CAACxC,aAAa;IACvC,IAAIwC,YAAY,EAAE;MACd,IAAI,CAAChC,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;MACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnH,KAAK,CAAC9F,qBAAqB,EAAE;MACtD,IAAI,CAAC6M,aAAa,GAAG,IAAI,CAACC,wBAAwB,EAAE;MACpD,IAAI,CAACI,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAAClG,mBAAmB,EAAE,CAACjF,qBAAqB,EAAE;MAC1F,MAAMyN,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACG,cAAc,EAAE6B,YAAY,CAAC;MAC7F,IAAI,CAACf,cAAc,CAACe,YAAY,EAAEtB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC9E,KAAK,EAAE;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqG,wBAAwB,CAACC,WAAW,EAAE;IAClC,IAAI,CAACtD,YAAY,GAAGsD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAa,CAAClE,SAAS,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAGD,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAAChI,OAAO,CAAC,IAAI,CAACuJ,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACJ,kBAAkB,EAAE;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIgD,kBAAkB,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC1D,eAAe,GAAG0D,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAC,sBAAsB,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAAC9D,sBAAsB,GAAG8D,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAC,iBAAiB,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACjE,cAAc,GAAGiE,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAC,QAAQ,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACpE,QAAQ,GAAGoE,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkB,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAACnE,eAAe,GAAGmE,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1D,SAAS,CAACvK,MAAM,EAAE;IACd,IAAI,CAACkO,OAAO,GAAGlO,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACImO,kBAAkB,CAACC,MAAM,EAAE;IACvB,IAAI,CAACjE,QAAQ,GAAGiE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,kBAAkB,CAACD,MAAM,EAAE;IACvB,IAAI,CAAChE,QAAQ,GAAGgE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,qBAAqB,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACC,wBAAwB,GAAGD,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIxC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAI4C,CAAC;IACL,IAAI5C,GAAG,CAACzL,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAqO,CAAC,GAAGjD,UAAU,CAACtS,IAAI,GAAGsS,UAAU,CAACxQ,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAM0T,MAAM,GAAG,IAAI,CAACC,MAAM,EAAE,GAAGnD,UAAU,CAAChO,KAAK,GAAGgO,UAAU,CAACtS,IAAI;MACjE,MAAM0V,IAAI,GAAG,IAAI,CAACD,MAAM,EAAE,GAAGnD,UAAU,CAACtS,IAAI,GAAGsS,UAAU,CAAChO,KAAK;MAC/DiR,CAAC,GAAG5C,GAAG,CAACzL,OAAO,IAAI,OAAO,GAAGsO,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAIlD,aAAa,CAACxS,IAAI,GAAG,CAAC,EAAE;MACxBuV,CAAC,IAAI/C,aAAa,CAACxS,IAAI;IAC3B;IACA,IAAI2V,CAAC;IACL,IAAIhD,GAAG,CAACxL,OAAO,IAAI,QAAQ,EAAE;MACzBwO,CAAC,GAAGrD,UAAU,CAACvS,GAAG,GAAGuS,UAAU,CAAC1Q,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACD+T,CAAC,GAAGhD,GAAG,CAACxL,OAAO,IAAI,KAAK,GAAGmL,UAAU,CAACvS,GAAG,GAAGuS,UAAU,CAACnO,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAIqO,aAAa,CAACzS,GAAG,GAAG,CAAC,EAAE;MACvB4V,CAAC,IAAInD,aAAa,CAACzS,GAAG;IAC1B;IACA,OAAO;MAAEwV,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACI5C,gBAAgB,CAACH,WAAW,EAAE1N,WAAW,EAAEyN,GAAG,EAAE;IAC5C;IACA;IACA,IAAIiD,aAAa;IACjB,IAAIjD,GAAG,CAACvL,QAAQ,IAAI,QAAQ,EAAE;MAC1BwO,aAAa,GAAG,CAAC1Q,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAI6Q,GAAG,CAACvL,QAAQ,KAAK,OAAO,EAAE;MAC/BwO,aAAa,GAAG,IAAI,CAACH,MAAM,EAAE,GAAG,CAACvQ,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACD8T,aAAa,GAAG,IAAI,CAACH,MAAM,EAAE,GAAG,CAAC,GAAG,CAACvQ,WAAW,CAACpD,KAAK;IAC1D;IACA,IAAI+T,aAAa;IACjB,IAAIlD,GAAG,CAACtL,QAAQ,IAAI,QAAQ,EAAE;MAC1BwO,aAAa,GAAG,CAAC3Q,WAAW,CAACtD,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACDiU,aAAa,GAAGlD,GAAG,CAACtL,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAACnC,WAAW,CAACtD,MAAM;IACnE;IACA;IACA,OAAO;MACH2T,CAAC,EAAE3C,WAAW,CAAC2C,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAE/C,WAAW,CAAC+C,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACA5C,cAAc,CAAC6C,KAAK,EAAEC,cAAc,EAAEtU,QAAQ,EAAE4R,QAAQ,EAAE;IACtD;IACA;IACA,MAAMtM,OAAO,GAAGiP,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAI9O,OAAO,GAAG,IAAI,CAACiP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIpM,OAAO,GAAG,IAAI,CAACgP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAIrM,OAAO,EAAE;MACTuO,CAAC,IAAIvO,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACT0O,CAAC,IAAI1O,OAAO;IAChB;IACA;IACA,IAAIiP,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAGxO,OAAO,CAACjF,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAIsU,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAG5O,OAAO,CAACnF,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAI0U,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACxP,OAAO,CAACjF,KAAK,EAAEoU,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAACxP,OAAO,CAACnF,MAAM,EAAEwU,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAI7C,WAAW,GAAG8C,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACHhD,WAAW;MACXN,0BAA0B,EAAEnM,OAAO,CAACjF,KAAK,GAAGiF,OAAO,CAACnF,MAAM,KAAK4R,WAAW;MAC1EiD,wBAAwB,EAAED,aAAa,KAAKzP,OAAO,CAACnF,MAAM;MAC1D8U,0BAA0B,EAAEJ,YAAY,IAAIvP,OAAO,CAACjF;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsR,6BAA6B,CAACO,GAAG,EAAEmC,KAAK,EAAErU,QAAQ,EAAE;IAChD,IAAI,IAAI,CAACkP,sBAAsB,EAAE;MAC7B,MAAMgG,eAAe,GAAGlV,QAAQ,CAAC0C,MAAM,GAAG2R,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAGnV,QAAQ,CAAC6C,KAAK,GAAGwR,KAAK,CAACP,CAAC;MAC/C,MAAM3G,SAAS,GAAGiI,aAAa,CAAC,IAAI,CAACtU,WAAW,CAACsL,SAAS,EAAE,CAACe,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAGkI,aAAa,CAAC,IAAI,CAACtU,WAAW,CAACsL,SAAS,EAAE,CAACc,QAAQ,CAAC;MACrE,MAAMmI,WAAW,GAAGnD,GAAG,CAAC8C,wBAAwB,IAAK7H,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI+H,eAAgB;MACvG,MAAMI,aAAa,GAAGpD,GAAG,CAAC+C,0BAA0B,IAAK/H,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAIiI,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoB,CAACC,KAAK,EAAElB,cAAc,EAAExS,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAACuQ,mBAAmB,IAAI,IAAI,CAAClD,eAAe,EAAE;MAClD,OAAO;QACH2E,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACzB,mBAAmB,CAACyB,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC7B,mBAAmB,CAAC6B;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAM5O,OAAO,GAAGiP,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAMtU,QAAQ,GAAG,IAAI,CAACuQ,aAAa;IACnC;IACA;IACA,MAAMkF,aAAa,GAAG1T,IAAI,CAAC2T,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAGxO,OAAO,CAACjF,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAMsV,cAAc,GAAG5T,IAAI,CAAC2T,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAG5O,OAAO,CAACnF,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAMyV,WAAW,GAAG7T,IAAI,CAAC2T,GAAG,CAAC1V,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAGkX,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAG9T,IAAI,CAAC2T,GAAG,CAAC1V,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAGiX,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAIzQ,OAAO,CAACjF,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjCyV,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC1E,eAAe,GAAGpP,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAGiX,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAIxO,OAAO,CAACnF,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnC4V,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAGpP,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAGkX,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAAC7B,mBAAmB,GAAG;MAAEyB,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIrE,cAAc,CAACE,QAAQ,EAAET,WAAW,EAAE;IAClC,IAAI,CAAC6E,mBAAmB,CAACpE,QAAQ,CAAC;IAClC,IAAI,CAACqE,wBAAwB,CAAC9E,WAAW,EAAES,QAAQ,CAAC;IACpD,IAAI,CAACsE,qBAAqB,CAAC/E,WAAW,EAAES,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAAChN,UAAU,EAAE;MACrB,IAAI,CAACuR,gBAAgB,CAACvE,QAAQ,CAAChN,UAAU,CAAC;IAC9C;IACA;IACA,IAAI,CAACqL,aAAa,GAAG2B,QAAQ;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACtC,gBAAgB,CAACnI,SAAS,CAACP,MAAM,EAAE;MACxC,MAAMZ,wBAAwB,GAAG,IAAI,CAACoQ,oBAAoB,EAAE;MAC5D,MAAMC,WAAW,GAAG,IAAIvQ,8BAA8B,CAAC8L,QAAQ,EAAE5L,wBAAwB,CAAC;MAC1F,IAAI,CAACsJ,gBAAgB,CAACjI,IAAI,CAACgP,WAAW,CAAC;IAC3C;IACA,IAAI,CAACrG,gBAAgB,GAAG,KAAK;EACjC;EACA;EACAgG,mBAAmB,CAACpE,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACiC,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAMyC,QAAQ,GAAG,IAAI,CAACxG,YAAY,CAAC9G,gBAAgB,CAAC,IAAI,CAAC6K,wBAAwB,CAAC;IAClF,IAAI0C,OAAO;IACX,IAAIC,OAAO,GAAG5E,QAAQ,CAAChM,QAAQ;IAC/B,IAAIgM,QAAQ,CAACjM,QAAQ,KAAK,QAAQ,EAAE;MAChC4Q,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAACvC,MAAM,EAAE,EAAE;MACpBuC,OAAO,GAAG3E,QAAQ,CAACjM,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACD4Q,OAAO,GAAG3E,QAAQ,CAACjM,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqP,QAAQ,CAAC1P,MAAM,EAAEK,CAAC,EAAE,EAAE;MACtCqP,QAAQ,CAACrP,CAAC,CAAC,CAAChI,KAAK,CAACwX,eAAe,GAAI,GAAEF,OAAQ,IAAGC,OAAQ,EAAC;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1E,yBAAyB,CAACzM,MAAM,EAAEuM,QAAQ,EAAE;IACxC,MAAM5R,QAAQ,GAAG,IAAI,CAACuQ,aAAa;IACnC,MAAMmG,KAAK,GAAG,IAAI,CAAC1C,MAAM,EAAE;IAC3B,IAAI7T,MAAM,EAAE7B,GAAG,EAAEoE,MAAM;IACvB,IAAIkP,QAAQ,CAAChM,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACAtH,GAAG,GAAG+G,MAAM,CAAC6O,CAAC;MACd/T,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAG7B,GAAG,GAAG,IAAI,CAAC8Q,eAAe;IACzD,CAAC,MACI,IAAIwC,QAAQ,CAAChM,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACAlD,MAAM,GAAG1C,QAAQ,CAACG,MAAM,GAAGkF,MAAM,CAAC6O,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAG,CAAC;MAC9DjP,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGuC,MAAM,GAAG,IAAI,CAAC0M,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMuH,8BAA8B,GAAG5U,IAAI,CAAC6U,GAAG,CAAC5W,QAAQ,CAAC0C,MAAM,GAAG2C,MAAM,CAAC6O,CAAC,GAAGlU,QAAQ,CAAC1B,GAAG,EAAE+G,MAAM,CAAC6O,CAAC,CAAC;MACpG,MAAM2C,cAAc,GAAG,IAAI,CAAC/H,oBAAoB,CAAC3O,MAAM;MACvDA,MAAM,GAAGwW,8BAA8B,GAAG,CAAC;MAC3CrY,GAAG,GAAG+G,MAAM,CAAC6O,CAAC,GAAGyC,8BAA8B;MAC/C,IAAIxW,MAAM,GAAG0W,cAAc,IAAI,CAAC,IAAI,CAAC7G,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QAC3E3Q,GAAG,GAAG+G,MAAM,CAAC6O,CAAC,GAAG2C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAMC,4BAA4B,GAAIlF,QAAQ,CAACjM,QAAQ,KAAK,OAAO,IAAI,CAAC+Q,KAAK,IAAM9E,QAAQ,CAACjM,QAAQ,KAAK,KAAK,IAAI+Q,KAAM;IACxH;IACA,MAAMK,2BAA2B,GAAInF,QAAQ,CAACjM,QAAQ,KAAK,KAAK,IAAI,CAAC+Q,KAAK,IAAM9E,QAAQ,CAACjM,QAAQ,KAAK,OAAO,IAAI+Q,KAAM;IACvH,IAAIrW,KAAK,EAAE9B,IAAI,EAAEsE,KAAK;IACtB,IAAIkU,2BAA2B,EAAE;MAC7BlU,KAAK,GAAG7C,QAAQ,CAACK,KAAK,GAAGgF,MAAM,CAACyO,CAAC,GAAG,IAAI,CAAC1E,eAAe;MACxD/O,KAAK,GAAGgF,MAAM,CAACyO,CAAC,GAAG,IAAI,CAAC1E,eAAe;IAC3C,CAAC,MACI,IAAI0H,4BAA4B,EAAE;MACnCvY,IAAI,GAAG8G,MAAM,CAACyO,CAAC;MACfzT,KAAK,GAAGL,QAAQ,CAAC6C,KAAK,GAAGwC,MAAM,CAACyO,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAM6C,8BAA8B,GAAG5U,IAAI,CAAC6U,GAAG,CAAC5W,QAAQ,CAAC6C,KAAK,GAAGwC,MAAM,CAACyO,CAAC,GAAG9T,QAAQ,CAACzB,IAAI,EAAE8G,MAAM,CAACyO,CAAC,CAAC;MACpG,MAAMkD,aAAa,GAAG,IAAI,CAAClI,oBAAoB,CAACzO,KAAK;MACrDA,KAAK,GAAGsW,8BAA8B,GAAG,CAAC;MAC1CpY,IAAI,GAAG8G,MAAM,CAACyO,CAAC,GAAG6C,8BAA8B;MAChD,IAAItW,KAAK,GAAG2W,aAAa,IAAI,CAAC,IAAI,CAAChH,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QACzE1Q,IAAI,GAAG8G,MAAM,CAACyO,CAAC,GAAGkD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAE1Y,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEmE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAExC,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+V,qBAAqB,CAAC7Q,MAAM,EAAEuM,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACzM,MAAM,EAAEuM,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;MAChD4C,eAAe,CAAC1R,MAAM,GAAG4B,IAAI,CAAC6U,GAAG,CAAC/E,eAAe,CAAC1R,MAAM,EAAE,IAAI,CAAC2O,oBAAoB,CAAC3O,MAAM,CAAC;MAC3F0R,eAAe,CAACxR,KAAK,GAAG0B,IAAI,CAAC6U,GAAG,CAAC/E,eAAe,CAACxR,KAAK,EAAE,IAAI,CAACyO,oBAAoB,CAACzO,KAAK,CAAC;IAC5F;IACA,MAAM4W,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACC,iBAAiB,EAAE,EAAE;MAC1BD,MAAM,CAAC3Y,GAAG,GAAG2Y,MAAM,CAAC1Y,IAAI,GAAG,GAAG;MAC9B0Y,MAAM,CAACvU,MAAM,GAAGuU,MAAM,CAACpU,KAAK,GAAGoU,MAAM,CAAC5J,SAAS,GAAG4J,MAAM,CAAC7J,QAAQ,GAAG,EAAE;MACtE6J,MAAM,CAAC5W,KAAK,GAAG4W,MAAM,CAAC9W,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAMkN,SAAS,GAAG,IAAI,CAACvM,WAAW,CAACsL,SAAS,EAAE,CAACiB,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAACtM,WAAW,CAACsL,SAAS,EAAE,CAACgB,QAAQ;MACtD6J,MAAM,CAAC9W,MAAM,GAAGxD,mBAAmB,CAACkV,eAAe,CAAC1R,MAAM,CAAC;MAC3D8W,MAAM,CAAC3Y,GAAG,GAAG3B,mBAAmB,CAACkV,eAAe,CAACvT,GAAG,CAAC;MACrD2Y,MAAM,CAACvU,MAAM,GAAG/F,mBAAmB,CAACkV,eAAe,CAACnP,MAAM,CAAC;MAC3DuU,MAAM,CAAC5W,KAAK,GAAG1D,mBAAmB,CAACkV,eAAe,CAACxR,KAAK,CAAC;MACzD4W,MAAM,CAAC1Y,IAAI,GAAG5B,mBAAmB,CAACkV,eAAe,CAACtT,IAAI,CAAC;MACvD0Y,MAAM,CAACpU,KAAK,GAAGlG,mBAAmB,CAACkV,eAAe,CAAChP,KAAK,CAAC;MACzD;MACA,IAAI+O,QAAQ,CAACjM,QAAQ,KAAK,QAAQ,EAAE;QAChCsR,MAAM,CAAC1E,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACD0E,MAAM,CAAC1E,UAAU,GAAGX,QAAQ,CAACjM,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAIiM,QAAQ,CAAChM,QAAQ,KAAK,QAAQ,EAAE;QAChCqR,MAAM,CAACzE,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACDyE,MAAM,CAACzE,cAAc,GAAGZ,QAAQ,CAAChM,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAIyH,SAAS,EAAE;QACX4J,MAAM,CAAC5J,SAAS,GAAG1Q,mBAAmB,CAAC0Q,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACV6J,MAAM,CAAC7J,QAAQ,GAAGzQ,mBAAmB,CAACyQ,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAAC0B,oBAAoB,GAAG+C,eAAe;IAC3CS,YAAY,CAAC,IAAI,CAACxC,YAAY,CAAC7Q,KAAK,EAAEgY,MAAM,CAAC;EACjD;EACA;EACA3G,uBAAuB,GAAG;IACtBgC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAAC7Q,KAAK,EAAE;MAClCX,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTsE,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACXvC,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACTkS,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACAnC,0BAA0B,GAAG;IACzBiC,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAACvK,KAAK,EAAE;MAC3BX,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRmE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACT+O,QAAQ,EAAE,EAAE;MACZuF,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACAlB,wBAAwB,CAAC9E,WAAW,EAAES,QAAQ,EAAE;IAC5C,MAAMqF,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMG,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,EAAE;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAACnI,sBAAsB;IACzD,MAAMnL,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACsL,SAAS,EAAE;IAC3C,IAAIgL,gBAAgB,EAAE;MAClB,MAAMtV,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,EAAE;MACtEsT,YAAY,CAAC2E,MAAM,EAAE,IAAI,CAACK,iBAAiB,CAAC1F,QAAQ,EAAET,WAAW,EAAErP,cAAc,CAAC,CAAC;MACnFwQ,YAAY,CAAC2E,MAAM,EAAE,IAAI,CAACM,iBAAiB,CAAC3F,QAAQ,EAAET,WAAW,EAAErP,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACDmV,MAAM,CAACrF,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI4F,eAAe,GAAG,EAAE;IACxB,IAAIjS,OAAO,GAAG,IAAI,CAACiP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIpM,OAAO,GAAG,IAAI,CAACgP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIrM,OAAO,EAAE;MACTiS,eAAe,IAAK,cAAajS,OAAQ,MAAK;IAClD;IACA,IAAIC,OAAO,EAAE;MACTgS,eAAe,IAAK,cAAahS,OAAQ,KAAI;IACjD;IACAyR,MAAM,CAACE,SAAS,GAAGK,eAAe,CAACC,IAAI,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI1T,MAAM,CAACsJ,SAAS,EAAE;MAClB,IAAI+J,gBAAgB,EAAE;QAClBH,MAAM,CAAC5J,SAAS,GAAG1Q,mBAAmB,CAACoH,MAAM,CAACsJ,SAAS,CAAC;MAC5D,CAAC,MACI,IAAIgK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC5J,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAItJ,MAAM,CAACqJ,QAAQ,EAAE;MACjB,IAAIgK,gBAAgB,EAAE;QAClBH,MAAM,CAAC7J,QAAQ,GAAGzQ,mBAAmB,CAACoH,MAAM,CAACqJ,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAIiK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC7J,QAAQ,GAAG,EAAE;MACxB;IACJ;IACAkF,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAACvK,KAAK,EAAEgY,MAAM,CAAC;EAC1C;EACA;EACAK,iBAAiB,CAAC1F,QAAQ,EAAET,WAAW,EAAErP,cAAc,EAAE;IACrD;IACA;IACA,IAAImV,MAAM,GAAG;MAAE3Y,GAAG,EAAE,EAAE;MAAEoE,MAAM,EAAE;IAAG,CAAC;IACpC,IAAI2O,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAE7O,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAI8P,QAAQ,CAAChM,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAM8R,cAAc,GAAG,IAAI,CAACjZ,SAAS,CAACK,eAAe,CAAC6Y,YAAY;MAClEV,MAAM,CAACvU,MAAM,GAAI,GAAEgV,cAAc,IAAIrG,YAAY,CAAC6C,CAAC,GAAG,IAAI,CAACvD,YAAY,CAACxQ,MAAM,CAAE,IAAG;IACvF,CAAC,MACI;MACD8W,MAAM,CAAC3Y,GAAG,GAAG3B,mBAAmB,CAAC0U,YAAY,CAAC6C,CAAC,CAAC;IACpD;IACA,OAAO+C,MAAM;EACjB;EACA;EACAM,iBAAiB,CAAC3F,QAAQ,EAAET,WAAW,EAAErP,cAAc,EAAE;IACrD;IACA;IACA,IAAImV,MAAM,GAAG;MAAE1Y,IAAI,EAAE,EAAE;MAAEsE,KAAK,EAAE;IAAG,CAAC;IACpC,IAAIwO,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAE7O,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAI8V,uBAAuB;IAC3B,IAAI,IAAI,CAAC5D,MAAM,EAAE,EAAE;MACf4D,uBAAuB,GAAGhG,QAAQ,CAACjM,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACDiS,uBAAuB,GAAGhG,QAAQ,CAACjM,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAIiS,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAACpZ,SAAS,CAACK,eAAe,CAACgZ,WAAW;MAChEb,MAAM,CAACpU,KAAK,GAAI,GAAEgV,aAAa,IAAIxG,YAAY,CAACyC,CAAC,GAAG,IAAI,CAACnD,YAAY,CAACtQ,KAAK,CAAE,IAAG;IACpF,CAAC,MACI;MACD4W,MAAM,CAAC1Y,IAAI,GAAG5B,mBAAmB,CAAC0U,YAAY,CAACyC,CAAC,CAAC;IACrD;IACA,OAAOmD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIb,oBAAoB,GAAG;IACnB;IACA,MAAM2B,YAAY,GAAG,IAAI,CAACrH,cAAc,EAAE;IAC1C,MAAMsH,aAAa,GAAG,IAAI,CAACxO,KAAK,CAAC9F,qBAAqB,EAAE;IACxD;IACA;IACA;IACA,MAAMuU,qBAAqB,GAAG,IAAI,CAAC5I,YAAY,CAAC6I,GAAG,CAAC3W,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,EAAE,CAACC,aAAa,CAACgC,qBAAqB,EAAE;IAC3E,CAAC,CAAC;IACF,OAAO;MACHyU,eAAe,EAAEpV,2BAA2B,CAACgV,YAAY,EAAEE,qBAAqB,CAAC;MACjFG,mBAAmB,EAAEhW,4BAA4B,CAAC2V,YAAY,EAAEE,qBAAqB,CAAC;MACtFI,gBAAgB,EAAEtV,2BAA2B,CAACiV,aAAa,EAAEC,qBAAqB,CAAC;MACnFK,oBAAoB,EAAElW,4BAA4B,CAAC4V,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACAnD,kBAAkB,CAAClO,MAAM,EAAE,GAAG2R,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAG1W,IAAI,CAAC2T,GAAG,CAACgD,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAE9R,MAAM,CAAC;EACd;EACA;EACA4J,wBAAwB,GAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAMnQ,KAAK,GAAG,IAAI,CAAC5B,SAAS,CAACK,eAAe,CAACgZ,WAAW;IACxD,MAAM3X,MAAM,GAAG,IAAI,CAAC1B,SAAS,CAACK,eAAe,CAAC6Y,YAAY;IAC1D,MAAM7V,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,EAAE;IACtE,OAAO;MACHV,GAAG,EAAEwD,cAAc,CAACxD,GAAG,GAAG,IAAI,CAAC8Q,eAAe;MAC9C7Q,IAAI,EAAEuD,cAAc,CAACvD,IAAI,GAAG,IAAI,CAAC6Q,eAAe;MAChDvM,KAAK,EAAEf,cAAc,CAACvD,IAAI,GAAG8B,KAAK,GAAG,IAAI,CAAC+O,eAAe;MACzD1M,MAAM,EAAEZ,cAAc,CAACxD,GAAG,GAAG6B,MAAM,GAAG,IAAI,CAACiP,eAAe;MAC1D/O,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC+O,eAAe;MACvCjP,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACiP;IAC9B,CAAC;EACL;EACA;EACA4E,MAAM,GAAG;IACL,OAAO,IAAI,CAAClT,WAAW,CAACkM,YAAY,EAAE,KAAK,KAAK;EACpD;EACA;EACAkK,iBAAiB,GAAG;IAChB,OAAO,CAAC,IAAI,CAAChI,sBAAsB,IAAI,IAAI,CAACH,SAAS;EACzD;EACA;EACAyF,UAAU,CAAC5C,QAAQ,EAAE+G,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAO/G,QAAQ,CAACrM,OAAO,IAAI,IAAI,GAAG,IAAI,CAACiK,QAAQ,GAAGoC,QAAQ,CAACrM,OAAO;IACtE;IACA,OAAOqM,QAAQ,CAACpM,OAAO,IAAI,IAAI,GAAG,IAAI,CAACiK,QAAQ,GAAGmC,QAAQ,CAACpM,OAAO;EACtE;EACA;EACAqK,kBAAkB,GAAG;IACjB,IAAI,OAAO1O,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAACwN,mBAAmB,CAAC/H,MAAM,EAAE;QAClC,MAAMrG,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAACoO,mBAAmB,CAACiK,OAAO,CAACC,IAAI,IAAI;QACrCzS,0BAA0B,CAAC,SAAS,EAAEyS,IAAI,CAACpT,OAAO,CAAC;QACnDQ,wBAAwB,CAAC,SAAS,EAAE4S,IAAI,CAACnT,OAAO,CAAC;QACjDU,0BAA0B,CAAC,UAAU,EAAEyS,IAAI,CAAClT,QAAQ,CAAC;QACrDM,wBAAwB,CAAC,UAAU,EAAE4S,IAAI,CAACjT,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACAuQ,gBAAgB,CAACnI,UAAU,EAAE;IACzB,IAAI,IAAI,CAACxE,KAAK,EAAE;MACZ5M,WAAW,CAACoR,UAAU,CAAC,CAAC4K,OAAO,CAACE,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACpJ,oBAAoB,CAAChJ,OAAO,CAACoS,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACpJ,oBAAoB,CAAClJ,IAAI,CAACsS,QAAQ,CAAC;UACxC,IAAI,CAACtP,KAAK,CAACtK,SAAS,CAACC,GAAG,CAAC2Z,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA1I,kBAAkB,GAAG;IACjB,IAAI,IAAI,CAAC5G,KAAK,EAAE;MACZ,IAAI,CAACkG,oBAAoB,CAACkJ,OAAO,CAACE,QAAQ,IAAI;QAC1C,IAAI,CAACtP,KAAK,CAACtK,SAAS,CAACU,MAAM,CAACkZ,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACpJ,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACAgB,cAAc,GAAG;IACb,MAAMrL,MAAM,GAAG,IAAI,CAACkO,OAAO;IAC3B,IAAIlO,MAAM,YAAYnJ,UAAU,EAAE;MAC9B,OAAOmJ,MAAM,CAAC3D,aAAa,CAACgC,qBAAqB,EAAE;IACvD;IACA;IACA,IAAI2B,MAAM,YAAY0T,OAAO,EAAE;MAC3B,OAAO1T,MAAM,CAAC3B,qBAAqB,EAAE;IACzC;IACA,MAAMrD,KAAK,GAAGgF,MAAM,CAAChF,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAGkF,MAAM,CAAClF,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACH7B,GAAG,EAAE+G,MAAM,CAAC6O,CAAC;MACbxR,MAAM,EAAE2C,MAAM,CAAC6O,CAAC,GAAG/T,MAAM;MACzB5B,IAAI,EAAE8G,MAAM,CAACyO,CAAC;MACdjR,KAAK,EAAEwC,MAAM,CAACyO,CAAC,GAAGzT,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASiS,YAAY,CAAC0G,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAI9T,GAAG,IAAI8T,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAAC/T,GAAG,CAAC,EAAE;MAC5B6T,WAAW,CAAC7T,GAAG,CAAC,GAAG8T,MAAM,CAAC9T,GAAG,CAAC;IAClC;EACJ;EACA,OAAO6T,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS5D,aAAa,CAAC+D,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAAChT,KAAK,EAAEiT,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC7K,cAAc,CAAC;IAClD,OAAO,CAAC4K,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAACnT,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOgT,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5E,4BAA4B,CAACgF,UAAU,EAAE;EAC9C,OAAO;IACHjb,GAAG,EAAEyD,IAAI,CAACyX,KAAK,CAACD,UAAU,CAACjb,GAAG,CAAC;IAC/BuE,KAAK,EAAEd,IAAI,CAACyX,KAAK,CAACD,UAAU,CAAC1W,KAAK,CAAC;IACnCH,MAAM,EAAEX,IAAI,CAACyX,KAAK,CAACD,UAAU,CAAC7W,MAAM,CAAC;IACrCnE,IAAI,EAAEwD,IAAI,CAACyX,KAAK,CAACD,UAAU,CAAChb,IAAI,CAAC;IACjC8B,KAAK,EAAE0B,IAAI,CAACyX,KAAK,CAACD,UAAU,CAAClZ,KAAK,CAAC;IACnCF,MAAM,EAAE4B,IAAI,CAACyX,KAAK,CAACD,UAAU,CAACpZ,MAAM;EACxC,CAAC;AACL;AACA,MAAMsZ,iCAAiC,GAAG,CACtC;EAAEhU,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAM8T,oCAAoC,GAAG,CACzC;EAAEjU,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+T,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzB1b,WAAW,GAAG;IACV,IAAI,CAAC2b,YAAY,GAAG,QAAQ;IAC5B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACrK,WAAW,GAAG,KAAK;EAC5B;EACArR,MAAM,CAACwC,UAAU,EAAE;IACf,MAAM6C,MAAM,GAAG7C,UAAU,CAACkL,SAAS,EAAE;IACrC,IAAI,CAACtL,WAAW,GAAGI,UAAU;IAC7B,IAAI,IAAI,CAACiZ,MAAM,IAAI,CAACpW,MAAM,CAAC1D,KAAK,EAAE;MAC9Ba,UAAU,CAACsL,UAAU,CAAC;QAAEnM,KAAK,EAAE,IAAI,CAAC8Z;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAACrW,MAAM,CAAC5D,MAAM,EAAE;MAChCe,UAAU,CAACsL,UAAU,CAAC;QAAErM,MAAM,EAAE,IAAI,CAACia;MAAQ,CAAC,CAAC;IACnD;IACAlZ,UAAU,CAACwJ,WAAW,CAACxL,SAAS,CAACC,GAAG,CAACwa,YAAY,CAAC;IAClD,IAAI,CAAC5J,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACIzR,GAAG,CAAC6H,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAAC4T,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,UAAU,GAAG3T,KAAK;IACvB,IAAI,CAAC6T,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIzb,IAAI,CAAC4H,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAAC+T,QAAQ,GAAG/T,KAAK;IACrB,IAAI,CAAC8T,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIvX,MAAM,CAACyD,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAAC2T,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG5T,KAAK;IAC1B,IAAI,CAAC6T,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACInX,KAAK,CAACsD,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAAC+T,QAAQ,GAAG/T,KAAK;IACrB,IAAI,CAAC8T,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIzE,KAAK,CAACrP,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAAC+T,QAAQ,GAAG/T,KAAK;IACrB,IAAI,CAAC8T,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACII,GAAG,CAAClU,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAAC+T,QAAQ,GAAG/T,KAAK;IACrB,IAAI,CAAC8T,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5Z,KAAK,CAAC8F,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAACrF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC0L,UAAU,CAAC;QAAEnM,KAAK,EAAE8F;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACgU,MAAM,GAAGhU,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIhG,MAAM,CAACgG,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAACrF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC0L,UAAU,CAAC;QAAErM,MAAM,EAAEgG;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACiU,OAAO,GAAGjU,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACImU,kBAAkB,CAAC7G,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAAClV,IAAI,CAACkV,MAAM,CAAC;IACjB,IAAI,CAACwG,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,gBAAgB,CAAC9G,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAACnV,GAAG,CAACmV,MAAM,CAAC;IAChB,IAAI,CAACuG,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI3N,KAAK,GAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvL,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACC,WAAW,EAAE,EAAE;MACtD;IACJ;IACA,MAAMkW,MAAM,GAAG,IAAI,CAACnW,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAMub,YAAY,GAAG,IAAI,CAAC1Z,WAAW,CAAC4J,WAAW,CAACzL,KAAK;IACvD,MAAM8E,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACsL,SAAS,EAAE;IAC3C,MAAM;MAAE/L,KAAK;MAAEF,MAAM;MAAEiN,QAAQ;MAAEC;IAAU,CAAC,GAAGtJ,MAAM;IACrD,MAAM0W,yBAAyB,GAAG,CAACpa,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAAC+M,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAMsN,uBAAuB,GAAG,CAACva,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAACkN,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAMsN,SAAS,GAAG,IAAI,CAACV,UAAU;IACjC,MAAMW,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC7B,MAAMxD,KAAK,GAAG,IAAI,CAAC5V,WAAW,CAACsL,SAAS,EAAE,CAACQ,SAAS,KAAK,KAAK;IAC9D,IAAIiO,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAItI,cAAc,GAAG,EAAE;IACvB,IAAIiI,yBAAyB,EAAE;MAC3BjI,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAImI,SAAS,KAAK,QAAQ,EAAE;MAC7BnI,cAAc,GAAG,QAAQ;MACzB,IAAIkE,KAAK,EAAE;QACPoE,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAIlE,KAAK,EAAE;MACZ,IAAIiE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7CnI,cAAc,GAAG,UAAU;QAC3BqI,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrDnI,cAAc,GAAG,YAAY;QAC7BsI,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpDnI,cAAc,GAAG,YAAY;MAC7BqI,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnDnI,cAAc,GAAG,UAAU;MAC3BsI,WAAW,GAAGF,OAAO;IACzB;IACA3D,MAAM,CAACrF,QAAQ,GAAG,IAAI,CAACiI,YAAY;IACnC5C,MAAM,CAAC4D,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChE5D,MAAM,CAAC8D,SAAS,GAAGL,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACZ,UAAU;IAClE7C,MAAM,CAAC+D,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxC9C,MAAM,CAAC6D,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAAChI,cAAc,GAAGA,cAAc;IAC5CgI,YAAY,CAACjI,UAAU,GAAGmI,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACV,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACI1O,OAAO,GAAG;IACN,IAAI,IAAI,CAACyE,WAAW,IAAI,CAAC,IAAI,CAACjP,WAAW,EAAE;MACvC;IACJ;IACA,MAAMmW,MAAM,GAAG,IAAI,CAACnW,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAMgc,MAAM,GAAG,IAAI,CAACna,WAAW,CAAC4J,WAAW;IAC3C,MAAM8P,YAAY,GAAGS,MAAM,CAAChc,KAAK;IACjCgc,MAAM,CAAC/b,SAAS,CAACU,MAAM,CAAC+Z,YAAY,CAAC;IACrCa,YAAY,CAAChI,cAAc,GACvBgI,YAAY,CAACjI,UAAU,GACnB0E,MAAM,CAAC8D,SAAS,GACZ9D,MAAM,CAAC+D,YAAY,GACf/D,MAAM,CAAC4D,UAAU,GACb5D,MAAM,CAAC6D,WAAW,GACd7D,MAAM,CAACrF,QAAQ,GACX,EAAE;IAC9B,IAAI,CAAC9Q,WAAW,GAAG,IAAI;IACvB,IAAI,CAACiP,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmL,sBAAsB,CAAC;EACzBhd,WAAW,CAACC,cAAc,EAAEM,SAAS,EAAEkJ,SAAS,EAAEkH,iBAAiB,EAAE;IACjE,IAAI,CAAC1Q,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkH,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;EACIsM,MAAM,GAAG;IACL,OAAO,IAAIvB,sBAAsB,EAAE;EACvC;EACA;AACJ;AACA;AACA;EACIwB,mBAAmB,CAAC/V,MAAM,EAAE;IACxB,OAAO,IAAIoJ,iCAAiC,CAACpJ,MAAM,EAAE,IAAI,CAAClH,cAAc,EAAE,IAAI,CAACM,SAAS,EAAE,IAAI,CAACkJ,SAAS,EAAE,IAAI,CAACkH,iBAAiB,CAAC;EACrI;AACJ;AACAqM,sBAAsB,CAAChX,IAAI;EAAA,iBAA6FgX,sBAAsB,EA1mEjCpf,EAAE,UA0mEiDP,EAAE,CAACI,aAAa,GA1mEnEG,EAAE,UA0mE8ED,QAAQ,GA1mExFC,EAAE,UA0mEmGgB,IAAI,CAAC0L,QAAQ,GA1mElH1M,EAAE,UA0mE6H2M,gBAAgB;AAAA,CAA6C;AACzSyS,sBAAsB,CAAC9W,KAAK,kBA3mEiFtI,EAAE;EAAA,OA2mEaof,sBAAsB;EAAA,SAAtBA,sBAAsB;EAAA,YAAc;AAAM,EAAG;AACzK;EAAA,mDA5mE6Gpf,EAAE,mBA4mEfof,sBAAsB,EAAc,CAAC;IACzH7W,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE9I,EAAE,CAACI;IAAc,CAAC,EAAE;MAAE0I,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1FJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEvH,IAAI,CAAC0L;IAAS,CAAC,EAAE;MAAEnE,IAAI,EAAEoE;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI4S,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVpd,WAAW,EACX;EACAqd,gBAAgB,EAAE1M,iBAAiB,EAAE2M,yBAAyB,EAAEC,gBAAgB,EAAEhS,mBAAmB,EAAEiS,SAAS,EAAEhb,OAAO,EAAEjC,SAAS,EAAEkd,eAAe,EAAEjS,SAAS,EAAEC,uBAAuB,EAAEiS,qBAAqB,EAAE;IAC9M,IAAI,CAACL,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC1M,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC2M,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAChS,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACiS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAChb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkd,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACjS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACiS,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAM,CAAC9X,MAAM,EAAE;IACX,MAAM+X,IAAI,GAAG,IAAI,CAACC,kBAAkB,EAAE;IACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACH,IAAI,CAAC;IAC1C,MAAMI,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;IACnD,MAAMI,aAAa,GAAG,IAAI1X,aAAa,CAACX,MAAM,CAAC;IAC/CqY,aAAa,CAACxP,SAAS,GAAGwP,aAAa,CAACxP,SAAS,IAAI,IAAI,CAAC+O,eAAe,CAACxV,KAAK;IAC/E,OAAO,IAAIkD,UAAU,CAAC6S,YAAY,EAAEJ,IAAI,EAAEE,IAAI,EAAEI,aAAa,EAAE,IAAI,CAAC1b,OAAO,EAAE,IAAI,CAAC+I,mBAAmB,EAAE,IAAI,CAAChL,SAAS,EAAE,IAAI,CAACiL,SAAS,EAAE,IAAI,CAACC,uBAAuB,EAAE,IAAI,CAACiS,qBAAqB,KAAK,gBAAgB,CAAC;EACzN;EACA;AACJ;AACA;AACA;AACA;EACIhK,QAAQ,GAAG;IACP,OAAO,IAAI,CAAC6J,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;EACIQ,kBAAkB,CAACH,IAAI,EAAE;IACrB,MAAME,IAAI,GAAG,IAAI,CAACvd,SAAS,CAACyK,aAAa,CAAC,KAAK,CAAC;IAChD8S,IAAI,CAACK,EAAE,GAAI,eAAchB,YAAY,EAAG,EAAC;IACzCW,IAAI,CAAC9c,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtC2c,IAAI,CAAC1S,WAAW,CAAC4S,IAAI,CAAC;IACtB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkB,GAAG;IACjB,MAAMD,IAAI,GAAG,IAAI,CAACrd,SAAS,CAACyK,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAAC2F,iBAAiB,CAAClG,mBAAmB,EAAE,CAACS,WAAW,CAAC0S,IAAI,CAAC;IAC9D,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIK,mBAAmB,CAACH,IAAI,EAAE;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACZ,SAAS,CAACa,GAAG,CAACpgB,cAAc,CAAC;IACrD;IACA,OAAO,IAAIqB,eAAe,CAACwe,IAAI,EAAE,IAAI,CAACR,yBAAyB,EAAE,IAAI,CAACc,OAAO,EAAE,IAAI,CAACZ,SAAS,EAAE,IAAI,CAACjd,SAAS,CAAC;EAClH;AACJ;AACA6c,OAAO,CAACpX,IAAI;EAAA,iBAA6FoX,OAAO,EAhtEHxf,EAAE,UAgtEmB8H,qBAAqB,GAhtE1C9H,EAAE,UAgtEqD2M,gBAAgB,GAhtEvE3M,EAAE,UAgtEkFA,EAAE,CAAC0gB,wBAAwB,GAhtE/G1gB,EAAE,UAgtE0Hof,sBAAsB,GAhtElJpf,EAAE,UAgtE6J+K,yBAAyB,GAhtExL/K,EAAE,UAgtEmMA,EAAE,CAAC2gB,QAAQ,GAhtEhN3gB,EAAE,UAgtE2NA,EAAE,CAACqI,MAAM,GAhtEtOrI,EAAE,UAgtEiPD,QAAQ,GAhtE3PC,EAAE,UAgtEsQwB,EAAE,CAACof,cAAc,GAhtEzR5gB,EAAE,UAgtEoSF,EAAE,CAAC+gB,QAAQ,GAhtEjT7gB,EAAE,UAgtE4T4L,6BAA6B,GAhtE3V5L,EAAE,UAgtEsWM,qBAAqB;AAAA,CAA6D;AACviBkf,OAAO,CAAClX,KAAK,kBAjtEgGtI,EAAE;EAAA,OAitEFwf,OAAO;EAAA,SAAPA,OAAO;EAAA,YAAc;AAAM,EAAG;AAC3I;EAAA,mDAltE6Gxf,EAAE,mBAktEfwf,OAAO,EAAc,CAAC;IAC1GjX,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAET;IAAsB,CAAC,EAAE;MAAES,IAAI,EAAEoE;IAAiB,CAAC,EAAE;MAAEpE,IAAI,EAAEvI,EAAE,CAAC0gB;IAAyB,CAAC,EAAE;MAAEnY,IAAI,EAAE6W;IAAuB,CAAC,EAAE;MAAE7W,IAAI,EAAEwC;IAA0B,CAAC,EAAE;MAAExC,IAAI,EAAEvI,EAAE,CAAC2gB;IAAS,CAAC,EAAE;MAAEpY,IAAI,EAAEvI,EAAE,CAACqI;IAAO,CAAC,EAAE;MAAEE,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrRJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAE/G,EAAE,CAACof;IAAe,CAAC,EAAE;MAAErY,IAAI,EAAEzI,EAAE,CAAC+gB;IAAS,CAAC,EAAE;MAAEtY,IAAI,EAAEqD;IAA8B,CAAC,EAAE;MAAErD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC/HJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAAClI,qBAAqB;MAChC,CAAC,EAAE;QACCiI,IAAI,EAAEpI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2gB,mBAAmB,GAAG,CACxB;EACInX,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAMiX,qCAAqC,GAAG,IAAIxgB,cAAc,CAAC,uCAAuC,CAAC;AACzG;AACA;AACA;AACA;AACA,MAAMygB,gBAAgB,CAAC;EACnB5e,WAAW,EACX;EACA6e,UAAU,EAAE;IACR,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;AACJ;AACAD,gBAAgB,CAAC5Y,IAAI;EAAA,iBAA6F4Y,gBAAgB,EA9wErBhhB,EAAE,mBA8wEqCA,EAAE,CAACI,UAAU;AAAA,CAA4C;AAC7M4gB,gBAAgB,CAACE,IAAI,kBA/wEwFlhB,EAAE;EAAA,MA+wETghB,gBAAgB;EAAA;EAAA;EAAA;AAAA,EAA6I;AACnQ;EAAA,mDAhxE6GhhB,EAAE,mBAgxEfghB,gBAAgB,EAAc,CAAC;IACnHzY,IAAI,EAAE/H,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCsP,QAAQ,EAAE,4DAA4D;MACtEqJ,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7Y,IAAI,EAAEvI,EAAE,CAACI;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7E;AACA;AACA;AACA;AACA,MAAMihB,mBAAmB,CAAC;EACtB;EACA,IAAI5X,OAAO,GAAG;IACV,OAAO,IAAI,CAACiK,QAAQ;EACxB;EACA,IAAIjK,OAAO,CAACA,OAAO,EAAE;IACjB,IAAI,CAACiK,QAAQ,GAAGjK,OAAO;IACvB,IAAI,IAAI,CAAC6X,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAI5X,OAAO,GAAG;IACV,OAAO,IAAI,CAACiK,QAAQ;EACxB;EACA,IAAIjK,OAAO,CAACA,OAAO,EAAE;IACjB,IAAI,CAACiK,QAAQ,GAAGjK,OAAO;IACvB,IAAI,IAAI,CAAC4X,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAIvY,WAAW,GAAG;IACd,OAAO,IAAI,CAACyY,YAAY;EAC5B;EACA,IAAIzY,WAAW,CAACsB,KAAK,EAAE;IACnB,IAAI,CAACmX,YAAY,GAAGzgB,qBAAqB,CAACsJ,KAAK,CAAC;EACpD;EACA;EACA,IAAIoX,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAACpX,KAAK,EAAE;IACpB,IAAI,CAACqX,aAAa,GAAG3gB,qBAAqB,CAACsJ,KAAK,CAAC;EACrD;EACA;EACA,IAAI6M,kBAAkB,GAAG;IACrB,OAAO,IAAI,CAACyK,mBAAmB;EACnC;EACA,IAAIzK,kBAAkB,CAAC7M,KAAK,EAAE;IAC1B,IAAI,CAACsX,mBAAmB,GAAG5gB,qBAAqB,CAACsJ,KAAK,CAAC;EAC3D;EACA;EACA,IAAI+M,aAAa,GAAG;IAChB,OAAO,IAAI,CAACjE,cAAc;EAC9B;EACA,IAAIiE,aAAa,CAAC/M,KAAK,EAAE;IACrB,IAAI,CAAC8I,cAAc,GAAGpS,qBAAqB,CAACsJ,KAAK,CAAC;EACtD;EACA;EACA,IAAIK,IAAI,GAAG;IACP,OAAO,IAAI,CAACkX,KAAK;EACrB;EACA,IAAIlX,IAAI,CAACL,KAAK,EAAE;IACZ,IAAI,CAACuX,KAAK,GAAG7gB,qBAAqB,CAACsJ,KAAK,CAAC;EAC7C;EACA;EACAjI,WAAW,CAACyf,QAAQ,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,IAAI,EAAE;IAC9E,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACT,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACvO,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACwO,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACM,qBAAqB,GAAGpgB,YAAY,CAACsM,KAAK;IAC/C,IAAI,CAAC+T,mBAAmB,GAAGrgB,YAAY,CAACsM,KAAK;IAC7C,IAAI,CAACgU,mBAAmB,GAAGtgB,YAAY,CAACsM,KAAK;IAC7C,IAAI,CAACiU,qBAAqB,GAAGvgB,YAAY,CAACsM,KAAK;IAC/C;IACA,IAAI,CAACkU,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACrS,aAAa,GAAG,IAAI1P,YAAY,EAAE;IACvC;IACA,IAAI,CAACgiB,cAAc,GAAG,IAAIhiB,YAAY,EAAE;IACxC;IACA,IAAI,CAACmC,MAAM,GAAG,IAAInC,YAAY,EAAE;IAChC;IACA,IAAI,CAAC0E,MAAM,GAAG,IAAI1E,YAAY,EAAE;IAChC;IACA,IAAI,CAACiiB,cAAc,GAAG,IAAIjiB,YAAY,EAAE;IACxC;IACA,IAAI,CAACkiB,mBAAmB,GAAG,IAAIliB,YAAY,EAAE;IAC7C,IAAI,CAACmiB,eAAe,GAAG,IAAIjhB,cAAc,CAACmgB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAACc,sBAAsB,GAAGb,qBAAqB;IACnD,IAAI,CAACnZ,cAAc,GAAG,IAAI,CAACga,sBAAsB,EAAE;EACvD;EACA;EACA,IAAIzd,UAAU,GAAG;IACb,OAAO,IAAI,CAACJ,WAAW;EAC3B;EACA;EACA,IAAI6L,GAAG,GAAG;IACN,OAAO,IAAI,CAACoR,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC5X,KAAK,GAAG,KAAK;EAC9C;EACAI,WAAW,GAAG;IACV,IAAI,CAAC0X,mBAAmB,CAAC/b,WAAW,EAAE;IACtC,IAAI,CAACgc,mBAAmB,CAAChc,WAAW,EAAE;IACtC,IAAI,CAAC8b,qBAAqB,CAAC9b,WAAW,EAAE;IACxC,IAAI,CAACic,qBAAqB,CAACjc,WAAW,EAAE;IACxC,IAAI,IAAI,CAACpB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACwK,OAAO,EAAE;IAC9B;EACJ;EACAsT,WAAW,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACzB,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;MAC5C,IAAI,CAACtc,WAAW,CAAC0L,UAAU,CAAC;QACxBnM,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB6M,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB/M,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBgN,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAI0R,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACR,IAAI,EAAE;QAChC,IAAI,CAACjB,SAAS,CAAC/Q,KAAK,EAAE;MAC1B;IACJ;IACA,IAAIwS,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACR,IAAI,GAAG,IAAI,CAACS,cAAc,EAAE,GAAG,IAAI,CAACC,cAAc,EAAE;IAC7D;EACJ;EACA;EACAC,cAAc,GAAG;IACb,IAAI,CAAC,IAAI,CAACtQ,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC9H,MAAM,EAAE;MAC3C,IAAI,CAAC8H,SAAS,GAAGkO,mBAAmB;IACxC;IACA,MAAM1b,UAAU,GAAI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAAC6c,QAAQ,CAAC9B,MAAM,CAAC,IAAI,CAACoD,YAAY,EAAE,CAAE;IACjF,IAAI,CAAChB,mBAAmB,GAAG/c,UAAU,CAACgL,WAAW,EAAE,CAACrK,SAAS,CAAC,MAAM,IAAI,CAACnD,MAAM,CAACwgB,IAAI,EAAE,CAAC;IACvF,IAAI,CAAChB,mBAAmB,GAAGhd,UAAU,CAACiL,WAAW,EAAE,CAACtK,SAAS,CAAC,MAAM,IAAI,CAACZ,MAAM,CAACie,IAAI,EAAE,CAAC;IACvFhe,UAAU,CAACkG,aAAa,EAAE,CAACvF,SAAS,CAAEkF,KAAK,IAAK;MAC5C,IAAI,CAACyX,cAAc,CAACnX,IAAI,CAACN,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACoY,OAAO,KAAKrhB,MAAM,IAAI,CAAC,IAAI,CAACwgB,YAAY,IAAI,CAACvgB,cAAc,CAACgJ,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACqY,cAAc,EAAE;QACtB,IAAI,CAACL,cAAc,EAAE;MACzB;IACJ,CAAC,CAAC;IACF,IAAI,CAACje,WAAW,CAACqH,oBAAoB,EAAE,CAACtG,SAAS,CAAEkF,KAAK,IAAK;MACzD,IAAI,CAAC0X,mBAAmB,CAACpX,IAAI,CAACN,KAAK,CAAC;IACxC,CAAC,CAAC;EACN;EACA;EACAkY,YAAY,GAAG;IACX,MAAMzU,gBAAgB,GAAI,IAAI,CAAC4S,SAAS,GACpC,IAAI,CAAC5S,gBAAgB,IAAI,IAAI,CAAC6U,uBAAuB,EAAG;IAC5D,MAAMjD,aAAa,GAAG,IAAI1X,aAAa,CAAC;MACpCkI,SAAS,EAAE,IAAI,CAACmR,IAAI;MACpBvT,gBAAgB;MAChB7F,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCE,WAAW,EAAE,IAAI,CAACA;IACtB,CAAC,CAAC;IACF,IAAI,IAAI,CAACxE,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChC+b,aAAa,CAAC/b,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClCic,aAAa,CAACjc,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAAC+M,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtCkP,aAAa,CAAClP,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxCiP,aAAa,CAACjP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACrI,aAAa,EAAE;MACpBsX,aAAa,CAACtX,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjBwX,aAAa,CAACxX,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAOwX,aAAa;EACxB;EACA;EACAiB,uBAAuB,CAAC7S,gBAAgB,EAAE;IACtC,MAAMkE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACwJ,GAAG,CAACoH,eAAe,KAAK;MACrD7Z,OAAO,EAAE6Z,eAAe,CAAC7Z,OAAO;MAChCC,OAAO,EAAE4Z,eAAe,CAAC5Z,OAAO;MAChCC,QAAQ,EAAE2Z,eAAe,CAAC3Z,QAAQ;MAClCC,QAAQ,EAAE0Z,eAAe,CAAC1Z,QAAQ;MAClCL,OAAO,EAAE+Z,eAAe,CAAC/Z,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAE8Z,eAAe,CAAC9Z,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDZ,UAAU,EAAE0a,eAAe,CAAC1a,UAAU,IAAIJ;IAC9C,CAAC,CAAC,CAAC;IACH,OAAOgG,gBAAgB,CAClBoF,SAAS,CAAC,IAAI,CAAC2P,2CAA2C,EAAE,CAAC,CAC7D3M,aAAa,CAAClE,SAAS,CAAC,CACxBqE,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CG,QAAQ,CAAC,IAAI,CAAC3M,IAAI,CAAC,CACnByM,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCL,kBAAkB,CAAC,IAAI,CAACuL,cAAc,CAAC,CACvC/K,kBAAkB,CAAC,IAAI,CAACkK,YAAY,CAAC,CACrC5J,qBAAqB,CAAC,IAAI,CAAC6L,uBAAuB,CAAC;EAC5D;EACA;EACAH,uBAAuB,GAAG;IACtB,MAAM9S,QAAQ,GAAG,IAAI,CAACoR,QAAQ,CACzB/L,QAAQ,EAAE,CACVwJ,mBAAmB,CAAC,IAAI,CAACmE,2CAA2C,EAAE,CAAC;IAC5E,IAAI,CAAClC,uBAAuB,CAAC9Q,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACAgT,2CAA2C,GAAG;IAC1C,IAAI,IAAI,CAACla,MAAM,YAAYyX,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAACzX,MAAM,CAAC0X,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAC1X,MAAM;IACtB;EACJ;EACA;EACAyZ,cAAc,GAAG;IACb,IAAI,CAAC,IAAI,CAAChe,WAAW,EAAE;MACnB,IAAI,CAACke,cAAc,EAAE;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAACle,WAAW,CAACsL,SAAS,EAAE,CAACvH,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAAC/D,WAAW,CAACC,WAAW,EAAE,EAAE;MACjC,IAAI,CAACD,WAAW,CAACpC,MAAM,CAAC,IAAI,CAACggB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAAC7Z,WAAW,EAAE;MAClB,IAAI,CAACmZ,qBAAqB,GAAG,IAAI,CAACld,WAAW,CAACmL,aAAa,EAAE,CAACpK,SAAS,CAACkF,KAAK,IAAI;QAC7E,IAAI,CAACkF,aAAa,CAACiT,IAAI,CAACnY,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACiX,qBAAqB,CAAC9b,WAAW,EAAE;IAC5C;IACA,IAAI,CAACic,qBAAqB,CAACjc,WAAW,EAAE;IACxC;IACA;IACA,IAAI,IAAI,CAACqc,cAAc,CAACpX,SAAS,CAACP,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACuX,qBAAqB,GAAG,IAAI,CAACf,SAAS,CAACzN,eAAe,CACtDrO,IAAI,CAACjE,SAAS,CAAC,MAAM,IAAI,CAACkhB,cAAc,CAACpX,SAAS,CAACP,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/D/E,SAAS,CAAC+P,QAAQ,IAAI;QACvB,IAAI,CAAC2M,cAAc,CAACW,IAAI,CAACtN,QAAQ,CAAC;QAClC,IAAI,IAAI,CAAC2M,cAAc,CAACpX,SAAS,CAACP,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAACuX,qBAAqB,CAACjc,WAAW,EAAE;QAC5C;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA6c,cAAc,GAAG;IACb,IAAI,IAAI,CAACje,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACG,MAAM,EAAE;IAC7B;IACA,IAAI,CAAC+c,qBAAqB,CAAC9b,WAAW,EAAE;IACxC,IAAI,CAACic,qBAAqB,CAACjc,WAAW,EAAE;EAC5C;AACJ;AACAib,mBAAmB,CAACjZ,IAAI;EAAA,iBAA6FiZ,mBAAmB,EA1hF3BrhB,EAAE,mBA0hF2Cwf,OAAO,GA1hFpDxf,EAAE,mBA0hF+DA,EAAE,CAAC2jB,WAAW,GA1hF/E3jB,EAAE,mBA0hF0FA,EAAE,CAAC4jB,gBAAgB,GA1hF/G5jB,EAAE,mBA0hF0H+gB,qCAAqC,GA1hFjK/gB,EAAE,mBA0hF4KwB,EAAE,CAACof,cAAc;AAAA,CAA4D;AACxWS,mBAAmB,CAACH,IAAI,kBA3hFqFlhB,EAAE;EAAA,MA2hFNqhB,mBAAmB;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA,WA3hFfrhB,EAAE;AAAA,EA2hFoqD;AACnxD;EAAA,mDA5hF6GA,EAAE,mBA4hFfqhB,mBAAmB,EAAc,CAAC;IACtH9Y,IAAI,EAAE/H,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCsP,QAAQ,EAAE,qEAAqE;MAC/EqJ,QAAQ,EAAE,qBAAqB;MAC/BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7Y,IAAI,EAAEiX;IAAQ,CAAC,EAAE;MAAEjX,IAAI,EAAEvI,EAAE,CAAC2jB;IAAY,CAAC,EAAE;MAAEpb,IAAI,EAAEvI,EAAE,CAAC4jB;IAAiB,CAAC,EAAE;MAAErb,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1IJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACuY,qCAAqC;MAChD,CAAC;IAAE,CAAC,EAAE;MAAExY,IAAI,EAAE/G,EAAE,CAACof,cAAc;MAAEjY,UAAU,EAAE,CAAC;QAC1CJ,IAAI,EAAEpI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoJ,MAAM,EAAE,CAAC;MACrChB,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEoK,SAAS,EAAE,CAAC;MACZrK,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEkG,gBAAgB,EAAE,CAAC;MACnBnG,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEiB,OAAO,EAAE,CAAC;MACVlB,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEkB,OAAO,EAAE,CAAC;MACVnB,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEjE,KAAK,EAAE,CAAC;MACRgE,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEnE,MAAM,EAAE,CAAC;MACTkE,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE4I,QAAQ,EAAE,CAAC;MACX7I,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAE6I,SAAS,EAAE,CAAC;MACZ9I,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEQ,aAAa,EAAE,CAAC;MAChBT,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAE8Z,cAAc,EAAE,CAAC;MACjB/Z,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEK,cAAc,EAAE,CAAC;MACjBN,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAE+Z,IAAI,EAAE,CAAC;MACPha,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEga,YAAY,EAAE,CAAC;MACfja,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAEkb,uBAAuB,EAAE,CAAC;MAC1Bnb,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEO,WAAW,EAAE,CAAC;MACdR,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,gCAAgC;IAC3C,CAAC,CAAC;IAAEiZ,YAAY,EAAE,CAAC;MACflZ,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAE0O,kBAAkB,EAAE,CAAC;MACrB3O,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,uCAAuC;IAClD,CAAC,CAAC;IAAE4O,aAAa,EAAE,CAAC;MAChB7O,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEkC,IAAI,EAAE,CAAC;MACPnC,IAAI,EAAE7H,KAAK;MACX8H,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE2H,aAAa,EAAE,CAAC;MAChB5H,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAE8hB,cAAc,EAAE,CAAC;MACjBla,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEiC,MAAM,EAAE,CAAC;MACT2F,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEwE,MAAM,EAAE,CAAC;MACToD,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAE+hB,cAAc,EAAE,CAAC;MACjBna,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEgiB,mBAAmB,EAAE,CAAC;MACtBpa,IAAI,EAAE5H;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASkjB,sDAAsD,CAACra,OAAO,EAAE;EACrE,OAAO,MAAMA,OAAO,CAACiW,gBAAgB,CAACtX,UAAU,EAAE;AACtD;AACA;AACA,MAAM2b,8CAA8C,GAAG;EACnDC,OAAO,EAAEhD,qCAAqC;EAC9CiD,IAAI,EAAE,CAACxE,OAAO,CAAC;EACfyE,UAAU,EAAEJ;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,CAAC;AAEpBA,aAAa,CAAC9b,IAAI;EAAA,iBAA6F8b,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBAzoF2FnkB,EAAE;EAAA,MAyoFCkkB;AAAa,EAAmK;AAChSA,aAAa,CAACE,IAAI,kBA1oF2FpkB,EAAE;EAAA,WA0oF2B,CAACwf,OAAO,EAAEsE,8CAA8C,CAAC;EAAA,UAAYriB,UAAU,EAAEG,YAAY,EAAElC,eAAe,EAAEA,eAAe;AAAA,EAAI;AAC7Q;EAAA,mDA3oF6GM,EAAE,mBA2oFfkkB,aAAa,EAAc,CAAC;IAChH3b,IAAI,EAAE3H,QAAQ;IACd4H,IAAI,EAAE,CAAC;MACC6b,OAAO,EAAE,CAAC5iB,UAAU,EAAEG,YAAY,EAAElC,eAAe,EAAE2hB,mBAAmB,EAAEL,gBAAgB,CAAC;MAC3FsD,OAAO,EAAE,CAACjD,mBAAmB,EAAEL,gBAAgB,EAAEthB,eAAe,CAAC;MACjE6kB,SAAS,EAAE,CAAC/E,OAAO,EAAEsE,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,0BAA0B,SAAS7X,gBAAgB,CAAC;EACtDvK,WAAW,CAACO,SAAS,EAAE8hB,QAAQ,EAAE;IAC7B,KAAK,CAAC9hB,SAAS,EAAE8hB,QAAQ,CAAC;EAC9B;EACAha,WAAW,GAAG;IACV,KAAK,CAACA,WAAW,EAAE;IACnB,IAAI,IAAI,CAACia,oBAAoB,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAAChiB,SAAS,CAACgJ,mBAAmB,CAAC,IAAI,CAAC+Y,oBAAoB,EAAE,IAAI,CAACC,mBAAmB,CAAC;IAC3F;EACJ;EACA7X,gBAAgB,GAAG;IACf,KAAK,CAACA,gBAAgB,EAAE;IACxB,IAAI,CAAC8X,gCAAgC,EAAE;IACvC,IAAI,CAACC,4BAA4B,CAAC,MAAM,IAAI,CAACD,gCAAgC,EAAE,CAAC;EACpF;EACAA,gCAAgC,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAChY,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAMkY,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;IACrD,MAAM5F,MAAM,GAAG2F,iBAAiB,IAAI,IAAI,CAACniB,SAAS,CAACa,IAAI;IACvD2b,MAAM,CAAC7R,WAAW,CAAC,IAAI,CAACV,iBAAiB,CAAC;EAC9C;EACAiY,4BAA4B,CAACG,EAAE,EAAE;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE;IACtC,IAAID,SAAS,EAAE;MACX,IAAI,IAAI,CAACN,mBAAmB,EAAE;QAC1B,IAAI,CAAChiB,SAAS,CAACgJ,mBAAmB,CAACsZ,SAAS,EAAE,IAAI,CAACN,mBAAmB,CAAC;MAC3E;MACA,IAAI,CAAChiB,SAAS,CAAC+I,gBAAgB,CAACuZ,SAAS,EAAED,EAAE,CAAC;MAC9C,IAAI,CAACL,mBAAmB,GAAGK,EAAE;IACjC;EACJ;EACAE,aAAa,GAAG;IACZ,IAAI,CAAC,IAAI,CAACR,oBAAoB,EAAE;MAC5B,MAAM/hB,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,CAACwiB,iBAAiB,EAAE;QAC7B,IAAI,CAACT,oBAAoB,GAAG,kBAAkB;MAClD,CAAC,MACI,IAAI/hB,SAAS,CAACyiB,uBAAuB,EAAE;QACxC,IAAI,CAACV,oBAAoB,GAAG,wBAAwB;MACxD,CAAC,MACI,IAAI/hB,SAAS,CAAC0iB,oBAAoB,EAAE;QACrC,IAAI,CAACX,oBAAoB,GAAG,qBAAqB;MACrD,CAAC,MACI,IAAI/hB,SAAS,CAAC2iB,mBAAmB,EAAE;QACpC,IAAI,CAACZ,oBAAoB,GAAG,oBAAoB;MACpD;IACJ;IACA,OAAO,IAAI,CAACA,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;EACIK,oBAAoB,GAAG;IACnB,MAAMpiB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,OAAQA,SAAS,CAACmiB,iBAAiB,IAC/BniB,SAAS,CAAC4iB,uBAAuB,IACjC5iB,SAAS,CAAC6iB,oBAAoB,IAC9B7iB,SAAS,CAAC8iB,mBAAmB,IAC7B,IAAI;EACZ;AACJ;AACAjB,0BAA0B,CAACpc,IAAI;EAAA,iBAA6Foc,0BAA0B,EA1uFzCxkB,EAAE,UA0uFyDD,QAAQ,GA1uFnEC,EAAE,UA0uF8EgB,IAAI,CAAC0L,QAAQ;AAAA,CAA6C;AACvP8X,0BAA0B,CAAClc,KAAK,kBA3uF6EtI,EAAE;EAAA,OA2uFiBwkB,0BAA0B;EAAA,SAA1BA,0BAA0B;EAAA,YAAc;AAAM,EAAG;AACjL;EAAA,mDA5uF6GxkB,EAAE,mBA4uFfwkB,0BAA0B,EAAc,CAAC;IAC7Hjc,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACzI,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEvH,IAAI,CAAC0L;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASvK,mBAAmB,EAAEkf,mBAAmB,EAAEL,gBAAgB,EAAEtc,mBAAmB,EAAEsF,8BAA8B,EAAEV,sBAAsB,EAAEqJ,iCAAiC,EAAE6R,0BAA0B,EAAE1G,sBAAsB,EAAEzX,kBAAkB,EAAEmZ,OAAO,EAAE5W,aAAa,EAAE+D,gBAAgB,EAAE5B,yBAAyB,EAAEmZ,aAAa,EAAEtY,6BAA6B,EAAEwT,sBAAsB,EAAE7R,UAAU,EAAEhG,wBAAwB,EAAEqW,oCAAoC,EAAED,iCAAiC,EAAE7V,qBAAqB,EAAEiC,mBAAmB,EAAEO,0BAA0B,EAAEH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}