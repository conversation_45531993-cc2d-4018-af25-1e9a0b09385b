using Oracul.Data.Models;
using Oracul.Server.Models;
using System.Security.Claims;

namespace Oracul.Server.Services
{
    public interface IJwtService
    {
        /// <summary>
        /// Generates an access token for the user
        /// </summary>
        /// <param name="user">The user to generate token for</param>
        /// <param name="roles">User roles</param>
        /// <param name="permissions">User permissions</param>
        /// <returns>JWT access token</returns>
        string GenerateAccessToken(User user, IEnumerable<string> roles, IEnumerable<string> permissions);

        /// <summary>
        /// Generates a refresh token
        /// </summary>
        /// <returns>Refresh token</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// Validates a JWT token and returns the claims
        /// </summary>
        /// <param name="token">The JWT token to validate</param>
        /// <returns>Claims principal if valid, null otherwise</returns>
        ClaimsPrincipal? ValidateToken(string token);

        /// <summary>
        /// Gets the user ID from a JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>User ID if valid, null otherwise</returns>
        int? GetUserIdFromToken(string token);

        /// <summary>
        /// Checks if a token is expired
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>True if expired, false otherwise</returns>
        bool IsTokenExpired(string token);
    }
}
