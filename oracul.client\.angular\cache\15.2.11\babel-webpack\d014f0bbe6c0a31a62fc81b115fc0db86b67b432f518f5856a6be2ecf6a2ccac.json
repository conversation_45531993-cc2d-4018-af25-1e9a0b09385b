{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/button-toggle\";\nimport * as i15 from \"@angular/material/paginator\";\nimport * as i16 from \"../profile-card/profile-card.component\";\nfunction ProfileSearchComponent_mat_spinner_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 20);\n  }\n}\nfunction ProfileSearchComponent_mat_icon_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileSearchComponent_div_59_div_12_app_profile_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-profile-card\", 32);\n    i0.ɵɵlistener(\"contactClicked\", function ProfileSearchComponent_div_59_div_12_app_profile_card_1_Template_app_profile_card_contactClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.onContactProfile($event));\n    })(\"profileClicked\", function ProfileSearchComponent_div_59_div_12_app_profile_card_1_Template_app_profile_card_profileClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.onProfileClicked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const profile_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"profile\", profile_r7)(\"compact\", ctx_r6.viewMode === \"grid\");\n  }\n}\nfunction ProfileSearchComponent_div_59_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, ProfileSearchComponent_div_59_div_12_app_profile_card_1_Template, 1, 2, \"app-profile-card\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"grid-view\", ctx_r3.viewMode === \"grid\")(\"list-view\", ctx_r3.viewMode === \"list\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.searchResults.profiles);\n  }\n}\nfunction ProfileSearchComponent_div_59_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No professionals found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or browse all available professionals.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProfileSearchComponent_div_59_div_13_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.clearSearch());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Show All Professionals \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function () {\n  return [10, 20, 50];\n};\nfunction ProfileSearchComponent_div_59_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"mat-paginator\", 36);\n    i0.ɵɵlistener(\"page\", function ProfileSearchComponent_div_59_div_14_Template_mat_paginator_page_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r5.searchResults.totalCount)(\"pageSize\", ctx_r5.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r5.currentPage - 1);\n  }\n}\nfunction ProfileSearchComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"mat-button-toggle-group\", 24);\n    i0.ɵɵlistener(\"valueChange\", function ProfileSearchComponent_div_59_Template_mat_button_toggle_group_valueChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.viewMode = $event);\n    });\n    i0.ɵɵelementStart(6, \"mat-button-toggle\", 25)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"grid_view\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-button-toggle\", 26)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"view_list\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(12, ProfileSearchComponent_div_59_div_12_Template, 2, 5, \"div\", 27);\n    i0.ɵɵtemplate(13, ProfileSearchComponent_div_59_div_13_Template, 11, 0, \"div\", 28);\n    i0.ɵɵtemplate(14, ProfileSearchComponent_div_59_div_14_Template, 2, 5, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.searchResults.totalCount, \" professional\", ctx_r2.searchResults.totalCount !== 1 ? \"s\" : \"\", \" found\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.viewMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.profiles.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.profiles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.totalPages > 1);\n  }\n}\nexport class ProfileSearchComponent {\n  constructor(formBuilder, profileService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.profileService = profileService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.searchResults = null;\n    this.isLoading = false;\n    this.viewMode = 'grid';\n    this.currentPage = 1;\n    this.pageSize = 20;\n    this.searchForm = this.formBuilder.group({\n      location: [''],\n      skills: [''],\n      experience: [''],\n      sortBy: ['relevance']\n    });\n  }\n  ngOnInit() {\n    // Load dummy data for demonstration\n    this.loadDummyData();\n  }\n  onSearch() {\n    this.currentPage = 1;\n    this.performSearch();\n  }\n  clearSearch() {\n    this.searchForm.reset({\n      location: '',\n      skills: '',\n      experience: '',\n      sortBy: 'relevance'\n    });\n    this.loadDummyData(); // Reset to show all profiles\n  }\n\n  quickSearch(term) {\n    // Determine if it's a location or skill search\n    const locations = ['california', 'arizona', 'sedona', 'los angeles', 'santa fe', 'new orleans', 'portland', 'asheville', 'boulder', 'san francisco'];\n    const isLocation = locations.some(loc => term.toLowerCase().includes(loc));\n    if (isLocation) {\n      this.searchForm.patchValue({\n        location: term,\n        skills: ''\n      });\n    } else {\n      this.searchForm.patchValue({\n        skills: term,\n        location: ''\n      });\n    }\n    this.onSearch();\n  }\n  performSearch() {\n    this.isLoading = true;\n    const formValue = this.searchForm.value;\n    // Simulate search delay for realistic experience\n    setTimeout(() => {\n      const allProfiles = this.createDummyProfiles();\n      let filteredProfiles = [...allProfiles];\n      // Apply location filter\n      if (formValue.location && formValue.location.trim()) {\n        const locationFilter = formValue.location.toLowerCase();\n        filteredProfiles = filteredProfiles.filter(profile => profile.location?.displayLocation?.toLowerCase().includes(locationFilter) || profile.location?.city?.toLowerCase().includes(locationFilter) || profile.location?.state?.toLowerCase().includes(locationFilter));\n      }\n      // Apply skills filter\n      if (formValue.skills && formValue.skills.trim()) {\n        const skillsFilter = formValue.skills.toLowerCase().split(',').map(s => s.trim());\n        filteredProfiles = filteredProfiles.filter(profile => skillsFilter.some(skill => profile.skills.some(profileSkill => profileSkill.name.toLowerCase().includes(skill)) || profile.professionalTitle?.toLowerCase().includes(skill) || profile.summary?.toLowerCase().includes(skill)));\n      }\n      // Apply sorting\n      switch (formValue.sortBy) {\n        case 'views':\n          filteredProfiles.sort((a, b) => b.profileViews - a.profileViews);\n          break;\n        case 'alphabetical':\n          filteredProfiles.sort((a, b) => a.firstName.localeCompare(b.firstName));\n          break;\n        case 'recent':\n          filteredProfiles.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());\n          break;\n        default:\n          // relevance\n          // Keep original order for relevance\n          break;\n      }\n      this.searchResults = {\n        profiles: filteredProfiles,\n        totalCount: filteredProfiles.length,\n        currentPage: this.currentPage,\n        totalPages: Math.ceil(filteredProfiles.length / this.pageSize),\n        pageSize: this.pageSize\n      };\n      this.isLoading = false;\n    }, 500); // 500ms delay to simulate API call\n  }\n\n  onPageChange(event) {\n    this.currentPage = event.pageIndex + 1;\n    this.pageSize = event.pageSize;\n    this.performSearch();\n  }\n  onContactProfile(profile) {\n    // Handle contact action - could open a dialog or navigate to contact form\n    if (profile.contactInfo?.email) {\n      window.location.href = `mailto:${profile.contactInfo.email}`;\n    } else {\n      this.snackBar.open('Contact information not available', 'Close', {\n        duration: 3000\n      });\n    }\n  }\n  onProfileClicked(profile) {\n    // All profiles lead to luna-starweaver for demo purposes\n    this.router.navigate(['/oracle', 1]); // Assuming luna-starweaver has ID 1\n  }\n\n  loadDummyData() {\n    // Create dummy search results with various oracle profiles\n    this.searchResults = {\n      profiles: this.createDummyProfiles(),\n      totalCount: 8,\n      currentPage: 1,\n      totalPages: 1,\n      pageSize: 20\n    };\n  }\n  createDummyProfiles() {\n    const baseProfile = {\n      userId: 1,\n      username: 'luna-starweaver',\n      slug: 'luna-starweaver',\n      isPublic: true,\n      profileCompletionPercentage: 95,\n      contactInfo: {\n        email: '<EMAIL>',\n        isEmailPublic: true,\n        website: 'https://starweaver.com',\n        phoneNumbers: [{\n          number: '+****************',\n          type: 'business',\n          isPublic: true,\n          isPrimary: true\n        }]\n      },\n      experiences: [{\n        company: 'Cosmic Wisdom Center',\n        position: 'Senior Astrologer',\n        startDate: new Date('2018-01-01'),\n        isCurrent: true,\n        description: 'Leading astrologer providing personalized readings',\n        location: 'Sedona, Arizona'\n      }],\n      portfolioItems: [{\n        title: 'Cosmic Birth Chart Analysis',\n        description: 'Comprehensive natal chart reading service',\n        imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],\n        technologies: ['Astrology', 'Spiritual Guidance'],\n        completedAt: new Date('2023-01-01'),\n        category: 'Astrology Services'\n      }],\n      blogPosts: [],\n      achievements: [],\n      certifications: [],\n      socialLinks: [],\n      profileViews: 1247,\n      createdAt: new Date('2020-01-01'),\n      updatedAt: new Date('2023-12-01')\n    };\n    return [{\n      ...baseProfile,\n      id: 1,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',\n      firstName: 'Luna',\n      lastName: 'Starweaver',\n      professionalTitle: 'Professional Astrologer & Cosmic Guide',\n      headline: 'Illuminating your path through the wisdom of the stars',\n      location: {\n        city: 'Sedona',\n        state: 'Arizona',\n        country: 'USA',\n        displayLocation: 'Sedona, Arizona'\n      },\n      summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling.',\n      skills: [{\n        name: 'Natal Chart Reading',\n        endorsements: 45,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Tarot Reading',\n        endorsements: 38,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Spiritual Counseling',\n        endorsements: 32,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Crystal Healing',\n        endorsements: 28,\n        proficiencyLevel: 'advanced'\n      }]\n    }, {\n      ...baseProfile,\n      id: 2,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',\n      firstName: 'Marcus',\n      lastName: 'Celestial',\n      professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',\n      headline: 'Unlocking the secrets of your cosmic blueprint',\n      location: {\n        city: 'Los Angeles',\n        state: 'California',\n        country: 'USA',\n        displayLocation: 'Los Angeles, California'\n      },\n      summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges.',\n      skills: [{\n        name: 'Birth Chart Analysis',\n        endorsements: 52,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Compatibility Reading',\n        endorsements: 41,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Life Transitions',\n        endorsements: 35,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 892\n    }, {\n      ...baseProfile,\n      id: 3,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',\n      firstName: 'Sage',\n      lastName: 'Moonchild',\n      professionalTitle: 'Crystal Healer & Energy Worker',\n      headline: 'Healing through the power of crystals and energy work',\n      location: {\n        city: 'Santa Fe',\n        state: 'New Mexico',\n        country: 'USA',\n        displayLocation: 'Santa Fe, New Mexico'\n      },\n      summary: 'Crystal healer and energy worker dedicated to helping others find balance and healing. I combine crystal therapy with meditation and chakra balancing.',\n      skills: [{\n        name: 'Crystal Healing',\n        endorsements: 38,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Energy Work',\n        endorsements: 34,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Chakra Balancing',\n        endorsements: 29,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Meditation',\n        endorsements: 25,\n        proficiencyLevel: 'intermediate'\n      }],\n      profileViews: 654\n    }, {\n      ...baseProfile,\n      id: 4,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',\n      firstName: 'River',\n      lastName: 'Palmistry',\n      professionalTitle: 'Third-Generation Palm Reader',\n      headline: 'Reading the stories written in your hands',\n      location: {\n        city: 'New Orleans',\n        state: 'Louisiana',\n        country: 'USA',\n        displayLocation: 'New Orleans, Louisiana'\n      },\n      summary: 'Third-generation palm reader with a gift for seeing life\\'s path through the lines of your hands. I provide insights into personality, relationships, and future possibilities.',\n      skills: [{\n        name: 'Palm Reading',\n        endorsements: 42,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Life Path Analysis',\n        endorsements: 36,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Personality Reading',\n        endorsements: 31,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 789\n    }, {\n      ...baseProfile,\n      id: 5,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',\n      firstName: 'Aurora',\n      lastName: 'Wisdom',\n      professionalTitle: 'Spiritual Counselor & Life Coach',\n      headline: 'Guiding souls toward their highest potential',\n      location: {\n        city: 'Portland',\n        state: 'Oregon',\n        country: 'USA',\n        displayLocation: 'Portland, Oregon'\n      },\n      summary: 'Spiritual counselor and life coach helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life\\'s journeys.',\n      skills: [{\n        name: 'Spiritual Counseling',\n        endorsements: 48,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Life Coaching',\n        endorsements: 44,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Purpose Discovery',\n        endorsements: 37,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 1156\n    }, {\n      ...baseProfile,\n      id: 6,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400',\n      firstName: 'Mystic',\n      lastName: 'Rose',\n      professionalTitle: 'Intuitive Reader & Dream Interpreter',\n      headline: 'Unlocking the messages from your subconscious mind',\n      location: {\n        city: 'Asheville',\n        state: 'North Carolina',\n        country: 'USA',\n        displayLocation: 'Asheville, North Carolina'\n      },\n      summary: 'Intuitive reader specializing in dream interpretation and subconscious guidance. I help clients understand the deeper meanings behind their dreams and intuitive insights.',\n      skills: [{\n        name: 'Dream Interpretation',\n        endorsements: 33,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Intuitive Reading',\n        endorsements: 29,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Subconscious Work',\n        endorsements: 26,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 567\n    }, {\n      ...baseProfile,\n      id: 7,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400',\n      firstName: 'Cosmic',\n      lastName: 'Dawn',\n      professionalTitle: 'Numerologist & Sacred Geometry Expert',\n      headline: 'Discovering your life\\'s blueprint through numbers',\n      location: {\n        city: 'Boulder',\n        state: 'Colorado',\n        country: 'USA',\n        displayLocation: 'Boulder, Colorado'\n      },\n      summary: 'Numerologist and sacred geometry expert who reveals the hidden patterns and meanings in your life through the power of numbers and geometric principles.',\n      skills: [{\n        name: 'Numerology',\n        endorsements: 41,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Sacred Geometry',\n        endorsements: 35,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Life Path Numbers',\n        endorsements: 32,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 723\n    }, {\n      ...baseProfile,\n      id: 8,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400',\n      firstName: 'Serenity',\n      lastName: 'Moon',\n      professionalTitle: 'Meditation Guide & Mindfulness Coach',\n      headline: 'Finding peace and clarity through mindful practice',\n      location: {\n        city: 'San Francisco',\n        state: 'California',\n        country: 'USA',\n        displayLocation: 'San Francisco, California'\n      },\n      summary: 'Meditation guide and mindfulness coach dedicated to helping others find inner peace and mental clarity through various meditation techniques and mindfulness practices.',\n      skills: [{\n        name: 'Meditation Guidance',\n        endorsements: 46,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Mindfulness Coaching',\n        endorsements: 39,\n        proficiencyLevel: 'expert'\n      }, {\n        name: 'Stress Relief',\n        endorsements: 34,\n        proficiencyLevel: 'advanced'\n      }, {\n        name: 'Inner Peace',\n        endorsements: 28,\n        proficiencyLevel: 'advanced'\n      }],\n      profileViews: 934\n    }];\n  }\n  static {\n    this.ɵfac = function ProfileSearchComponent_Factory(t) {\n      return new (t || ProfileSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileSearchComponent,\n      selectors: [[\"app-profile-search\"]],\n      decls: 60,\n      vars: 7,\n      consts: [[1, \"search-container\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"search-fields\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"location\", \"placeholder\", \"City, State, Country\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"skills\", \"placeholder\", \"tarot, astrology, healing, etc.\"], [\"formControlName\", \"sortBy\"], [\"value\", \"relevance\"], [\"value\", \"views\"], [\"value\", \"alphabetical\"], [\"value\", \"recent\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\"], [1, \"search-suggestions\"], [1, \"suggestions-label\"], [\"mat-button\", \"\", 1, \"suggestion-chip\", 3, \"click\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"view-options\"], [1, \"view-toggle\", 3, \"value\", \"valueChange\"], [\"value\", \"grid\"], [\"value\", \"list\"], [\"class\", \"profiles-container\", 3, \"grid-view\", \"list-view\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"pagination-container\", 4, \"ngIf\"], [1, \"profiles-container\"], [3, \"profile\", \"compact\", \"contactClicked\", \"profileClicked\", 4, \"ngFor\", \"ngForOf\"], [3, \"profile\", \"compact\", \"contactClicked\", \"profileClicked\"], [1, \"no-results\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"pagination-container\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n      template: function ProfileSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Find Oracle Professionals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Search through our directory of spiritual advisors and service providers. Try searching by location (e.g., \\\"California\\\", \\\"Sedona\\\") or skills (e.g., \\\"tarot\\\", \\\"astrology\\\", \\\"healing\\\"). \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ProfileSearchComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"mat-form-field\", 3)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 4);\n          i0.ɵɵelementStart(16, \"mat-icon\", 5);\n          i0.ɵɵtext(17, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 3)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Skills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 6);\n          i0.ɵɵelementStart(22, \"mat-icon\", 5);\n          i0.ɵɵtext(23, \"psychology\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 3)(25, \"mat-label\");\n          i0.ɵɵtext(26, \"Sort By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-select\", 7)(28, \"mat-option\", 8);\n          i0.ɵɵtext(29, \"Relevance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-option\", 9);\n          i0.ɵɵtext(31, \"Most Viewed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-option\", 10);\n          i0.ɵɵtext(33, \"Name (A-Z)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-option\", 11);\n          i0.ɵɵtext(35, \"Recently Updated\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"mat-icon\", 5);\n          i0.ɵɵtext(37, \"sort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"button\", 12);\n          i0.ɵɵtemplate(39, ProfileSearchComponent_mat_spinner_39_Template, 1, 0, \"mat-spinner\", 13);\n          i0.ɵɵtemplate(40, ProfileSearchComponent_mat_icon_40_Template, 2, 0, \"mat-icon\", 14);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_42_listener() {\n            return ctx.clearSearch();\n          });\n          i0.ɵɵelementStart(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" Clear \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 16)(47, \"span\", 17);\n          i0.ɵɵtext(48, \"Try searching for:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_49_listener() {\n            return ctx.quickSearch(\"California\");\n          });\n          i0.ɵɵtext(50, \"California\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_51_listener() {\n            return ctx.quickSearch(\"tarot\");\n          });\n          i0.ɵɵtext(52, \"Tarot Reading\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_53_listener() {\n            return ctx.quickSearch(\"astrology\");\n          });\n          i0.ɵɵtext(54, \"Astrology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_55_listener() {\n            return ctx.quickSearch(\"healing\");\n          });\n          i0.ɵɵtext(56, \"Crystal Healing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileSearchComponent_Template_button_click_57_listener() {\n            return ctx.quickSearch(\"meditation\");\n          });\n          i0.ɵɵtext(58, \"Meditation\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(59, ProfileSearchComponent_div_59_Template, 15, 6, \"div\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Searching...\" : \"Search\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchResults);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatButton, i8.MatIcon, i9.MatFormField, i9.MatLabel, i9.MatSuffix, i10.MatInput, i11.MatSelect, i12.MatOption, i13.MatProgressSpinner, i14.MatButtonToggleGroup, i14.MatButtonToggle, i15.MatPaginator, i16.ProfileCardComponent],\n      styles: [\".search-container[_ngcontent-%COMP%] {\\n      padding: 20px;\\n      max-width: 1200px;\\n      margin: 0 auto;\\n    }\\n\\n    .search-fields[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 16px;\\n      margin-bottom: 16px;\\n      flex-wrap: wrap;\\n    }\\n\\n    .search-fields[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n      flex: 1;\\n      min-width: 200px;\\n    }\\n\\n    .search-suggestions[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n      margin-top: 12px;\\n      flex-wrap: wrap;\\n    }\\n\\n    .suggestions-label[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n      color: #666;\\n      margin-right: 8px;\\n    }\\n\\n    .suggestion-chip[_ngcontent-%COMP%] {\\n      font-size: 0.8rem;\\n      height: 32px;\\n      border-radius: 16px;\\n      background-color: #f5f5f5;\\n      color: #673ab7;\\n      border: 1px solid #e0e0e0;\\n    }\\n\\n    .suggestion-chip[_ngcontent-%COMP%]:hover {\\n      background-color: #e3f2fd;\\n      border-color: #673ab7;\\n    }\\n\\n    .results-header[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n      margin: 24px 0 16px 0;\\n      flex-wrap: wrap;\\n      gap: 16px;\\n    }\\n\\n    .results-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0;\\n      color: #333;\\n      font-weight: 500;\\n    }\\n\\n    .view-options[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n    }\\n\\n    .view-toggle[_ngcontent-%COMP%] {\\n      border: 1px solid #e0e0e0;\\n      border-radius: 6px;\\n    }\\n\\n    .profiles-container.grid-view[_ngcontent-%COMP%] {\\n      display: grid;\\n      grid-template-columns: repeat(2, 1fr);\\n      gap: 20px;\\n      margin-bottom: 24px;\\n    }\\n\\n    .profiles-container.list-view[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 16px;\\n      margin-bottom: 24px;\\n    }\\n\\n    .pagination-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      margin-top: 32px;\\n    }\\n\\n    .no-results[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 48px 24px;\\n      color: #666;\\n    }\\n\\n    .no-results[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n      font-size: 48px;\\n      width: 48px;\\n      height: 48px;\\n      color: #ccc;\\n      margin-bottom: 16px;\\n    }\\n\\n    .no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0 0 8px 0;\\n      color: #333;\\n    }\\n\\n    .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 0 0 24px 0;\\n      font-size: 0.95rem;\\n    }\\n\\n    @media (max-width: 768px) {\\n      .search-container[_ngcontent-%COMP%] {\\n        padding: 16px;\\n      }\\n\\n      .search-fields[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        gap: 12px;\\n      }\\n\\n      .results-header[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: flex-start;\\n        gap: 12px;\\n      }\\n\\n      .profiles-container.grid-view[_ngcontent-%COMP%] {\\n        grid-template-columns: 1fr;\\n        gap: 16px;\\n      }\\n    }\\n\\n    @media (max-width: 1024px) {\\n      .profiles-container.grid-view[_ngcontent-%COMP%] {\\n        grid-template-columns: 1fr;\\n      }\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;IAiDgBA,kCAA2D;;;;;IAC3DA,gCAA6B;IAAAA,sBAAM;IAAAA,iBAAW;;;;;;IAqChDA,4CAK8C;IAD5CA;MAAAA;MAAA;MAAA,OAAkBA,8CAAwB;IAAA,EAAC;MAAAA;MAAA;MAAA,OACzBA,+CAAwB;IAAA,EADC;IAE7CA,iBAAmB;;;;;IAJjBA,oCAAmB;;;;;IAHvBA,+BAA0J;IACxJA,gHAMmB;IACrBA,iBAAM;;;;IAR0BA,uDAAuC;IAE/CA,eAAyB;IAAzBA,uDAAyB;;;;;;IASjDA,+BAAoE;IACxDA,0BAAU;IAAAA,iBAAW;IAC/BA,0BAAI;IAAAA,sCAAsB;IAAAA,iBAAK;IAC/BA,yBAAG;IAAAA,yFAAyE;IAAAA,iBAAI;IAChFA,kCAAkE;IAAxBA;MAAAA;MAAA;MAAA,OAASA,oCAAa;IAAA,EAAC;IAC/DA,gCAAU;IAAAA,uBAAO;IAAAA,iBAAW;IAC5BA,yCACF;IAAAA,iBAAS;;;;;;;;;IAIXA,+BAAuE;IAMnEA;MAAAA;MAAA;MAAA,OAAQA,2CAAoB;IAAA,EAAC;IAE/BA,iBAAgB;;;;IANdA,eAAmC;IAAnCA,wDAAmC;;;;;;IAvCzCA,+BAAkD;IAE1CA,YAAgG;IAAAA,iBAAK;IACzGA,+BAA0B;IACCA;MAAAA;MAAA;MAAA;IAAA,EAAoB;IAC3CA,6CAAgC;IACpBA,yBAAS;IAAAA,iBAAW;IAEhCA,6CAAgC;IACpBA,0BAAS;IAAAA,iBAAW;IAMtCA,iFAQM;IAGNA,kFAQM;IAGNA,iFASM;IACRA,iBAAM;;;;IA7CEA,eAAgG;IAAhGA,uIAAgG;IAEzEA,eAAoB;IAApBA,uCAAoB;IAWgEA,eAAuC;IAAvCA,+DAAuC;IAW/HA,eAAyC;IAAzCA,iEAAyC;IAW/BA,eAAkC;IAAlCA,0DAAkC;;;AAiKjF,OAAM,MAAOC,sBAAsB;EAQjCC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,MAAc,EACdC,QAAqB;IAHrB,gBAAW,GAAXH,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAVlB,kBAAa,GAA+B,IAAI;IAChD,cAAS,GAAG,KAAK;IACjB,aAAQ,GAAoB,MAAM;IAClC,gBAAW,GAAG,CAAC;IACf,aAAQ,GAAG,EAAE;IAQX,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC;MACvCC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,WAAW;KACrB,CAAC;EACJ;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,QAAQ;IACN,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,WAAW;IACT,IAAI,CAACX,UAAU,CAACY,KAAK,CAAC;MACpBV,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;KACT,CAAC;IACF,IAAI,CAACE,aAAa,EAAE,CAAC,CAAC;EACxB;;EAEAM,WAAW,CAACC,IAAY;IACtB;IACA,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC;IACpJ,MAAMC,UAAU,GAAGD,SAAS,CAACE,IAAI,CAACC,GAAG,IAAIJ,IAAI,CAACK,WAAW,EAAE,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAC;IAE1E,IAAIF,UAAU,EAAE;MACd,IAAI,CAAChB,UAAU,CAACqB,UAAU,CAAC;QAAEnB,QAAQ,EAAEY,IAAI;QAAEX,MAAM,EAAE;MAAE,CAAE,CAAC;KAC3D,MAAM;MACL,IAAI,CAACH,UAAU,CAACqB,UAAU,CAAC;QAAElB,MAAM,EAAEW,IAAI;QAAEZ,QAAQ,EAAE;MAAE,CAAE,CAAC;;IAG5D,IAAI,CAACM,QAAQ,EAAE;EACjB;EAEQE,aAAa;IACnB,IAAI,CAACY,SAAS,GAAG,IAAI;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACvB,UAAU,CAACwB,KAAK;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,WAAW,GAAG,IAAI,CAACC,mBAAmB,EAAE;MAC9C,IAAIC,gBAAgB,GAAG,CAAC,GAAGF,WAAW,CAAC;MAEvC;MACA,IAAIH,SAAS,CAACrB,QAAQ,IAAIqB,SAAS,CAACrB,QAAQ,CAAC2B,IAAI,EAAE,EAAE;QACnD,MAAMC,cAAc,GAAGP,SAAS,CAACrB,QAAQ,CAACiB,WAAW,EAAE;QACvDS,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACC,OAAO,IAChDA,OAAO,CAAC9B,QAAQ,EAAE+B,eAAe,EAAEd,WAAW,EAAE,CAACC,QAAQ,CAACU,cAAc,CAAC,IACzEE,OAAO,CAAC9B,QAAQ,EAAEgC,IAAI,EAAEf,WAAW,EAAE,CAACC,QAAQ,CAACU,cAAc,CAAC,IAC9DE,OAAO,CAAC9B,QAAQ,EAAEiC,KAAK,EAAEhB,WAAW,EAAE,CAACC,QAAQ,CAACU,cAAc,CAAC,CAChE;;MAGH;MACA,IAAIP,SAAS,CAACpB,MAAM,IAAIoB,SAAS,CAACpB,MAAM,CAAC0B,IAAI,EAAE,EAAE;QAC/C,MAAMO,YAAY,GAAGb,SAAS,CAACpB,MAAM,CAACgB,WAAW,EAAE,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAS,IAAKA,CAAC,CAACV,IAAI,EAAE,CAAC;QAC3FD,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACC,OAAO,IAChDI,YAAY,CAACnB,IAAI,CAAEuB,KAAa,IAC9BR,OAAO,CAAC7B,MAAM,CAACc,IAAI,CAACwB,YAAY,IAC9BA,YAAY,CAACC,IAAI,CAACvB,WAAW,EAAE,CAACC,QAAQ,CAACoB,KAAK,CAAC,CAChD,IACDR,OAAO,CAACW,iBAAiB,EAAExB,WAAW,EAAE,CAACC,QAAQ,CAACoB,KAAK,CAAC,IACxDR,OAAO,CAACY,OAAO,EAAEzB,WAAW,EAAE,CAACC,QAAQ,CAACoB,KAAK,CAAC,CAC/C,CACF;;MAGH;MACA,QAAQjB,SAAS,CAAClB,MAAM;QACtB,KAAK,OAAO;UACVuB,gBAAgB,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,YAAY,GAAGF,CAAC,CAACE,YAAY,CAAC;UAChE;QACF,KAAK,cAAc;UACjBpB,gBAAgB,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACG,SAAS,CAACC,aAAa,CAACH,CAAC,CAACE,SAAS,CAAC,CAAC;UACvE;QACF,KAAK,QAAQ;UACXrB,gBAAgB,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAII,IAAI,CAACJ,CAAC,CAACK,SAAS,CAAC,CAACC,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACL,CAAC,CAACM,SAAS,CAAC,CAACC,OAAO,EAAE,CAAC;UAClG;QACF;UAAS;UACP;UACA;MAAM;MAGV,IAAI,CAACC,aAAa,GAAG;QACnBC,QAAQ,EAAE3B,gBAAgB;QAC1B4B,UAAU,EAAE5B,gBAAgB,CAAC6B,MAAM;QACnChD,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BiD,UAAU,EAAEC,IAAI,CAACC,IAAI,CAAChC,gBAAgB,CAAC6B,MAAM,GAAG,IAAI,CAACI,QAAQ,CAAC;QAC9DA,QAAQ,EAAE,IAAI,CAACA;OAChB;MAED,IAAI,CAACvC,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAwC,YAAY,CAACC,KAAgB;IAC3B,IAAI,CAACtD,WAAW,GAAGsD,KAAK,CAACC,SAAS,GAAG,CAAC;IACtC,IAAI,CAACH,QAAQ,GAAGE,KAAK,CAACF,QAAQ;IAC9B,IAAI,CAACnD,aAAa,EAAE;EACtB;EAEAuD,gBAAgB,CAACjC,OAAoB;IACnC;IACA,IAAIA,OAAO,CAACkC,WAAW,EAAEC,KAAK,EAAE;MAC9BC,MAAM,CAAClE,QAAQ,CAACmE,IAAI,GAAG,UAAUrC,OAAO,CAACkC,WAAW,CAACC,KAAK,EAAE;KAC7D,MAAM;MACL,IAAI,CAACpE,QAAQ,CAACuE,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;QAC/DC,QAAQ,EAAE;OACX,CAAC;;EAEN;EAEAC,gBAAgB,CAACxC,OAAoB;IACnC;IACA,IAAI,CAAClC,MAAM,CAAC2E,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEQlE,aAAa;IACnB;IACA,IAAI,CAAC+C,aAAa,GAAG;MACnBC,QAAQ,EAAE,IAAI,CAAC5B,mBAAmB,EAAE;MACpC6B,UAAU,EAAE,CAAC;MACb/C,WAAW,EAAE,CAAC;MACdiD,UAAU,EAAE,CAAC;MACbG,QAAQ,EAAE;KACX;EACH;EAEQlC,mBAAmB;IACzB,MAAM+C,WAAW,GAAG;MAClBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE,iBAAiB;MACvBC,QAAQ,EAAE,IAAI;MACdC,2BAA2B,EAAE,EAAE;MAC/Bb,WAAW,EAAE;QACXC,KAAK,EAAE,qBAAqB;QAC5Ba,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE,wBAAwB;QACjCC,YAAY,EAAE,CACZ;UACEC,MAAM,EAAE,mBAAmB;UAC3BC,IAAI,EAAE,UAAU;UAChBN,QAAQ,EAAE,IAAI;UACdO,SAAS,EAAE;SACZ;OAEJ;MACDC,WAAW,EAAE,CACX;QACEC,OAAO,EAAE,sBAAsB;QAC/BC,QAAQ,EAAE,mBAAmB;QAC7BC,SAAS,EAAE,IAAItC,IAAI,CAAC,YAAY,CAAC;QACjCuC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,oDAAoD;QACjEzF,QAAQ,EAAE;OACX,CACF;MACD0F,cAAc,EAAE,CACd;QACEC,KAAK,EAAE,6BAA6B;QACpCF,WAAW,EAAE,2CAA2C;QACxDG,SAAS,EAAE,CAAC,oEAAoE,CAAC;QACjFC,YAAY,EAAE,CAAC,WAAW,EAAE,oBAAoB,CAAC;QACjDC,WAAW,EAAE,IAAI7C,IAAI,CAAC,YAAY,CAAC;QACnC8C,QAAQ,EAAE;OACX,CACF;MACDC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfrD,YAAY,EAAE,IAAI;MAClBsD,SAAS,EAAE,IAAInD,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC;IAED,OAAO,CACL;MACE,GAAGuB,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,MAAM;MACjBwD,QAAQ,EAAE,YAAY;MACtB9D,iBAAiB,EAAE,wCAAwC;MAC3D+D,QAAQ,EAAE,wDAAwD;MAClExG,QAAQ,EAAE;QACRgC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,SAAS;QAChBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,wKAAwK;MACjLzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,qBAAqB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC7E;QAAEnE,IAAI,EAAE,eAAe;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACvE;QAAEnE,IAAI,EAAE,sBAAsB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAChF;QAAEnE,IAAI,EAAE,iBAAiB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE;KAE9E,EACD;MACE,GAAGnC,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,QAAQ;MACnBwD,QAAQ,EAAE,WAAW;MACrB9D,iBAAiB,EAAE,2CAA2C;MAC9D+D,QAAQ,EAAE,gDAAgD;MAC1DxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,YAAY;QACnBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,8JAA8J;MACvKzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,sBAAsB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC9E;QAAEnE,IAAI,EAAE,uBAAuB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC/E;QAAEnE,IAAI,EAAE,kBAAkB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAC7E;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,MAAM;MACjBwD,QAAQ,EAAE,WAAW;MACrB9D,iBAAiB,EAAE,gCAAgC;MACnD+D,QAAQ,EAAE,uDAAuD;MACjExG,QAAQ,EAAE;QACRgC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,YAAY;QACnBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,wJAAwJ;MACjKzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,iBAAiB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACzE;QAAEnE,IAAI,EAAE,aAAa;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACrE;QAAEnE,IAAI,EAAE,kBAAkB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC5E;QAAEnE,IAAI,EAAE,YAAY;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAc,CAAE,CAC3E;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,OAAO;MAClBwD,QAAQ,EAAE,WAAW;MACrB9D,iBAAiB,EAAE,8BAA8B;MACjD+D,QAAQ,EAAE,2CAA2C;MACrDxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,WAAW;QAClBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,iLAAiL;MAC1LzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,cAAc;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACtE;QAAEnE,IAAI,EAAE,oBAAoB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC5E;QAAEnE,IAAI,EAAE,qBAAqB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAChF;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,iEAAiE;MAClFvD,SAAS,EAAE,QAAQ;MACnBwD,QAAQ,EAAE,QAAQ;MAClB9D,iBAAiB,EAAE,kCAAkC;MACrD+D,QAAQ,EAAE,8CAA8C;MACxDxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,QAAQ;QACfwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,2JAA2J;MACpKzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,sBAAsB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC9E;QAAEnE,IAAI,EAAE,eAAe;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACvE;QAAEnE,IAAI,EAAE,mBAAmB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAC9E;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,QAAQ;MACnBwD,QAAQ,EAAE,MAAM;MAChB9D,iBAAiB,EAAE,sCAAsC;MACzD+D,QAAQ,EAAE,oDAAoD;MAC9DxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,gBAAgB;QACvBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,4KAA4K;MACrLzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,sBAAsB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC9E;QAAEnE,IAAI,EAAE,mBAAmB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC3E;QAAEnE,IAAI,EAAE,mBAAmB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAC9E;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,QAAQ;MACnBwD,QAAQ,EAAE,MAAM;MAChB9D,iBAAiB,EAAE,uCAAuC;MAC1D+D,QAAQ,EAAE,oDAAoD;MAC9DxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,UAAU;QACjBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,0JAA0J;MACnKzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,YAAY;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACpE;QAAEnE,IAAI,EAAE,iBAAiB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACzE;QAAEnE,IAAI,EAAE,mBAAmB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CAC9E;MACD7D,YAAY,EAAE;KACf,EACD;MACE,GAAG0B,WAAW;MACd6B,EAAE,EAAE,CAAC;MACLC,eAAe,EAAE,oEAAoE;MACrFvD,SAAS,EAAE,UAAU;MACrBwD,QAAQ,EAAE,MAAM;MAChB9D,iBAAiB,EAAE,sCAAsC;MACzD+D,QAAQ,EAAE,oDAAoD;MAC9DxG,QAAQ,EAAE;QACRgC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,YAAY;QACnBwE,OAAO,EAAE,KAAK;QACd1E,eAAe,EAAE;OAClB;MACDW,OAAO,EAAE,yKAAyK;MAClLzC,MAAM,EAAE,CACN;QAAEuC,IAAI,EAAE,qBAAqB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC7E;QAAEnE,IAAI,EAAE,sBAAsB;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC9E;QAAEnE,IAAI,EAAE,eAAe;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EACzE;QAAEnE,IAAI,EAAE,aAAa;QAAEkE,YAAY,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAU,CAAE,CACxE;MACD7D,YAAY,EAAE;KACf,CACF;EACH;;;uBA3XWtD,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAoH;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAlQ/BzH,8BAA8B;UAIZA,sBAAM;UAAAA,iBAAW;UAC3BA,2CACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,iNACF;UAAAA,iBAAoB;UAEtBA,wCAAkB;UACeA;YAAA,OAAY0H,cAAU;UAAA,EAAC;UACpD1H,+BAA2B;UAEZA,yBAAQ;UAAAA,iBAAY;UAC/BA,4BAA8E;UAC9EA,oCAAoB;UAAAA,4BAAW;UAAAA,iBAAW;UAG5CA,0CAAqC;UACxBA,uBAAM;UAAAA,iBAAY;UAC7BA,4BAAuF;UACvFA,oCAAoB;UAAAA,2BAAU;UAAAA,iBAAW;UAG3CA,0CAAqC;UACxBA,wBAAO;UAAAA,iBAAY;UAC9BA,sCAAqC;UACLA,0BAAS;UAAAA,iBAAa;UACpDA,sCAA0B;UAAAA,4BAAW;UAAAA,iBAAa;UAClDA,uCAAiC;UAAAA,2BAAU;UAAAA,iBAAa;UACxDA,uCAA2B;UAAAA,iCAAgB;UAAAA,iBAAa;UAE1DA,oCAAoB;UAAAA,qBAAI;UAAAA,iBAAW;UAGrCA,mCAA+E;UAC7EA,0FAA2D;UAC3DA,oFAA8C;UAC9CA,aACF;UAAAA,iBAAS;UAETA,mCAAwF;UAA/CA;YAAA,OAAS0H,iBAAa;UAAA,EAAC;UAC9D1H,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,wBACF;UAAAA,iBAAS;UAIXA,gCAAgC;UACEA,mCAAkB;UAAAA,iBAAO;UACzDA,mCAA+E;UAApCA;YAAA,OAAS0H,gBAAY,YAAY,CAAC;UAAA,EAAC;UAAC1H,2BAAU;UAAAA,iBAAS;UAClGA,mCAA0E;UAA/BA;YAAA,OAAS0H,gBAAY,OAAO,CAAC;UAAA,EAAC;UAAC1H,8BAAa;UAAAA,iBAAS;UAChGA,mCAA8E;UAAnCA;YAAA,OAAS0H,gBAAY,WAAW,CAAC;UAAA,EAAC;UAAC1H,0BAAS;UAAAA,iBAAS;UAChGA,mCAA4E;UAAjCA;YAAA,OAAS0H,gBAAY,SAAS,CAAC;UAAA,EAAC;UAAC1H,gCAAe;UAAAA,iBAAS;UACpGA,mCAA+E;UAApCA;YAAA,OAAS0H,gBAAY,YAAY,CAAC;UAAA,EAAC;UAAC1H,2BAAU;UAAAA,iBAAS;UAItGA,2EA+CM;UACRA,iBAAmB;;;UAhGXA,gBAAwB;UAAxBA,0CAAwB;UAyB8BA,gBAAsB;UAAtBA,wCAAsB;UAC9DA,eAAe;UAAfA,oCAAe;UAClBA,eAAgB;UAAhBA,qCAAgB;UAC3BA,eACF;UADEA,0EACF;UAEiEA,eAAsB;UAAtBA,wCAAsB;UAiB9DA,gBAAmB;UAAnBA,wCAAmB", "names": ["i0", "ProfileSearchComponent", "constructor", "formBuilder", "profileService", "router", "snackBar", "searchForm", "group", "location", "skills", "experience", "sortBy", "ngOnInit", "loadDummyData", "onSearch", "currentPage", "performSearch", "clearSearch", "reset", "quickSearch", "term", "locations", "isLocation", "some", "loc", "toLowerCase", "includes", "patchValue", "isLoading", "formValue", "value", "setTimeout", "allProfiles", "createDummyProfiles", "filteredProfiles", "trim", "locationFilter", "filter", "profile", "displayLocation", "city", "state", "skillsFilter", "split", "map", "s", "skill", "profileSkill", "name", "professional<PERSON>itle", "summary", "sort", "a", "b", "profileViews", "firstName", "localeCompare", "Date", "updatedAt", "getTime", "searchResults", "profiles", "totalCount", "length", "totalPages", "Math", "ceil", "pageSize", "onPageChange", "event", "pageIndex", "onContactProfile", "contactInfo", "email", "window", "href", "open", "duration", "onProfileClicked", "navigate", "baseProfile", "userId", "username", "slug", "isPublic", "profileCompletionPercentage", "isEmailPublic", "website", "phoneNumbers", "number", "type", "isPrimary", "experiences", "company", "position", "startDate", "isCurrent", "description", "portfolioItems", "title", "imageUrls", "technologies", "completedAt", "category", "blogPosts", "achievements", "certifications", "socialLinks", "createdAt", "id", "profilePhotoUrl", "lastName", "headline", "country", "endorsements", "proficiencyLevel", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-search\\profile-search.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { PageEvent } from '@angular/material/paginator';\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { UserProfile, ProfileSearchFilters, ProfileSearchResult } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-search',\r\n  template: `\r\n    <div class=\"search-container\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>search</mat-icon>\r\n            Find Oracle Professionals\r\n          </mat-card-title>\r\n          <mat-card-subtitle>\r\n            Search through our directory of spiritual advisors and service providers. Try searching by location (e.g., \"California\", \"Sedona\") or skills (e.g., \"tarot\", \"astrology\", \"healing\").\r\n          </mat-card-subtitle>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\">\r\n            <div class=\"search-fields\">\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Location</mat-label>\r\n                <input matInput formControlName=\"location\" placeholder=\"City, State, Country\">\r\n                <mat-icon matSuffix>location_on</mat-icon>\r\n              </mat-form-field>\r\n              \r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Skills</mat-label>\r\n                <input matInput formControlName=\"skills\" placeholder=\"tarot, astrology, healing, etc.\">\r\n                <mat-icon matSuffix>psychology</mat-icon>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\">\r\n                <mat-label>Sort By</mat-label>\r\n                <mat-select formControlName=\"sortBy\">\r\n                  <mat-option value=\"relevance\">Relevance</mat-option>\r\n                  <mat-option value=\"views\">Most Viewed</mat-option>\r\n                  <mat-option value=\"alphabetical\">Name (A-Z)</mat-option>\r\n                  <mat-option value=\"recent\">Recently Updated</mat-option>\r\n                </mat-select>\r\n                <mat-icon matSuffix>sort</mat-icon>\r\n              </mat-form-field>\r\n              \r\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading\">\r\n                <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\r\n                <mat-icon *ngIf=\"!isLoading\">search</mat-icon>\r\n                {{ isLoading ? 'Searching...' : 'Search' }}\r\n              </button>\r\n\r\n              <button mat-stroked-button type=\"button\" (click)=\"clearSearch()\" [disabled]=\"isLoading\">\r\n                <mat-icon>clear</mat-icon>\r\n                Clear\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Quick Search Suggestions -->\r\n            <div class=\"search-suggestions\">\r\n              <span class=\"suggestions-label\">Try searching for:</span>\r\n              <button mat-button class=\"suggestion-chip\" (click)=\"quickSearch('California')\">California</button>\r\n              <button mat-button class=\"suggestion-chip\" (click)=\"quickSearch('tarot')\">Tarot Reading</button>\r\n              <button mat-button class=\"suggestion-chip\" (click)=\"quickSearch('astrology')\">Astrology</button>\r\n              <button mat-button class=\"suggestion-chip\" (click)=\"quickSearch('healing')\">Crystal Healing</button>\r\n              <button mat-button class=\"suggestion-chip\" (click)=\"quickSearch('meditation')\">Meditation</button>\r\n            </div>\r\n          </form>\r\n          \r\n          <div class=\"search-results\" *ngIf=\"searchResults\">\r\n            <div class=\"results-header\">\r\n              <h3>{{ searchResults.totalCount }} professional{{ searchResults.totalCount !== 1 ? 's' : '' }} found</h3>\r\n              <div class=\"view-options\">\r\n                <mat-button-toggle-group [(value)]=\"viewMode\" class=\"view-toggle\">\r\n                  <mat-button-toggle value=\"grid\">\r\n                    <mat-icon>grid_view</mat-icon>\r\n                  </mat-button-toggle>\r\n                  <mat-button-toggle value=\"list\">\r\n                    <mat-icon>view_list</mat-icon>\r\n                  </mat-button-toggle>\r\n                </mat-button-toggle-group>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profiles-container\" [class.grid-view]=\"viewMode === 'grid'\" [class.list-view]=\"viewMode === 'list'\" *ngIf=\"searchResults.profiles.length > 0\">\r\n              <app-profile-card\r\n                *ngFor=\"let profile of searchResults.profiles\"\r\n                [profile]=\"profile\"\r\n                [compact]=\"viewMode === 'grid'\"\r\n                (contactClicked)=\"onContactProfile($event)\"\r\n                (profileClicked)=\"onProfileClicked($event)\">\r\n              </app-profile-card>\r\n            </div>\r\n\r\n            <!-- No Results Message -->\r\n            <div class=\"no-results\" *ngIf=\"searchResults.profiles.length === 0\">\r\n              <mat-icon>search_off</mat-icon>\r\n              <h3>No professionals found</h3>\r\n              <p>Try adjusting your search criteria or browse all available professionals.</p>\r\n              <button mat-raised-button color=\"primary\" (click)=\"clearSearch()\">\r\n                <mat-icon>refresh</mat-icon>\r\n                Show All Professionals\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Pagination -->\r\n            <div class=\"pagination-container\" *ngIf=\"searchResults.totalPages > 1\">\r\n              <mat-paginator\r\n                [length]=\"searchResults.totalCount\"\r\n                [pageSize]=\"pageSize\"\r\n                [pageSizeOptions]=\"[10, 20, 50]\"\r\n                [pageIndex]=\"currentPage - 1\"\r\n                (page)=\"onPageChange($event)\"\r\n                showFirstLastButtons>\r\n              </mat-paginator>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .search-container {\r\n      padding: 20px;\r\n      max-width: 1200px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    .search-fields {\r\n      display: flex;\r\n      gap: 16px;\r\n      margin-bottom: 16px;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .search-fields mat-form-field {\r\n      flex: 1;\r\n      min-width: 200px;\r\n    }\r\n\r\n    .search-suggestions {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin-top: 12px;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .suggestions-label {\r\n      font-size: 0.9rem;\r\n      color: #666;\r\n      margin-right: 8px;\r\n    }\r\n\r\n    .suggestion-chip {\r\n      font-size: 0.8rem;\r\n      height: 32px;\r\n      border-radius: 16px;\r\n      background-color: #f5f5f5;\r\n      color: #673ab7;\r\n      border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .suggestion-chip:hover {\r\n      background-color: #e3f2fd;\r\n      border-color: #673ab7;\r\n    }\r\n\r\n    .results-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin: 24px 0 16px 0;\r\n      flex-wrap: wrap;\r\n      gap: 16px;\r\n    }\r\n\r\n    .results-header h3 {\r\n      margin: 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .view-options {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n    }\r\n\r\n    .view-toggle {\r\n      border: 1px solid #e0e0e0;\r\n      border-radius: 6px;\r\n    }\r\n\r\n    .profiles-container.grid-view {\r\n      display: grid;\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 20px;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .profiles-container.list-view {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .pagination-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-top: 32px;\r\n    }\r\n\r\n    .no-results {\r\n      text-align: center;\r\n      padding: 48px 24px;\r\n      color: #666;\r\n    }\r\n\r\n    .no-results mat-icon {\r\n      font-size: 48px;\r\n      width: 48px;\r\n      height: 48px;\r\n      color: #ccc;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .no-results h3 {\r\n      margin: 0 0 8px 0;\r\n      color: #333;\r\n    }\r\n\r\n    .no-results p {\r\n      margin: 0 0 24px 0;\r\n      font-size: 0.95rem;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .search-container {\r\n        padding: 16px;\r\n      }\r\n\r\n      .search-fields {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n      }\r\n\r\n      .results-header {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n      }\r\n\r\n      .profiles-container.grid-view {\r\n        grid-template-columns: 1fr;\r\n        gap: 16px;\r\n      }\r\n    }\r\n\r\n    @media (max-width: 1024px) {\r\n      .profiles-container.grid-view {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class ProfileSearchComponent implements OnInit {\r\n  searchForm: FormGroup;\r\n  searchResults: ProfileSearchResult | null = null;\r\n  isLoading = false;\r\n  viewMode: 'grid' | 'list' = 'grid';\r\n  currentPage = 1;\r\n  pageSize = 20;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.searchForm = this.formBuilder.group({\r\n      location: [''],\r\n      skills: [''],\r\n      experience: [''],\r\n      sortBy: ['relevance']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Load dummy data for demonstration\r\n    this.loadDummyData();\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.currentPage = 1;\r\n    this.performSearch();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchForm.reset({\r\n      location: '',\r\n      skills: '',\r\n      experience: '',\r\n      sortBy: 'relevance'\r\n    });\r\n    this.loadDummyData(); // Reset to show all profiles\r\n  }\r\n\r\n  quickSearch(term: string): void {\r\n    // Determine if it's a location or skill search\r\n    const locations = ['california', 'arizona', 'sedona', 'los angeles', 'santa fe', 'new orleans', 'portland', 'asheville', 'boulder', 'san francisco'];\r\n    const isLocation = locations.some(loc => term.toLowerCase().includes(loc));\r\n\r\n    if (isLocation) {\r\n      this.searchForm.patchValue({ location: term, skills: '' });\r\n    } else {\r\n      this.searchForm.patchValue({ skills: term, location: '' });\r\n    }\r\n\r\n    this.onSearch();\r\n  }\r\n\r\n  private performSearch(): void {\r\n    this.isLoading = true;\r\n    const formValue = this.searchForm.value;\r\n\r\n    // Simulate search delay for realistic experience\r\n    setTimeout(() => {\r\n      const allProfiles = this.createDummyProfiles();\r\n      let filteredProfiles = [...allProfiles];\r\n\r\n      // Apply location filter\r\n      if (formValue.location && formValue.location.trim()) {\r\n        const locationFilter = formValue.location.toLowerCase();\r\n        filteredProfiles = filteredProfiles.filter(profile =>\r\n          profile.location?.displayLocation?.toLowerCase().includes(locationFilter) ||\r\n          profile.location?.city?.toLowerCase().includes(locationFilter) ||\r\n          profile.location?.state?.toLowerCase().includes(locationFilter)\r\n        );\r\n      }\r\n\r\n      // Apply skills filter\r\n      if (formValue.skills && formValue.skills.trim()) {\r\n        const skillsFilter = formValue.skills.toLowerCase().split(',').map((s: string) => s.trim());\r\n        filteredProfiles = filteredProfiles.filter(profile =>\r\n          skillsFilter.some((skill: string) =>\r\n            profile.skills.some(profileSkill =>\r\n              profileSkill.name.toLowerCase().includes(skill)\r\n            ) ||\r\n            profile.professionalTitle?.toLowerCase().includes(skill) ||\r\n            profile.summary?.toLowerCase().includes(skill)\r\n          )\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      switch (formValue.sortBy) {\r\n        case 'views':\r\n          filteredProfiles.sort((a, b) => b.profileViews - a.profileViews);\r\n          break;\r\n        case 'alphabetical':\r\n          filteredProfiles.sort((a, b) => a.firstName.localeCompare(b.firstName));\r\n          break;\r\n        case 'recent':\r\n          filteredProfiles.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());\r\n          break;\r\n        default: // relevance\r\n          // Keep original order for relevance\r\n          break;\r\n      }\r\n\r\n      this.searchResults = {\r\n        profiles: filteredProfiles,\r\n        totalCount: filteredProfiles.length,\r\n        currentPage: this.currentPage,\r\n        totalPages: Math.ceil(filteredProfiles.length / this.pageSize),\r\n        pageSize: this.pageSize\r\n      };\r\n\r\n      this.isLoading = false;\r\n    }, 500); // 500ms delay to simulate API call\r\n  }\r\n\r\n  onPageChange(event: PageEvent): void {\r\n    this.currentPage = event.pageIndex + 1;\r\n    this.pageSize = event.pageSize;\r\n    this.performSearch();\r\n  }\r\n\r\n  onContactProfile(profile: UserProfile): void {\r\n    // Handle contact action - could open a dialog or navigate to contact form\r\n    if (profile.contactInfo?.email) {\r\n      window.location.href = `mailto:${profile.contactInfo.email}`;\r\n    } else {\r\n      this.snackBar.open('Contact information not available', 'Close', {\r\n        duration: 3000\r\n      });\r\n    }\r\n  }\r\n\r\n  onProfileClicked(profile: UserProfile): void {\r\n    // All profiles lead to luna-starweaver for demo purposes\r\n    this.router.navigate(['/oracle', 1]); // Assuming luna-starweaver has ID 1\r\n  }\r\n\r\n  private loadDummyData(): void {\r\n    // Create dummy search results with various oracle profiles\r\n    this.searchResults = {\r\n      profiles: this.createDummyProfiles(),\r\n      totalCount: 8,\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      pageSize: 20\r\n    };\r\n  }\r\n\r\n  private createDummyProfiles(): UserProfile[] {\r\n    const baseProfile = {\r\n      userId: 1,\r\n      username: 'luna-starweaver',\r\n      slug: 'luna-starweaver',\r\n      isPublic: true,\r\n      profileCompletionPercentage: 95,\r\n      contactInfo: {\r\n        email: '<EMAIL>',\r\n        isEmailPublic: true,\r\n        website: 'https://starweaver.com',\r\n        phoneNumbers: [\r\n          {\r\n            number: '+****************',\r\n            type: 'business',\r\n            isPublic: true,\r\n            isPrimary: true\r\n          }\r\n        ]\r\n      },\r\n      experiences: [\r\n        {\r\n          company: 'Cosmic Wisdom Center',\r\n          position: 'Senior Astrologer',\r\n          startDate: new Date('2018-01-01'),\r\n          isCurrent: true,\r\n          description: 'Leading astrologer providing personalized readings',\r\n          location: 'Sedona, Arizona'\r\n        }\r\n      ],\r\n      portfolioItems: [\r\n        {\r\n          title: 'Cosmic Birth Chart Analysis',\r\n          description: 'Comprehensive natal chart reading service',\r\n          imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],\r\n          technologies: ['Astrology', 'Spiritual Guidance'],\r\n          completedAt: new Date('2023-01-01'),\r\n          category: 'Astrology Services'\r\n        }\r\n      ],\r\n      blogPosts: [],\r\n      achievements: [],\r\n      certifications: [],\r\n      socialLinks: [],\r\n      profileViews: 1247,\r\n      createdAt: new Date('2020-01-01'),\r\n      updatedAt: new Date('2023-12-01')\r\n    };\r\n\r\n    return [\r\n      {\r\n        ...baseProfile,\r\n        id: 1,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',\r\n        firstName: 'Luna',\r\n        lastName: 'Starweaver',\r\n        professionalTitle: 'Professional Astrologer & Cosmic Guide',\r\n        headline: 'Illuminating your path through the wisdom of the stars',\r\n        location: {\r\n          city: 'Sedona',\r\n          state: 'Arizona',\r\n          country: 'USA',\r\n          displayLocation: 'Sedona, Arizona'\r\n        },\r\n        summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling.',\r\n        skills: [\r\n          { name: 'Natal Chart Reading', endorsements: 45, proficiencyLevel: 'expert' },\r\n          { name: 'Tarot Reading', endorsements: 38, proficiencyLevel: 'expert' },\r\n          { name: 'Spiritual Counseling', endorsements: 32, proficiencyLevel: 'advanced' },\r\n          { name: 'Crystal Healing', endorsements: 28, proficiencyLevel: 'advanced' }\r\n        ]\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 2,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',\r\n        firstName: 'Marcus',\r\n        lastName: 'Celestial',\r\n        professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',\r\n        headline: 'Unlocking the secrets of your cosmic blueprint',\r\n        location: {\r\n          city: 'Los Angeles',\r\n          state: 'California',\r\n          country: 'USA',\r\n          displayLocation: 'Los Angeles, California'\r\n        },\r\n        summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges.',\r\n        skills: [\r\n          { name: 'Birth Chart Analysis', endorsements: 52, proficiencyLevel: 'expert' },\r\n          { name: 'Compatibility Reading', endorsements: 41, proficiencyLevel: 'expert' },\r\n          { name: 'Life Transitions', endorsements: 35, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 892\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 3,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',\r\n        firstName: 'Sage',\r\n        lastName: 'Moonchild',\r\n        professionalTitle: 'Crystal Healer & Energy Worker',\r\n        headline: 'Healing through the power of crystals and energy work',\r\n        location: {\r\n          city: 'Santa Fe',\r\n          state: 'New Mexico',\r\n          country: 'USA',\r\n          displayLocation: 'Santa Fe, New Mexico'\r\n        },\r\n        summary: 'Crystal healer and energy worker dedicated to helping others find balance and healing. I combine crystal therapy with meditation and chakra balancing.',\r\n        skills: [\r\n          { name: 'Crystal Healing', endorsements: 38, proficiencyLevel: 'expert' },\r\n          { name: 'Energy Work', endorsements: 34, proficiencyLevel: 'expert' },\r\n          { name: 'Chakra Balancing', endorsements: 29, proficiencyLevel: 'advanced' },\r\n          { name: 'Meditation', endorsements: 25, proficiencyLevel: 'intermediate' }\r\n        ],\r\n        profileViews: 654\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 4,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',\r\n        firstName: 'River',\r\n        lastName: 'Palmistry',\r\n        professionalTitle: 'Third-Generation Palm Reader',\r\n        headline: 'Reading the stories written in your hands',\r\n        location: {\r\n          city: 'New Orleans',\r\n          state: 'Louisiana',\r\n          country: 'USA',\r\n          displayLocation: 'New Orleans, Louisiana'\r\n        },\r\n        summary: 'Third-generation palm reader with a gift for seeing life\\'s path through the lines of your hands. I provide insights into personality, relationships, and future possibilities.',\r\n        skills: [\r\n          { name: 'Palm Reading', endorsements: 42, proficiencyLevel: 'expert' },\r\n          { name: 'Life Path Analysis', endorsements: 36, proficiencyLevel: 'expert' },\r\n          { name: 'Personality Reading', endorsements: 31, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 789\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 5,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',\r\n        firstName: 'Aurora',\r\n        lastName: 'Wisdom',\r\n        professionalTitle: 'Spiritual Counselor & Life Coach',\r\n        headline: 'Guiding souls toward their highest potential',\r\n        location: {\r\n          city: 'Portland',\r\n          state: 'Oregon',\r\n          country: 'USA',\r\n          displayLocation: 'Portland, Oregon'\r\n        },\r\n        summary: 'Spiritual counselor and life coach helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life\\'s journeys.',\r\n        skills: [\r\n          { name: 'Spiritual Counseling', endorsements: 48, proficiencyLevel: 'expert' },\r\n          { name: 'Life Coaching', endorsements: 44, proficiencyLevel: 'expert' },\r\n          { name: 'Purpose Discovery', endorsements: 37, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 1156\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 6,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400',\r\n        firstName: 'Mystic',\r\n        lastName: 'Rose',\r\n        professionalTitle: 'Intuitive Reader & Dream Interpreter',\r\n        headline: 'Unlocking the messages from your subconscious mind',\r\n        location: {\r\n          city: 'Asheville',\r\n          state: 'North Carolina',\r\n          country: 'USA',\r\n          displayLocation: 'Asheville, North Carolina'\r\n        },\r\n        summary: 'Intuitive reader specializing in dream interpretation and subconscious guidance. I help clients understand the deeper meanings behind their dreams and intuitive insights.',\r\n        skills: [\r\n          { name: 'Dream Interpretation', endorsements: 33, proficiencyLevel: 'expert' },\r\n          { name: 'Intuitive Reading', endorsements: 29, proficiencyLevel: 'expert' },\r\n          { name: 'Subconscious Work', endorsements: 26, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 567\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 7,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400',\r\n        firstName: 'Cosmic',\r\n        lastName: 'Dawn',\r\n        professionalTitle: 'Numerologist & Sacred Geometry Expert',\r\n        headline: 'Discovering your life\\'s blueprint through numbers',\r\n        location: {\r\n          city: 'Boulder',\r\n          state: 'Colorado',\r\n          country: 'USA',\r\n          displayLocation: 'Boulder, Colorado'\r\n        },\r\n        summary: 'Numerologist and sacred geometry expert who reveals the hidden patterns and meanings in your life through the power of numbers and geometric principles.',\r\n        skills: [\r\n          { name: 'Numerology', endorsements: 41, proficiencyLevel: 'expert' },\r\n          { name: 'Sacred Geometry', endorsements: 35, proficiencyLevel: 'expert' },\r\n          { name: 'Life Path Numbers', endorsements: 32, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 723\r\n      },\r\n      {\r\n        ...baseProfile,\r\n        id: 8,\r\n        profilePhotoUrl: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400',\r\n        firstName: 'Serenity',\r\n        lastName: 'Moon',\r\n        professionalTitle: 'Meditation Guide & Mindfulness Coach',\r\n        headline: 'Finding peace and clarity through mindful practice',\r\n        location: {\r\n          city: 'San Francisco',\r\n          state: 'California',\r\n          country: 'USA',\r\n          displayLocation: 'San Francisco, California'\r\n        },\r\n        summary: 'Meditation guide and mindfulness coach dedicated to helping others find inner peace and mental clarity through various meditation techniques and mindfulness practices.',\r\n        skills: [\r\n          { name: 'Meditation Guidance', endorsements: 46, proficiencyLevel: 'expert' },\r\n          { name: 'Mindfulness Coaching', endorsements: 39, proficiencyLevel: 'expert' },\r\n          { name: 'Stress Relief', endorsements: 34, proficiencyLevel: 'advanced' },\r\n          { name: 'Inner Peace', endorsements: 28, proficiencyLevel: 'advanced' }\r\n        ],\r\n        profileViews: 934\r\n      }\r\n    ];\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}