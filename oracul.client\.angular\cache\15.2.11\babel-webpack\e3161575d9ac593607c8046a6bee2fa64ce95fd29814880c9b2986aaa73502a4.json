{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ProfileAnalyticsComponent {\n  static {\n    this.ɵfac = function ProfileAnalyticsComponent_Factory(t) {\n      return new (t || ProfileAnalyticsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileAnalyticsComponent,\n      selectors: [[\"app-profile-analytics\"]],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"profile-analytics\"]],\n      template: function ProfileAnalyticsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Profile Analytics \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n          i0.ɵɵtext(9, \"Profile analytics component - Coming soon!\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".profile-analytics[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHJvZmlsZS9jb21wb25lbnRzL3Byb2ZpbGUtYW5hbHl0aWNzL3Byb2ZpbGUtYW5hbHl0aWNzLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0kscUJBQXFCLGFBQWEsRUFBRSIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5wcm9maWxlLWFuYWx5dGljcyB7IHBhZGRpbmc6IDIwcHg7IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAuBA,OAAM,MAAOA,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAlBlCC,8BAA+B;UAIbA,yBAAS;UAAAA,iBAAW;UAC9BA,mCACF;UAAAA,iBAAiB;UAEnBA,wCAAkB;UACbA,0DAA0C;UAAAA,iBAAI", "names": ["ProfileAnalyticsComponent", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-analytics\\profile-analytics.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-profile-analytics',\r\n  template: `\r\n    <div class=\"profile-analytics\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>analytics</mat-icon>\r\n            Profile Analytics\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <p>Profile analytics component - Coming soon!</p>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .profile-analytics { padding: 20px; }\r\n  `]\r\n})\r\nexport class ProfileAnalyticsComponent {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}