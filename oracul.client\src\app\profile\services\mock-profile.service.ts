import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';
import {
  UserProfile,
  ProfileUpdateRequest,
  SkillEndorsementRequest,
  ProfileViewRequest,
  ProfileAnalytics,
  ProfileSearchFilters,
  ProfileSearchResult,
  BlogPost,
  Achievement,
  Certification,
  WorkExperience,
  PortfolioItem,
  ProfileSkill
} from '../models/profile.models';

@Injectable({
  providedIn: 'root'
})
export class MockProfileService {
  private mockProfiles: UserProfile[] = [
    {
      id: 1,
      userId: 1,
      username: 'luna-starweaver',
      slug: 'luna-starweaver',
      isPublic: true,
      profileCompletionPercentage: 92,
      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=150&h=150&fit=crop&crop=face',
      coverPhotoUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=300&fit=crop',
      firstName: 'Luna',
      lastName: 'Starweaver',
      professionalTitle: 'Professional Astrologer & Cosmic Guide',
      headline: 'Illuminating life paths through ancient wisdom and celestial insights. Helping souls navigate their cosmic journey for over 12 years.',
      location: {
        city: 'Sedona',
        state: 'AZ',
        country: 'USA',
        displayLocation: 'Sedona, AZ'
      },
      contactInfo: {
        email: '<EMAIL>',
        isEmailPublic: true,
        website: 'https://starweaver-astrology.com',
        portfolioUrl: 'https://luna-cosmic-readings.com',
        phoneNumbers: [
          {
            id: 1,
            number: '+1 (928) 555-STAR',
            type: 'business',
            isPublic: true,
            isPrimary: true
          }
        ],
        businessAddress: {
          street: '777 Crystal Vortex Lane',
          city: 'Sedona',
          state: 'AZ',
          postalCode: '86336',
          country: 'USA',
          isPublic: true
        }
      },
      summary: 'Gifted astrologer and spiritual guide with over 12 years of experience helping individuals discover their cosmic purpose. Specializing in natal chart readings, relationship compatibility, career guidance, and spiritual awakening through the wisdom of the stars. My intuitive approach combines traditional astrological techniques with modern psychological insights to provide transformative guidance for your soul\'s journey.',
      skills: [
        { id: 1, name: 'Natal Chart Reading', category: 'Core Astrology', endorsements: 89, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },
        { id: 2, name: 'Synastry & Compatibility', category: 'Relationship Astrology', endorsements: 76, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },
        { id: 3, name: 'Transit Forecasting', category: 'Predictive Astrology', endorsements: 82, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },
        { id: 4, name: 'Tarot Reading', category: 'Divination Arts', endorsements: 65, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },
        { id: 5, name: 'Crystal Healing', category: 'Energy Work', endorsements: 58, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },
        { id: 6, name: 'Vedic Astrology', category: 'Ancient Systems', endorsements: 43, isEndorsedByCurrentUser: false, proficiencyLevel: 'intermediate' },
        { id: 7, name: 'Chakra Balancing', category: 'Energy Work', endorsements: 52, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },
        { id: 8, name: 'Moon Phase Rituals', category: 'Lunar Magic', endorsements: 71, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' }
      ],
      blogPosts: [
        {
          id: 1,
          title: 'Mercury Retrograde: Navigating Communication Challenges',
          excerpt: 'Discover how to harness the transformative energy of Mercury retrograde periods and turn apparent setbacks into opportunities for growth and reflection.',
          publishedAt: new Date('2024-01-15'),
          readCount: 2847,
          tags: ['Mercury Retrograde', 'Planetary Transits', 'Communication'],
          featuredImageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=200&fit=crop',
          slug: 'mercury-retrograde-navigation-guide'
        },
        {
          id: 2,
          title: 'Full Moon Manifestation: Lunar Cycles for Personal Growth',
          excerpt: 'Learn to align your intentions with the powerful energy of the full moon and create meaningful transformation in your life through lunar wisdom.',
          publishedAt: new Date('2024-02-20'),
          readCount: 3156,
          tags: ['Full Moon', 'Manifestation', 'Lunar Cycles', 'Spiritual Growth'],
          featuredImageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=200&fit=crop',
          slug: 'full-moon-manifestation-guide'
        },
        {
          id: 3,
          title: 'Understanding Your North Node: Your Soul\'s Purpose',
          excerpt: 'Explore the profound meaning of your North Node placement and how it reveals your karmic path and spiritual mission in this lifetime.',
          publishedAt: new Date('2024-03-10'),
          readCount: 1923,
          tags: ['North Node', 'Soul Purpose', 'Karmic Astrology', 'Spiritual Path'],
          featuredImageUrl: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=200&fit=crop',
          slug: 'north-node-soul-purpose-guide'
        }
      ],
      achievements: [
        {
          id: 1,
          title: 'Certified Professional Astrologer (CPA)',
          description: 'Achieved professional certification from the International Society for Astrological Research, demonstrating mastery of traditional and modern astrological techniques.',
          achievedAt: new Date('2019-08-15'),
          organization: 'International Society for Astrological Research',
          imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop'
        },
        {
          id: 2,
          title: 'Spiritual Guidance Excellence Award',
          description: 'Recognized for outstanding contribution to spiritual healing and guidance in the metaphysical community.',
          achievedAt: new Date('2023-11-01'),
          organization: 'Sedona Spiritual Council'
        },
        {
          id: 3,
          title: 'Featured Astrologer - Cosmic Wisdom Magazine',
          description: 'Selected as featured astrologer for the annual "Rising Stars in Astrology" edition, highlighting innovative approaches to chart interpretation.',
          achievedAt: new Date('2023-03-21'),
          organization: 'Cosmic Wisdom Magazine'
        }
      ],
      certifications: [
        {
          id: 1,
          name: 'Certified Professional Astrologer (CPA)',
          issuingOrganization: 'International Society for Astrological Research',
          issueDate: new Date('2019-08-15'),
          expirationDate: new Date('2025-08-15'),
          credentialId: 'ISAR-CPA-2019-7734',
          credentialUrl: 'https://isar-astrology.com/verification'
        },
        {
          id: 2,
          name: 'Advanced Tarot Certification',
          issuingOrganization: 'American Tarot Association',
          issueDate: new Date('2020-12-01'),
          credentialId: 'ATA-ADV-2020-9821'
        },
        {
          id: 3,
          name: 'Crystal Healing Practitioner',
          issuingOrganization: 'Crystal Healing Institute',
          issueDate: new Date('2021-06-30'),
          expirationDate: new Date('2026-06-30'),
          credentialId: 'CHI-PRAC-2021-5567'
        }
      ],
      experiences: [
        {
          id: 1,
          company: 'Starweaver Astrology Studio',
          position: 'Founder & Lead Astrologer',
          startDate: new Date('2018-01-01'),
          isCurrent: true,
          description: 'Founded and operate a thriving astrology practice offering personalized readings, workshops, and spiritual guidance. Specialize in natal chart interpretation, relationship compatibility, and life transition guidance.',
          location: 'Sedona, AZ',
          companyLogoUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=48&h=48&fit=crop',
          achievements: [
            'Built client base of 500+ regular customers',
            'Conducted over 2,000 successful readings',
            'Developed signature "Cosmic Life Path" methodology',
            'Featured in 3 major spiritual wellness publications'
          ]
        },
        {
          id: 2,
          company: 'Mystic Moon Wellness Center',
          position: 'Senior Astrologer & Workshop Leader',
          startDate: new Date('2015-06-01'),
          endDate: new Date('2017-12-31'),
          isCurrent: false,
          description: 'Provided astrological consultations and led group workshops on lunar cycles, planetary transits, and spiritual awakening. Mentored junior practitioners in chart interpretation techniques.',
          location: 'Santa Fe, NM',
          companyLogoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop',
          achievements: [
            'Increased center\'s astrology revenue by 150%',
            'Developed popular "Moon Goddess" workshop series',
            'Trained 12 apprentice astrologers',
            'Maintained 98% client satisfaction rating'
          ]
        },
        {
          id: 3,
          company: 'Crystal Visions Metaphysical Shop',
          position: 'Resident Astrologer',
          startDate: new Date('2012-03-01'),
          endDate: new Date('2015-05-31'),
          isCurrent: false,
          description: 'Provided walk-in astrological consultations and tarot readings. Educated customers on crystal healing properties and lunar cycle practices.',
          location: 'Asheville, NC',
          companyLogoUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=48&h=48&fit=crop',
          achievements: [
            'Performed over 1,500 readings',
            'Established loyal customer following',
            'Created educational crystal healing guides',
            'Organized monthly new moon circles'
          ]
        }
      ],
      portfolioItems: [
        {
          id: 1,
          title: 'Cosmic Love Compatibility Reading',
          description: 'Comprehensive synastry analysis for a celebrity couple, revealing deep karmic connections and relationship dynamics through detailed chart comparison.',
          imageUrls: ['https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'],
          projectUrl: 'https://starweaver-astrology.com/case-studies/cosmic-love',
          technologies: ['Synastry Analysis', 'Composite Charts', 'Transit Forecasting', 'Karmic Astrology'],
          completedAt: new Date('2023-08-15'),
          clientName: 'Celebrity Couple (Confidential)',
          category: 'Relationship Reading',
          testimonial: {
            id: 1,
            clientName: 'Sarah M.',
            clientTitle: 'Entertainment Industry Professional',
            testimonialText: 'Luna\'s reading was incredibly accurate and insightful. She helped us understand our relationship dynamics on a soul level.',
            rating: 5,
            givenAt: new Date('2023-08-20')
          }
        },
        {
          id: 2,
          title: 'Corporate Executive Career Guidance',
          description: 'In-depth natal chart analysis focusing on career path, leadership potential, and optimal timing for major business decisions.',
          imageUrls: ['https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=300&fit=crop'],
          projectUrl: 'https://starweaver-astrology.com/case-studies/executive-guidance',
          technologies: ['Natal Chart Reading', 'Midheaven Analysis', 'Saturn Return Guidance', 'Eclipse Timing'],
          completedAt: new Date('2023-11-20'),
          clientName: 'Fortune 500 Executive',
          category: 'Career Guidance',
          testimonial: {
            id: 2,
            clientName: 'Michael R.',
            clientTitle: 'CEO',
            testimonialText: 'The timing guidance Luna provided was spot-on. I made the career move exactly when she suggested and it was incredibly successful.',
            rating: 5,
            givenAt: new Date('2023-12-01')
          }
        },
        {
          id: 3,
          title: 'Spiritual Awakening Journey Map',
          description: 'Transformative reading combining natal chart analysis with North Node exploration to guide a client through their spiritual awakening process.',
          imageUrls: ['https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=300&fit=crop'],
          projectUrl: 'https://starweaver-astrology.com/case-studies/spiritual-awakening',
          technologies: ['North Node Analysis', 'Spiritual Astrology', 'Chakra Alignment', 'Past Life Regression'],
          completedAt: new Date('2024-01-10'),
          clientName: 'Spiritual Seeker',
          category: 'Spiritual Guidance'
        }
      ],
      socialLinks: [
        {
          id: 1,
          platform: 'instagram',
          url: 'https://instagram.com/luna_starweaver',
          displayName: '@luna_starweaver',
          isPublic: true
        },
        {
          id: 2,
          platform: 'youtube',
          url: 'https://youtube.com/c/StarweaverAstrology',
          displayName: 'Starweaver Astrology',
          isPublic: true
        },
        {
          id: 3,
          platform: 'twitter',
          url: 'https://twitter.com/luna_cosmic',
          displayName: '@luna_cosmic',
          isPublic: true
        },
        {
          id: 4,
          platform: 'facebook',
          url: 'https://facebook.com/StarweaverAstrologyStudio',
          displayName: 'Starweaver Astrology Studio',
          isPublic: true
        }
      ],
      profileViews: 3847,
      lastViewedAt: new Date('2024-01-20'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-01-20')
    }
  ];

  private mockAnalytics: ProfileAnalytics = {
    profileViews: 3847,
    uniqueVisitors: 2156,
    viewsThisMonth: 287,
    viewsThisWeek: 73,
    topReferrers: ['instagram.com', 'youtube.com', 'google.com', 'astrology.com'],
    skillEndorsements: 536,
    blogPostViews: 7926,
    contactButtonClicks: 89
  };

  // Profile CRUD Operations
  getProfile(identifier: string): Observable<UserProfile> {
    const profile = this.mockProfiles.find(p => p.slug === identifier || p.id.toString() === identifier);
    if (profile) {
      return of(profile).pipe(delay(500)); // Simulate network delay
    }
    throw new Error('Profile not found');
  }

  getCurrentUserProfile(): Observable<UserProfile> {
    return of(this.mockProfiles[0]).pipe(delay(300));
  }

  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {
    const profile = { ...this.mockProfiles[0], ...updates } as UserProfile;
    this.mockProfiles[0] = profile;
    return of(profile).pipe(delay(800));
  }

  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {
    const newProfile = { ...this.mockProfiles[0], ...profileData, id: Date.now() };
    this.mockProfiles.push(newProfile);
    return of(newProfile).pipe(delay(1000));
  }

  deleteProfile(): Observable<void> {
    return of(void 0).pipe(delay(500));
  }

  // Profile Search
  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {
    const results: ProfileSearchResult = {
      profiles: this.mockProfiles,
      totalCount: this.mockProfiles.length,
      currentPage: page,
      totalPages: 1,
      pageSize: limit
    };
    return of(results).pipe(delay(600));
  }

  // Skills Management
  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {
    const newSkill: ProfileSkill = {
      ...skill,
      id: Date.now(),
      endorsements: 0,
      isEndorsedByCurrentUser: false
    };
    return of(newSkill).pipe(delay(400));
  }

  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {
    const skill = this.mockProfiles[0].skills.find(s => s.id === skillId);
    if (skill) {
      Object.assign(skill, updates);
      return of(skill).pipe(delay(400));
    }
    throw new Error('Skill not found');
  }

  deleteSkill(skillId: number): Observable<void> {
    return of(void 0).pipe(delay(300));
  }

  endorseSkill(request: SkillEndorsementRequest): Observable<void> {
    return of(void 0).pipe(delay(400));
  }

  // Experience Management
  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {
    const newExperience: WorkExperience = { ...experience, id: Date.now() };
    return of(newExperience).pipe(delay(500));
  }

  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {
    const experience = this.mockProfiles[0].experiences.find(e => e.id === experienceId);
    if (experience) {
      Object.assign(experience, updates);
      return of(experience).pipe(delay(500));
    }
    throw new Error('Experience not found');
  }

  deleteExperience(experienceId: number): Observable<void> {
    return of(void 0).pipe(delay(300));
  }

  // Portfolio Management
  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {
    const newItem: PortfolioItem = { ...item, id: Date.now() };
    return of(newItem).pipe(delay(600));
  }

  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {
    const item = this.mockProfiles[0].portfolioItems.find(i => i.id === itemId);
    if (item) {
      Object.assign(item, updates);
      return of(item).pipe(delay(500));
    }
    throw new Error('Portfolio item not found');
  }

  deletePortfolioItem(itemId: number): Observable<void> {
    return of(void 0).pipe(delay(300));
  }

  // Achievements & Certifications
  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {
    const newAchievement: Achievement = { ...achievement, id: Date.now() };
    return of(newAchievement).pipe(delay(400));
  }

  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {
    const newCertification: Certification = { ...certification, id: Date.now() };
    return of(newCertification).pipe(delay(400));
  }

  // Blog Posts
  getBlogPosts(profileId: number): Observable<BlogPost[]> {
    return of(this.mockProfiles[0].blogPosts).pipe(delay(400));
  }

  // Analytics
  getProfileAnalytics(): Observable<ProfileAnalytics> {
    return of(this.mockAnalytics).pipe(delay(500));
  }

  recordProfileView(request: ProfileViewRequest): Observable<void> {
    return of(void 0).pipe(delay(200));
  }

  // File Upload
  uploadProfilePhoto(file: File): Observable<{ url: string }> {
    // Simulate file upload
    const mockUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face';
    return of({ url: mockUrl }).pipe(delay(1500));
  }

  uploadCoverPhoto(file: File): Observable<{ url: string }> {
    // Simulate file upload
    const mockUrl = 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=300&fit=crop';
    return of({ url: mockUrl }).pipe(delay(1500));
  }

  // Utility Methods
  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {
    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`;
    return of({ slug }).pipe(delay(300));
  }

  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {
    const available = !this.mockProfiles.some(p => p.slug === slug);
    return of({ available }).pipe(delay(300));
  }

  // Social Sharing
  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {
    const profile = this.mockProfiles[0];
    return of({
      title: `${profile.firstName} ${profile.lastName} - ${profile.professionalTitle}`,
      description: profile.summary || 'Professional profile',
      imageUrl: profile.profilePhotoUrl || '',
      url: `${window.location.origin}/profile/${profile.slug}`
    }).pipe(delay(300));
  }
}
