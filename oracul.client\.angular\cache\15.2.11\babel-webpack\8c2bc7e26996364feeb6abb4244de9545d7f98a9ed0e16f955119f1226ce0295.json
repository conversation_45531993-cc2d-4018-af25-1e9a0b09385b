{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/divider\";\nimport * as i9 from \"@angular/material/menu\";\nfunction AppComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 10)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Welcome, \", user_r6.firstName, \"! \");\n  }\n}\nfunction AppComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, AppComponent_div_14_span_1_Template, 2, 1, \"span\", 12);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementStart(3, \"button\", 13)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-menu\", null, 14)(8, \"button\", 15)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 15)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"mat-divider\");\n    i0.ɵɵelementStart(19, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_14_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.logout());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Logout\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r5 = i0.ɵɵreference(7);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 2, ctx_r1.authService.currentUser$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r5);\n  }\n}\nfunction AppComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"button\", 19)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 20)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Register \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.title = 'oracul.client';\n  }\n  ngOnInit() {\n    // Initialize authentication state\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.snackBar.open('Logged out successfully', 'Close', {\n          duration: 3000,\n          panelClass: ['success-snackbar']\n        });\n        this.router.navigate(['/login']);\n      },\n      error: error => {\n        this.snackBar.open('Logout failed', 'Close', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        // Still navigate to login even if logout fails\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 20,\n      vars: 7,\n      consts: [[1, \"app-container\"], [\"color\", \"primary\"], [\"routerLink\", \"/oracles\", 1, \"app-title\"], [1, \"nav-links\"], [\"mat-button\", \"\", \"routerLink\", \"/oracles\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", 4, \"ngIf\"], [1, \"toolbar-spacer\"], [\"class\", \"auth-section\", 4, \"ngIf\", \"ngIfElse\"], [\"authButtons\", \"\"], [1, \"content-container\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\"], [1, \"auth-section\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"menu\", \"matMenu\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"user-info\"], [1, \"auth-buttons\"], [\"mat-button\", \"\", \"routerLink\", \"/login\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/register\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 2);\n          i0.ɵɵtext(5, \"Oracul\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"button\", 4)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Oracles \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, AppComponent_button_11_Template, 4, 0, \"button\", 5);\n          i0.ɵɵpipe(12, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"span\", 6);\n          i0.ɵɵtemplate(14, AppComponent_div_14_Template, 24, 4, \"div\", 7);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵtemplate(16, AppComponent_ng_template_16_Template, 9, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 9);\n          i0.ɵɵelement(19, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(17);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 3, ctx.authService.isAuthenticated$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(15, 5, ctx.authService.isAuthenticated$))(\"ngIfElse\", _r2);\n        }\n      },\n      dependencies: [i4.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i5.MatToolbar, i6.MatButton, i6.MatIconButton, i7.MatIcon, i8.MatDivider, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger, i4.AsyncPipe],\n      styles: [\"[_nghost-%COMP%] {\\r\\n  display: block;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.app-container[_ngcontent-%COMP%] {\\r\\n  min-height: 100vh;\\r\\n  background-color: #f5f5f5;\\r\\n}\\r\\n\\r\\n.app-title[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: 500;\\r\\n  margin-left: 8px;\\r\\n  cursor: pointer;\\r\\n  text-decoration: none;\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n.nav-links[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  margin-left: 32px;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.nav-links[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n}\\r\\n\\r\\n.nav-links[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\r\\n  background-color: rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n.nav-links[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 4px;\\r\\n}\\r\\n\\r\\n.toolbar-spacer[_ngcontent-%COMP%] {\\r\\n  flex: 1 1 auto;\\r\\n}\\r\\n\\r\\n.auth-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n}\\r\\n\\r\\n.auth-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 8px;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.auth-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n}\\r\\n\\r\\n.auth-buttons[_ngcontent-%COMP%]   mat-raised-button[_ngcontent-%COMP%] {\\r\\n  color: #333;\\r\\n}\\r\\n\\r\\n.content-container[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  margin: 40px 0;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .nav-links[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .app-title[_ngcontent-%COMP%] {\\r\\n    font-size: 1.2rem;\\r\\n  }\\r\\n\\r\\n  .auth-buttons[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 4px;\\r\\n  }\\r\\n\\r\\n  .auth-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n    font-size: 0.9rem;\\r\\n    padding: 4px 8px;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .app-title[_ngcontent-%COMP%] {\\r\\n    font-size: 1rem;\\r\\n  }\\r\\n\\r\\n  .user-info[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  overflow-x: auto;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n.temperature[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #ff6b35;\\r\\n}\\r\\n\\r\\n.summary[_ngcontent-%COMP%] {\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .content-container[_ngcontent-%COMP%] {\\r\\n    padding: 10px;\\r\\n  }\\r\\n\\r\\n  .weather-table[_ngcontent-%COMP%] {\\r\\n    font-size: 14px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\nmat-card[_ngcontent-%COMP%] {\\r\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\nmat-card[_ngcontent-%COMP%]:hover {\\r\\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%] {\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\nbutton[mat-raised-button][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFjO0VBQ2QsV0FBVztFQUNYLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLGdCQUFnQjtFQUNoQixlQUFlO0VBQ2YscUJBQXFCO0VBQ3JCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsaUJBQWlCO0VBQ2pCLFFBQVE7QUFDVjs7QUFFQTtFQUNFLCtCQUErQjtBQUNqQzs7QUFFQTtFQUNFLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGVBQWU7RUFDZiwrQkFBK0I7QUFDakM7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsUUFBUTtFQUNSLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLCtCQUErQjtBQUNqQzs7QUFFQTtFQUNFLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGFBQWE7RUFDYixpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsUUFBUTtBQUNWOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHVCQUF1QjtFQUN2QixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsU0FBUztFQUNULGFBQWE7QUFDZjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsUUFBUTtBQUNWOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFO0lBQ0UsYUFBYTtFQUNmOztFQUVBO0lBQ0UsaUJBQWlCO0VBQ25COztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLFFBQVE7RUFDVjs7RUFFQTtJQUNFLGlCQUFpQjtJQUNqQixnQkFBZ0I7RUFDbEI7QUFDRjs7QUFFQTtFQUNFO0lBQ0UsZUFBZTtFQUNqQjs7RUFFQTtJQUNFLGFBQWE7RUFDZjtBQUNGOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0FBQ2I7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0U7SUFDRSxhQUFhO0VBQ2Y7O0VBRUE7SUFDRSxlQUFlO0VBQ2pCO0FBQ0Y7O0FBRUEsaUNBQWlDO0FBQ2pDO0VBQ0UscUNBQXFDO0VBQ3JDLGdDQUFnQztBQUNsQzs7QUFFQTtFQUNFLHNDQUFzQztBQUN4Qzs7QUFFQTtFQUNFLGFBQWE7QUFDZjs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbn1cclxuXHJcbi5hcHAtY29udGFpbmVyIHtcclxuICBtaW4taGVpZ2h0OiAxMDB2aDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG59XHJcblxyXG4uYXBwLXRpdGxlIHtcclxuICBmb250LXNpemU6IDEuNXJlbTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIG1hcmdpbi1sZWZ0OiA4cHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICBjb2xvcjogaW5oZXJpdDtcclxufVxyXG5cclxuLm5hdi1saW5rcyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBtYXJnaW4tbGVmdDogMzJweDtcclxuICBnYXA6IDhweDtcclxufVxyXG5cclxuLm5hdi1saW5rcyBidXR0b24ge1xyXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XHJcbn1cclxuXHJcbi5uYXYtbGlua3MgYnV0dG9uLmFjdGl2ZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59XHJcblxyXG4ubmF2LWxpbmtzIGJ1dHRvbiBtYXQtaWNvbiB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA0cHg7XHJcbn1cclxuXHJcbi50b29sYmFyLXNwYWNlciB7XHJcbiAgZmxleDogMSAxIGF1dG87XHJcbn1cclxuXHJcbi5hdXRoLXNlY3Rpb24ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDE2cHg7XHJcbn1cclxuXHJcbi51c2VyLWluZm8ge1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xyXG59XHJcblxyXG4uYXV0aC1idXR0b25zIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGdhcDogOHB4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi5hdXRoLWJ1dHRvbnMgYnV0dG9uIHtcclxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xyXG59XHJcblxyXG4uYXV0aC1idXR0b25zIG1hdC1yYWlzZWQtYnV0dG9uIHtcclxuICBjb2xvcjogIzMzMztcclxufVxyXG5cclxuLmNvbnRlbnQtY29udGFpbmVyIHtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG59XHJcblxyXG4uaGVhZGVyLWNhcmQge1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn1cclxuXHJcbi5oZWFkZXItY2FyZCBtYXQtY2FyZC10aXRsZSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogOHB4O1xyXG59XHJcblxyXG4ubG9hZGluZy1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgbWFyZ2luOiA0MHB4IDA7XHJcbn1cclxuXHJcbi5sb2FkaW5nLWNvbnRlbnQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMTZweDtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG59XHJcblxyXG4uZGF0YS1jYXJkIHtcclxuICBtYXJnaW4tdG9wOiAyMHB4O1xyXG59XHJcblxyXG4uZGF0YS1jYXJkIG1hdC1jYXJkLXRpdGxlIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiA4cHg7XHJcbn1cclxuXHJcbi8qIFJlc3BvbnNpdmUgRGVzaWduICovXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gIC5uYXYtbGlua3Mge1xyXG4gICAgZGlzcGxheTogbm9uZTtcclxuICB9XHJcblxyXG4gIC5hcHAtdGl0bGUge1xyXG4gICAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgfVxyXG5cclxuICAuYXV0aC1idXR0b25zIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDRweDtcclxuICB9XHJcblxyXG4gIC5hdXRoLWJ1dHRvbnMgYnV0dG9uIHtcclxuICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgcGFkZGluZzogNHB4IDhweDtcclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xyXG4gIC5hcHAtdGl0bGUge1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG4gIH1cclxuXHJcbiAgLnVzZXItaW5mbyB7XHJcbiAgICBkaXNwbGF5OiBub25lO1xyXG4gIH1cclxufVxyXG5cclxuLnRhYmxlLWNvbnRhaW5lciB7XHJcbiAgb3ZlcmZsb3cteDogYXV0bztcclxuICBtYXJnaW4tdG9wOiAxNnB4O1xyXG59XHJcblxyXG4ud2VhdGhlci10YWJsZSB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi53ZWF0aGVyLXRhYmxlIHRoIHtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG59XHJcblxyXG4ud2VhdGhlci10YWJsZSB0aCBtYXQtaWNvbiB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcclxufVxyXG5cclxuLnRlbXBlcmF0dXJlIHtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGNvbG9yOiAjZmY2YjM1O1xyXG59XHJcblxyXG4uc3VtbWFyeSB7XHJcbiAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gIGNvbG9yOiAjNjY2O1xyXG59XHJcblxyXG4vKiBSZXNwb25zaXZlIGRlc2lnbiAqL1xyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuY29udGVudC1jb250YWluZXIge1xyXG4gICAgcGFkZGluZzogMTBweDtcclxuICB9XHJcblxyXG4gIC53ZWF0aGVyLXRhYmxlIHtcclxuICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICB9XHJcbn1cclxuXHJcbi8qIE1hdGVyaWFsIERlc2lnbiBlbmhhbmNlbWVudHMgKi9cclxubWF0LWNhcmQge1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7XHJcbiAgdHJhbnNpdGlvbjogYm94LXNoYWRvdyAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbm1hdC1jYXJkOmhvdmVyIHtcclxuICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjE1KTtcclxufVxyXG5cclxuYnV0dG9uW21hdC1yYWlzZWQtYnV0dG9uXSB7XHJcbiAgbWFyZ2luOiA4cHggMDtcclxufVxyXG5cclxuYnV0dG9uW21hdC1yYWlzZWQtYnV0dG9uXSBtYXQtaWNvbiB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;IAaMA,kCAAkH;IACtGA,yBAAS;IAAAA,iBAAW;IAC9BA,2BACF;IAAAA,iBAAS;;;;;IAQTA,gCAAyE;IACvEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,4DACF;;;;;;IAJFA,+BAAyF;IAEvFA,uEAEO;;IAEPA,kCAAmD;IACvCA,8BAAc;IAAAA,iBAAW;IAErCA,0CAA0B;IAEZA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,wBAAO;IAAAA,iBAAO;IAEtBA,mCAAsB;IACVA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,yBAAQ;IAAAA,iBAAO;IAEvBA,+BAA2B;IAC3BA,mCAAyC;IAAnBA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IACtCA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,uBAAM;IAAAA,iBAAO;;;;;IAnBEA,eAAuC;IAAvCA,4EAAuC;IAIxCA,eAA0B;IAA1BA,uCAA0B;;;;;IAsBlDA,+BAA0B;IAEZA,qBAAK;IAAAA,iBAAW;IAC1BA,uBACF;IAAAA,iBAAS;IACTA,kCAAgE;IACpDA,0BAAU;IAAAA,iBAAW;IAC/BA,0BACF;IAAAA,iBAAS;;;AChDjB,OAAM,MAAOC,YAAY;EAGvBC,YACSC,WAAwB,EACvBC,MAAc,EACdC,QAAqB;IAFtB,gBAAW,GAAXF,WAAW;IACV,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IALlB,UAAK,GAAG,eAAe;EAMpB;EAEHC,QAAQ;IACN;EAAA;EAGFC,MAAM;IACJ,IAAI,CAACJ,WAAW,CAACI,MAAM,EAAE,CAACC,SAAS,CAAC;MAClCC,IAAI,EAAE,MAAK;QACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UACrDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACT,QAAQ,CAACK,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE;UAC3CC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF;QACA,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;;;uBA/BWZ,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAc;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDTzBnB,8BAA2B;UAGbA,0BAAU;UAAAA,iBAAW;UAC/BA,+BAA8C;UAAAA,sBAAM;UAAAA,iBAAO;UAG3DA,8BAAuB;UAETA,0BAAU;UAAAA,iBAAW;UAC/BA,0BACF;UAAAA,iBAAS;UACTA,qEAGS;;UACXA,iBAAM;UAENA,2BAAoC;UAGpCA,gEAwBM;;UAGNA,gHAWc;UAChBA,iBAAc;UAGdA,+BAA+B;UAE7BA,iCAA+B;UACjCA,iBAAM;;;;UAtDoEA,gBAA0C;UAA1CA,8EAA0C;UAS5GA,eAA4C;UAA5CA,8EAA4C", "names": ["i0", "AppComponent", "constructor", "authService", "router", "snackBar", "ngOnInit", "logout", "subscribe", "next", "open", "duration", "panelClass", "navigate", "error", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.ts"], "sourcesContent": ["<!-- Authentication-aware Layout -->\r\n<div class=\"app-container\">\r\n  <!-- Top Toolbar - Always show -->\r\n  <mat-toolbar color=\"primary\">\r\n    <mat-icon>psychology</mat-icon>\r\n    <span class=\"app-title\" routerLink=\"/oracles\">Oracul</span>\r\n\r\n    <!-- Navigation Links -->\r\n    <div class=\"nav-links\">\r\n      <button mat-button routerLink=\"/oracles\" routerLinkActive=\"active\">\r\n        <mat-icon>psychology</mat-icon>\r\n        Oracles\r\n      </button>\r\n      <button mat-button routerLink=\"/dashboard\" routerLinkActive=\"active\" *ngIf=\"authService.isAuthenticated$ | async\">\r\n        <mat-icon>dashboard</mat-icon>\r\n        Dashboard\r\n      </button>\r\n    </div>\r\n\r\n    <span class=\"toolbar-spacer\"></span>\r\n\r\n    <!-- Authentication Section -->\r\n    <div *ngIf=\"authService.isAuthenticated$ | async; else authButtons\" class=\"auth-section\">\r\n      <!-- User Info -->\r\n      <span class=\"user-info\" *ngIf=\"authService.currentUser$ | async as user\">\r\n        Welcome, {{ user.firstName }}!\r\n      </span>\r\n\r\n      <button mat-icon-button [matMenuTriggerFor]=\"menu\">\r\n        <mat-icon>account_circle</mat-icon>\r\n      </button>\r\n      <mat-menu #menu=\"matMenu\">\r\n        <button mat-menu-item>\r\n          <mat-icon>person</mat-icon>\r\n          <span>Profile</span>\r\n        </button>\r\n        <button mat-menu-item>\r\n          <mat-icon>settings</mat-icon>\r\n          <span>Settings</span>\r\n        </button>\r\n        <mat-divider></mat-divider>\r\n        <button mat-menu-item (click)=\"logout()\">\r\n          <mat-icon>logout</mat-icon>\r\n          <span>Logout</span>\r\n        </button>\r\n      </mat-menu>\r\n    </div>\r\n\r\n    <!-- Login/Register Buttons -->\r\n    <ng-template #authButtons>\r\n      <div class=\"auth-buttons\">\r\n        <button mat-button routerLink=\"/login\">\r\n          <mat-icon>login</mat-icon>\r\n          Login\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" routerLink=\"/register\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Register\r\n        </button>\r\n      </div>\r\n    </ng-template>\r\n  </mat-toolbar>\r\n\r\n  <!-- Main Content Area -->\r\n  <div class=\"content-container\">\r\n    <!-- Router Outlet for All Components -->\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from './auth/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'oracul.client';\r\n\r\n  constructor(\r\n    public authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Initialize authentication state\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout().subscribe({\r\n      next: () => {\r\n        this.snackBar.open('Logged out successfully', 'Close', {\r\n          duration: 3000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n        this.router.navigate(['/login']);\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open('Logout failed', 'Close', {\r\n          duration: 3000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        // Still navigate to login even if logout fails\r\n        this.router.navigate(['/login']);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}