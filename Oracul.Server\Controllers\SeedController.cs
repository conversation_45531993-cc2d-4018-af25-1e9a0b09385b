using Microsoft.AspNetCore.Mvc;
using Oracul.Server.Models;
using Oracul.Server.Services;

namespace Oracul.Server.Controllers
{
    /// <summary>
    /// Controller for database seeding operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SeedController : ControllerBase
    {
        private readonly ProfileSeedService _profileSeedService;
        private readonly ILogger<SeedController> _logger;

        public SeedController(ProfileSeedService profileSeedService, ILogger<SeedController> logger)
        {
            _profileSeedService = profileSeedService;
            _logger = logger;
        }

        /// <summary>
        /// Seed the database with astrology-focused profile data
        /// </summary>
        [HttpPost("profiles")]
        public async Task<ActionResult<ApiResponse<bool>>> SeedProfiles()
        {
            try
            {
                await _profileSeedService.SeedProfileDataAsync();
                
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Профилните данни са заредени успешно в базата данни",
                    Data = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профилните данни");
                return StatusCode(500, new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Възникна грешка при зареждане на профилните данни",
                    Errors = new List<string> { ex.Message },
                    Data = false
                });
            }
        }
    }
}
