# Entity Framework Migrations Guide

This guide explains how to manage Entity Framework Core migrations for the Oracul application using the provided helper scripts.

## 📁 Migration Scripts

We provide multiple helper scripts to simplify migration management:

- **`migrate-clean.ps1`** - PowerShell script (recommended, tested and working)
- **`migrate.bat`** - Batch file (for Windows Command Prompt)
- **`migrate.ps1`** - Enhanced PowerShell script (may have emoji compatibility issues)

## 🚀 Quick Start

### Prerequisites
1. Ensure .NET SDK is installed
2. Ensure Entity Framework tools are installed:
   ```bash
   dotnet tool install --global dotnet-ef
   ```
3. Verify installation:
   ```bash
   dotnet ef --version
   ```

## 📋 Common Migration Tasks

### 1. Update Database (Apply Pending Migrations)

This is the most common operation - applying all pending migrations to your database.

**PowerShell (Recommended):**
```powershell
.\migrate-clean.ps1
# or explicitly
.\migrate-clean.ps1 -Action update
```

**Command Prompt:**
```cmd
migrate.bat
# or explicitly
migrate.bat update
```

**Manual Command:**
```bash
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server
```

### 2. Create New Migration

When you make changes to your entity models, you need to create a new migration.

**PowerShell (Recommended):**
```powershell
.\migrate-clean.ps1 -Action add -MigrationName "AddUserProfileTable"
```

**Command Prompt:**
```cmd
migrate.bat add AddUserProfileTable
```

**Manual Command:**
```bash
dotnet ef migrations add AddUserProfileTable --project Oracul.Data --startup-project Oracul.Server
```

### 3. List All Migrations

View all migrations and their status.

**PowerShell:**
```powershell
.\migrate.ps1 -Action list
```

**Command Prompt:**
```cmd
migrate.bat list
```

**Manual Command:**
```bash
dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server
```

### 4. Remove Last Migration

⚠️ **Warning**: Only remove migrations that haven't been applied to production!

**PowerShell:**
```powershell
.\migrate.ps1 -Action remove
```

**Command Prompt:**
```cmd
migrate.bat remove
```

**Manual Command:**
```bash
dotnet ef migrations remove --project Oracul.Data --startup-project Oracul.Server
```

### 5. Generate SQL Script

Generate SQL script for all migrations (useful for production deployments).

**PowerShell:**
```powershell
.\migrate.ps1 -Action script
```

**Command Prompt:**
```cmd
migrate.bat script
```

**Manual Command:**
```bash
dotnet ef migrations script --project Oracul.Data --startup-project Oracul.Server
```

### 6. Drop Database

⚠️ **DANGER**: This will completely delete your database!

**PowerShell:**
```powershell
.\migrate.ps1 -Action drop
```

**Command Prompt:**
```cmd
migrate.bat drop
```

## 📝 Step-by-Step Workflow

### Adding a New Entity

1. **Create your entity class** in `Oracul.Data/Models/`:
   ```csharp
   public class Product : BaseEntity
   {
       public string Name { get; set; } = string.Empty;
       public decimal Price { get; set; }
       public string Description { get; set; } = string.Empty;
   }
   ```

2. **Add DbSet to OraculDbContext**:
   ```csharp
   public DbSet<Product> Products { get; set; }
   ```

3. **Configure entity in OnModelCreating** (if needed):
   ```csharp
   modelBuilder.Entity<Product>(entity =>
   {
       entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
       entity.Property(e => e.Price).HasPrecision(18, 2);
   });
   ```

4. **Create migration**:
   ```powershell
   .\migrate.ps1 -Action add -MigrationName "AddProductTable"
   ```

5. **Review the generated migration** in `Oracul.Data/Migrations/`

6. **Apply migration to database**:
   ```powershell
   .\migrate.ps1
   ```

### Modifying Existing Entity

1. **Update your entity class**
2. **Create migration**:
   ```powershell
   .\migrate.ps1 -Action add -MigrationName "UpdateProductAddCategory"
   ```
3. **Apply migration**:
   ```powershell
   .\migrate.ps1
   ```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Issue: "dotnet-ef command not found"
```bash
# Install EF Core tools globally
dotnet tool install --global dotnet-ef

# Update if already installed
dotnet tool update --global dotnet-ef
```

#### Issue: "No DbContext was found"
- Ensure you're running commands from the solution root directory
- Verify both projects build successfully:
  ```bash
  dotnet build
  ```

#### Issue: "Pending model changes"
- This happens when using dynamic values in seed data
- Use static values for seed data instead of `DateTime.Now`

#### Issue: Connection string problems
- Verify connection string in `Oracul.Server/appsettings.json`
- Ensure SQL Server is running and accessible

#### Issue: Migration already applied
- Check migration status: `.\migrate.ps1 -Action list`
- If you need to rollback, specify target migration:
  ```bash
  dotnet ef database update PreviousMigrationName --project Oracul.Data --startup-project Oracul.Server
  ```

## 📊 Migration Best Practices

### ✅ Do's
- **Use descriptive migration names**: `AddUserEmailIndex`, `UpdateOrderStatusEnum`
- **Review generated migrations** before applying
- **Test migrations** on development database first
- **Backup production database** before applying migrations
- **Use transactions** for complex data migrations
- **Keep migrations small** and focused on single changes

### ❌ Don'ts
- **Don't edit applied migrations** that are in production
- **Don't delete migration files** once they're applied to production
- **Don't use dynamic values** in seed data (use static dates/values)
- **Don't apply untested migrations** to production

## 🎯 Production Deployment

For production environments:

1. **Generate SQL script**:
   ```powershell
   .\migrate.ps1 -Action script > migration-script.sql
   ```

2. **Review the script** thoroughly

3. **Backup production database**

4. **Apply script** during maintenance window

5. **Verify application** works correctly

## 📞 Getting Help

If you encounter issues:

1. Check this README for common solutions
2. Review Entity Framework documentation
3. Check migration files in `Oracul.Data/Migrations/`
4. Verify connection string and database accessibility

## 🔗 Useful Commands Reference

```bash
# Check EF tools version
dotnet ef --version

# Build solution
dotnet build

# Check database connection
dotnet ef database drop --dry-run --project Oracul.Data --startup-project Oracul.Server

# Generate migration with specific output directory
dotnet ef migrations add MigrationName --output-dir Migrations --project Oracul.Data --startup-project Oracul.Server
```

---

**Happy migrating! 🚀**
