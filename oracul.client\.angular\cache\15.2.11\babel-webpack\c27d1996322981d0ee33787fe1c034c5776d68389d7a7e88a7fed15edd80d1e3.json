{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "mappings": ";AAEA,SAASA,SAAS,QAAQ,kBAAkB;AAG5CC,qCAAwB,CAACC,eAAe,CAACF,SAAS,CAAC,CAChDG,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "names": ["AppModule", "__Ng<PERSON>li_bootstrap_1", "bootstrapModule", "catch", "err", "console", "error"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\main.ts"], "sourcesContent": ["import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\nimport { AppModule } from './app/app.module';\r\n\r\n\r\nplatformBrowserDynamic().bootstrapModule(AppModule)\r\n  .catch(err => console.error(err));\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}