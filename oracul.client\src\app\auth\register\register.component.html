<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header class="register-header">
      <mat-card-title>
        <mat-icon class="register-icon">person_add</mat-icon>
        {{ getStepTitle() }}
      </mat-card-title>
      <mat-card-subtitle>
        <span *ngIf="registrationType === 'general'">Присъединете се към Оракул и започнете своето пътуване</span>
        <span *ngIf="isOracleRegistration()">
          Стъпка {{ currentStep }} от {{ totalSteps }}
          <mat-progress-bar
            mode="determinate"
            [value]="(currentStep / totalSteps) * 100"
            class="step-progress">
          </mat-progress-bar>
        </span>
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Registration Type Selection -->
      <div class="registration-type-selection" *ngIf="currentStep === 1">
        <h3>Изберете тип регистрация</h3>
        <div class="type-buttons-compact">
          <button
            mat-stroked-button
            type="button"
            class="type-button-compact general-button"
            [class.selected]="registrationType === 'general'"
            (click)="setRegistrationType('general')">
            <mat-icon>person</mat-icon>
            <span>Обикновен потребител</span>
          </button>

          <button
            mat-stroked-button
            type="button"
            class="type-button-compact oracle-button"
            [class.selected]="isOracleRegistration()"
            (click)="setRegistrationType('oracle')">
            <mat-icon>auto_awesome</mat-icon>
            <span>Оракул</span>
          </button>
        </div>
        <p class="type-description">
          <span *ngIf="registrationType === 'general'">За хора, които търсят духовно водачество</span>
          <span *ngIf="isOracleRegistration()">За практикуващи астролози, таро читци и духовни консултанти</span>
        </p>
      </div>

      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form"
            *ngIf="isOracleRegistration() || registrationType === 'general'">

        <!-- Step 1: Basic Information (Both types) -->
        <div *ngIf="currentStep === 1" class="form-step">
          <!-- Name Fields Row -->
          <div class="name-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Първо име</mat-label>
              <input
                matInput
                type="text"
                formControlName="firstName"
                placeholder="Въведете първо име"
                autocomplete="given-name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
                {{ getFirstNameErrorMessage() }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Фамилно име</mat-label>
              <input
                matInput
                type="text"
                formControlName="lastName"
                placeholder="Въведете фамилно име"
                autocomplete="family-name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
                {{ getLastNameErrorMessage() }}
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Email Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Имейл адрес</mat-label>
            <input
              matInput
              type="email"
              formControlName="email"
              placeholder="Въведете вашия имейл"
              autocomplete="email">
            <mat-icon matSuffix>email</mat-icon>
            <mat-error *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
              {{ getEmailErrorMessage() }}
            </mat-error>
          </mat-form-field>

          <!-- Phone Number Field (Optional) -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Телефонен номер (по избор)</mat-label>
            <input
              matInput
              type="tel"
              formControlName="phoneNumber"
              placeholder="Въведете телефонен номер"
              autocomplete="tel">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>

          <!-- Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Парола</mat-label>
            <input
              matInput
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password"
              placeholder="Създайте парола"
              autocomplete="new-password">
            <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="hidePassword = !hidePassword"
              [attr.aria-label]="'Скрий парола'"
              [attr.aria-pressed]="hidePassword">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
              {{ getPasswordErrorMessage() }}
            </mat-error>
          </mat-form-field>

          <!-- Confirm Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Потвърдете паролата</mat-label>
            <input
              matInput
              [type]="hideConfirmPassword ? 'password' : 'text'"
              formControlName="confirmPassword"
              placeholder="Потвърдете паролата"
              autocomplete="new-password">
            <button
              mat-icon-button
              matSuffix
              type="button"
              (click)="hideConfirmPassword = !hideConfirmPassword"
              [attr.aria-label]="'Скрий парола'"
              [attr.aria-pressed]="hideConfirmPassword">
              <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || registerForm.hasError('passwordMismatch')">
              {{ getConfirmPasswordErrorMessage() }}
            </mat-error>
          </mat-form-field>

          <!-- Terms and Conditions for General Users -->
          <div class="terms-section" *ngIf="registrationType === 'general'">
            <mat-checkbox formControlName="acceptTerms" color="primary">
              Съгласявам се с <a href="#" target="_blank">Условията за ползване</a> и <a href="#" target="_blank">Политиката за поверителност</a>
            </mat-checkbox>
            <mat-error *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched">
              Трябва да приемете условията и политиката за поверителност
            </mat-error>
          </div>
        </div>

        <!-- Step 2: Professional Information (Oracle only) -->
        <div *ngIf="currentStep === 2 && isOracleRegistration()" class="form-step">
          <h3>Професионална информация</h3>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Професионална титла</mat-label>
            <input
              matInput
              type="text"
              formControlName="professionalTitle"
              placeholder="напр. Астролог, Таро читец, Кристален лечител">
            <mat-icon matSuffix>work</mat-icon>
            <mat-error *ngIf="registerForm.get('professionalTitle')?.invalid && registerForm.get('professionalTitle')?.touched">
              {{ getFieldErrorMessage('professionalTitle') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Основна специализация</mat-label>
            <mat-select formControlName="primarySpecialization">
              <mat-option *ngFor="let spec of getSpecializationOptions()" [value]="spec">
                {{ spec }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="registerForm.get('primarySpecialization')?.invalid && registerForm.get('primarySpecialization')?.touched">
              {{ getFieldErrorMessage('primarySpecialization') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Заглавие (кратко описание)</mat-label>
            <input
              matInput
              type="text"
              formControlName="headline"
              placeholder="Кратко описание на вашите услуги"
              maxlength="220">
            <mat-icon matSuffix>title</mat-icon>
            <mat-hint>{{ registerForm.get('headline')?.value?.length || 0 }}/220</mat-hint>
            <mat-error *ngIf="registerForm.get('headline')?.invalid && registerForm.get('headline')?.touched">
              {{ getFieldErrorMessage('headline') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Подробно описание</mat-label>
            <textarea
              matInput
              formControlName="summary"
              placeholder="Подробно описание на вашия опит и услуги"
              rows="4"
              maxlength="2000">
            </textarea>
            <mat-hint>{{ registerForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>
            <mat-error *ngIf="registerForm.get('summary')?.invalid && registerForm.get('summary')?.touched">
              {{ getFieldErrorMessage('summary') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Години опит</mat-label>
            <input
              matInput
              type="number"
              formControlName="yearsOfExperience"
              placeholder="0"
              min="0"
              max="50">
            <mat-icon matSuffix>schedule</mat-icon>
            <mat-error *ngIf="registerForm.get('yearsOfExperience')?.invalid && registerForm.get('yearsOfExperience')?.touched">
              {{ getFieldErrorMessage('yearsOfExperience') }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Step 3: Location Information (Oracle only) -->
        <div *ngIf="currentStep === 3 && isOracleRegistration()" class="form-step">
          <h3>Местоположение</h3>

          <div class="location-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Град</mat-label>
              <input
                matInput
                type="text"
                formControlName="city"
                placeholder="Въведете град">
              <mat-icon matSuffix>location_city</mat-icon>
              <mat-error *ngIf="registerForm.get('city')?.invalid && registerForm.get('city')?.touched">
                {{ getFieldErrorMessage('city') }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Област/Регион</mat-label>
              <input
                matInput
                type="text"
                formControlName="state"
                placeholder="Въведете област">
              <mat-icon matSuffix>map</mat-icon>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Държава</mat-label>
            <input
              matInput
              type="text"
              formControlName="country"
              placeholder="Въведете държава">
            <mat-icon matSuffix>public</mat-icon>
            <mat-error *ngIf="registerForm.get('country')?.invalid && registerForm.get('country')?.touched">
              {{ getFieldErrorMessage('country') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Показвано местоположение (по избор)</mat-label>
            <input
              matInput
              type="text"
              formControlName="displayLocation"
              placeholder="Как да се показва местоположението ви публично">
            <mat-icon matSuffix>visibility</mat-icon>
          </mat-form-field>
        </div>

        <!-- Step 4: Oracle-Specific Information -->
        <div *ngIf="currentStep === 4 && isOracleRegistration()" class="form-step">
          <h3>Оракулска информация</h3>

          <div class="birth-info-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Дата на раждане</mat-label>
              <input
                matInput
                [matDatepicker]="birthDatePicker"
                formControlName="birthDate"
                placeholder="дд.мм.гггг"
                type="text">
              <mat-datepicker-toggle matSuffix [for]="birthDatePicker">
                <mat-icon matDatepickerToggleIcon>cake</mat-icon>
              </mat-datepicker-toggle>
              <mat-datepicker #birthDatePicker></mat-datepicker>
              <mat-error *ngIf="registerForm.get('birthDate')?.invalid && registerForm.get('birthDate')?.touched">
                {{ getFieldErrorMessage('birthDate') }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Час на раждане (по избор)</mat-label>
              <div class="time-input-container">
                <input
                  matInput
                  type="time"
                  formControlName="birthTime"
                  [step]="timeFormat24h ? 60 : 60">
                <button
                  mat-icon-button
                  type="button"
                  matSuffix
                  (click)="toggleTimeFormat()"
                  [title]="timeFormat24h ? 'Превключи на 12-часов формат' : 'Превключи на 24-часов формат'">
                  <mat-icon>{{ timeFormat24h ? 'schedule' : 'access_time' }}</mat-icon>
                </button>
              </div>
              <mat-hint>{{ timeFormat24h ? '24-часов формат' : '12-часов формат' }}</mat-hint>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Място на раждане</mat-label>
            <input
              matInput
              type="text"
              formControlName="birthLocation"
              placeholder="Град и държава на раждане">
            <mat-icon matSuffix>place</mat-icon>
            <mat-error *ngIf="registerForm.get('birthLocation')?.invalid && registerForm.get('birthLocation')?.touched">
              {{ getFieldErrorMessage('birthLocation') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Астрологична зодия (по избор)</mat-label>
            <mat-select formControlName="astrologicalSign">
              <mat-option *ngFor="let sign of getAstrologicalSigns()" [value]="sign">
                {{ sign }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Методи на работа</mat-label>
            <mat-select formControlName="oracleTypes" multiple>
              <mat-option *ngFor="let type of getOracleTypeOptions()" [value]="type">
                {{ type }}
              </mat-option>
            </mat-select>
            <mat-hint>Изберете всички методи, които използвате</mat-hint>
            <mat-error *ngIf="registerForm.get('oracleTypes')?.invalid && registerForm.get('oracleTypes')?.touched">
              {{ getFieldErrorMessage('oracleTypes') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Езици</mat-label>
            <mat-select formControlName="languagesSpoken" multiple>
              <mat-option *ngFor="let lang of getLanguageOptions()" [value]="lang">
                {{ lang }}
              </mat-option>
            </mat-select>
            <mat-hint>Изберете езиците, на които предлагате консултации</mat-hint>
            <mat-error *ngIf="registerForm.get('languagesSpoken')?.invalid && registerForm.get('languagesSpoken')?.touched">
              {{ getFieldErrorMessage('languagesSpoken') }}
            </mat-error>
          </mat-form-field>

          <!-- Skills Section -->
          <div class="skills-section">
            <h4>Умения и специализации</h4>
            <div class="skills-chips" *ngIf="registerForm.get('skills')?.value?.length > 0">
              <mat-chip-set>
                <mat-chip
                  *ngFor="let skill of registerForm.get('skills')?.value || []; let i = index"
                  (removed)="removeSkill(i)"
                  removable="true">
                  {{ skill }}
                  <mat-icon matChipRemove>cancel</mat-icon>
                </mat-chip>
              </mat-chip-set>
            </div>
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Добавете умение</mat-label>
              <input
                matInput
                #skillInput
                placeholder="Въведете умение и натиснете Enter или кликнете +"
                (keydown.enter)="addSkill($any($event), skillInput)">
              <button
                mat-icon-button
                type="button"
                matSuffix
                (click)="addSkillFromInput(skillInput)"
                [disabled]="!skillInput.value.trim()"
                title="Добави умение">
                <mat-icon>add</mat-icon>
              </button>
            </mat-form-field>
            <mat-hint>Добавете поне 3 умения свързани с вашата практика</mat-hint>
            <mat-error *ngIf="registerForm.get('skills')?.invalid && registerForm.get('skills')?.touched">
              {{ getFieldErrorMessage('skills') }}
            </mat-error>
          </div>
        </div>

        <!-- Step 5: Contact & Business Information (Oracle only) -->
        <div *ngIf="currentStep === 5 && isOracleRegistration()" class="form-step">
          <h3>Контакти и бизнес информация</h3>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Уебсайт (по избор)</mat-label>
            <input
              matInput
              type="url"
              formControlName="website"
              placeholder="https://вашия-сайт.com">
            <mat-icon matSuffix>language</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Портфолио URL (по избор)</mat-label>
            <input
              matInput
              type="url"
              formControlName="portfolioUrl"
              placeholder="https://портфолио.com">
            <mat-icon matSuffix>work</mat-icon>
          </mat-form-field>

          <h4>Бизнес адрес (по избор)</h4>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Улица</mat-label>
            <input
              matInput
              type="text"
              formControlName="businessStreet"
              placeholder="Улица и номер">
            <mat-icon matSuffix>home</mat-icon>
          </mat-form-field>

          <div class="business-address-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Град</mat-label>
              <input
                matInput
                type="text"
                formControlName="businessCity"
                placeholder="Град">
              <mat-icon matSuffix>location_city</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Пощенски код</mat-label>
              <input
                matInput
                type="text"
                formControlName="businessPostalCode"
                placeholder="Пощенски код">
              <mat-icon matSuffix>markunread_mailbox</mat-icon>
            </mat-form-field>
          </div>

          <mat-checkbox formControlName="isBusinessAddressPublic" color="primary">
            Показвай бизнес адреса публично
          </mat-checkbox>
        </div>

        <!-- Step 6: Consultation Rates & Terms (Oracle only) -->
        <div *ngIf="currentStep === 6 && isOracleRegistration()" class="form-step">
          <h3>Тарифи и условия</h3>

          <div class="rates-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Часова тарифа (по избор)</mat-label>
              <input
                matInput
                type="number"
                formControlName="hourlyRate"
                placeholder="0"
                min="0">
              <mat-icon matSuffix>schedule</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Тарифа за сесия (по избор)</mat-label>
              <input
                matInput
                type="number"
                formControlName="sessionRate"
                placeholder="0"
                min="0">
              <mat-icon matSuffix>event</mat-icon>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Валута</mat-label>
            <mat-select formControlName="currency">
              <mat-option value="BGN">BGN (Български лев)</mat-option>
              <mat-option value="EUR">EUR (Евро)</mat-option>
              <mat-option value="USD">USD (Долар)</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Terms and Conditions for Oracle -->
          <div class="terms-section">
            <mat-checkbox formControlName="acceptTerms" color="primary">
              Съгласявам се с <a href="#" target="_blank">Условията за ползване</a>,
              <a href="#" target="_blank">Политиката за поверителност</a> и
              <a href="#" target="_blank">Условията за оракули</a>
            </mat-checkbox>
            <mat-error *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched">
              Трябва да приемете всички условия и политики
            </mat-error>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="navigation-buttons" *ngIf="isOracleRegistration()">
          <button
            mat-button
            type="button"
            *ngIf="currentStep > 1"
            (click)="previousStep()"
            [disabled]="isLoading">
            <mat-icon>arrow_back</mat-icon>
            Назад
          </button>

          <div class="spacer"></div>

          <button
            mat-raised-button
            color="primary"
            type="submit"
            [disabled]="isLoading || !isCurrentStepValid()">
            <mat-icon *ngIf="isLoading">
              <mat-spinner diameter="20"></mat-spinner>
            </mat-icon>
            <mat-icon *ngIf="!isLoading && currentStep < totalSteps">arrow_forward</mat-icon>
            <mat-icon *ngIf="!isLoading && currentStep === totalSteps">person_add</mat-icon>
            {{ isLoading ? 'Обработка...' : (currentStep < totalSteps ? 'Напред' : 'Създай акаунт') }}
          </button>
        </div>

        <!-- Register Button for General Users -->
        <button
          *ngIf="registrationType === 'general'"
          mat-raised-button
          color="primary"
          type="submit"
          class="full-width register-button"
          [disabled]="isLoading">
          <mat-icon *ngIf="isLoading">
            <mat-spinner diameter="20"></mat-spinner>
          </mat-icon>
          <mat-icon *ngIf="!isLoading">person_add</mat-icon>
          {{ isLoading ? 'Създаване на акаунт...' : 'Създай акаунт' }}
        </button>

        <!-- Divider -->
        <div class="divider-container" *ngIf="registrationType === 'general'">
          <mat-divider></mat-divider>
          <span class="divider-text">или</span>
          <mat-divider></mat-divider>
        </div>

        <!-- OAuth Buttons -->
        <div class="oauth-buttons" *ngIf="registrationType === 'general'">
          <!-- Google Sign-Up Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button google-button"
            [disabled]="isLoading"
            (click)="signUpWithGoogle()">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="oauth-icon">
            Регистрация с Google
          </button>

          <!-- Facebook Sign-Up Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button facebook-button"
            [disabled]="isLoading"
            (click)="signUpWithFacebook()">
            <mat-icon class="oauth-icon facebook-icon">facebook</mat-icon>
            Регистрация с Facebook
          </button>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions class="register-actions">
      <mat-divider></mat-divider>

      <div class="login-section">
        <p class="login-text">Вече имате акаунт?</p>
        <button
          mat-stroked-button
          color="primary"
          type="button"
          (click)="navigateToLogin()"
          class="full-width">
          <mat-icon>login</mat-icon>
          Влезте в акаунта си
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
