{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SkillsManagementComponent {\n  static {\n    this.ɵfac = function SkillsManagementComponent_Factory(t) {\n      return new (t || SkillsManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SkillsManagementComponent,\n      selectors: [[\"app-skills-management\"]],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"skills-management\"]],\n      template: function SkillsManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Manage Skills \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n          i0.ɵɵtext(9, \"Skills management component - Coming soon!\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".skills-management[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHJvZmlsZS9jb21wb25lbnRzL3NraWxscy1tYW5hZ2VtZW50L3NraWxscy1tYW5hZ2VtZW50LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0kscUJBQXFCLGFBQWEsRUFBRSIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5za2lsbHMtbWFuYWdlbWVudCB7IHBhZGRpbmc6IDIwcHg7IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAuBA,OAAM,MAAOA,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAlBlCC,8BAA+B;UAIbA,0BAAU;UAAAA,iBAAW;UAC/BA,+BACF;UAAAA,iBAAiB;UAEnBA,wCAAkB;UACbA,0DAA0C;UAAAA,iBAAI", "names": ["SkillsManagementComponent", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\skills-management\\skills-management.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-skills-management',\r\n  template: `\r\n    <div class=\"skills-management\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>psychology</mat-icon>\r\n            Manage Skills\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <p>Skills management component - Coming soon!</p>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .skills-management { padding: 20px; }\r\n  `]\r\n})\r\nexport class SkillsManagementComponent {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}