using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Portfolio technology entity for astrology techniques and tools used
    /// </summary>
    public class PortfolioTechnology : BaseEntity
    {
        [Required]
        public int PortfolioItemId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Technology { get; set; } = string.Empty;

        // Navigation properties
        public virtual PortfolioItem PortfolioItem { get; set; } = null!;
    }
}
