using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Profile skill entity for astrology and spiritual expertise
    /// </summary>
    public class ProfileSkill : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Category { get; set; }

        public int Endorsements { get; set; } = 0;

        [MaxLength(20)]
        public string? ProficiencyLevel { get; set; } // beginner, intermediate, advanced, expert

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual ICollection<SkillEndorsement> SkillEndorsements { get; set; } = new List<SkillEndorsement>();
    }
}
