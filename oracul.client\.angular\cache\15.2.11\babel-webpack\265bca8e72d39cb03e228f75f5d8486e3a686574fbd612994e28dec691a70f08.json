{"ast": null, "code": "import { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ElementRef, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle repositioning after scroll events. */\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\nconst LONGPRESS_DELAY = 500;\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\nclass _MatTooltipBase {\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // If tooltip is disabled, hide immediately.\n    if (this._disabled) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n      this._ngZone.runOutsideAngular(() => {\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        Promise.resolve().then(() => {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        });\n      });\n    }\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n    this._overlay = _overlay;\n    this._elementRef = _elementRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._ariaDescriber = _ariaDescriber;\n    this._focusMonitor = _focusMonitor;\n    this._dir = _dir;\n    this._defaultOptions = _defaultOptions;\n    this._position = 'below';\n    this._positionAtOrigin = false;\n    this._disabled = false;\n    this._viewInitialized = false;\n    this._pointerExitEventsInitialized = false;\n    this._viewportMargin = 8;\n    this._cssClassPrefix = 'mat';\n    this._showDelay = this._defaultOptions.showDelay;\n    this._hideDelay = this._defaultOptions.hideDelay;\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    this.touchGestures = 'auto';\n    this._message = '';\n    /** Manually-bound passive event listeners. */\n    this._passiveListeners = [];\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n    if (_defaultOptions) {\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n      if (_defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n      }\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n    }\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    clearTimeout(this._touchstartTimeout);\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = this._overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset({\n      ...origin.main,\n      ...overlay.main\n    }), this._addOffset({\n      ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        const touch = event.targetTouches?.[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        clearTimeout(this._touchstartTimeout);\n        this._touchstartTimeout = setTimeout(() => this.show(undefined, origin), LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        clearTimeout(this._touchstartTimeout);\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n}\n_MatTooltipBase.ɵfac = function _MatTooltipBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n_MatTooltipBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTooltipBase,\n  inputs: {\n    position: [\"matTooltipPosition\", \"position\"],\n    positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n    disabled: [\"matTooltipDisabled\", \"disabled\"],\n    showDelay: [\"matTooltipShowDelay\", \"showDelay\"],\n    hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"],\n    touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"],\n    message: [\"matTooltip\", \"message\"],\n    tooltipClass: [\"matTooltipClass\", \"tooltipClass\"]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTooltipBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Platform\n    }, {\n      type: i3.AriaDescriber\n    }, {\n      type: i3.FocusMonitor\n    }, {\n      type: undefined\n    }, {\n      type: i4.Directionality\n    }, {\n      type: undefined\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip extends _MatTooltipBase {\n  constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n    super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n    this._tooltipComponent = TooltipComponent;\n    this._cssClassPrefix = 'mat-mdc';\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n}\nMatTooltip.ɵfac = function MatTooltip_Factory(t) {\n  return new (t || MatTooltip)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i3.AriaDescriber), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i4.Directionality, 8), i0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nMatTooltip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTooltip,\n  selectors: [[\"\", \"matTooltip\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n  exportAs: [\"matTooltip\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Platform\n    }, {\n      type: i3.AriaDescriber\n    }, {\n      type: i3.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i4.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass _TooltipComponentBase {\n  constructor(_changeDetectorRef, animationMode) {\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Whether interactions on the page should close the tooltip */\n    this._closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n    this._isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    this._onHide = new Subject();\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    clearTimeout(this._hideTimeoutId);\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    clearTimeout(this._showTimeoutId);\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {}\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    clearTimeout(this._showTimeoutId);\n    clearTimeout(this._hideTimeoutId);\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    this._isVisible = isVisible;\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n}\n_TooltipComponentBase.ɵfac = function _TooltipComponentBase_Factory(t) {\n  return new (t || _TooltipComponentBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n_TooltipComponentBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TooltipComponentBase\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_TooltipComponentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent extends _TooltipComponentBase {\n  constructor(changeDetectorRef, _elementRef, animationMode) {\n    super(changeDetectorRef, animationMode);\n    this._elementRef = _elementRef;\n    /* Whether the tooltip text overflows to multiple lines */\n    this._isMultiline = false;\n    this._showAnimation = 'mat-mdc-tooltip-show';\n    this._hideAnimation = 'mat-mdc-tooltip-hide';\n  }\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n}\nTooltipComponent.ɵfac = function TooltipComponent_Factory(t) {\n  return new (t || TooltipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nTooltipComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TooltipComponent,\n  selectors: [[\"mat-tooltip-component\"]],\n  viewQuery: function TooltipComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n    }\n  },\n  hostAttrs: [\"aria-hidden\", \"true\"],\n  hostVars: 2,\n  hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n        return ctx._handleMouseLeave($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"zoom\", ctx.isVisible() ? 1 : null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 4,\n  vars: 4,\n  consts: [[1, \"mdc-tooltip\", \"mdc-tooltip--shown\", \"mat-mdc-tooltip\", 3, \"ngClass\", \"animationend\"], [\"tooltip\", \"\"], [1, \"mdc-tooltip__surface\", \"mdc-tooltip__surface-animation\"]],\n  template: function TooltipComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n        return ctx._handleAnimationEnd($event);\n      });\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n      i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.message);\n    }\n  },\n  dependencies: [i5.NgClass],\n  styles: [\".mdc-tooltip__surface{word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - (2 * 8px));margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - (2 * 8px));align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color, #fff)}.mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color, #000)}.mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font, inherit);font-size:var(--mdc-plain-tooltip-supporting-text-size, inherit);font-weight:var(--mdc-plain-tooltip-supporting-text-weight, inherit);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, inherit)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n        '[style.zoom]': 'isVisible() ? 1 : null',\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mdc-tooltip__surface{word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - (2 * 8px));margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - (2 * 8px));align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color, #fff)}.mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color, #000)}.mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font, inherit);font-size:var(--mdc-plain-tooltip-supporting-text-size, inherit);font-weight:var(--mdc-plain-tooltip-supporting-text-weight, inherit);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, inherit)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [\n  // TODO(crisbeto): these values are based on MDC's CSS.\n  // We should be able to use their styles directly once we land #19432.\n  state('initial, void, hidden', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), state('visible', style({\n    transform: 'scale(1)'\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)'))])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTooltipModule {}\nMatTooltipModule.ɵfac = function MatTooltipModule_Factory(t) {\n  return new (t || MatTooltipModule)();\n};\nMatTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTooltipModule\n});\nMatTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      declarations: [MatTooltip, TooltipComponent],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };", "map": {"version": 3, "names": ["takeUntil", "take", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i0", "InjectionToken", "ElementRef", "Directive", "Inject", "Input", "Optional", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "i5", "DOCUMENT", "CommonModule", "i2", "normalizePassiveListenerOptions", "ANIMATION_MODULE_TYPE", "i3", "A11yModule", "i4", "i1", "Overlay", "OverlayModule", "ComponentPortal", "Subject", "trigger", "state", "style", "transition", "animate", "CdkScrollableModule", "MatCommonModule", "SCROLL_THROTTLE_MS", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "MAT_TOOLTIP_DEFAULT_OPTIONS", "providedIn", "factory", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "LONGPRESS_DELAY", "MIN_VIEWPORT_TOOLTIP_THRESHOLD", "UNBOUNDED_ANCHOR_GAP", "MIN_HEIGHT", "MAX_WIDTH", "_MatTooltipBase", "_position", "value", "_overlayRef", "_updatePosition", "_tooltipInstance", "show", "updatePosition", "positionAt<PERSON><PERSON><PERSON>", "_position<PERSON><PERSON><PERSON><PERSON><PERSON>", "_detach", "disabled", "_disabled", "hide", "_setupPointerEnterEventsIfNeeded", "_showDelay", "_hideDelay", "_mouseLeaveHideDelay", "message", "_message", "_ariaDescriber", "removeDescription", "_elementRef", "nativeElement", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "_ngZone", "runOutsideAngular", "Promise", "resolve", "then", "describe", "tooltipClass", "_tooltipClass", "_setTooltipClass", "constructor", "_overlay", "_scrollDispatcher", "_viewContainerRef", "_platform", "_focusMonitor", "scrollStrategy", "_dir", "_defaultOptions", "_document", "_viewInitialized", "_pointerExitEventsInitialized", "_viewportMargin", "_cssClassPrefix", "touchGestures", "_passiveListeners", "_destroyed", "_scrollStrategy", "change", "pipe", "subscribe", "ngAfterViewInit", "monitor", "origin", "run", "ngOnDestroy", "clearTimeout", "_touchstartTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "stopMonitoring", "delay", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "_portal", "_tooltipComponent", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "undefined", "existingStrategy", "getConfig", "positionStrategy", "_origin", "scrollableAncestors", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "panelClass", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "disableTooltipInteractivity", "addPanelClass", "has<PERSON>tta<PERSON>", "detach", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "main", "fallback", "isLtr", "originPosition", "originX", "originY", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMicrotaskEmpty", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "point", "_disableNativeGesturesIfNecessary", "touch", "targetTouches", "clientX", "clientY", "setTimeout", "_addListeners", "exitListeners", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "element", "gestures", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "ɵfac", "ɵdir", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewContainerRef", "NgZone", "Platform", "AriaDescriber", "FocusMonitor", "Directionality", "decorators", "args", "MatTooltip", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "viewContainerRef", "ngZone", "platform", "ariaDescriber", "focusMonitor", "dir", "defaultOptions", "TooltipComponent", "offset", "offsetY", "offsetX", "selector", "exportAs", "host", "_TooltipComponentBase", "_changeDetectorRef", "animationMode", "_closeOnInteraction", "_isVisible", "_onHide", "_animationsDisabled", "_hideTimeoutId", "_showTimeoutId", "_toggleVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_handleAnimationEnd", "animationName", "_showAnimation", "_hideAnimation", "toVisible", "tooltip", "_tooltip", "showClass", "hideClass", "classList", "remove", "add", "getComputedStyle", "styles", "getPropertyValue", "ChangeDetectorRef", "changeDetectorRef", "_isMultiline", "_isTooltipMultiline", "rect", "getBoundingClientRect", "height", "width", "ɵcmp", "Ng<PERSON><PERSON>", "encapsulation", "None", "changeDetection", "OnPush", "template", "static", "matTooltipAnimations", "tooltipState", "opacity", "transform", "MatTooltipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/tooltip.mjs"], "sourcesContent": ["import { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ElementRef, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\nconst LONGPRESS_DELAY = 500;\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\nclass _MatTooltipBase {\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    get positionAtOrigin() {\n        return this._positionAtOrigin;\n    }\n    set positionAtOrigin(value) {\n        this._positionAtOrigin = coerceBooleanProperty(value);\n        this._detach();\n        this._overlayRef = null;\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._positionAtOrigin = false;\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        this._viewportMargin = 8;\n        this._cssClassPrefix = 'mat';\n        this._showDelay = this._defaultOptions.showDelay;\n        this._hideDelay = this._defaultOptions.hideDelay;\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        this._scrollStrategy = scrollStrategy;\n        this._document = _document;\n        if (_defaultOptions) {\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.positionAtOrigin) {\n                this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n            }\n        });\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay, origin) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay(origin);\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle(origin) {\n        this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay(origin) {\n        if (this._overlayRef) {\n            const existingStrategy = this._overlayRef.getConfig()\n                .positionStrategy;\n            if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n                return this._overlayRef;\n            }\n            this._detach();\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._scrollStrategy(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                event => {\n                    this._setupPointerExitEventsIfNeeded();\n                    let point = undefined;\n                    if (event.x !== undefined && event.y !== undefined) {\n                        point = event;\n                    }\n                    this.show(undefined, point);\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                event => {\n                    const touch = event.targetTouches?.[0];\n                    const origin = touch ? { x: touch.clientX, y: touch.clientY } : undefined;\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    this._touchstartTimeout = setTimeout(() => this.show(undefined, origin), LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n}\n_MatTooltipBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTooltipBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatTooltipBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTooltipBase, inputs: { position: [\"matTooltipPosition\", \"position\"], positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTooltipBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i2.Platform }, { type: i3.AriaDescriber }, { type: i3.FocusMonitor }, { type: undefined }, { type: i4.Directionality }, { type: undefined }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], positionAtOrigin: [{\n                type: Input,\n                args: ['matTooltipPositionAtOrigin']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip extends _MatTooltipBase {\n    constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n        super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n        this._tooltipComponent = TooltipComponent;\n        this._cssClassPrefix = 'mat-mdc';\n        this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n    }\n    _addOffset(position) {\n        const offset = UNBOUNDED_ANCHOR_GAP;\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        if (position.originY === 'top') {\n            position.offsetY = -offset;\n        }\n        else if (position.originY === 'bottom') {\n            position.offsetY = offset;\n        }\n        else if (position.originX === 'start') {\n            position.offsetX = isLtr ? -offset : offset;\n        }\n        else if (position.originX === 'end') {\n            position.offsetX = isLtr ? offset : -offset;\n        }\n        return position;\n    }\n}\nMatTooltip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltip, deps: [{ token: i1.Overlay }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i2.Platform }, { token: i3.AriaDescriber }, { token: i3.FocusMonitor }, { token: MAT_TOOLTIP_SCROLL_STRATEGY }, { token: i4.Directionality, optional: true }, { token: MAT_TOOLTIP_DEFAULT_OPTIONS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nMatTooltip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTooltip, selector: \"[matTooltip]\", host: { classAttribute: \"mat-mdc-tooltip-trigger\" }, exportAs: [\"matTooltip\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-mdc-tooltip-trigger',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i2.Platform }, { type: i3.AriaDescriber }, { type: i3.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n                }] }, { type: i4.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass _TooltipComponentBase {\n    constructor(_changeDetectorRef, animationMode) {\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Whether the tooltip is currently visible. */\n        this._isVisible = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        clearTimeout(this._hideTimeoutId);\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        clearTimeout(this._showTimeoutId);\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() { }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        clearTimeout(this._showTimeoutId);\n        clearTimeout(this._hideTimeoutId);\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        this._isVisible = isVisible;\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n}\n_TooltipComponentBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _TooltipComponentBase, deps: [{ token: i0.ChangeDetectorRef }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_TooltipComponentBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _TooltipComponentBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _TooltipComponentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent extends _TooltipComponentBase {\n    constructor(changeDetectorRef, _elementRef, animationMode) {\n        super(changeDetectorRef, animationMode);\n        this._elementRef = _elementRef;\n        /* Whether the tooltip text overflows to multiple lines */\n        this._isMultiline = false;\n        this._showAnimation = 'mat-mdc-tooltip-show';\n        this._hideAnimation = 'mat-mdc-tooltip-hide';\n    }\n    _onShow() {\n        this._isMultiline = this._isTooltipMultiline();\n        this._markForCheck();\n    }\n    /** Whether the tooltip text has overflown to the next line */\n    _isTooltipMultiline() {\n        const rect = this._elementRef.nativeElement.getBoundingClientRect();\n        return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n    }\n}\nTooltipComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: TooltipComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nTooltipComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: TooltipComponent, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" }, properties: { \"style.zoom\": \"isVisible() ? 1 : null\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - (2 * 8px));margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - (2 * 8px));align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color, #fff)}.mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color, #000)}.mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font, inherit);font-size:var(--mdc-plain-tooltip-supporting-text-size, inherit);font-weight:var(--mdc-plain-tooltip-supporting-text-weight, inherit);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, inherit)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"], dependencies: [{ kind: \"directive\", type: i5.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                        '[style.zoom]': 'isVisible() ? 1 : null',\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - (2 * 8px));margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - (2 * 8px));align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color, #fff)}.mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape, var(--mdc-shape-small, 4px))}.mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color, #000)}.mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font, inherit);font-size:var(--mdc-plain-tooltip-supporting-text-size, inherit);font-weight:var(--mdc-plain-tooltip-supporting-text-weight, inherit);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, inherit)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        // TODO(crisbeto): these values are based on MDC's CSS.\n        // We should be able to use their styles directly once we land #19432.\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0.8)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTooltipModule {\n}\nMatTooltipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTooltipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltipModule, declarations: [MatTooltip, TooltipComponent], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] });\nMatTooltipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    declarations: [MatTooltip, TooltipComponent],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC1K,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;AAC7D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,SAASC,iCAAiC,CAACC,QAAQ,EAAE;EACjD,OAAOC,KAAK,CAAE,qBAAoBD,QAAS,eAAc,CAAC;AAC9D;AACA;AACA,MAAME,2BAA2B,GAAG,IAAIpC,cAAc,CAAC,6BAA6B,CAAC;AACrF;AACA,SAASqC,mCAAmC,CAACC,OAAO,EAAE;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAAEC,cAAc,EAAET;EAAmB,CAAC,CAAC;AAC5F;AACA;AACA,MAAMU,4CAA4C,GAAG;EACjDC,OAAO,EAAEP,2BAA2B;EACpCQ,IAAI,EAAE,CAACvB,OAAO,CAAC;EACfwB,UAAU,EAAER;AAChB,CAAC;AACD;AACA,SAASS,mCAAmC,GAAG;EAC3C,OAAO;IACHC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;EACvB,CAAC;AACL;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAIlD,cAAc,CAAC,6BAA6B,EAAE;EAClFmD,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMO,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,WAAW,GAAG,eAAe;AACnC;AACA,MAAMC,sBAAsB,GAAGxC,+BAA+B,CAAC;EAAEyC,OAAO,EAAE;AAAK,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,GAAG;AAC3B;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,GAAG;AACrB,MAAMC,eAAe,CAAC;EAClB;EACA,IAAI5B,QAAQ,GAAG;IACX,OAAO,IAAI,CAAC6B,SAAS;EACzB;EACA,IAAI7B,QAAQ,CAAC8B,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGC,KAAK;MACtB,IAAI,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;QACtC,IAAI,CAACE,gBAAgB,EAAEC,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACH,WAAW,CAACI,cAAc,EAAE;MACrC;IACJ;EACJ;EACA,IAAIC,gBAAgB,GAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgB,CAACN,KAAK,EAAE;IACxB,IAAI,CAACO,iBAAiB,GAAG5E,qBAAqB,CAACqE,KAAK,CAAC;IACrD,IAAI,CAACQ,OAAO,EAAE;IACd,IAAI,CAACP,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIQ,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACT,KAAK,EAAE;IAChB,IAAI,CAACU,SAAS,GAAG/E,qBAAqB,CAACqE,KAAK,CAAC;IAC7C;IACA,IAAI,IAAI,CAACU,SAAS,EAAE;MAChB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,EAAE;IAC3C;EACJ;EACA;EACA,IAAI7B,SAAS,GAAG;IACZ,OAAO,IAAI,CAAC8B,UAAU;EAC1B;EACA,IAAI9B,SAAS,CAACiB,KAAK,EAAE;IACjB,IAAI,CAACa,UAAU,GAAGjF,oBAAoB,CAACoE,KAAK,CAAC;EACjD;EACA;EACA,IAAIhB,SAAS,GAAG;IACZ,OAAO,IAAI,CAAC8B,UAAU;EAC1B;EACA,IAAI9B,SAAS,CAACgB,KAAK,EAAE;IACjB,IAAI,CAACc,UAAU,GAAGlF,oBAAoB,CAACoE,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACY,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAChE;EACJ;EACA;EACA,IAAIE,OAAO,GAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAO,CAAChB,KAAK,EAAE;IACf,IAAI,CAACkB,cAAc,CAACC,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAACC,aAAa,EAAE,IAAI,CAACJ,QAAQ,EAAE,SAAS,CAAC;IAC/F;IACA;IACA;IACA,IAAI,CAACA,QAAQ,GAAGjB,KAAK,IAAI,IAAI,GAAGsB,MAAM,CAACtB,KAAK,CAAC,CAACuB,IAAI,EAAE,GAAG,EAAE;IACzD,IAAI,CAAC,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACO,iBAAiB,EAAE,EAAE;MAC5C,IAAI,CAACb,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,EAAE;MACvC,IAAI,CAACa,qBAAqB,EAAE;MAC5B,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA;QACA;QACAC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACZ,cAAc,CAACa,QAAQ,CAAC,IAAI,CAACX,WAAW,CAACC,aAAa,EAAE,IAAI,CAACL,OAAO,EAAE,SAAS,CAAC;QACzF,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACA,IAAIgB,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAAChC,KAAK,EAAE;IACpB,IAAI,CAACiC,aAAa,GAAGjC,KAAK;IAC1B,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAAC+B,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IAC7C;EACJ;EACAE,WAAW,CAACC,QAAQ,EAAEhB,WAAW,EAAEiB,iBAAiB,EAAEC,iBAAiB,EAAEZ,OAAO,EAAEa,SAAS,EAAErB,cAAc,EAAEsB,aAAa,EAAEC,cAAc,EAAEC,IAAI,EAAEC,eAAe,EAAEC,SAAS,EAAE;IAC1K,IAAI,CAACR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiB,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACsB,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC5C,SAAS,GAAG,OAAO;IACxB,IAAI,CAACQ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,6BAA6B,GAAG,KAAK;IAC1C,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACnC,UAAU,GAAG,IAAI,CAAC8B,eAAe,CAAC5D,SAAS;IAChD,IAAI,CAAC+B,UAAU,GAAG,IAAI,CAAC6B,eAAe,CAAC3D,SAAS;IAChD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiE,aAAa,GAAG,MAAM;IAC3B,IAAI,CAAChC,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACiC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,UAAU,GAAG,IAAI3F,OAAO,EAAE;IAC/B,IAAI,CAAC4F,eAAe,GAAGX,cAAc;IACrC,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAID,eAAe,EAAE;MACjB,IAAIA,eAAe,CAACzE,QAAQ,EAAE;QAC1B,IAAI,CAACA,QAAQ,GAAGyE,eAAe,CAACzE,QAAQ;MAC5C;MACA,IAAIyE,eAAe,CAACrC,gBAAgB,EAAE;QAClC,IAAI,CAACA,gBAAgB,GAAGqC,eAAe,CAACrC,gBAAgB;MAC5D;MACA,IAAIqC,eAAe,CAACM,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAGN,eAAe,CAACM,aAAa;MACtD;IACJ;IACAP,IAAI,CAACW,MAAM,CAACC,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAACI,SAAS,CAAC,MAAM;MACzD,IAAI,IAAI,CAACtD,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAuD,eAAe,GAAG;IACd;IACA,IAAI,CAACX,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACjC,gCAAgC,EAAE;IACvC,IAAI,CAAC4B,aAAa,CACbiB,OAAO,CAAC,IAAI,CAACrC,WAAW,CAAC,CACzBkC,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAACG,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAM,EAAE;QACT,IAAI,CAAChC,OAAO,CAACiC,GAAG,CAAC,MAAM,IAAI,CAAChD,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAI+C,MAAM,KAAK,UAAU,EAAE;QAC5B,IAAI,CAAChC,OAAO,CAACiC,GAAG,CAAC,MAAM,IAAI,CAACvD,IAAI,EAAE,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIwD,WAAW,GAAG;IACV,MAAMvC,aAAa,GAAG,IAAI,CAACD,WAAW,CAACC,aAAa;IACpDwC,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;IACrC,IAAI,IAAI,CAAC7D,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC8D,OAAO,EAAE;MAC1B,IAAI,CAAC5D,gBAAgB,GAAG,IAAI;IAChC;IACA;IACA,IAAI,CAAC+C,iBAAiB,CAACc,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MAClD7C,aAAa,CAAC8C,mBAAmB,CAACF,KAAK,EAAEC,QAAQ,EAAE3E,sBAAsB,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAAC2D,iBAAiB,CAACkB,MAAM,GAAG,CAAC;IACjC,IAAI,CAACjB,UAAU,CAACkB,IAAI,EAAE;IACtB,IAAI,CAAClB,UAAU,CAACmB,QAAQ,EAAE;IAC1B,IAAI,CAACpD,cAAc,CAACC,iBAAiB,CAACE,aAAa,EAAE,IAAI,CAACL,OAAO,EAAE,SAAS,CAAC;IAC7E,IAAI,CAACwB,aAAa,CAAC+B,cAAc,CAAClD,aAAa,CAAC;EACpD;EACA;EACAjB,IAAI,CAACoE,KAAK,GAAG,IAAI,CAACzF,SAAS,EAAE2E,MAAM,EAAE;IACjC,IAAI,IAAI,CAACjD,QAAQ,IAAI,CAAC,IAAI,CAACO,OAAO,IAAI,IAAI,CAACQ,iBAAiB,EAAE,EAAE;MAC5D,IAAI,CAACrB,gBAAgB,EAAEsE,wBAAwB,EAAE;MACjD;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACjB,MAAM,CAAC;IAC9C,IAAI,CAAClD,OAAO,EAAE;IACd,IAAI,CAACoE,OAAO,GACR,IAAI,CAACA,OAAO,IAAI,IAAIrH,eAAe,CAAC,IAAI,CAACsH,iBAAiB,EAAE,IAAI,CAACvC,iBAAiB,CAAC;IACvF,MAAMwC,QAAQ,GAAI,IAAI,CAAC3E,gBAAgB,GAAGuE,UAAU,CAACK,MAAM,CAAC,IAAI,CAACH,OAAO,CAAC,CAACE,QAAS;IACnFA,QAAQ,CAACE,eAAe,GAAG,IAAI,CAAC5D,WAAW,CAACC,aAAa;IACzDyD,QAAQ,CAAC/D,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAC/CgE,QAAQ,CACHG,WAAW,EAAE,CACb3B,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAAC/C,OAAO,EAAE,CAAC;IACpC,IAAI,CAAC0B,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IACzC,IAAI,CAACR,qBAAqB,EAAE;IAC5BqD,QAAQ,CAAC1E,IAAI,CAACoE,KAAK,CAAC;EACxB;EACA;EACA7D,IAAI,CAAC6D,KAAK,GAAG,IAAI,CAACxF,SAAS,EAAE;IACzB,MAAM8F,QAAQ,GAAG,IAAI,CAAC3E,gBAAgB;IACtC,IAAI2E,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,SAAS,EAAE,EAAE;QACtBJ,QAAQ,CAACnE,IAAI,CAAC6D,KAAK,CAAC;MACxB,CAAC,MACI;QACDM,QAAQ,CAACL,wBAAwB,EAAE;QACnC,IAAI,CAACjE,OAAO,EAAE;MAClB;IACJ;EACJ;EACA;EACA2E,MAAM,CAACzB,MAAM,EAAE;IACX,IAAI,CAAClC,iBAAiB,EAAE,GAAG,IAAI,CAACb,IAAI,EAAE,GAAG,IAAI,CAACP,IAAI,CAACgF,SAAS,EAAE1B,MAAM,CAAC;EACzE;EACA;EACAlC,iBAAiB,GAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACrB,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+E,SAAS,EAAE;EACvE;EACA;EACAP,cAAc,CAACjB,MAAM,EAAE;IACnB,IAAI,IAAI,CAACzD,WAAW,EAAE;MAClB,MAAMoF,gBAAgB,GAAG,IAAI,CAACpF,WAAW,CAACqF,SAAS,EAAE,CAChDC,gBAAgB;MACrB,IAAI,CAAC,CAAC,IAAI,CAACjF,gBAAgB,IAAI,CAACoD,MAAM,KAAK2B,gBAAgB,CAACG,OAAO,YAAYvJ,UAAU,EAAE;QACvF,OAAO,IAAI,CAACgE,WAAW;MAC3B;MACA,IAAI,CAACO,OAAO,EAAE;IAClB;IACA,MAAMiF,mBAAmB,GAAG,IAAI,CAACpD,iBAAiB,CAACqD,2BAA2B,CAAC,IAAI,CAACtE,WAAW,CAAC;IAChG;IACA,MAAMuE,QAAQ,GAAG,IAAI,CAACvD,QAAQ,CACzBlE,QAAQ,EAAE,CACV0H,mBAAmB,CAAC,IAAI,CAACtF,gBAAgB,GAAGoD,MAAM,IAAI,IAAI,CAACtC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAC1FyE,qBAAqB,CAAE,IAAG,IAAI,CAAC7C,eAAgB,UAAS,CAAC,CACzD8C,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,kBAAkB,CAAC,IAAI,CAAChD,eAAe,CAAC,CACxCiD,wBAAwB,CAACP,mBAAmB,CAAC;IAClDE,QAAQ,CAACM,eAAe,CAAC3C,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAI;MAC1E,IAAI,CAAC6C,2BAA2B,CAAC7C,MAAM,CAAC8C,cAAc,CAAC;MACvD,IAAI,IAAI,CAAChG,gBAAgB,EAAE;QACvB,IAAIkD,MAAM,CAAC+C,wBAAwB,CAACC,gBAAgB,IAAI,IAAI,CAAClG,gBAAgB,CAAC+E,SAAS,EAAE,EAAE;UACvF;UACA;UACA,IAAI,CAACxD,OAAO,CAACiC,GAAG,CAAC,MAAM,IAAI,CAAChD,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,WAAW,GAAG,IAAI,CAACmC,QAAQ,CAACkE,MAAM,CAAC;MACpCC,SAAS,EAAE,IAAI,CAAC7D,IAAI;MACpB6C,gBAAgB,EAAEI,QAAQ;MAC1Ba,UAAU,EAAG,GAAE,IAAI,CAACxD,eAAgB,IAAG1D,WAAY,EAAC;MACpDmD,cAAc,EAAE,IAAI,CAACW,eAAe;IACxC,CAAC,CAAC;IACF,IAAI,CAAClD,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;IACtC,IAAI,CAACA,WAAW,CACXwG,WAAW,EAAE,CACbnD,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAAC/C,OAAO,EAAE,CAAC;IACpC,IAAI,CAACP,WAAW,CACXyG,oBAAoB,EAAE,CACtBpD,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAACpD,gBAAgB,EAAEwG,sBAAsB,EAAE,CAAC;IACrE,IAAI,CAAC1G,WAAW,CACX2G,aAAa,EAAE,CACftD,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAACU,KAAK,IAAI;MACpB,IAAI,IAAI,CAACzC,iBAAiB,EAAE,IAAIyC,KAAK,CAAC4C,OAAO,KAAKhL,MAAM,IAAI,CAACC,cAAc,CAACmI,KAAK,CAAC,EAAE;QAChFA,KAAK,CAAC6C,cAAc,EAAE;QACtB7C,KAAK,CAAC8C,eAAe,EAAE;QACvB,IAAI,CAACrF,OAAO,CAACiC,GAAG,CAAC,MAAM,IAAI,CAAChD,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACgC,eAAe,EAAEqE,2BAA2B,EAAE;MACnD,IAAI,CAAC/G,WAAW,CAACgH,aAAa,CAAE,GAAE,IAAI,CAACjE,eAAgB,gCAA+B,CAAC;IAC3F;IACA,OAAO,IAAI,CAAC/C,WAAW;EAC3B;EACA;EACAO,OAAO,GAAG;IACN,IAAI,IAAI,CAACP,WAAW,IAAI,IAAI,CAACA,WAAW,CAACiH,WAAW,EAAE,EAAE;MACpD,IAAI,CAACjH,WAAW,CAACkH,MAAM,EAAE;IAC7B;IACA,IAAI,CAAChH,gBAAgB,GAAG,IAAI;EAChC;EACA;EACAD,eAAe,CAACwE,UAAU,EAAE;IACxB,MAAMxG,QAAQ,GAAGwG,UAAU,CAACY,SAAS,EAAE,CAACC,gBAAgB;IACxD,MAAM7B,MAAM,GAAG,IAAI,CAAC0D,UAAU,EAAE;IAChC,MAAM9I,OAAO,GAAG,IAAI,CAAC+I,mBAAmB,EAAE;IAC1CnJ,QAAQ,CAACoJ,aAAa,CAAC,CACnB,IAAI,CAACC,UAAU,CAAC;MAAE,GAAG7D,MAAM,CAAC8D,IAAI;MAAE,GAAGlJ,OAAO,CAACkJ;IAAK,CAAC,CAAC,EACpD,IAAI,CAACD,UAAU,CAAC;MAAE,GAAG7D,MAAM,CAAC+D,QAAQ;MAAE,GAAGnJ,OAAO,CAACmJ;IAAS,CAAC,CAAC,CAC/D,CAAC;EACN;EACA;EACAF,UAAU,CAACrJ,QAAQ,EAAE;IACjB,OAAOA,QAAQ;EACnB;EACA;AACJ;AACA;AACA;EACIkJ,UAAU,GAAG;IACT,MAAMM,KAAK,GAAG,CAAC,IAAI,CAAChF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,MAAM9B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIyJ,cAAc;IAClB,IAAIzJ,QAAQ,IAAI,OAAO,IAAIA,QAAQ,IAAI,OAAO,EAAE;MAC5CyJ,cAAc,GAAG;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE3J,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG;MAAS,CAAC;IAC3F,CAAC,MACI,IAAIA,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAIwJ,KAAM,IAC5BxJ,QAAQ,IAAI,OAAO,IAAI,CAACwJ,KAAM,EAAE;MACjCC,cAAc,GAAG;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IAC5D,CAAC,MACI,IAAI3J,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAIwJ,KAAM,IAC7BxJ,QAAQ,IAAI,MAAM,IAAI,CAACwJ,KAAM,EAAE;MAChCC,cAAc,GAAG;QAAEC,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAS,CAAC;IAC1D,CAAC,MACI,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAM7J,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAE6J,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACN,cAAc,CAACC,OAAO,EAAED,cAAc,CAACE,OAAO,CAAC;IACrF,OAAO;MACHL,IAAI,EAAEG,cAAc;MACpBF,QAAQ,EAAE;QAAEG,OAAO,EAAEG,CAAC;QAAEF,OAAO,EAAEG;MAAE;IACvC,CAAC;EACL;EACA;EACAX,mBAAmB,GAAG;IAClB,MAAMK,KAAK,GAAG,CAAC,IAAI,CAAChF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,MAAM9B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIgK,eAAe;IACnB,IAAIhK,QAAQ,IAAI,OAAO,EAAE;MACrBgK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAChE,CAAC,MACI,IAAIlK,QAAQ,IAAI,OAAO,EAAE;MAC1BgK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC7D,CAAC,MACI,IAAIlK,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAIwJ,KAAM,IAC5BxJ,QAAQ,IAAI,OAAO,IAAI,CAACwJ,KAAM,EAAE;MACjCQ,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC7D,CAAC,MACI,IAAIlK,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAIwJ,KAAM,IAC7BxJ,QAAQ,IAAI,MAAM,IAAI,CAACwJ,KAAM,EAAE;MAChCQ,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC/D,CAAC,MACI,IAAI,OAAON,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAM7J,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAE6J,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACC,eAAe,CAACC,QAAQ,EAAED,eAAe,CAACE,QAAQ,CAAC;IACzF,OAAO;MACHZ,IAAI,EAAEU,eAAe;MACrBT,QAAQ,EAAE;QAAEU,QAAQ,EAAEJ,CAAC;QAAEK,QAAQ,EAAEJ;MAAE;IACzC,CAAC;EACL;EACA;EACAvG,qBAAqB,GAAG;IACpB;IACA;IACA,IAAI,IAAI,CAACtB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACa,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5C,IAAI,CAACb,gBAAgB,CAACkI,aAAa,EAAE;MACrC,IAAI,CAAC3G,OAAO,CAAC4G,gBAAgB,CAAChF,IAAI,CAAC5H,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC0H,UAAU,CAAC,CAAC,CAACI,SAAS,CAAC,MAAM;QACpF,IAAI,IAAI,CAACpD,gBAAgB,EAAE;UACvB,IAAI,CAACF,WAAW,CAACI,cAAc,EAAE;QACrC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA6B,gBAAgB,CAACF,YAAY,EAAE;IAC3B,IAAI,IAAI,CAAC7B,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC6B,YAAY,GAAGA,YAAY;MACjD,IAAI,CAAC7B,gBAAgB,CAACkI,aAAa,EAAE;IACzC;EACJ;EACA;EACAJ,eAAe,CAACF,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,IAAI,CAAC9J,QAAQ,KAAK,OAAO,IAAI,IAAI,CAACA,QAAQ,KAAK,OAAO,EAAE;MACxD,IAAI8J,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,QAAQ;MAChB,CAAC,MACI,IAAIA,CAAC,KAAK,QAAQ,EAAE;QACrBA,CAAC,GAAG,KAAK;MACb;IACJ,CAAC,MACI;MACD,IAAID,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,OAAO;MACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;QACpBA,CAAC,GAAG,KAAK;MACb;IACJ;IACA,OAAO;MAAEA,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACA9B,2BAA2B,CAACC,cAAc,EAAE;IACxC,MAAM;MAAEiC,QAAQ;MAAER,OAAO;MAAEC;IAAQ,CAAC,GAAG1B,cAAc;IACrD,IAAIoC,WAAW;IACf;IACA;IACA,IAAIH,QAAQ,KAAK,QAAQ,EAAE;MACvB;MACA;MACA;MACA,IAAI,IAAI,CAAC1F,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,KAAK,KAAK,EAAE;QACxCuI,WAAW,GAAGX,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACtD,CAAC,MACI;QACDW,WAAW,GAAGX,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACxD;IACJ,CAAC,MACI;MACDW,WAAW,GAAGH,QAAQ,KAAK,QAAQ,IAAIP,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;IAChF;IACA,IAAIU,WAAW,KAAK,IAAI,CAACC,gBAAgB,EAAE;MACvC,MAAM9D,UAAU,GAAG,IAAI,CAACzE,WAAW;MACnC,IAAIyE,UAAU,EAAE;QACZ,MAAM+D,WAAW,GAAI,GAAE,IAAI,CAACzF,eAAgB,IAAG1D,WAAY,GAAE;QAC7DoF,UAAU,CAACgE,gBAAgB,CAACD,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC;QAChE9D,UAAU,CAACuC,aAAa,CAACwB,WAAW,GAAGF,WAAW,CAAC;MACvD;MACA,IAAI,CAACC,gBAAgB,GAAGD,WAAW;IACvC;EACJ;EACA;EACA3H,gCAAgC,GAAG;IAC/B;IACA,IAAI,IAAI,CAACF,SAAS,IACd,CAAC,IAAI,CAACM,OAAO,IACb,CAAC,IAAI,CAAC6B,gBAAgB,IACtB,IAAI,CAACK,iBAAiB,CAACkB,MAAM,EAAE;MAC/B;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACuE,4BAA4B,EAAE,EAAE;MACrC,IAAI,CAACzF,iBAAiB,CAAC0F,IAAI,CAAC,CACxB,YAAY,EACZ3E,KAAK,IAAI;QACL,IAAI,CAAC4E,+BAA+B,EAAE;QACtC,IAAIC,KAAK,GAAG1D,SAAS;QACrB,IAAInB,KAAK,CAAC8D,CAAC,KAAK3C,SAAS,IAAInB,KAAK,CAAC+D,CAAC,KAAK5C,SAAS,EAAE;UAChD0D,KAAK,GAAG7E,KAAK;QACjB;QACA,IAAI,CAAC7D,IAAI,CAACgF,SAAS,EAAE0D,KAAK,CAAC;MAC/B,CAAC,CACJ,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAAC7F,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAAC8F,iCAAiC,EAAE;MACxC,IAAI,CAAC7F,iBAAiB,CAAC0F,IAAI,CAAC,CACxB,YAAY,EACZ3E,KAAK,IAAI;QACL,MAAM+E,KAAK,GAAG/E,KAAK,CAACgF,aAAa,GAAG,CAAC,CAAC;QACtC,MAAMvF,MAAM,GAAGsF,KAAK,GAAG;UAAEjB,CAAC,EAAEiB,KAAK,CAACE,OAAO;UAAElB,CAAC,EAAEgB,KAAK,CAACG;QAAQ,CAAC,GAAG/D,SAAS;QACzE;QACA;QACA,IAAI,CAACyD,+BAA+B,EAAE;QACtChF,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,IAAI,CAACA,kBAAkB,GAAGsF,UAAU,CAAC,MAAM,IAAI,CAAChJ,IAAI,CAACgF,SAAS,EAAE1B,MAAM,CAAC,EAAEjE,eAAe,CAAC;MAC7F,CAAC,CACJ,CAAC;IACN;IACA,IAAI,CAAC4J,aAAa,CAAC,IAAI,CAACnG,iBAAiB,CAAC;EAC9C;EACA2F,+BAA+B,GAAG;IAC9B,IAAI,IAAI,CAAC/F,6BAA6B,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,6BAA6B,GAAG,IAAI;IACzC,MAAMwG,aAAa,GAAG,EAAE;IACxB,IAAI,IAAI,CAACX,4BAA4B,EAAE,EAAE;MACrCW,aAAa,CAACV,IAAI,CAAC,CACf,YAAY,EACZ3E,KAAK,IAAI;QACL,MAAMsF,SAAS,GAAGtF,KAAK,CAACuF,aAAa;QACrC,IAAI,CAACD,SAAS,IAAI,CAAC,IAAI,CAACtJ,WAAW,EAAEwJ,cAAc,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;UACrE,IAAI,CAAC5I,IAAI,EAAE;QACf;MACJ,CAAC,CACJ,EAAE,CAAC,OAAO,EAAEsD,KAAK,IAAI,IAAI,CAAC0F,cAAc,CAAC1F,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAAChB,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAAC8F,iCAAiC,EAAE;MACxC,MAAMa,gBAAgB,GAAG,MAAM;QAC3B/F,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,IAAI,CAACnD,IAAI,CAAC,IAAI,CAACgC,eAAe,CAAC1D,iBAAiB,CAAC;MACrD,CAAC;MACDqK,aAAa,CAACV,IAAI,CAAC,CAAC,UAAU,EAAEgB,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAEA,gBAAgB,CAAC,CAAC;IACzF;IACA,IAAI,CAACP,aAAa,CAACC,aAAa,CAAC;IACjC,IAAI,CAACpG,iBAAiB,CAAC0F,IAAI,CAAC,GAAGU,aAAa,CAAC;EACjD;EACAD,aAAa,CAACQ,SAAS,EAAE;IACrBA,SAAS,CAAC7F,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MACrC,IAAI,CAAC9C,WAAW,CAACC,aAAa,CAACyI,gBAAgB,CAAC7F,KAAK,EAAEC,QAAQ,EAAE3E,sBAAsB,CAAC;IAC5F,CAAC,CAAC;EACN;EACAoJ,4BAA4B,GAAG;IAC3B,OAAO,CAAC,IAAI,CAACpG,SAAS,CAACwH,GAAG,IAAI,CAAC,IAAI,CAACxH,SAAS,CAACyH,OAAO;EACzD;EACA;EACAL,cAAc,CAAC1F,KAAK,EAAE;IAClB,IAAI,IAAI,CAACzC,iBAAiB,EAAE,EAAE;MAC1B,MAAMyI,mBAAmB,GAAG,IAAI,CAACrH,SAAS,CAACsH,gBAAgB,CAACjG,KAAK,CAACiF,OAAO,EAAEjF,KAAK,CAACkF,OAAO,CAAC;MACzF,MAAMgB,OAAO,GAAG,IAAI,CAAC/I,WAAW,CAACC,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI4I,mBAAmB,KAAKE,OAAO,IAAI,CAACA,OAAO,CAACT,QAAQ,CAACO,mBAAmB,CAAC,EAAE;QAC3E,IAAI,CAACtJ,IAAI,EAAE;MACf;IACJ;EACJ;EACA;EACAoI,iCAAiC,GAAG;IAChC,MAAMqB,QAAQ,GAAG,IAAI,CAACnH,aAAa;IACnC,IAAImH,QAAQ,KAAK,KAAK,EAAE;MACpB,MAAMD,OAAO,GAAG,IAAI,CAAC/I,WAAW,CAACC,aAAa;MAC9C,MAAM1D,KAAK,GAAGwM,OAAO,CAACxM,KAAK;MAC3B;MACA;MACA,IAAIyM,QAAQ,KAAK,IAAI,IAAKD,OAAO,CAACE,QAAQ,KAAK,OAAO,IAAIF,OAAO,CAACE,QAAQ,KAAK,UAAW,EAAE;QACxF1M,KAAK,CAAC2M,UAAU,GACZ3M,KAAK,CAAC4M,YAAY,GACd5M,KAAK,CAAC6M,gBAAgB,GAClB7M,KAAK,CAAC8M,aAAa,GACf,MAAM;MAC1B;MACA;MACA;MACA,IAAIL,QAAQ,KAAK,IAAI,IAAI,CAACD,OAAO,CAACO,SAAS,EAAE;QACzC/M,KAAK,CAACgN,cAAc,GAAG,MAAM;MACjC;MACAhN,KAAK,CAACiN,WAAW,GAAG,MAAM;MAC1BjN,KAAK,CAACkN,uBAAuB,GAAG,aAAa;IACjD;EACJ;AACJ;AACA/K,eAAe,CAACgL,IAAI;EAAmF/O,EAAE;AAAA,CAAiF;AAC1L+D,eAAe,CAACiL,IAAI,kBADmFhP,EAAE;EAAA,MACJ+D,eAAe;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA,EAAqb;AACziB;EAAA,mDAFuG/D,EAAE,mBAET+D,eAAe,EAAc,CAAC;IAClHkL,IAAI,EAAE9O;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE8O,IAAI,EAAE5N,EAAE,CAACC;IAAQ,CAAC,EAAE;MAAE2N,IAAI,EAAEjP,EAAE,CAACE;IAAW,CAAC,EAAE;MAAE+O,IAAI,EAAE5N,EAAE,CAAC6N;IAAiB,CAAC,EAAE;MAAED,IAAI,EAAEjP,EAAE,CAACmP;IAAiB,CAAC,EAAE;MAAEF,IAAI,EAAEjP,EAAE,CAACoP;IAAO,CAAC,EAAE;MAAEH,IAAI,EAAElO,EAAE,CAACsO;IAAS,CAAC,EAAE;MAAEJ,IAAI,EAAE/N,EAAE,CAACoO;IAAc,CAAC,EAAE;MAAEL,IAAI,EAAE/N,EAAE,CAACqO;IAAa,CAAC,EAAE;MAAEN,IAAI,EAAE5F;IAAU,CAAC,EAAE;MAAE4F,IAAI,EAAE7N,EAAE,CAACoO;IAAe,CAAC,EAAE;MAAEP,IAAI,EAAE5F;IAAU,CAAC,EAAE;MAAE4F,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QACrVR,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAAC7O,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsB,QAAQ,EAAE,CAAC;MACvC8M,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEnL,gBAAgB,EAAE,CAAC;MACnB0K,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEhL,QAAQ,EAAE,CAAC;MACXuK,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE1M,SAAS,EAAE,CAAC;MACZiM,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEzM,SAAS,EAAE,CAAC;MACZgM,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAExI,aAAa,EAAE,CAAC;MAChB+H,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEzK,OAAO,EAAE,CAAC;MACVgK,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzJ,YAAY,EAAE,CAAC;MACfgJ,IAAI,EAAE5O,KAAK;MACXqP,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAAS5L,eAAe,CAAC;EACrCqC,WAAW,CAAC7D,OAAO,EAAEqN,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,YAAY,EAAExJ,cAAc,EAAEyJ,GAAG,EAAEC,cAAc,EAAEvJ,SAAS,EAAE;IAChK,KAAK,CAACtE,OAAO,EAAEqN,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,YAAY,EAAExJ,cAAc,EAAEyJ,GAAG,EAAEC,cAAc,EAAEvJ,SAAS,CAAC;IAC7J,IAAI,CAACiC,iBAAiB,GAAGuH,gBAAgB;IACzC,IAAI,CAACpJ,eAAe,GAAG,SAAS;IAChC,IAAI,CAACD,eAAe,GAAGrD,8BAA8B;EACzD;EACA6H,UAAU,CAACrJ,QAAQ,EAAE;IACjB,MAAMmO,MAAM,GAAG1M,oBAAoB;IACnC,MAAM+H,KAAK,GAAG,CAAC,IAAI,CAAChF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,IAAI9B,QAAQ,CAAC2J,OAAO,KAAK,KAAK,EAAE;MAC5B3J,QAAQ,CAACoO,OAAO,GAAG,CAACD,MAAM;IAC9B,CAAC,MACI,IAAInO,QAAQ,CAAC2J,OAAO,KAAK,QAAQ,EAAE;MACpC3J,QAAQ,CAACoO,OAAO,GAAGD,MAAM;IAC7B,CAAC,MACI,IAAInO,QAAQ,CAAC0J,OAAO,KAAK,OAAO,EAAE;MACnC1J,QAAQ,CAACqO,OAAO,GAAG7E,KAAK,GAAG,CAAC2E,MAAM,GAAGA,MAAM;IAC/C,CAAC,MACI,IAAInO,QAAQ,CAAC0J,OAAO,KAAK,KAAK,EAAE;MACjC1J,QAAQ,CAACqO,OAAO,GAAG7E,KAAK,GAAG2E,MAAM,GAAG,CAACA,MAAM;IAC/C;IACA,OAAOnO,QAAQ;EACnB;AACJ;AACAwN,UAAU,CAACZ,IAAI;EAAA,iBAA6FY,UAAU,EA/Df3P,EAAE,mBA+D+BqB,EAAE,CAACC,OAAO,GA/D3CtB,EAAE,mBA+DsDA,EAAE,CAACE,UAAU,GA/DrEF,EAAE,mBA+DgFqB,EAAE,CAAC6N,gBAAgB,GA/DrGlP,EAAE,mBA+DgHA,EAAE,CAACmP,gBAAgB,GA/DrInP,EAAE,mBA+DgJA,EAAE,CAACoP,MAAM,GA/D3JpP,EAAE,mBA+DsKe,EAAE,CAACsO,QAAQ,GA/DnLrP,EAAE,mBA+D8LkB,EAAE,CAACoO,aAAa,GA/DhNtP,EAAE,mBA+D2NkB,EAAE,CAACqO,YAAY,GA/D5OvP,EAAE,mBA+DuPqC,2BAA2B,GA/DpRrC,EAAE,mBA+D+RoB,EAAE,CAACoO,cAAc,MA/DlTxP,EAAE,mBA+D6UmD,2BAA2B,MA/D1WnD,EAAE,mBA+DqYa,QAAQ;AAAA,CAA4C;AACliB8O,UAAU,CAACX,IAAI,kBAhEwFhP,EAAE;EAAA,MAgET2P,UAAU;EAAA;EAAA;EAAA;EAAA,WAhEH3P,EAAE;AAAA,EAgEkJ;AAC3P;EAAA,mDAjEuGA,EAAE,mBAiET2P,UAAU,EAAc,CAAC;IAC7GV,IAAI,EAAE9O,SAAS;IACfuP,IAAI,EAAE,CAAC;MACCe,QAAQ,EAAE,cAAc;MACxBC,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1B,IAAI,EAAE5N,EAAE,CAACC;IAAQ,CAAC,EAAE;MAAE2N,IAAI,EAAEjP,EAAE,CAACE;IAAW,CAAC,EAAE;MAAE+O,IAAI,EAAE5N,EAAE,CAAC6N;IAAiB,CAAC,EAAE;MAAED,IAAI,EAAEjP,EAAE,CAACmP;IAAiB,CAAC,EAAE;MAAEF,IAAI,EAAEjP,EAAE,CAACoP;IAAO,CAAC,EAAE;MAAEH,IAAI,EAAElO,EAAE,CAACsO;IAAS,CAAC,EAAE;MAAEJ,IAAI,EAAE/N,EAAE,CAACoO;IAAc,CAAC,EAAE;MAAEL,IAAI,EAAE/N,EAAE,CAACqO;IAAa,CAAC,EAAE;MAAEN,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QAC9QR,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAACrN,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAE4M,IAAI,EAAE7N,EAAE,CAACoO,cAAc;MAAEC,UAAU,EAAE,CAAC;QAC1CR,IAAI,EAAE3O;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2O,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAE3O;MACV,CAAC,EAAE;QACC2O,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAACvM,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAAC7O,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAM+P,qBAAqB,CAAC;EACxBxK,WAAW,CAACyK,kBAAkB,EAAEC,aAAa,EAAE;IAC3C,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACE,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,OAAO,GAAG,IAAIxP,OAAO,EAAE;IAC5B,IAAI,CAACyP,mBAAmB,GAAGJ,aAAa,KAAK,gBAAgB;EACjE;EACA;AACJ;AACA;AACA;EACIzM,IAAI,CAACoE,KAAK,EAAE;IACR;IACAX,YAAY,CAAC,IAAI,CAACqJ,cAAc,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG/D,UAAU,CAAC,MAAM;MACnC,IAAI,CAACgE,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACD,cAAc,GAAG/H,SAAS;IACnC,CAAC,EAAEZ,KAAK,CAAC;EACb;EACA;AACJ;AACA;AACA;EACI7D,IAAI,CAAC6D,KAAK,EAAE;IACR;IACAX,YAAY,CAAC,IAAI,CAACsJ,cAAc,CAAC;IACjC,IAAI,CAACD,cAAc,GAAG9D,UAAU,CAAC,MAAM;MACnC,IAAI,CAACgE,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACF,cAAc,GAAG9H,SAAS;IACnC,CAAC,EAAEZ,KAAK,CAAC;EACb;EACA;EACAS,WAAW,GAAG;IACV,OAAO,IAAI,CAAC+H,OAAO;EACvB;EACA;EACA9H,SAAS,GAAG;IACR,OAAO,IAAI,CAAC6H,UAAU;EAC1B;EACAnJ,WAAW,GAAG;IACV,IAAI,CAACa,wBAAwB,EAAE;IAC/B,IAAI,CAACuI,OAAO,CAAC1I,QAAQ,EAAE;IACvB,IAAI,CAACU,eAAe,GAAG,IAAI;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI2B,sBAAsB,GAAG;IACrB,IAAI,IAAI,CAACmG,mBAAmB,EAAE;MAC1B,IAAI,CAACnM,IAAI,CAAC,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0H,aAAa,GAAG;IACZ,IAAI,CAACuE,kBAAkB,CAACS,YAAY,EAAE;EAC1C;EACAC,iBAAiB,CAAC;IAAE9D;EAAc,CAAC,EAAE;IACjC,IAAI,CAACA,aAAa,IAAI,CAAC,IAAI,CAACxE,eAAe,CAAC0E,QAAQ,CAACF,aAAa,CAAC,EAAE;MACjE,IAAI,IAAI,CAACtE,SAAS,EAAE,EAAE;QAClB,IAAI,CAACvE,IAAI,CAAC,IAAI,CAACI,oBAAoB,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACwM,kBAAkB,CAAC,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAO,GAAG,CAAE;EACZ;EACAC,mBAAmB,CAAC;IAAEC;EAAc,CAAC,EAAE;IACnC,IAAIA,aAAa,KAAK,IAAI,CAACC,cAAc,IAAID,aAAa,KAAK,IAAI,CAACE,cAAc,EAAE;MAChF,IAAI,CAACL,kBAAkB,CAACG,aAAa,KAAK,IAAI,CAACC,cAAc,CAAC;IAClE;EACJ;EACA;EACAlJ,wBAAwB,GAAG;IACvBZ,YAAY,CAAC,IAAI,CAACsJ,cAAc,CAAC;IACjCtJ,YAAY,CAAC,IAAI,CAACqJ,cAAc,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,cAAc,GAAG9H,SAAS;EACzD;EACA;EACAmI,kBAAkB,CAACM,SAAS,EAAE;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAACf,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,CAAC,IAAI,CAAC5H,SAAS,EAAE,EAAE;MACxB,IAAI,CAAC8H,OAAO,CAAC3I,IAAI,EAAE;IACvB;EACJ;EACA;EACA+I,iBAAiB,CAAClI,SAAS,EAAE;IACzB;IACA;IACA;IACA,MAAM4I,OAAO,GAAG,IAAI,CAACC,QAAQ,CAAC1M,aAAa;IAC3C,MAAM2M,SAAS,GAAG,IAAI,CAACL,cAAc;IACrC,MAAMM,SAAS,GAAG,IAAI,CAACL,cAAc;IACrCE,OAAO,CAACI,SAAS,CAACC,MAAM,CAACjJ,SAAS,GAAG+I,SAAS,GAAGD,SAAS,CAAC;IAC3DF,OAAO,CAACI,SAAS,CAACE,GAAG,CAAClJ,SAAS,GAAG8I,SAAS,GAAGC,SAAS,CAAC;IACxD,IAAI,CAAClB,UAAU,GAAG7H,SAAS;IAC3B;IACA;IACA,IAAIA,SAAS,IAAI,CAAC,IAAI,CAAC+H,mBAAmB,IAAI,OAAOoB,gBAAgB,KAAK,UAAU,EAAE;MAClF,MAAMC,MAAM,GAAGD,gBAAgB,CAACP,OAAO,CAAC;MACxC;MACA,IAAIQ,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IACtDD,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EAAE;QACtD,IAAI,CAACtB,mBAAmB,GAAG,IAAI;MACnC;IACJ;IACA,IAAI/H,SAAS,EAAE;MACX,IAAI,CAACsI,OAAO,EAAE;IAClB;IACA,IAAI,IAAI,CAACP,mBAAmB,EAAE;MAC1Ba,OAAO,CAACI,SAAS,CAACE,GAAG,CAAC,yBAAyB,CAAC;MAChD,IAAI,CAACb,kBAAkB,CAACrI,SAAS,CAAC;IACtC;EACJ;AACJ;AACAyH,qBAAqB,CAAC7B,IAAI;EAAA,iBAA6F6B,qBAAqB,EA7NrC5Q,EAAE,mBA6NqDA,EAAE,CAACyS,iBAAiB,GA7N3EzS,EAAE,mBA6NsFiB,qBAAqB;AAAA,CAA4D;AAChR2P,qBAAqB,CAAC5B,IAAI,kBA9N6EhP,EAAE;EAAA,MA8NE4Q;AAAqB,EAAiB;AACjJ;EAAA,mDA/NuG5Q,EAAE,mBA+NT4Q,qBAAqB,EAAc,CAAC;IACxH3B,IAAI,EAAE9O;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE8O,IAAI,EAAEjP,EAAE,CAACyS;IAAkB,CAAC,EAAE;MAAExD,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QAC9FR,IAAI,EAAE3O;MACV,CAAC,EAAE;QACC2O,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAACzO,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,MAAMoP,gBAAgB,SAASO,qBAAqB,CAAC;EACjDxK,WAAW,CAACsM,iBAAiB,EAAErN,WAAW,EAAEyL,aAAa,EAAE;IACvD,KAAK,CAAC4B,iBAAiB,EAAE5B,aAAa,CAAC;IACvC,IAAI,CAACzL,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACsN,YAAY,GAAG,KAAK;IACzB,IAAI,CAACf,cAAc,GAAG,sBAAsB;IAC5C,IAAI,CAACC,cAAc,GAAG,sBAAsB;EAChD;EACAJ,OAAO,GAAG;IACN,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC9C,IAAI,CAACtG,aAAa,EAAE;EACxB;EACA;EACAsG,mBAAmB,GAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACxN,WAAW,CAACC,aAAa,CAACwN,qBAAqB,EAAE;IACnE,OAAOD,IAAI,CAACE,MAAM,GAAGlP,UAAU,IAAIgP,IAAI,CAACG,KAAK,IAAIlP,SAAS;EAC9D;AACJ;AACAuM,gBAAgB,CAACtB,IAAI;EAAA,iBAA6FsB,gBAAgB,EA9P3BrQ,EAAE,mBA8P2CA,EAAE,CAACyS,iBAAiB,GA9PjEzS,EAAE,mBA8P4EA,EAAE,CAACE,UAAU,GA9P3FF,EAAE,mBA8PsGiB,qBAAqB;AAAA,CAA4D;AAChSoP,gBAAgB,CAAC4C,IAAI,kBA/PkFjT,EAAE;EAAA,MA+PHqQ,gBAAgB;EAAA;EAAA;IAAA;MA/PfrQ,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA,2BA+PuF,MAAM;EAAA;EAAA;IAAA;MA/P/FA,EAAE;QAAA,OA+PH,6BAAyB;MAAA;IAAA;IAAA;MA/PxBA,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,+BA+PwkB;MA/P1kBA,EAAE;QAAA,OA+Puf,+BAA2B;MAAA,EAAE;MA/PthBA,EAAE,4BA+P+oB;MA/PjpBA,EAAE,UA+P0pB;MA/P5pBA,EAAE,eA+PgqB;IAAA;IAAA;MA/PlqBA,EAAE,wDA+PukB;MA/PzkBA,EAAE,wCA+Pke;MA/PpeA,EAAE,aA+P0pB;MA/P5pBA,EAAE,+BA+P0pB;IAAA;EAAA;EAAA,eAAgrIY,EAAE,CAACsS,OAAO;EAAA;EAAA;EAAA;AAAA,EAAyJ;AACtlK;EAAA,mDAhQuGlT,EAAE,mBAgQTqQ,gBAAgB,EAAc,CAAC;IACnHpB,IAAI,EAAE1O,SAAS;IACfmP,IAAI,EAAE,CAAC;MAAEe,QAAQ,EAAE,uBAAuB;MAAE0C,aAAa,EAAE3S,iBAAiB,CAAC4S,IAAI;MAAEC,eAAe,EAAE5S,uBAAuB,CAAC6S,MAAM;MAAE3C,IAAI,EAAE;QAC9H;QACA;QACA,cAAc,EAAE,wBAAwB;QACxC,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE;MACnB,CAAC;MAAE4C,QAAQ,EAAE,oTAAoT;MAAEhB,MAAM,EAAE,CAAC,umIAAumI;IAAE,CAAC;EACl8I,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtD,IAAI,EAAEjP,EAAE,CAACyS;IAAkB,CAAC,EAAE;MAAExD,IAAI,EAAEjP,EAAE,CAACE;IAAW,CAAC,EAAE;MAAE+O,IAAI,EAAE5F,SAAS;MAAEoG,UAAU,EAAE,CAAC;QACvHR,IAAI,EAAE3O;MACV,CAAC,EAAE;QACC2O,IAAI,EAAE7O,MAAM;QACZsP,IAAI,EAAE,CAACzO,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+Q,QAAQ,EAAE,CAAC;MACvC/C,IAAI,EAAEvO,SAAS;MACfgP,IAAI,EAAE,CAAC,SAAS,EAAE;QACV;QACA;QACA8D,MAAM,EAAE;MACZ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB;EACAC,YAAY,EAAEhS,OAAO,CAAC,OAAO,EAAE;EAC3B;EACA;EACAC,KAAK,CAAC,uBAAuB,EAAEC,KAAK,CAAC;IAAE+R,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CAAC,CAAC,EAC9EjS,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IAAEgS,SAAS,EAAE;EAAW,CAAC,CAAC,CAAC,EAClD/R,UAAU,CAAC,cAAc,EAAEC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,UAAU,CAAC,aAAa,EAAEC,OAAO,CAAC,iCAAiC,CAAC,CAAC,CACxE;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+R,gBAAgB,CAAC;AAEvBA,gBAAgB,CAAC9E,IAAI;EAAA,iBAA6F8E,gBAAgB;AAAA,CAAkD;AACpLA,gBAAgB,CAACC,IAAI,kBAxTkF9T,EAAE;EAAA,MAwTU6T;AAAgB,EAAqM;AACxUA,gBAAgB,CAACE,IAAI,kBAzTkF/T,EAAE;EAAA,WAyTuC,CAAC2C,4CAA4C,CAAC;EAAA,UAAYxB,UAAU,EAAEL,YAAY,EAAES,aAAa,EAAES,eAAe,EAAEA,eAAe,EAAED,mBAAmB;AAAA,EAAI;AAC5S;EAAA,mDA1TuG/B,EAAE,mBA0TT6T,gBAAgB,EAAc,CAAC;IACnH5E,IAAI,EAAEtO,QAAQ;IACd+O,IAAI,EAAE,CAAC;MACCsE,OAAO,EAAE,CAAC7S,UAAU,EAAEL,YAAY,EAAES,aAAa,EAAES,eAAe,CAAC;MACnEiS,OAAO,EAAE,CAACtE,UAAU,EAAEU,gBAAgB,EAAErO,eAAe,EAAED,mBAAmB,CAAC;MAC7EmS,YAAY,EAAE,CAACvE,UAAU,EAAEU,gBAAgB,CAAC;MAC5C8D,SAAS,EAAE,CAACxR,4CAA4C;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASQ,2BAA2B,EAAEJ,mCAAmC,EAAEV,2BAA2B,EAAEC,mCAAmC,EAAEK,4CAA4C,EAAEgN,UAAU,EAAEkE,gBAAgB,EAAE5R,kBAAkB,EAAEqB,mBAAmB,EAAE+M,gBAAgB,EAAEtM,eAAe,EAAE6M,qBAAqB,EAAE1O,iCAAiC,EAAEuR,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}