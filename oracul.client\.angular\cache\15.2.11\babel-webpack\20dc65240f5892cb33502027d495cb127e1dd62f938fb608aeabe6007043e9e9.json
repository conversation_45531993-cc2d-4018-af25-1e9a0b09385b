{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TokenService {\n  constructor() {\n    this.TOKEN_KEY = 'access_token';\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    this.USER_KEY = 'user_info';\n    this.REMEMBER_ME_KEY = 'remember_me';\n  }\n  // Token management\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);\n  }\n  setToken(token, rememberMe = false) {\n    if (rememberMe) {\n      localStorage.setItem(this.TOKEN_KEY, token);\n      sessionStorage.removeItem(this.TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.TOKEN_KEY, token);\n      localStorage.removeItem(this.TOKEN_KEY);\n    }\n  }\n  getRefreshToken() {\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\n  }\n  setRefreshToken(token, rememberMe = false) {\n    if (rememberMe) {\n      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    }\n  }\n  setUser(user, rememberMe = false) {\n    const userStr = JSON.stringify(user);\n    if (rememberMe) {\n      localStorage.setItem(this.USER_KEY, userStr);\n      sessionStorage.removeItem(this.USER_KEY);\n    } else {\n      sessionStorage.setItem(this.USER_KEY, userStr);\n      localStorage.removeItem(this.USER_KEY);\n    }\n  }\n  getStoredUser() {\n    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  setRememberMe(rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');\n    } else {\n      localStorage.removeItem(this.REMEMBER_ME_KEY);\n    }\n  }\n  getRememberMe() {\n    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';\n  }\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp < currentTime;\n    } catch (error) {\n      return true; // If we can't parse the token, consider it expired\n    }\n  }\n\n  clearAllTokens() {\n    // Clear from both localStorage and sessionStorage\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\n    sessionStorage.removeItem(this.TOKEN_KEY);\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    sessionStorage.removeItem(this.USER_KEY);\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    return !!token && !this.isTokenExpired(token);\n  }\n  static {\n    this.ɵfac = function TokenService_Factory(t) {\n      return new (t || TokenService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TokenService,\n      factory: TokenService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,YAAY;EAHzBC;IAImB,cAAS,GAAG,cAAc;IAC1B,sBAAiB,GAAG,eAAe;IACnC,aAAQ,GAAG,WAAW;IACtB,oBAAe,GAAG,aAAa;;EAEhD;EACAC,QAAQ;IACN,OAAOC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAIC,cAAc,CAACF,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC;EACvF;EAEAE,QAAQ,CAACC,KAAa,EAAEC,aAAsB,KAAK;IACjD,IAAIA,UAAU,EAAE;MACdN,YAAY,CAACO,OAAO,CAAC,IAAI,CAACL,SAAS,EAAEG,KAAK,CAAC;MAC3CF,cAAc,CAACK,UAAU,CAAC,IAAI,CAACN,SAAS,CAAC;KAC1C,MAAM;MACLC,cAAc,CAACI,OAAO,CAAC,IAAI,CAACL,SAAS,EAAEG,KAAK,CAAC;MAC7CL,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACN,SAAS,CAAC;;EAE3C;EAEAO,eAAe;IACb,OAAOT,YAAY,CAACC,OAAO,CAAC,IAAI,CAACS,iBAAiB,CAAC,IAAIP,cAAc,CAACF,OAAO,CAAC,IAAI,CAACS,iBAAiB,CAAC;EACvG;EAEAC,eAAe,CAACN,KAAa,EAAEC,aAAsB,KAAK;IACxD,IAAIA,UAAU,EAAE;MACdN,YAAY,CAACO,OAAO,CAAC,IAAI,CAACG,iBAAiB,EAAEL,KAAK,CAAC;MACnDF,cAAc,CAACK,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;KAClD,MAAM;MACLP,cAAc,CAACI,OAAO,CAAC,IAAI,CAACG,iBAAiB,EAAEL,KAAK,CAAC;MACrDL,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;;EAEnD;EAEAE,OAAO,CAACC,IAAS,EAAEP,aAAsB,KAAK;IAC5C,MAAMQ,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IACpC,IAAIP,UAAU,EAAE;MACdN,YAAY,CAACO,OAAO,CAAC,IAAI,CAACU,QAAQ,EAAEH,OAAO,CAAC;MAC5CX,cAAc,CAACK,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;KACzC,MAAM;MACLd,cAAc,CAACI,OAAO,CAAC,IAAI,CAACU,QAAQ,EAAEH,OAAO,CAAC;MAC9Cd,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;;EAE1C;EAEAC,aAAa;IACX,MAAMJ,OAAO,GAAGd,YAAY,CAACC,OAAO,CAAC,IAAI,CAACgB,QAAQ,CAAC,IAAId,cAAc,CAACF,OAAO,CAAC,IAAI,CAACgB,QAAQ,CAAC;IAC5F,OAAOH,OAAO,GAAGC,IAAI,CAACI,KAAK,CAACL,OAAO,CAAC,GAAG,IAAI;EAC7C;EAEAM,aAAa,CAACd,UAAmB;IAC/B,IAAIA,UAAU,EAAE;MACdN,YAAY,CAACO,OAAO,CAAC,IAAI,CAACc,eAAe,EAAE,MAAM,CAAC;KACnD,MAAM;MACLrB,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACa,eAAe,CAAC;;EAEjD;EAEAC,aAAa;IACX,OAAOtB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACoB,eAAe,CAAC,KAAK,MAAM;EAC9D;EAEAE,cAAc,CAAClB,KAAa;IAC1B,IAAI;MACF,MAAMmB,OAAO,GAAGT,IAAI,CAACI,KAAK,CAACM,IAAI,CAACpB,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MACjD,OAAOP,OAAO,CAACQ,GAAG,GAAGL,WAAW;KACjC,CAAC,OAAOM,KAAK,EAAE;MACd,OAAO,IAAI,CAAC,CAAC;;EAEjB;;EAEAC,cAAc;IACZ;IACAlC,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACN,SAAS,CAAC;IACvCF,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IAC/CV,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;IACtCjB,YAAY,CAACQ,UAAU,CAAC,IAAI,CAACa,eAAe,CAAC;IAE7ClB,cAAc,CAACK,UAAU,CAAC,IAAI,CAACN,SAAS,CAAC;IACzCC,cAAc,CAACK,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IACjDP,cAAc,CAACK,UAAU,CAAC,IAAI,CAACS,QAAQ,CAAC;EAC1C;EAEAkB,eAAe;IACb,MAAM9B,KAAK,GAAG,IAAI,CAACN,QAAQ,EAAE;IAC7B,OAAO,CAAC,CAACM,KAAK,IAAI,CAAC,IAAI,CAACkB,cAAc,CAAClB,KAAK,CAAC;EAC/C;;;uBAxFWR,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAuC,SAAZvC,YAAY;MAAAwC,YAFX;IAAM;EAAA", "names": ["TokenService", "constructor", "getToken", "localStorage", "getItem", "TOKEN_KEY", "sessionStorage", "setToken", "token", "rememberMe", "setItem", "removeItem", "getRefreshToken", "REFRESH_TOKEN_KEY", "setRefreshToken", "setUser", "user", "userStr", "JSON", "stringify", "USER_KEY", "getStoredUser", "parse", "setRememberMe", "REMEMBER_ME_KEY", "getRememberMe", "isTokenExpired", "payload", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "exp", "error", "clearAllTokens", "isAuthenticated", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\token.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TokenService {\n  private readonly TOKEN_KEY = 'access_token';\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\n  private readonly USER_KEY = 'user_info';\n  private readonly REMEMBER_ME_KEY = 'remember_me';\n\n  // Token management\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);\n  }\n\n  setToken(token: string, rememberMe: boolean = false): void {\n    if (rememberMe) {\n      localStorage.setItem(this.TOKEN_KEY, token);\n      sessionStorage.removeItem(this.TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.TOKEN_KEY, token);\n      localStorage.removeItem(this.TOKEN_KEY);\n    }\n  }\n\n  getRefreshToken(): string | null {\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);\n  }\n\n  setRefreshToken(token: string, rememberMe: boolean = false): void {\n    if (rememberMe) {\n      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    } else {\n      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);\n      localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    }\n  }\n\n  setUser(user: any, rememberMe: boolean = false): void {\n    const userStr = JSON.stringify(user);\n    if (rememberMe) {\n      localStorage.setItem(this.USER_KEY, userStr);\n      sessionStorage.removeItem(this.USER_KEY);\n    } else {\n      sessionStorage.setItem(this.USER_KEY, userStr);\n      localStorage.removeItem(this.USER_KEY);\n    }\n  }\n\n  getStoredUser(): any | null {\n    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  setRememberMe(rememberMe: boolean): void {\n    if (rememberMe) {\n      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');\n    } else {\n      localStorage.removeItem(this.REMEMBER_ME_KEY);\n    }\n  }\n\n  getRememberMe(): boolean {\n    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';\n  }\n\n  isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp < currentTime;\n    } catch (error) {\n      return true; // If we can't parse the token, consider it expired\n    }\n  }\n\n  clearAllTokens(): void {\n    // Clear from both localStorage and sessionStorage\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\n    \n    sessionStorage.removeItem(this.TOKEN_KEY);\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    sessionStorage.removeItem(this.USER_KEY);\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    return !!token && !this.isTokenExpired(token);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}