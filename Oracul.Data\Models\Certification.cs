using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Certification entity for astrology and spiritual certifications
    /// </summary>
    public class Certification : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string IssuingOrganization { get; set; } = string.Empty;

        [Required]
        public DateTime IssueDate { get; set; }

        public DateTime? ExpirationDate { get; set; }

        [MaxLength(100)]
        public string? CredentialId { get; set; }

        [MaxLength(500)]
        public string? CredentialUrl { get; set; }

        [MaxLength(500)]
        public string? ImageUrl { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
    }
}
