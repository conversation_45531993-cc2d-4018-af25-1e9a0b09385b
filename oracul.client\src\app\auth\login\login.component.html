<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header class="login-header">
      <mat-card-title>
        <mat-icon class="login-icon">lock</mat-icon>
        {{ t.auth.loginTitle }}
      </mat-card-title>
      <mat-card-subtitle>
        Въведете данните си за достъп до акаунта
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <!-- Email Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ t.auth.email }}</mat-label>
          <input
            matInput
            type="email"
            formControlName="email"
            [placeholder]="'Въведете вашия имейл'"
            autocomplete="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
            {{ getEmailErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Password Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ t.auth.password }}</mat-label>
          <input
            matInput
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            [placeholder]="'Въведете вашата парола'"
            autocomplete="current-password">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="hidePassword = !hidePassword"
            [attr.aria-label]="'Скрий парола'"
            [attr.aria-pressed]="hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
            {{ getPasswordErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Remember Me Checkbox -->
        <div class="remember-me-container">
          <mat-checkbox formControlName="rememberMe" color="primary">
            {{ t.auth.rememberMe }}
          </mat-checkbox>
        </div>

        <!-- Login Button -->
        <button
          mat-raised-button
          color="primary"
          type="submit"
          class="full-width login-button"
          [disabled]="isLoading">
          <mat-icon *ngIf="isLoading">
            <mat-spinner diameter="20"></mat-spinner>
          </mat-icon>
          <mat-icon *ngIf="!isLoading">login</mat-icon>
          {{ isLoading ? 'Влизане...' : t.auth.signIn }}
        </button>

        <!-- Divider -->
        <div class="divider-container">
          <mat-divider></mat-divider>
          <span class="divider-text">{{ t.auth.orContinueWith }}</span>
          <mat-divider></mat-divider>
        </div>

        <!-- OAuth Buttons -->
        <div class="oauth-buttons">
          <!-- Google Sign-In Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button google-button"
            [disabled]="isLoading"
            (click)="signInWithGoogle()">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="oauth-icon">
            Продължи с Google
          </button>

          <!-- Facebook Sign-In Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button facebook-button"
            [disabled]="isLoading"
            (click)="signInWithFacebook()">
            <mat-icon class="oauth-icon facebook-icon">facebook</mat-icon>
            Продължи с Facebook
          </button>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions class="login-actions">
      <div class="action-links">
        <button
          mat-button
          color="accent"
          type="button"
          (click)="navigateToForgotPassword()">
          {{ t.auth.forgotPassword }}
        </button>
      </div>

      <mat-divider></mat-divider>

      <div class="register-section">
        <p class="register-text">{{ t.auth.dontHaveAccount }}</p>
        <button
          mat-stroked-button
          color="primary"
          type="button"
          (click)="navigateToRegister()"
          class="full-width">
          <mat-icon>person_add</mat-icon>
          {{ t.auth.createAccount }}
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
