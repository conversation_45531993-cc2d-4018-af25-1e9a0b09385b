#!/usr/bin/env pwsh

Write-Host "🚀 Starting Oracul Development Environment..." -ForegroundColor Cyan
Write-Host ""

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Check if ports are available
Write-Host "🔍 Checking port availability..." -ForegroundColor Yellow

if (Test-Port 5144) {
    Write-Host "⚠️  Port 5144 is already in use. Backend may already be running." -ForegroundColor Yellow
} else {
    Write-Host "✅ Port 5144 is available for backend" -ForegroundColor Green
}

if (Test-Port 4200) {
    Write-Host "⚠️  Port 4200 is already in use. Frontend may already be running." -ForegroundColor Yellow
} else {
    Write-Host "✅ Port 4200 is available for frontend" -ForegroundColor Green
}

Write-Host ""

# Start Backend
Write-Host "🔧 Starting Backend (ASP.NET Core) on port 5144..." -ForegroundColor Green
$backendJob = Start-Job -ScriptBlock {
    Set-Location "Oracul.Server"
    dotnet run
}

Write-Host "⏳ Waiting for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# Start Frontend
Write-Host "🎨 Starting Frontend (Angular) on port 4200..." -ForegroundColor Blue
$frontendJob = Start-Job -ScriptBlock {
    Set-Location "oracul.client"
    npm start
}

Write-Host ""
Write-Host "🌟 Development servers are starting..." -ForegroundColor Cyan
Write-Host "📱 Frontend: http://localhost:4200" -ForegroundColor Blue
Write-Host "🔧 Backend:  http://localhost:5144" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Features available:" -ForegroundColor Magenta
Write-Host "   • Scroll-based navigation highlighting" -ForegroundColor White
Write-Host "   • Static ports (no CORS issues)" -ForegroundColor White
Write-Host "   • Live reload for development" -ForegroundColor White
Write-Host ""

# Wait for user input
Write-Host "Press any key to stop all services..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop services
Write-Host ""
Write-Host "🛑 Stopping development servers..." -ForegroundColor Red
Stop-Job $backendJob -Force
Stop-Job $frontendJob -Force
Remove-Job $backendJob
Remove-Job $frontendJob

Write-Host "✅ All services stopped." -ForegroundColor Green
