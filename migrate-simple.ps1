# Simple PowerShell script for Entity Framework migrations
param(
    [Parameter(Mandatory=$false)]
    [string]$Action = "update",
    
    [Parameter(Mandatory=$false)]
    [string]$MigrationName = ""
)

$DataProject = "Oracul.Data"
$StartupProject = "Oracul.Server"

Write-Host "🚀 Entity Framework Migration Helper" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "Data Project: $DataProject" -ForegroundColor Cyan
Write-Host "Startup Project: $StartupProject" -ForegroundColor Cyan
Write-Host ""

switch ($Action.ToLower()) {
    "add" {
        if ([string]::IsNullOrEmpty($MigrationName)) {
            Write-Host "❌ Error: Migration name is required for 'add' action" -ForegroundColor Red
            Write-Host "💡 Usage: .\migrate-simple.ps1 -Action add -MigrationName YourMigrationName" -ForegroundColor Yellow
            exit 1
        }
        Write-Host "📝 Creating new migration: $MigrationName" -ForegroundColor Yellow
        dotnet ef migrations add $MigrationName --project $DataProject --startup-project $StartupProject
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migration '$MigrationName' created successfully!" -ForegroundColor Green
            Write-Host "💡 Next step: Run .\migrate-simple.ps1 to apply the migration" -ForegroundColor Cyan
        }
    }
    "update" {
        Write-Host "🔄 Updating database with latest migrations..." -ForegroundColor Yellow
        dotnet ef database update --project $DataProject --startup-project $StartupProject
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database updated successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Database update failed. Check connection string and ensure SQL Server is running." -ForegroundColor Red
        }
    }
    "remove" {
        Write-Host "⚠️  WARNING: This will remove the last migration!" -ForegroundColor Red
        Write-Host "Only do this if the migration hasn't been applied to production." -ForegroundColor Yellow
        $confirmation = Read-Host "Continue? (yes/no)"
        if ($confirmation -eq "yes") {
            Write-Host "🗑️  Removing last migration..." -ForegroundColor Yellow
            dotnet ef migrations remove --project $DataProject --startup-project $StartupProject
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Last migration removed successfully!" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ Migration removal cancelled." -ForegroundColor Yellow
        }
    }
    "list" {
        Write-Host "📋 Listing all migrations..." -ForegroundColor Yellow
        dotnet ef migrations list --project $DataProject --startup-project $StartupProject
    }
    "script" {
        Write-Host "📄 Generating SQL script for all migrations..." -ForegroundColor Yellow
        $scriptFile = "migration-script-$(Get-Date -Format 'yyyyMMdd-HHmmss').sql"
        dotnet ef migrations script --project $DataProject --startup-project $StartupProject --output $scriptFile
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ SQL script generated: $scriptFile" -ForegroundColor Green
        }
    }
    "help" {
        Write-Host "📖 Available Actions:" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "  update      - Apply all pending migrations to database (default)" -ForegroundColor White
        Write-Host "  add <name>  - Create a new migration with specified name" -ForegroundColor White
        Write-Host "  remove      - Remove the last migration (with confirmation)" -ForegroundColor White
        Write-Host "  list        - List all migrations and their status" -ForegroundColor White
        Write-Host "  script      - Generate SQL script for all migrations" -ForegroundColor White
        Write-Host "  help        - Show this help message" -ForegroundColor White
        Write-Host ""
        Write-Host "📚 Examples:" -ForegroundColor Cyan
        Write-Host "  .\migrate-simple.ps1" -ForegroundColor Gray
        Write-Host "  .\migrate-simple.ps1 -Action add -MigrationName AddUserTable" -ForegroundColor Gray
        Write-Host "  .\migrate-simple.ps1 -Action list" -ForegroundColor Gray
        Write-Host ""
        Write-Host "📖 For detailed instructions, see: MIGRATION-README.md" -ForegroundColor Cyan
    }
    default {
        Write-Host "❌ Unknown action: $Action" -ForegroundColor Red
        Write-Host "💡 Run .\migrate-simple.ps1 -Action help for available actions" -ForegroundColor Yellow
    }
}
