.theme-selector {
  padding: 20px;
}

.theme-selector h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: var(--theme-primary);
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.theme-option {
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--theme-surface);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.theme-option.selected {
  border-color: var(--theme-primary);
  box-shadow: 0 4px 16px rgba(103, 58, 183, 0.3);
}

.theme-preview {
  display: flex;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.color-bar {
  flex: 1;
}

.color-bar.primary {
  flex: 2;
}

.theme-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.theme-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.selected-icon {
  color: var(--theme-primary);
  font-size: 20px;
}

.theme-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 20px;
}

.theme-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive design */
@media (max-width: 600px) {
  .theme-grid {
    grid-template-columns: 1fr;
  }
  
  .theme-actions {
    flex-direction: column;
  }
  
  .theme-actions button {
    width: 100%;
    justify-content: center;
  }
}
