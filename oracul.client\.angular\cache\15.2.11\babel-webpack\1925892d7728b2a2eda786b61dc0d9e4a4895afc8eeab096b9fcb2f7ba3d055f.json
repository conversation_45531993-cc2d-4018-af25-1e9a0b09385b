{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Configuration for opening a modal dialog. */\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass DialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    this.backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    this.closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    this.closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    this.closeOnOverlayDetachments = true;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n    super();\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._config = _config;\n    this._interactivityChecker = _interactivityChecker;\n    this._ngZone = _ngZone;\n    this._overlayRef = _overlayRef;\n    this._focusMonitor = _focusMonitor;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    this._elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    this._closeInteractionType = null;\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    };\n    this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n    this._document = _document;\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus() {\n    const element = this._elementRef.nativeElement;\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n    // dialog. If the element inside the dialog can't be focused, then the container is focused\n    // so the user can't tab into other elements behind it.\n    switch (this._config.autoFocus) {\n      case false:\n      case 'dialog':\n        // Ensure that focus is on the dialog container. It's possible that a different\n        // component tried to move focus while the open animation was running. See:\n        // https://github.com/angular/components/issues/16215. Note that we only want to do this\n        // if the focus isn't inside the dialog already, because it's possible that the consumer\n        // turned off `autoFocus` in order to move focus themselves.\n        if (!this._containsFocus()) {\n          element.focus();\n        }\n        break;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this._config.autoFocus);\n        break;\n    }\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n    // Save the previously focused element. This element will be re-focused\n    // when the dialog closes.\n    if (this._document) {\n      this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n}\nCdkDialogContainer.ɵfac = function CdkDialogContainer_Factory(t) {\n  return new (t || CdkDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(DialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n};\nCdkDialogContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CdkDialogContainer,\n  selectors: [[\"cdk-dialog-container\"]],\n  viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n    }\n  },\n  hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n  hostVars: 6,\n  hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledBy)(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkPortalOutlet\", \"\"]],\n  template: function CdkDialogContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n    }\n  },\n  dependencies: [i3.CdkPortalOutlet],\n  styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet></ng-template>\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DialogConfig]\n      }]\n    }, {\n      type: i1.InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.OverlayRef\n    }, {\n      type: i1.FocusMonitor\n    }];\n  }, {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    /** Emits when the dialog has been closed. */\n    this.closed = new Subject();\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n    this._overlay = _overlay;\n    this._injector = _injector;\n    this._defaultOptions = _defaultOptions;\n    this._parentDialog = _parentDialog;\n    this._overlayContainer = _overlayContainer;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this._ariaHiddenElements = new Map();\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._scrollStrategy = scrollStrategy;\n  }\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || `cdk-dialog-${uniqueId++}`;\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }), config.componentFactoryResolver);\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n}\nDialog.ɵfac = function Dialog_Factory(t) {\n  return new (t || Dialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DEFAULT_DIALOG_CONFIG, 8), i0.ɵɵinject(Dialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(DIALOG_SCROLL_STRATEGY));\n};\nDialog.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Dialog,\n  factory: Dialog.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: DialogConfig,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DEFAULT_DIALOG_CONFIG]\n      }]\n    }, {\n      type: Dialog,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i1$1.OverlayContainer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DIALOG_SCROLL_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DialogModule {}\nDialogModule.ɵfac = function DialogModule_Factory(t) {\n  return new (t || DialogModule)();\n};\nDialogModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DialogModule\n});\nDialogModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n  imports: [OverlayModule, PortalModule, A11yModule,\n  // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n  // don't have to remember to import it or be faced with an unhelpful error.\n  PortalModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      declarations: [CdkDialogContainer],\n      providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };", "map": {"version": 3, "names": ["i1", "A11yModule", "i1$1", "Overlay", "OverlayConfig", "OverlayRef", "OverlayModule", "_getFocusedElementPierceShadowDom", "i3", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "DOCUMENT", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "InjectionToken", "Injector", "TemplateRef", "Injectable", "SkipSelf", "NgModule", "ESCAPE", "hasModifierKey", "Subject", "defer", "of", "Directionality", "startWith", "DialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_document", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_overlayRef", "_focusMonitor", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "attachDomPortal", "portal", "_portalOutlet", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "_contentAttached", "_ariaLabelledBy", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "attachTemplatePortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "focusedSuccessfully", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "create", "backdropClick", "subscribe", "ɵfac", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "FocusMonitor", "ɵcmp", "type", "args", "encapsulation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "template", "styles", "undefined", "decorators", "static", "DialogRef", "overlayRef", "config", "closed", "keydownEvents", "outsidePointerEvents", "id", "event", "keyCode", "preventDefault", "close", "<PERSON><PERSON><PERSON><PERSON>", "_detachSubscription", "detachments", "containerInstance", "closedSubject", "unsubscribe", "dispose", "next", "complete", "componentInstance", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "block", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "uniqueId", "Dialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "afterOpened", "_afterOpenedAtThisLevel", "_overlay", "_injector", "_defaultOptions", "_overlayContainer", "scrollStrategy", "_afterAllClosedAtThisLevel", "_ariaHiddenElements", "Map", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "_scrollStrategy", "open", "componentOrTemplateRef", "defaults", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "push", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "positionStrategy", "position", "global", "centerHorizontally", "centerVertically", "direction", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "disposeOnNavigation", "userInjector", "injector", "viewContainerRef", "providers", "useValue", "containerType", "container", "containerPortal", "parent", "componentFactoryResolver", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "templateContext", "contentRef", "fallbackInjector", "get", "optional", "value", "change", "emitEvent", "index", "indexOf", "splice", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "OverlayContainer", "ɵprov", "items", "DialogModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/cdk/fesm2020/dialog.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Optional CSS class or classes applied to the overlay panel. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Optional CSS class or classes applied to the overlay backdrop. */\n        this.backdropClass = '';\n        /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Dialog label applied via `aria-label` */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the previously-focused element upon closing.\n         * Has the following behavior based on the type that is passed in:\n         * - `boolean` - when true, will return focus to the element that was focused before the dialog\n         *    was opened, otherwise won't restore focus at all.\n         * - `string` - focus will be restored to the first element that matches the CSS selector.\n         * - `HTMLElement` - focus will be restored to the specific element.\n         */\n        this.restoreFocus = true;\n        /**\n         * Whether the dialog should close when the user navigates backwards or forwards through browser\n         * history. This does not apply to navigation via anchor element unless using URL-hash based\n         * routing (`HashLocationStrategy` in the Angular router).\n         */\n        this.closeOnNavigation = true;\n        /**\n         * Whether the dialog should close when the dialog service is destroyed. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead.\n         */\n        this.closeOnDestroy = true;\n        /**\n         * Whether the dialog should close when the underlying overlay is detached. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n         * external detachment can happen as a result of a scroll strategy triggering it or when the\n         * browser location changes.\n         */\n        this.closeOnOverlayDetachments = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n        super();\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._config = _config;\n        this._interactivityChecker = _interactivityChecker;\n        this._ngZone = _ngZone;\n        this._overlayRef = _overlayRef;\n        this._focusMonitor = _focusMonitor;\n        /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n        this._elementFocusedBeforeDialogWasOpened = null;\n        /**\n         * Type of interaction that led to the dialog being closed. This is used to determine\n         * whether the focus style will be applied when returning focus to its original location\n         * after the dialog is closed.\n         */\n        this._closeInteractionType = null;\n        /**\n         * Attaches a DOM portal to the dialog container.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwDialogContentAlreadyAttachedError();\n            }\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._contentAttached();\n            return result;\n        };\n        this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n        this._document = _document;\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus() {\n        const element = this._elementRef.nativeElement;\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n        // dialog. If the element inside the dialog can't be focused, then the container is focused\n        // so the user can't tab into other elements behind it.\n        switch (this._config.autoFocus) {\n            case false:\n            case 'dialog':\n                // Ensure that focus is on the dialog container. It's possible that a different\n                // component tried to move focus while the open animation was running. See:\n                // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                // if the focus isn't inside the dialog already, because it's possible that the consumer\n                // turned off `autoFocus` in order to move focus themselves.\n                if (!this._containsFocus()) {\n                    element.focus();\n                }\n                break;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this._config.autoFocus);\n                break;\n        }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer() {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        // Save the previously focused element. This element will be re-focused\n        // when the dialog closes.\n        if (this._document) {\n            this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n}\nCdkDialogContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: DialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component });\nCdkDialogContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDialogContainer, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledBy\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"], dependencies: [{ kind: \"directive\", type: i3.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DialogConfig]\n                }] }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: i1.FocusMonitor }]; }, propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        /** Emits when the dialog has been closed. */\n        this.closed = new Subject();\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n        this._overlay = _overlay;\n        this._injector = _injector;\n        this._defaultOptions = _defaultOptions;\n        this._parentDialog = _parentDialog;\n        this._overlayContainer = _overlayContainer;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this._ariaHiddenElements = new Map();\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._scrollStrategy = scrollStrategy;\n    }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || `cdk-dialog-${uniqueId++}`;\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }), config.componentFactoryResolver);\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n}\nDialog.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Dialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: DEFAULT_DIALOG_CONFIG, optional: true }, { token: Dialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: DIALOG_SCROLL_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });\nDialog.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Dialog });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: DialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DEFAULT_DIALOG_CONFIG]\n                }] }, { type: Dialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DIALOG_SCROLL_STRATEGY]\n                }] }]; } });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DialogModule {\n}\nDialogModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDialogModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DialogModule, declarations: [CdkDialogContainer], imports: [OverlayModule, PortalModule, A11yModule], exports: [\n        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n        // don't have to remember to import it or be faced with an unhelpful error.\n        PortalModule,\n        CdkDialogContainer] });\nDialogModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DialogModule, providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER], imports: [OverlayModule, PortalModule, A11yModule, \n        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n        // don't have to remember to import it or be faced with an unhelpful error.\n        PortalModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    declarations: [CdkDialogContainer],\n                    providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,aAAa,QAAQ,sBAAsB;AACxF,SAASC,iCAAiC,QAAQ,uBAAuB;AACzE,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACzL,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA,MAAMC,YAAY,CAAC;EACfC,WAAW,GAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,gBAAgB;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACzC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sCAAsC,GAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAAS/C,gBAAgB,CAAC;EAC9C2B,WAAW,CAACqB,WAAW,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,EAAE;IACxH,KAAK,EAAE;IACP,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC;IACA,IAAI,CAACC,oCAAoC,GAAG,IAAI;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,EAAE,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFjB,sCAAsC,EAAE;MAC5C;MACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACF,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACK,gBAAgB,EAAE;MACvB,OAAOD,MAAM;IACjB,CAAC;IACD,IAAI,CAACE,eAAe,GAAG,IAAI,CAACd,OAAO,CAACd,cAAc,IAAI,IAAI;IAC1D,IAAI,CAACa,SAAS,GAAGA,SAAS;EAC9B;EACAc,gBAAgB,GAAG;IACf,IAAI,CAACE,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoB,GAAG;IACnB,IAAI,CAACC,UAAU,EAAE;EACrB;EACAC,WAAW,GAAG;IACV,IAAI,CAACC,aAAa,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqB,CAACb,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,EAAE,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFjB,sCAAsC,EAAE;IAC5C;IACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACY,qBAAqB,CAACb,MAAM,CAAC;IAC/D,IAAI,CAACK,gBAAgB,EAAE;IACvB,OAAOD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIU,oBAAoB,CAACd,MAAM,EAAE;IACzB,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,EAAE,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFjB,sCAAsC,EAAE;IAC5C;IACA,MAAMkB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACa,oBAAoB,CAACd,MAAM,CAAC;IAC9D,IAAI,CAACK,gBAAgB,EAAE;IACvB,OAAOD,MAAM;EACjB;EACA;EACA;EACAW,eAAe,GAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE,EAAE;MACxB,IAAI,CAACN,UAAU,EAAE;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC1B,qBAAqB,CAAC2B,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAAC3B,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAG,MAAM;UACnBL,OAAO,CAACM,mBAAmB,CAAC,MAAM,EAAED,QAAQ,CAAC;UAC7CL,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAED,QAAQ,CAAC;UAClDL,OAAO,CAACO,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACDP,OAAO,CAACQ,gBAAgB,CAAC,MAAM,EAAEH,QAAQ,CAAC;QAC1CL,OAAO,CAACQ,gBAAgB,CAAC,WAAW,EAAEH,QAAQ,CAAC;MACnD,CAAC,CAAC;IACN;IACAL,OAAO,CAACS,KAAK,CAACR,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIS,mBAAmB,CAACC,QAAQ,EAAEV,OAAO,EAAE;IACnC,IAAIW,cAAc,GAAG,IAAI,CAACzC,WAAW,CAAC0C,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACb,WAAW,CAACa,cAAc,EAAEX,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIT,UAAU,GAAG;IACT,MAAMQ,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;IAC9C;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAACvC,OAAO,CAACX,SAAS;MAC1B,KAAK,KAAK;MACV,KAAK,QAAQ;QACT;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAACmC,cAAc,EAAE,EAAE;UACxBE,OAAO,CAACS,KAAK,EAAE;QACnB;QACA;MACJ,KAAK,IAAI;MACT,KAAK,gBAAgB;QACjB,IAAI,CAACM,UAAU,CAACC,4BAA4B,EAAE,CAACC,IAAI,CAACC,mBAAmB,IAAI;UACvE;UACA;UACA,IAAI,CAACA,mBAAmB,EAAE;YACtB,IAAI,CAACC,qBAAqB,EAAE;UAChC;QACJ,CAAC,CAAC;QACF;MACJ,KAAK,eAAe;QAChB,IAAI,CAACT,mBAAmB,CAAC,0CAA0C,CAAC;QACpE;MACJ;QACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAACpC,OAAO,CAACX,SAAS,CAAC;QAChD;IAAM;EAElB;EACA;EACA+B,aAAa,GAAG;IACZ,MAAM0B,WAAW,GAAG,IAAI,CAAC9C,OAAO,CAACV,YAAY;IAC7C,IAAIyD,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjCC,kBAAkB,GAAG,IAAI,CAAChD,SAAS,CAACyC,aAAa,CAACM,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAACzC,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIyC,WAAW,EAAE;MAClBC,kBAAkB,GAAGD,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAAC9C,OAAO,CAACV,YAAY,IACzByD,kBAAkB,IAClB,OAAOA,kBAAkB,CAACZ,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMa,aAAa,GAAGrG,iCAAiC,EAAE;MACzD,MAAM+E,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACS,aAAa,IACdA,aAAa,KAAK,IAAI,CAACjD,SAAS,CAACkD,IAAI,IACrCD,aAAa,KAAKtB,OAAO,IACzBA,OAAO,CAACwB,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAAC5C,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAAC+C,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAACzC,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDyC,kBAAkB,CAACZ,KAAK,EAAE;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAACM,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACW,OAAO,EAAE;IAC7B;EACJ;EACA;EACAP,qBAAqB,GAAG;IACpB;IACA,IAAI,IAAI,CAAChD,WAAW,CAAC0C,aAAa,CAACJ,KAAK,EAAE;MACtC,IAAI,CAACtC,WAAW,CAAC0C,aAAa,CAACJ,KAAK,EAAE;IAC1C;EACJ;EACA;EACAX,cAAc,GAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAAC7B,WAAW,CAAC0C,aAAa;IAC9C,MAAMS,aAAa,GAAGrG,iCAAiC,EAAE;IACzD,OAAO+E,OAAO,KAAKsB,aAAa,IAAItB,OAAO,CAACwB,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACAjC,oBAAoB,GAAG;IACnB,IAAI,CAAC0B,UAAU,GAAG,IAAI,CAAC3C,iBAAiB,CAACuD,MAAM,CAAC,IAAI,CAACxD,WAAW,CAAC0C,aAAa,CAAC;IAC/E;IACA;IACA,IAAI,IAAI,CAACxC,SAAS,EAAE;MAChB,IAAI,CAACM,oCAAoC,GAAG1D,iCAAiC,EAAE;IACnF;EACJ;EACA;EACAqE,qBAAqB,GAAG;IACpB;IACA;IACA,IAAI,CAACb,WAAW,CAACmD,aAAa,EAAE,CAACC,SAAS,CAAC,MAAM;MAC7C,IAAI,IAAI,CAACvD,OAAO,CAACnB,YAAY,EAAE;QAC3B,IAAI,CAAC0C,eAAe,EAAE;MAC1B;IACJ,CAAC,CAAC;EACN;AACJ;AACA3B,kBAAkB,CAAC4D,IAAI;EAAA,iBAA6F5D,kBAAkB,EAA5BzC,EAAE,mBAA4CA,EAAE,CAACsG,UAAU,GAA3DtG,EAAE,mBAAsEf,EAAE,CAACsH,gBAAgB,GAA3FvG,EAAE,mBAAsGD,QAAQ,MAAhHC,EAAE,mBAA2IoB,YAAY,GAAzJpB,EAAE,mBAAoKf,EAAE,CAACuH,oBAAoB,GAA7LxG,EAAE,mBAAwMA,EAAE,CAACyG,MAAM,GAAnNzG,EAAE,mBAA8Nb,IAAI,CAACG,UAAU,GAA/OU,EAAE,mBAA0Pf,EAAE,CAACyH,YAAY;AAAA,CAA4C;AACjajE,kBAAkB,CAACkE,IAAI,kBADmF3G,EAAE;EAAA,MACJyC,kBAAkB;EAAA;EAAA;IAAA;MADhBzC,EAAE,aAC6eL,eAAe;IAAA;IAAA;MAAA;MAD9fK,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA,wBACoF,IAAI;EAAA;EAAA;IAAA;MAD1FA,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,iFAC6nB;IAAA;EAAA;EAAA,eAA8JP,EAAE,CAACE,eAAe;EAAA;EAAA;AAAA,EAAuN;AAChnC;EAAA,mDAF0GK,EAAE,mBAEZyC,kBAAkB,EAAc,CAAC;IACrHmE,IAAI,EAAE3G,SAAS;IACf4G,IAAI,EAAE,CAAC;MAAE3B,QAAQ,EAAE,sBAAsB;MAAE4B,aAAa,EAAE5G,iBAAiB,CAAC6G,IAAI;MAAEC,eAAe,EAAE7G,uBAAuB,CAAC8G,OAAO;MAAEC,IAAI,EAAE;QAC9H,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,4CAA4C;QACtE,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEC,QAAQ,EAAE,+CAA+C;MAAEC,MAAM,EAAE,CAAC,mGAAmG;IAAE,CAAC;EACzL,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAE5G,EAAE,CAACsG;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAE3H,EAAE,CAACsH;IAAiB,CAAC,EAAE;MAAEK,IAAI,EAAES,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtHV,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEvG,MAAM;QACZwG,IAAI,EAAE,CAAC9G,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE6G,IAAI,EAAES,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAEvG,MAAM;QACZwG,IAAI,EAAE,CAACzF,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAEwF,IAAI,EAAE3H,EAAE,CAACuH;IAAqB,CAAC,EAAE;MAAEI,IAAI,EAAE5G,EAAE,CAACyG;IAAO,CAAC,EAAE;MAAEG,IAAI,EAAEzH,IAAI,CAACG;IAAW,CAAC,EAAE;MAAEsH,IAAI,EAAE3H,EAAE,CAACyH;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEpD,aAAa,EAAE,CAAC;MAC1JsD,IAAI,EAAEtG,SAAS;MACfuG,IAAI,EAAE,CAAClH,eAAe,EAAE;QAAE4H,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZnG,WAAW,CAACoG,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAACC,MAAM,GAAG,IAAI5G,OAAO,EAAE;IAC3B,IAAI,CAACW,YAAY,GAAGgG,MAAM,CAAChG,YAAY;IACvC,IAAI,CAACyE,aAAa,GAAGsB,UAAU,CAACtB,aAAa,EAAE;IAC/C,IAAI,CAACyB,aAAa,GAAGH,UAAU,CAACG,aAAa,EAAE;IAC/C,IAAI,CAACC,oBAAoB,GAAGJ,UAAU,CAACI,oBAAoB,EAAE;IAC7D,IAAI,CAACC,EAAE,GAAGJ,MAAM,CAACI,EAAE,CAAC,CAAC;IACrB,IAAI,CAACF,aAAa,CAACxB,SAAS,CAAC2B,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKnH,MAAM,IAAI,CAAC,IAAI,CAACa,YAAY,IAAI,CAACZ,cAAc,CAACiH,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACC,KAAK,CAACb,SAAS,EAAE;UAAEc,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAAChC,aAAa,CAACC,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAAC1E,YAAY,EAAE;QACpB,IAAI,CAACwG,KAAK,CAACb,SAAS,EAAE;UAAEc,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,mBAAmB,GAAGX,UAAU,CAACY,WAAW,EAAE,CAACjC,SAAS,CAAC,MAAM;MAChE;MACA,IAAIsB,MAAM,CAACpF,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAAC4F,KAAK,EAAE;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAK,CAACzE,MAAM,EAAEe,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC8D,iBAAiB,EAAE;MACxB,MAAMC,aAAa,GAAG,IAAI,CAACZ,MAAM;MACjC,IAAI,CAACW,iBAAiB,CAACnF,qBAAqB,GAAGqB,OAAO,EAAE2D,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACC,mBAAmB,CAACI,WAAW,EAAE;MACtC,IAAI,CAACf,UAAU,CAACgB,OAAO,EAAE;MACzBF,aAAa,CAACG,IAAI,CAACjF,MAAM,CAAC;MAC1B8E,aAAa,CAACI,QAAQ,EAAE;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACN,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAO,cAAc,GAAG;IACb,IAAI,CAACpB,UAAU,CAACoB,cAAc,EAAE;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAU,CAACnH,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAAC6F,UAAU,CAACqB,UAAU,CAAC;MAAEnH,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACAmH,aAAa,CAACC,OAAO,EAAE;IACnB,IAAI,CAACvB,UAAU,CAACsB,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgB,CAACD,OAAO,EAAE;IACtB,IAAI,CAACvB,UAAU,CAACwB,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,sBAAsB,GAAG,IAAI3I,cAAc,CAAC,sBAAsB,CAAC;AACzE;AACA,MAAM4I,WAAW,GAAG,IAAI5I,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAM6I,qBAAqB,GAAG,IAAI7I,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA,SAAS8I,uCAAuC,CAACC,OAAO,EAAE;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,EAAE;AACjD;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCC,OAAO,EAAER,sBAAsB;EAC/BS,IAAI,EAAE,CAACvK,OAAO,CAAC;EACfwK,UAAU,EAAEP;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIQ,QAAQ,GAAG,CAAC;AAChB,MAAMC,MAAM,CAAC;EACT;EACA,IAAIC,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIC,WAAW,GAAG;IACd,OAAO,IAAI,CAACF,aAAa,GAAG,IAAI,CAACA,aAAa,CAACE,WAAW,GAAG,IAAI,CAACC,uBAAuB;EAC7F;EACA9I,WAAW,CAAC+I,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEN,aAAa,EAAEO,iBAAiB,EAAEC,cAAc,EAAE;IAChG,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACO,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACN,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACQ,0BAA0B,GAAG,IAAI1J,OAAO,EAAE;IAC/C,IAAI,CAACoJ,uBAAuB,GAAG,IAAIpJ,OAAO,EAAE;IAC5C,IAAI,CAAC2J,mBAAmB,GAAG,IAAIC,GAAG,EAAE;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG5J,KAAK,CAAC,MAAM,IAAI,CAAC+I,WAAW,CAACc,MAAM,GACnD,IAAI,CAACC,kBAAkB,EAAE,GACzB,IAAI,CAACA,kBAAkB,EAAE,CAACC,IAAI,CAAC5J,SAAS,CAACkG,SAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAAC2D,eAAe,GAAGR,cAAc;EACzC;EACAS,IAAI,CAACC,sBAAsB,EAAExD,MAAM,EAAE;IACjC,MAAMyD,QAAQ,GAAI,IAAI,CAACb,eAAe,IAAI,IAAIlJ,YAAY,EAAG;IAC7DsG,MAAM,GAAG;MAAE,GAAGyD,QAAQ;MAAE,GAAGzD;IAAO,CAAC;IACnCA,MAAM,CAACI,EAAE,GAAGJ,MAAM,CAACI,EAAE,IAAK,cAAa+B,QAAQ,EAAG,EAAC;IACnD,IAAInC,MAAM,CAACI,EAAE,IACT,IAAI,CAACsD,aAAa,CAAC1D,MAAM,CAACI,EAAE,CAAC,KAC5B,OAAOtE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMhB,KAAK,CAAE,mBAAkBkF,MAAM,CAACI,EAAG,iDAAgD,CAAC;IAC9F;IACA,MAAMuD,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC5D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG,IAAI,CAAC2C,QAAQ,CAAClE,MAAM,CAACmF,aAAa,CAAC;IACtD,MAAME,SAAS,GAAG,IAAI/D,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAM8D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAAChE,UAAU,EAAE8D,SAAS,EAAE7D,MAAM,CAAC;IAC5E6D,SAAS,CAACjD,iBAAiB,GAAGkD,eAAe;IAC7C,IAAI,CAACE,oBAAoB,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE9D,MAAM,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACqC,WAAW,CAACc,MAAM,EAAE;MAC1B,IAAI,CAACc,4CAA4C,EAAE;IACvD;IACA,IAAI,CAAC5B,WAAW,CAAC6B,IAAI,CAACL,SAAS,CAAC;IAChCA,SAAS,CAAC5D,MAAM,CAACvB,SAAS,CAAC,MAAM,IAAI,CAACyF,iBAAiB,CAACN,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACrB,WAAW,CAACxB,IAAI,CAAC6C,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIO,QAAQ,GAAG;IACPC,cAAc,CAAC,IAAI,CAAChC,WAAW,EAAEiC,MAAM,IAAIA,MAAM,CAAC9D,KAAK,EAAE,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIkD,aAAa,CAACtD,EAAE,EAAE;IACd,OAAO,IAAI,CAACiC,WAAW,CAACkC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAClE,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACA9D,WAAW,GAAG;IACV;IACA;IACA;IACA+H,cAAc,CAAC,IAAI,CAAC9B,uBAAuB,EAAE+B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACtE,MAAM,CAACrF,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAACwJ,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC9B,uBAAuB,EAAE+B,MAAM,IAAIA,MAAM,CAAC9D,KAAK,EAAE,CAAC;IACtE,IAAI,CAACuC,0BAA0B,CAAC9B,QAAQ,EAAE;IAC1C,IAAI,CAACwB,uBAAuB,CAACxB,QAAQ,EAAE;IACvC,IAAI,CAACsB,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIqB,iBAAiB,CAAC5D,MAAM,EAAE;IACtB,MAAMwE,KAAK,GAAG,IAAI7M,aAAa,CAAC;MAC5B8M,gBAAgB,EAAEzE,MAAM,CAACyE,gBAAgB,IACrC,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,EAAE,CAACC,MAAM,EAAE,CAACC,kBAAkB,EAAE,CAACC,gBAAgB,EAAE;MAC7E/B,cAAc,EAAE9C,MAAM,CAAC8C,cAAc,IAAI,IAAI,CAACQ,eAAe,EAAE;MAC/DzJ,UAAU,EAAEmG,MAAM,CAACnG,UAAU;MAC7BC,WAAW,EAAEkG,MAAM,CAAClG,WAAW;MAC/BgL,SAAS,EAAE9E,MAAM,CAAC8E,SAAS;MAC3BC,QAAQ,EAAE/E,MAAM,CAAC+E,QAAQ;MACzBC,SAAS,EAAEhF,MAAM,CAACgF,SAAS;MAC3BC,QAAQ,EAAEjF,MAAM,CAACiF,QAAQ;MACzBC,SAAS,EAAElF,MAAM,CAACkF,SAAS;MAC3BjL,KAAK,EAAE+F,MAAM,CAAC/F,KAAK;MACnBC,MAAM,EAAE8F,MAAM,CAAC9F,MAAM;MACrBiL,mBAAmB,EAAEnF,MAAM,CAACtF;IAChC,CAAC,CAAC;IACF,IAAIsF,MAAM,CAACjG,aAAa,EAAE;MACtByK,KAAK,CAACzK,aAAa,GAAGiG,MAAM,CAACjG,aAAa;IAC9C;IACA,OAAOyK,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIT,gBAAgB,CAACnC,OAAO,EAAEiC,SAAS,EAAE7D,MAAM,EAAE;IACzC,MAAMoF,YAAY,GAAGpF,MAAM,CAACqF,QAAQ,IAAIrF,MAAM,CAACsF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAEtI,YAAY;MAAE8L,QAAQ,EAAExF;IAAO,CAAC,EAC3C;MAAEgC,OAAO,EAAElC,SAAS;MAAE0F,QAAQ,EAAE3B;IAAU,CAAC,EAC3C;MAAE7B,OAAO,EAAEpK,UAAU;MAAE4N,QAAQ,EAAE5D;IAAQ,CAAC,CAC7C;IACD,IAAI6D,aAAa;IACjB,IAAIzF,MAAM,CAAC0F,SAAS,EAAE;MAClB,IAAI,OAAO1F,MAAM,CAAC0F,SAAS,KAAK,UAAU,EAAE;QACxCD,aAAa,GAAGzF,MAAM,CAAC0F,SAAS;MACpC,CAAC,MACI;QACDD,aAAa,GAAGzF,MAAM,CAAC0F,SAAS,CAACxG,IAAI;QACrCqG,SAAS,CAACrB,IAAI,CAAC,GAAGlE,MAAM,CAAC0F,SAAS,CAACH,SAAS,CAACvF,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDyF,aAAa,GAAG1K,kBAAkB;IACtC;IACA,MAAM4K,eAAe,GAAG,IAAIzN,eAAe,CAACuN,aAAa,EAAEzF,MAAM,CAACsF,gBAAgB,EAAExM,QAAQ,CAAC0F,MAAM,CAAC;MAAEoH,MAAM,EAAER,YAAY,IAAI,IAAI,CAACzC,SAAS;MAAE4C;IAAU,CAAC,CAAC,EAAEvF,MAAM,CAAC6F,wBAAwB,CAAC;IAC5L,MAAMC,YAAY,GAAGlE,OAAO,CAACmE,MAAM,CAACJ,eAAe,CAAC;IACpD,OAAOG,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhC,oBAAoB,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE9D,MAAM,EAAE;IAC7E,IAAIwD,sBAAsB,YAAYzK,WAAW,EAAE;MAC/C,MAAMsM,QAAQ,GAAG,IAAI,CAACY,eAAe,CAACjG,MAAM,EAAE6D,SAAS,EAAEC,eAAe,EAAEnE,SAAS,CAAC;MACpF,IAAIuG,OAAO,GAAG;QAAEC,SAAS,EAAEnG,MAAM,CAAC7F,IAAI;QAAE0J;MAAU,CAAC;MACnD,IAAI7D,MAAM,CAACoG,eAAe,EAAE;QACxBF,OAAO,GAAG;UACN,GAAGA,OAAO;UACV,IAAI,OAAOlG,MAAM,CAACoG,eAAe,KAAK,UAAU,GAC1CpG,MAAM,CAACoG,eAAe,EAAE,GACxBpG,MAAM,CAACoG,eAAe;QAChC,CAAC;MACL;MACAtC,eAAe,CAACrH,oBAAoB,CAAC,IAAItE,cAAc,CAACqL,sBAAsB,EAAE,IAAI,EAAE0C,OAAO,EAAEb,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAACY,eAAe,CAACjG,MAAM,EAAE6D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAACnB,SAAS,CAAC;MACzF,MAAM0D,UAAU,GAAGvC,eAAe,CAACtH,qBAAqB,CAAC,IAAItE,eAAe,CAACsL,sBAAsB,EAAExD,MAAM,CAACsF,gBAAgB,EAAED,QAAQ,EAAErF,MAAM,CAAC6F,wBAAwB,CAAC,CAAC;MACzKhC,SAAS,CAAC3C,iBAAiB,GAAGmF,UAAU,CAACL,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAe,CAACjG,MAAM,EAAE6D,SAAS,EAAEC,eAAe,EAAEwC,gBAAgB,EAAE;IAClE,MAAMlB,YAAY,GAAGpF,MAAM,CAACqF,QAAQ,IAAIrF,MAAM,CAACsF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAEP,WAAW;MAAE+D,QAAQ,EAAExF,MAAM,CAAC7F;IAAK,CAAC,EAC/C;MAAE6H,OAAO,EAAElC,SAAS;MAAE0F,QAAQ,EAAE3B;IAAU,CAAC,CAC9C;IACD,IAAI7D,MAAM,CAACuF,SAAS,EAAE;MAClB,IAAI,OAAOvF,MAAM,CAACuF,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAACrB,IAAI,CAAC,GAAGlE,MAAM,CAACuF,SAAS,CAAC1B,SAAS,EAAE7D,MAAM,EAAE8D,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDyB,SAAS,CAACrB,IAAI,CAAC,GAAGlE,MAAM,CAACuF,SAAS,CAAC;MACvC;IACJ;IACA,IAAIvF,MAAM,CAAC8E,SAAS,KACf,CAACM,YAAY,IACV,CAACA,YAAY,CAACmB,GAAG,CAAC/M,cAAc,EAAE,IAAI,EAAE;MAAEgN,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClEjB,SAAS,CAACrB,IAAI,CAAC;QACXlC,OAAO,EAAExI,cAAc;QACvBgM,QAAQ,EAAE;UAAEiB,KAAK,EAAEzG,MAAM,CAAC8E,SAAS;UAAE4B,MAAM,EAAEnN,EAAE;QAAG;MACtD,CAAC,CAAC;IACN;IACA,OAAOT,QAAQ,CAAC0F,MAAM,CAAC;MAAEoH,MAAM,EAAER,YAAY,IAAIkB,gBAAgB;MAAEf;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIpB,iBAAiB,CAACN,SAAS,EAAE8C,SAAS,EAAE;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACvE,WAAW,CAACwE,OAAO,CAAChD,SAAS,CAAC;IACjD,IAAI+C,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACvE,WAAW,CAACyE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAACvE,WAAW,CAACc,MAAM,EAAE;QAC1B,IAAI,CAACH,mBAAmB,CAAC+D,OAAO,CAAC,CAACC,aAAa,EAAEnK,OAAO,KAAK;UACzD,IAAImK,aAAa,EAAE;YACfnK,OAAO,CAACoK,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACDnK,OAAO,CAACO,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC4F,mBAAmB,CAACkE,KAAK,EAAE;QAChC,IAAIP,SAAS,EAAE;UACX,IAAI,CAACvD,kBAAkB,EAAE,CAACpC,IAAI,EAAE;QACpC;MACJ;IACJ;EACJ;EACA;EACAiD,4CAA4C,GAAG;IAC3C,MAAMkD,gBAAgB,GAAG,IAAI,CAACtE,iBAAiB,CAACuE,mBAAmB,EAAE;IACrE;IACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAACnE,MAAM,GAAG,CAAC,EAAEqE,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;QAC3B,IAAIC,OAAO,KAAKN,gBAAgB,IAC5BM,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAAC3E,mBAAmB,CAAC4E,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACR,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA7D,kBAAkB,GAAG;IACjB,MAAMwC,MAAM,GAAG,IAAI,CAACtD,aAAa;IACjC,OAAOsD,MAAM,GAAGA,MAAM,CAACxC,kBAAkB,EAAE,GAAG,IAAI,CAACL,0BAA0B;EACjF;AACJ;AACAX,MAAM,CAACzD,IAAI;EAAA,iBAA6FyD,MAAM,EA1YJ9J,EAAE,UA0YoBb,IAAI,CAACC,OAAO,GA1YlCY,EAAE,UA0Y6CA,EAAE,CAACQ,QAAQ,GA1Y1DR,EAAE,UA0YqEoJ,qBAAqB,MA1Y5FpJ,EAAE,UA0YuH8J,MAAM,OA1Y/H9J,EAAE,UA0Y0Kb,IAAI,CAACqQ,gBAAgB,GA1YjMxP,EAAE,UA0Y4MkJ,sBAAsB;AAAA,CAA6C;AAC3XY,MAAM,CAAC2F,KAAK,kBA3Y8FzP,EAAE;EAAA,OA2YA8J,MAAM;EAAA,SAANA,MAAM;AAAA,EAAG;AACrH;EAAA,mDA5Y0G9J,EAAE,mBA4YZ8J,MAAM,EAAc,CAAC;IACzGlD,IAAI,EAAElG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEkG,IAAI,EAAEzH,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAEwH,IAAI,EAAE5G,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEoG,IAAI,EAAExF,YAAY;MAAEkG,UAAU,EAAE,CAAC;QAChHV,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEvG,MAAM;QACZwG,IAAI,EAAE,CAACuC,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAExC,IAAI,EAAEkD,MAAM;MAAExC,UAAU,EAAE,CAAC;QAC/BV,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEjG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEiG,IAAI,EAAEzH,IAAI,CAACqQ;IAAiB,CAAC,EAAE;MAAE5I,IAAI,EAAES,SAAS;MAAEC,UAAU,EAAE,CAAC;QACnEV,IAAI,EAAEvG,MAAM;QACZwG,IAAI,EAAE,CAACqC,sBAAsB;MACjC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,SAAS6C,cAAc,CAAC2D,KAAK,EAAE9K,QAAQ,EAAE;EACrC,IAAIsK,CAAC,GAAGQ,KAAK,CAAC7E,MAAM;EACpB,OAAOqE,CAAC,EAAE,EAAE;IACRtK,QAAQ,CAAC8K,KAAK,CAACR,CAAC,CAAC,CAAC;EACtB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,YAAY,CAAC;AAEnBA,YAAY,CAACtJ,IAAI;EAAA,iBAA6FsJ,YAAY;AAAA,CAAkD;AAC5KA,YAAY,CAACC,IAAI,kBAhbyF5P,EAAE;EAAA,MAgbG2P;AAAY,EAI7F;AAC9BA,YAAY,CAACE,IAAI,kBArbyF7P,EAAE;EAAA,WAqb4B,CAAC8J,MAAM,EAAEL,+BAA+B,CAAC;EAAA,UAAYlK,aAAa,EAAEO,YAAY,EAAEZ,UAAU;EAC5N;EACA;EACAY,YAAY;AAAA,EAAI;AACxB;EAAA,mDAzb0GE,EAAE,mBAybZ2P,YAAY,EAAc,CAAC;IAC/G/I,IAAI,EAAEhG,QAAQ;IACdiG,IAAI,EAAE,CAAC;MACCiJ,OAAO,EAAE,CAACvQ,aAAa,EAAEO,YAAY,EAAEZ,UAAU,CAAC;MAClD6Q,OAAO,EAAE;MACL;MACA;MACAjQ,YAAY,EACZ2C,kBAAkB,CACrB;MACDuN,YAAY,EAAE,CAACvN,kBAAkB,CAAC;MAClCwK,SAAS,EAAE,CAACnD,MAAM,EAAEL,+BAA+B;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAShH,kBAAkB,EAAE2G,qBAAqB,EAAED,WAAW,EAAED,sBAAsB,EAAEO,+BAA+B,EAAEJ,uCAAuC,EAAES,MAAM,EAAE1I,YAAY,EAAEuO,YAAY,EAAEnI,SAAS,EAAEjF,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}