<div class="profile-edit-container" *ngIf="!isLoading">
  <div class="edit-header">
    <h1>
      <mat-icon>edit</mat-icon>
      Edit Profile
    </h1>
    <div class="header-actions">
      <button mat-stroked-button (click)="onCancel()" [disabled]="isSaving">
        Cancel
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="onSubmit()"
        [disabled]="isSaving || profileForm.invalid">
        <mat-icon *ngIf="isSaving">
          <mat-spinner diameter="20"></mat-spinner>
        </mat-icon>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>
  </div>

  <form [formGroup]="profileForm" class="profile-form">
    <!-- Photo Upload Section -->
    <mat-card class="photo-section">
      <mat-card-header>
        <mat-card-title>Profile Photos</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="photo-uploads">
          <div class="profile-photo-upload">
            <div class="photo-preview">
              <img [src]="profile?.profilePhotoUrl || '/assets/images/default-avatar.png'"
                   alt="Profile Photo" class="profile-photo">
              <div class="photo-overlay">
                <mat-icon>camera_alt</mat-icon>
              </div>
            </div>
            <input type="file" #profilePhotoInput (change)="onProfilePhotoSelected($event)"
                   accept="image/*" style="display: none;">
            <button mat-stroked-button (click)="profilePhotoInput.click()">
              Change Profile Photo
            </button>
          </div>

          <div class="cover-photo-upload">
            <div class="cover-preview">
              <div class="cover-photo"
                   [style.background-image]="(profile && profile.coverPhotoUrl) ? ('url(' + profile.coverPhotoUrl + ')') : 'var(--theme-gradient-primary)'">
                <div class="photo-overlay">
                  <mat-icon>camera_alt</mat-icon>
                </div>
              </div>
            </div>
            <input type="file" #coverPhotoInput (change)="onCoverPhotoSelected($event)"
                   accept="image/*" style="display: none;">
            <button mat-stroked-button (click)="coverPhotoInput.click()">
              Change Cover Photo
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Basic Information -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Basic Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstName" required>
            <mat-error *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched">
              {{ getErrorMessage('firstName') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastName" required>
            <mat-error *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched">
              {{ getErrorMessage('lastName') }}
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Title</mat-label>
          <input matInput formControlName="professionalTitle"
                 placeholder="e.g., Senior Software Engineer, UX Designer">
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Headline</mat-label>
          <textarea matInput formControlName="headline" rows="2"
                    placeholder="A brief, compelling description of what you do"
                    maxlength="220"></textarea>
          <mat-hint align="end">{{ profileForm.get('headline')?.value?.length || 0 }}/220</mat-hint>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Professional Summary</mat-label>
          <textarea matInput formControlName="summary" rows="6"
                    placeholder="Describe your expertise, experience, and what makes you unique"
                    maxlength="2000"></textarea>
          <mat-hint align="end">{{ profileForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>
        </mat-form-field>
      </mat-card-content>
    </mat-card>

    <!-- Location Information -->
    <mat-card class="form-section" formGroupName="location">
      <mat-card-header>
        <mat-card-title>Location</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>City</mat-label>
            <input matInput formControlName="city">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>State/Province</mat-label>
            <input matInput formControlName="state">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Country</mat-label>
            <input matInput formControlName="country">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Display Location</mat-label>
            <input matInput formControlName="displayLocation"
                   placeholder="e.g., San Francisco, CA">
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Contact Information -->
    <mat-card class="form-section" formGroupName="contactInfo">
      <mat-card-header>
        <mat-card-title>Contact Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" type="email">
            <mat-error *ngIf="profileForm.get('contactInfo.email')?.invalid && profileForm.get('contactInfo.email')?.touched">
              {{ getErrorMessage('contactInfo.email') }}
            </mat-error>
          </mat-form-field>

          <div class="checkbox-field">
            <mat-checkbox formControlName="isEmailPublic">
              Make email public
            </mat-checkbox>
          </div>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Website</mat-label>
            <input matInput formControlName="website" placeholder="https://yourwebsite.com">
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Portfolio URL</mat-label>
            <input matInput formControlName="portfolioUrl" placeholder="https://portfolio.com">
          </mat-form-field>
        </div>

        <!-- Phone Numbers -->
        <div class="phone-numbers-section">
          <h4>Phone Numbers</h4>
          <div formArrayName="phoneNumbers">
            <div *ngFor="let phone of phoneNumbers.controls; let i = index"
                 [formGroupName]="i" class="phone-number-item">
              <div class="form-row">
                <mat-form-field appearance="outline" class="phone-input">
                  <mat-label>Phone Number</mat-label>
                  <input matInput formControlName="number" placeholder="+****************">
                </mat-form-field>

                <mat-form-field appearance="outline" class="phone-type">
                  <mat-label>Type</mat-label>
                  <mat-select formControlName="type">
                    <mat-option *ngFor="let type of getPhoneTypeOptions()" [value]="type.value">
                      {{ type.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <div class="phone-controls">
                  <mat-checkbox formControlName="isPublic">Public</mat-checkbox>
                  <mat-checkbox formControlName="isPrimary">Primary</mat-checkbox>
                  <button mat-icon-button color="warn" (click)="removePhoneNumber(i)" type="button">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button mat-stroked-button (click)="addPhoneNumber()" type="button">
            <mat-icon>add</mat-icon>
            Add Phone Number
          </button>
        </div>

        <!-- Business Address -->
        <div class="business-address-section" formGroupName="businessAddress">
          <h4>Business Address</h4>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Street Address</mat-label>
            <input matInput formControlName="street">
          </mat-form-field>

          <div class="form-row">
            <mat-form-field appearance="outline" class="third-width">
              <mat-label>City</mat-label>
              <input matInput formControlName="city">
            </mat-form-field>

            <mat-form-field appearance="outline" class="third-width">
              <mat-label>State</mat-label>
              <input matInput formControlName="state">
            </mat-form-field>

            <mat-form-field appearance="outline" class="third-width">
              <mat-label>Postal Code</mat-label>
              <input matInput formControlName="postalCode">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Country</mat-label>
              <input matInput formControlName="country">
            </mat-form-field>

            <div class="checkbox-field">
              <mat-checkbox formControlName="isPublic">
                Make business address public
              </mat-checkbox>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Social Links -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Social Links</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div formArrayName="socialLinks">
          <div *ngFor="let link of socialLinks.controls; let i = index"
               [formGroupName]="i" class="social-link-item">
            <div class="form-row">
              <mat-form-field appearance="outline" class="platform-select">
                <mat-label>Platform</mat-label>
                <mat-select formControlName="platform">
                  <mat-option *ngFor="let platform of getPlatformOptions()" [value]="platform.value">
                    {{ platform.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="url-input">
                <mat-label>URL</mat-label>
                <input matInput formControlName="url" placeholder="https://linkedin.com/in/yourname">
              </mat-form-field>

              <div class="link-controls">
                <mat-checkbox formControlName="isPublic">Public</mat-checkbox>
                <button mat-icon-button color="warn" (click)="removeSocialLink(i)" type="button">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
        <button mat-stroked-button (click)="addSocialLink()" type="button">
          <mat-icon>add</mat-icon>
          Add Social Link
        </button>
      </mat-card-content>
    </mat-card>

    <!-- Privacy Settings -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>Privacy Settings</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="privacy-controls">
          <mat-slide-toggle formControlName="isPublic" color="primary">
            <span class="toggle-label">Make profile public</span>
            <p class="toggle-description">
              When enabled, your profile will be visible to everyone and searchable.
              When disabled, only you can see your profile.
            </p>
          </mat-slide-toggle>
        </div>
      </mat-card-content>
    </mat-card>
  </form>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>
