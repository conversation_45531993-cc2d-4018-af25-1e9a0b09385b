{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../auth/services/auth.service\";\nimport * as i3 from \"../../core/i18n/translation.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"../../core/theme/theme-selector/theme-selector.component\";\nfunction NavigationComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToLogin());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_24_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToRegister());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.t.common.login, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.t.home.startJourney, \" \");\n  }\n}\nfunction NavigationComponent_div_25_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.ngIf;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.t.dashboard.welcome, \", \", user_r11.firstName, \"! \");\n  }\n}\nfunction NavigationComponent_div_25_div_4_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 36);\n  }\n  if (rf & 2) {\n    const user_r12 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵproperty(\"src\", user_r12.profilePictureUrl, i0.ɵɵsanitizeUrl)(\"alt\", user_r12.firstName + \" \" + user_r12.lastName);\n  }\n}\nfunction NavigationComponent_div_25_div_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 37);\n    i0.ɵɵtext(1, \"account_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavigationComponent_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, NavigationComponent_div_25_div_4_img_1_Template, 1, 2, \"img\", 34);\n    i0.ɵɵtemplate(2, NavigationComponent_div_25_div_4_ng_template_2_Template, 2, 0, \"ng-template\", null, 35, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r12 = ctx.ngIf;\n    const _r14 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r12.profilePictureUrl)(\"ngIfElse\", _r14);\n  }\n}\nfunction NavigationComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NavigationComponent_div_25_span_1_Template, 2, 2, \"span\", 16);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementStart(3, \"button\", 17);\n    i0.ɵɵtemplate(4, NavigationComponent_div_25_div_4_Template, 4, 2, \"div\", 18);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-menu\", 19, 20)(8, \"button\", 21)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 22)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 23)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 24)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"api\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"Test API Connection\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"button\", 25)(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-icon\", 26);\n    i0.ɵɵtext(34, \"chevron_right\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 27)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"mat-divider\");\n    i0.ɵɵelementStart(41, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_25_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.logout());\n    });\n    i0.ɵɵelementStart(42, \"mat-icon\");\n    i0.ɵɵtext(43, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"mat-menu\", 29, 30)(48, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_25_Template_div_click_48_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelement(49, \"app-theme-selector\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r9 = i0.ɵɵreference(7);\n    const _r10 = i0.ɵɵreference(47);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 10, ctx_r1.authService.currentUser$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 12, ctx_r1.authService.currentUser$));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.myProfile);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.findProfessionals);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.dashboard);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.themes);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.t.common.settings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.logout);\n  }\n}\nfunction NavigationComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_46_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateToLogin());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_46_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.navigateToRegister());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.common.login, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.startJourney, \" \");\n  }\n}\nfunction NavigationComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"button\", 42)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 43)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 44)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_47_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.logout());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.myProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.findProfessionals, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.dashboard, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.logout, \" \");\n  }\n}\nexport class NavigationComponent {\n  constructor(router, route, authService, t) {\n    this.router = router;\n    this.route = route;\n    this.authService = authService;\n    this.t = t;\n    this.currentRoute = '';\n    this.isMobileMenuOpen = false;\n    this.isHomePage = false;\n    this.activeSection = 'hero';\n    // Track current route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.currentRoute = event.url;\n      const wasHomePage = this.isHomePage;\n      this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\n      this.closeMobileMenu();\n      // Initialize scroll detection when navigating to home page\n      if (this.isHomePage && !wasHomePage) {\n        // Clean up previous observer if it exists\n        if (this.intersectionObserver) {\n          this.intersectionObserver.disconnect();\n        }\n        this.initializeScrollDetection();\n      } else if (!this.isHomePage && this.intersectionObserver) {\n        // Clean up observer when leaving home page\n        this.intersectionObserver.disconnect();\n        this.activeSection = 'hero'; // Reset to default\n      }\n    });\n  }\n\n  ngOnInit() {\n    // Check initial route\n    this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\n    // Handle fragment navigation when arriving at home page\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      // Check for fragment after navigation\n      const fragment = this.route.snapshot.fragment;\n      if (fragment && this.isHomePage) {\n        setTimeout(() => {\n          this.scrollToSection(fragment);\n        }, 300); // Increased delay to ensure DOM is ready\n      }\n    });\n    // Initialize scroll detection when on home page\n    if (this.isHomePage) {\n      this.initializeScrollDetection();\n    }\n  }\n  ngOnDestroy() {\n    // Clean up intersection observer\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n  }\n  // Check if user is authenticated\n  isAuthenticated() {\n    return this.authService.isAuthenticated();\n  }\n  // Navigation methods - adapt behavior based on current page\n  navigateToSection(sectionId) {\n    this.closeMobileMenu();\n    if (this.isHomePage) {\n      // If on home page, scroll to section\n      this.scrollToSection(sectionId);\n      // Update active section immediately for better UX\n      this.activeSection = sectionId;\n    } else {\n      // If on other page, navigate to home with fragment\n      this.router.navigate(['/home'], {\n        fragment: sectionId\n      });\n    }\n  }\n  // Smooth scroll to section (only works when on home page)\n  scrollToSection(sectionId) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n    }\n  }\n  // Authentication navigation\n  navigateToLogin() {\n    this.closeMobileMenu();\n    this.router.navigate(['/login']);\n  }\n  navigateToRegister() {\n    this.closeMobileMenu();\n    this.router.navigate(['/register']);\n  }\n  // Mobile menu controls\n  toggleMobileMenu() {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n  closeMobileMenu() {\n    this.isMobileMenuOpen = false;\n  }\n  // Logout functionality\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/home']);\n      },\n      error: () => {\n        // Still navigate to home even if logout fails\n        this.router.navigate(['/home']);\n      }\n    });\n  }\n  // Initialize scroll detection using Intersection Observer\n  initializeScrollDetection() {\n    // Wait for DOM to be ready\n    setTimeout(() => {\n      const sections = ['hero', 'astrologers', 'articles', 'horoscope'];\n      const sectionElements = sections.map(id => document.getElementById(id)).filter(el => el !== null);\n      if (sectionElements.length === 0) {\n        return; // No sections found, skip initialization\n      }\n      // Create intersection observer\n      this.intersectionObserver = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            // Update active section when section comes into view\n            this.activeSection = entry.target.id;\n          }\n        });\n      }, {\n        // Trigger when section is 30% visible\n        threshold: 0.3,\n        // Offset from top to account for fixed navigation\n        rootMargin: '-80px 0px -50% 0px'\n      });\n      // Observe all sections\n      sectionElements.forEach(section => {\n        if (section) {\n          this.intersectionObserver.observe(section);\n        }\n      });\n    }, 500);\n  }\n  // Check if a navigation link is active\n  isActiveSection(sectionId) {\n    return this.isHomePage && this.activeSection === sectionId;\n  }\n  static {\n    this.ɵfac = function NavigationComponent_Factory(t) {\n      return new (t || NavigationComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavigationComponent,\n      selectors: [[\"app-navigation\"]],\n      decls: 48,\n      vars: 34,\n      consts: [[1, \"unified-navigation\"], [1, \"nav-container\"], [1, \"nav-brand\", 3, \"click\"], [1, \"nav-links\"], [\"mat-button\", \"\", 1, \"nav-link\", 3, \"click\"], [\"class\", \"nav-actions\", 4, \"ngIf\"], [\"class\", \"nav-user-menu\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"mobile-menu-toggle\", 3, \"click\"], [1, \"mobile-menu\"], [\"mat-button\", \"\", 1, \"mobile-nav-link\", 3, \"click\"], [\"class\", \"mobile-actions\", 4, \"ngIf\"], [\"class\", \"mobile-user-actions\", 4, \"ngIf\"], [1, \"nav-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"login-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"register-btn\", 3, \"click\"], [1, \"nav-user-menu\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"user-avatar-button\", 3, \"matMenuTriggerFor\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile/edit\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profiles/search\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/dashboard\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/test-api\"], [\"mat-menu-item\", \"\", 3, \"matMenuTriggerFor\"], [1, \"submenu-arrow\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"theme-menu\"], [\"themeMenu\", \"matMenu\"], [1, \"theme-menu-content\", 3, \"click\"], [1, \"user-info\"], [1, \"user-avatar\"], [\"class\", \"avatar-image\", 3, \"src\", \"alt\", 4, \"ngIf\", \"ngIfElse\"], [\"defaultAvatar\", \"\"], [1, \"avatar-image\", 3, \"src\", \"alt\"], [1, \"default-avatar\"], [1, \"mobile-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"mobile-login-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"mobile-register-btn\", 3, \"click\"], [1, \"mobile-user-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/profile/edit\", 1, \"mobile-nav-link\"], [\"mat-button\", \"\", \"routerLink\", \"/profiles/search\", 1, \"mobile-nav-link\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"mobile-nav-link\"]],\n      template: function NavigationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_div_click_2_listener() {\n            return ctx.navigateToSection(\"hero\");\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_8_listener() {\n            return ctx.navigateToSection(\"hero\");\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_12_listener() {\n            return ctx.navigateToSection(\"astrologers\");\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_16_listener() {\n            return ctx.navigateToSection(\"articles\");\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_20_listener() {\n            return ctx.navigateToSection(\"horoscope\");\n          });\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"stars\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, NavigationComponent_div_24_Template, 9, 2, \"div\", 5);\n          i0.ɵɵtemplate(25, NavigationComponent_div_25_Template, 50, 14, \"div\", 6);\n          i0.ɵɵelementStart(26, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_26_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelementStart(27, \"mat-icon\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 8)(30, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_30_listener() {\n            return ctx.navigateToSection(\"hero\");\n          });\n          i0.ɵɵelementStart(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_34_listener() {\n            return ctx.navigateToSection(\"astrologers\");\n          });\n          i0.ɵɵelementStart(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_38_listener() {\n            return ctx.navigateToSection(\"articles\");\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_42_listener() {\n            return ctx.navigateToSection(\"horoscope\");\n          });\n          i0.ɵɵelementStart(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"stars\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, NavigationComponent_div_46_Template, 9, 2, \"div\", 10);\n          i0.ɵɵtemplate(47, NavigationComponent_div_47_Template, 17, 4, \"div\", 11);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.t.nav.brand);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"hero\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.home, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"astrologers\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.astrologers, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"articles\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.articles, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"horoscope\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.horoscope, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated());\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isMobileMenuOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isMobileMenuOpen ? \"close\" : \"menu\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"open\", ctx.isMobileMenuOpen);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"hero\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.home, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"astrologers\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.astrologers, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"articles\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.articles, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"horoscope\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.horoscope, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated());\n        }\n      },\n      dependencies: [i4.NgIf, i1.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatIcon, i7.MatDivider, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.ThemeSelectorComponent, i4.AsyncPipe],\n      styles: [\".unified-navigation[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  z-index: 1000;\\r\\n  background: rgba(255, 255, 255, 0.95);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n          backdrop-filter: blur(10px);\\r\\n  border-bottom: 1px solid var(--theme-accent-light);\\r\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.nav-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n  padding: 0 20px;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: space-between;\\r\\n  height: 64px;\\r\\n}\\r\\n\\r\\n.nav-brand[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: 700;\\r\\n  color: var(--theme-primary);\\r\\n  cursor: pointer;\\r\\n  transition: color 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-brand[_ngcontent-%COMP%]:hover {\\r\\n  color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n.nav-brand[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 28px;\\r\\n  width: 28px;\\r\\n  height: 28px;\\r\\n}\\r\\n\\r\\n.nav-links[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.nav-link[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text);\\r\\n  transition: all 0.3s ease;\\r\\n  border-radius: 8px;\\r\\n  padding: 8px 16px;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--theme-accent-light);\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.nav-link.active[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n  font-weight: 600;\\r\\n  box-shadow: 0 2px 8px rgba(103, 69, 92, 0.3);\\r\\n}\\r\\n\\r\\n.nav-link.active[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--theme-accent);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.nav-link.active[_ngcontent-%COMP%]::after {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  bottom: -2px;\\r\\n  left: 50%;\\r\\n  transform: translateX(-50%);\\r\\n  width: 80%;\\r\\n  height: 2px;\\r\\n  background: linear-gradient(90deg, var(--theme-accent), var(--theme-primary));\\r\\n  border-radius: 1px;\\r\\n}\\r\\n\\r\\n.nav-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 20px;\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n}\\r\\n\\r\\n.nav-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.login-btn[_ngcontent-%COMP%], .register-btn[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-weight: 500;\\r\\n  border-radius: 8px;\\r\\n  padding: 8px 16px;\\r\\n}\\r\\n\\r\\n.register-btn[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.nav-user-menu[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.user-avatar-button[_ngcontent-%COMP%] {\\r\\n  padding: 4px;\\r\\n}\\r\\n\\r\\n.user-avatar[_ngcontent-%COMP%] {\\r\\n  width: 32px;\\r\\n  height: 32px;\\r\\n  border-radius: 50%;\\r\\n  overflow: hidden;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  background: var(--theme-primary-light);\\r\\n}\\r\\n\\r\\n.avatar-image[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  object-fit: cover;\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.default-avatar[_ngcontent-%COMP%] {\\r\\n  font-size: 24px;\\r\\n  width: 24px;\\r\\n  height: 24px;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.mobile-menu-toggle[_ngcontent-%COMP%] {\\r\\n  display: none;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.mobile-menu-toggle.active[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n\\r\\n.mobile-menu[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 100%;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  background: rgba(255, 255, 255, 0.98);\\r\\n  -webkit-backdrop-filter: blur(15px);\\r\\n          backdrop-filter: blur(15px);\\r\\n  border-bottom: 1px solid var(--theme-accent-light);\\r\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\r\\n  transform: translateY(-100%);\\r\\n  opacity: 0;\\r\\n  visibility: hidden;\\r\\n  transition: all 0.3s ease;\\r\\n  padding: 20px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.mobile-menu.open[_ngcontent-%COMP%] {\\r\\n  transform: translateY(0);\\r\\n  opacity: 1;\\r\\n  visibility: visible;\\r\\n}\\r\\n\\r\\n.mobile-nav-link[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  padding: 12px 16px;\\r\\n  border-radius: 8px;\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text);\\r\\n  transition: all 0.3s ease;\\r\\n  justify-content: flex-start;\\r\\n  width: 100%;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.mobile-nav-link[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--theme-accent-light);\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.mobile-nav-link.active[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n  font-weight: 600;\\r\\n  box-shadow: 0 2px 8px rgba(103, 69, 92, 0.3);\\r\\n}\\r\\n\\r\\n.mobile-nav-link.active[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--theme-accent);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.mobile-nav-link.active[_ngcontent-%COMP%]::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  left: 0;\\r\\n  top: 50%;\\r\\n  transform: translateY(-50%);\\r\\n  width: 4px;\\r\\n  height: 60%;\\r\\n  background: linear-gradient(180deg, var(--theme-accent), white);\\r\\n  border-radius: 0 2px 2px 0;\\r\\n}\\r\\n\\r\\n.mobile-actions[_ngcontent-%COMP%], .mobile-user-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n  margin-top: 16px;\\r\\n  padding-top: 16px;\\r\\n  border-top: 1px solid var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.mobile-login-btn[_ngcontent-%COMP%], .mobile-register-btn[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 8px;\\r\\n  padding: 12px 24px;\\r\\n  border-radius: 8px;\\r\\n  font-weight: 500;\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.mobile-register-btn[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.user-menu[_ngcontent-%COMP%] {\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.user-menu[_ngcontent-%COMP%]   .mat-menu-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  padding: 12px 16px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.submenu-arrow[_ngcontent-%COMP%] {\\r\\n  margin-left: auto;\\r\\n  font-size: 18px;\\r\\n}\\r\\n\\r\\n.theme-menu-content[_ngcontent-%COMP%] {\\r\\n  padding: 16px;\\r\\n  min-width: 200px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .nav-links[_ngcontent-%COMP%], .nav-actions[_ngcontent-%COMP%], .nav-user-menu[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .mobile-menu-toggle[_ngcontent-%COMP%] {\\r\\n    display: flex;\\r\\n  }\\r\\n\\r\\n  .nav-container[_ngcontent-%COMP%] {\\r\\n    padding: 0 16px;\\r\\n  }\\r\\n\\r\\n  .nav-brand[_ngcontent-%COMP%] {\\r\\n    font-size: 1.3rem;\\r\\n  }\\r\\n\\r\\n  .nav-brand[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n    font-size: 24px;\\r\\n    width: 24px;\\r\\n    height: 24px;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .nav-container[_ngcontent-%COMP%] {\\r\\n    padding: 0 12px;\\r\\n  }\\r\\n\\r\\n  .nav-brand[_ngcontent-%COMP%] {\\r\\n    font-size: 1.2rem;\\r\\n  }\\r\\n\\r\\n  .mobile-menu[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .unified-navigation[_ngcontent-%COMP%] {\\r\\n  background: rgba(30, 30, 30, 0.95);\\r\\n  border-bottom-color: var(--theme-accent-dark);\\r\\n}\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .mobile-menu[_ngcontent-%COMP%] {\\r\\n  background: rgba(30, 30, 30, 0.98);\\r\\n  border-bottom-color: var(--theme-accent-dark);\\r\\n}\\r\\n\\r\\n\\r\\n.nav-link[_ngcontent-%COMP%], .mobile-nav-link[_ngcontent-%COMP%], .login-btn[_ngcontent-%COMP%], .register-btn[_ngcontent-%COMP%] {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n\\r\\n.nav-link[_ngcontent-%COMP%]:focus, .mobile-nav-link[_ngcontent-%COMP%]:focus, .login-btn[_ngcontent-%COMP%]:focus, .register-btn[_ngcontent-%COMP%]:focus {\\r\\n  outline: 2px solid var(--theme-accent);\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n\\r\\nbody[_ngcontent-%COMP%] {\\r\\n  padding-top: 64px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiBA,aAAa,QAAwB,iBAAiB;AACvE,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;ICoCnCC,+BAAoD;IACPA;MAAAA;MAAA;MAAA,OAASA,uCAAiB;IAAA,EAAC;IACpEA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAC1BA,YACF;IAAAA,iBAAS;IACTA,kCAA8F;IAApDA;MAAAA;MAAA;MAAA,OAASA,0CAAoB;IAAA,EAAC;IACtEA,gCAAU;IAAAA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAS;;;;IALPA,eACF;IADEA,sDACF;IAGEA,eACF;IADEA,2DACF;;;;;IAMAA,gCAAyE;IACvEA,YACF;IAAAA,iBAAO;;;;;IADLA,eACF;IADEA,sFACF;;;;;IAIIA,0BAG0B;;;;IAFrBA,kEAA8B;;;;;IAIjCA,oCAAiC;IAAAA,8BAAc;IAAAA,iBAAW;;;;;IAN9DA,+BAA0E;IACxEA,kFAG0B;IAC1BA,mIAEc;IAChBA,iBAAM;;;;;IAPEA,eAA8B;IAA9BA,iDAA8B;;;;;;IAR1CA,+BAAqD;IAEnDA,8EAEO;;IAEPA,kCAAkF;IAChFA,4EAQM;;IACRA,iBAAS;IACTA,wCAAgD;IAElCA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAAqB;IAAAA,iBAAO;IAEpCA,mCAAoD;IACxCA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAA6B;IAAAA,iBAAO;IAE5CA,mCAA8C;IAClCA,0BAAS;IAAAA,iBAAW;IAC9BA,6BAAM;IAAAA,aAAqB;IAAAA,iBAAO;IAEpCA,mCAA6C;IACjCA,oBAAG;IAAAA,iBAAW;IACxBA,6BAAM;IAAAA,oCAAmB;IAAAA,iBAAO;IAElCA,mCAAsD;IAC1CA,wBAAO;IAAAA,iBAAW;IAC5BA,6BAAM;IAAAA,aAAkB;IAAAA,iBAAO;IAC/BA,qCAAgC;IAAAA,8BAAa;IAAAA,iBAAW;IAE1DA,mCAAsB;IACVA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,aAAuB;IAAAA,iBAAO;IAEtCA,+BAA2B;IAC3BA,mCAAyC;IAAnBA;MAAAA;MAAA;MAAA,OAASA,+BAAQ;IAAA,EAAC;IACtCA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAAkB;IAAAA,iBAAO;IAKnCA,yCAAkD;IAChBA;MAAA,OAASC,wBAAwB;IAAA,EAAC;IAChED,sCAAyC;IAC3CA,iBAAM;;;;;;IApDiBA,eAAuC;IAAvCA,6EAAuC;IAIxCA,eAA8B;IAA9BA,uCAA8B;IAC1BA,eAAuC;IAAvCA,6EAAuC;IAazDA,eAAqB;IAArBA,4CAAqB;IAIrBA,eAA6B;IAA7BA,oDAA6B;IAI7BA,eAAqB;IAArBA,4CAAqB;IAMPA,eAA+B;IAA/BA,wCAA+B;IAE7CA,eAAkB;IAAlBA,yCAAkB;IAKlBA,eAAuB;IAAvBA,8CAAuB;IAKvBA,eAAkB;IAAlBA,yCAAkB;;;;;;IA8C9BA,+BAAuD;IACVA;MAAAA;MAAA;MAAA,OAASA,wCAAiB;IAAA,EAAC;IACpEA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAC1BA,YACF;IAAAA,iBAAS;IACTA,kCAAqG;IAA3DA;MAAAA;MAAA;MAAA,OAASA,2CAAoB;IAAA,EAAC;IACtEA,gCAAU;IAAAA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAS;;;;IALPA,eACF;IADEA,sDACF;IAGEA,eACF;IADEA,2DACF;;;;;;IAIFA,+BAA2D;IAE7CA,sBAAM;IAAAA,iBAAW;IAC3BA,YACF;IAAAA,iBAAS;IACTA,kCAAyE;IAC7DA,sBAAM;IAAAA,iBAAW;IAC3BA,YACF;IAAAA,iBAAS;IACTA,kCAAmE;IACvDA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;IACTA,kCAA8D;IAA3CA;MAAAA;MAAA;MAAA,OAASA,+BAAQ;IAAA,EAAC;IACnCA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;IAC3BA,aACF;IAAAA,iBAAS;;;;IAbPA,eACF;IADEA,uDACF;IAGEA,eACF;IADEA,+DACF;IAGEA,eACF;IADEA,uDACF;IAGEA,eACF;IADEA,oDACF;;;AD/JN,OAAM,MAAOE,mBAAmB;EAO9BC,YACUC,MAAc,EACdC,KAAqB,EACtBC,WAAwB,EACxBC,CAAqB;IAHpB,WAAM,GAANH,MAAM;IACN,UAAK,GAALC,KAAK;IACN,gBAAW,GAAXC,WAAW;IACX,MAAC,GAADC,CAAC;IAVV,iBAAY,GAAG,EAAE;IACjB,qBAAgB,GAAG,KAAK;IACxB,eAAU,GAAG,KAAK;IAClB,kBAAa,GAAG,MAAM;IASpB;IACA,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,IAAI,CACrBV,MAAM,CAACW,KAAK,IAAIA,KAAK,YAAYZ,aAAa,CAAC,CAChD,CAACa,SAAS,CAAED,KAAK,IAAI;MACpB,IAAI,CAACE,YAAY,GAAIF,KAAuB,CAACG,GAAG;MAChD,MAAMC,WAAW,GAAG,IAAI,CAACC,UAAU;MACnC,IAAI,CAACA,UAAU,GAAG,IAAI,CAACH,YAAY,KAAK,GAAG,IAAI,IAAI,CAACA,YAAY,KAAK,OAAO,IAAI,IAAI,CAACA,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;MACrH,IAAI,CAACC,eAAe,EAAE;MAEtB;MACA,IAAI,IAAI,CAACF,UAAU,IAAI,CAACD,WAAW,EAAE;QACnC;QACA,IAAI,IAAI,CAACI,oBAAoB,EAAE;UAC7B,IAAI,CAACA,oBAAoB,CAACC,UAAU,EAAE;;QAExC,IAAI,CAACC,yBAAyB,EAAE;OACjC,MAAM,IAAI,CAAC,IAAI,CAACL,UAAU,IAAI,IAAI,CAACG,oBAAoB,EAAE;QACxD;QACA,IAAI,CAACA,oBAAoB,CAACC,UAAU,EAAE;QACtC,IAAI,CAACE,aAAa,GAAG,MAAM,CAAC,CAAC;;IAEjC,CAAC,CAAC;EACJ;;EAEAC,QAAQ;IACN;IACA,IAAI,CAACP,UAAU,GAAG,IAAI,CAACH,YAAY,KAAK,GAAG,IAAI,IAAI,CAACA,YAAY,KAAK,OAAO,IAAI,IAAI,CAACA,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;IAErH;IACA,IAAI,CAACZ,MAAM,CAACI,MAAM,CAACC,IAAI,CACrBV,MAAM,CAACW,KAAK,IAAIA,KAAK,YAAYZ,aAAa,CAAC,CAChD,CAACa,SAAS,CAAC,MAAK;MACf;MACA,MAAMY,QAAQ,GAAG,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACD,QAAQ;MAC7C,IAAIA,QAAQ,IAAI,IAAI,CAACR,UAAU,EAAE;QAC/BU,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,eAAe,CAACH,QAAQ,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACR,UAAU,EAAE;MACnB,IAAI,CAACK,yBAAyB,EAAE;;EAEpC;EAEAO,WAAW;IACT;IACA,IAAI,IAAI,CAACT,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,UAAU,EAAE;;EAE1C;EAEA;EACAS,eAAe;IACb,OAAO,IAAI,CAACtB,WAAW,CAACsB,eAAe,EAAE;EAC3C;EAEA;EACAC,iBAAiB,CAACC,SAAiB;IACjC,IAAI,CAACb,eAAe,EAAE;IAEtB,IAAI,IAAI,CAACF,UAAU,EAAE;MACnB;MACA,IAAI,CAACW,eAAe,CAACI,SAAS,CAAC;MAC/B;MACA,IAAI,CAACT,aAAa,GAAGS,SAAS;KAC/B,MAAM;MACL;MACA,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAAER,QAAQ,EAAEO;MAAS,CAAE,CAAC;;EAE5D;EAEA;EACQJ,eAAe,CAACI,SAAiB;IACvC,MAAME,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACJ,SAAS,CAAC;IAClD,IAAIE,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT,CAAC;;EAEN;EAEA;EACAC,eAAe;IACb,IAAI,CAACtB,eAAe,EAAE;IACtB,IAAI,CAACb,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAS,kBAAkB;IAChB,IAAI,CAACvB,eAAe,EAAE;IACtB,IAAI,CAACb,MAAM,CAAC2B,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;EACAU,gBAAgB;IACd,IAAI,CAACC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAzB,eAAe;IACb,IAAI,CAACyB,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EACAC,MAAM;IACJ,IAAI,CAACrC,WAAW,CAACqC,MAAM,EAAE,CAAChC,SAAS,CAAC;MAClCiC,IAAI,EAAE,MAAK;QACT,IAAI,CAACxC,MAAM,CAAC2B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC,CAAC;MACDc,KAAK,EAAE,MAAK;QACV;QACA,IAAI,CAACzC,MAAM,CAAC2B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACQX,yBAAyB;IAC/B;IACAK,UAAU,CAAC,MAAK;MACd,MAAMqB,QAAQ,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,CAAC;MACjE,MAAMC,eAAe,GAAGD,QAAQ,CAACE,GAAG,CAACC,EAAE,IAAIhB,QAAQ,CAACC,cAAc,CAACe,EAAE,CAAC,CAAC,CAAClD,MAAM,CAACmD,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;MAEjG,IAAIH,eAAe,CAACI,MAAM,KAAK,CAAC,EAAE;QAChC,OAAO,CAAC;;MAGV;MACA,IAAI,CAACjC,oBAAoB,GAAG,IAAIkC,oBAAoB,CACjDC,OAAO,IAAI;QACVA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;UACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;YACxB;YACA,IAAI,CAACnC,aAAa,GAAGkC,KAAK,CAACE,MAAM,CAACR,EAAE;;QAExC,CAAC,CAAC;MACJ,CAAC,EACD;QACE;QACAS,SAAS,EAAE,GAAG;QACd;QACAC,UAAU,EAAE;OACb,CACF;MAED;MACAZ,eAAe,CAACO,OAAO,CAACM,OAAO,IAAG;QAChC,IAAIA,OAAO,EAAE;UACX,IAAI,CAAC1C,oBAAqB,CAAC2C,OAAO,CAACD,OAAO,CAAC;;MAE/C,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAE,eAAe,CAAChC,SAAiB;IAC/B,OAAO,IAAI,CAACf,UAAU,IAAI,IAAI,CAACM,aAAa,KAAKS,SAAS;EAC5D;;;uBA7KW5B,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAA6D;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCVhCnE,8BAAgC;UAGLA;YAAA,OAASoE,sBAAkB,MAAM,CAAC;UAAA,EAAC;UACxDpE,gCAAU;UAAAA,4BAAY;UAAAA,iBAAW;UACjCA,4BAAM;UAAAA,YAAiB;UAAAA,iBAAO;UAIhCA,8BAAuB;UACFA;YAAA,OAASoE,sBAAkB,MAAM,CAAC;UAAA,EAAC;UAGpDpE,gCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,kCAEwD;UAFrCA;YAAA,OAASoE,sBAAkB,aAAa,CAAC;UAAA,EAAC;UAG3DpE,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UACTA,kCAEqD;UAFlCA;YAAA,OAASoE,sBAAkB,UAAU,CAAC;UAAA,EAAC;UAGxDpE,iCAAU;UAAAA,wBAAO;UAAAA,iBAAW;UAC5BA,aACF;UAAAA,iBAAS;UACTA,kCAEsD;UAFnCA;YAAA,OAASoE,sBAAkB,WAAW,CAAC;UAAA,EAAC;UAGzDpE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;UAIXA,sEASM;UAGNA,wEAwDM;UAGNA,kCAAkH;UAA/DA;YAAA,OAASoE,sBAAkB;UAAA,EAAC;UAC7EpE,iCAAU;UAAAA,aAAyC;UAAAA,iBAAW;UAKlEA,+BAAyD;UACpCA;YAAA,OAASoE,sBAAkB,MAAM,CAAC;UAAA,EAAC;UAGpDpE,iCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,kCAEwD;UAFrCA;YAAA,OAASoE,sBAAkB,aAAa,CAAC;UAAA,EAAC;UAG3DpE,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UACTA,kCAEqD;UAFlCA;YAAA,OAASoE,sBAAkB,UAAU,CAAC;UAAA,EAAC;UAGxDpE,iCAAU;UAAAA,wBAAO;UAAAA,iBAAW;UAC5BA,aACF;UAAAA,iBAAS;UACTA,kCAEsD;UAFnCA;YAAA,OAASoE,sBAAkB,WAAW,CAAC;UAAA,EAAC;UAGzDpE,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;UAGTA,uEASM;UAGNA,wEAiBM;UACRA,iBAAM;;;UAtKIA,eAAiB;UAAjBA,qCAAiB;UAOfA,eAAwC;UAAxCA,qDAAwC;UAE9CA,eACF;UADEA,oDACF;UAGQA,eAA+C;UAA/CA,4DAA+C;UAErDA,eACF;UADEA,2DACF;UAGQA,eAA4C;UAA5CA,yDAA4C;UAElDA,eACF;UADEA,wDACF;UAGQA,eAA6C;UAA7CA,0DAA6C;UAEnDA,eACF;UADEA,yDACF;UAIwBA,eAAwB;UAAxBA,6CAAwB;UAYtBA,eAAuB;UAAvBA,4CAAuB;UA2D6BA,eAAiC;UAAjCA,8CAAiC;UACrGA,eAAyC;UAAzCA,6DAAyC;UAK9BA,eAA+B;UAA/BA,4CAA+B;UAG9CA,eAAwC;UAAxCA,qDAAwC;UAE9CA,eACF;UADEA,oDACF;UAGQA,eAA+C;UAA/CA,4DAA+C;UAErDA,eACF;UADEA,2DACF;UAGQA,eAA4C;UAA5CA,yDAA4C;UAElDA,eACF;UADEA,wDACF;UAGQA,eAA6C;UAA7CA,0DAA6C;UAEnDA,eACF;UADEA,yDACF;UAG6BA,eAAwB;UAAxBA,6CAAwB;UAYnBA,eAAuB;UAAvBA,4CAAuB", "names": ["NavigationEnd", "filter", "i0", "$event", "NavigationComponent", "constructor", "router", "route", "authService", "t", "events", "pipe", "event", "subscribe", "currentRoute", "url", "wasHomePage", "isHomePage", "startsWith", "closeMobileMenu", "intersectionObserver", "disconnect", "initializeScrollDetection", "activeSection", "ngOnInit", "fragment", "snapshot", "setTimeout", "scrollToSection", "ngOnDestroy", "isAuthenticated", "navigateToSection", "sectionId", "navigate", "element", "document", "getElementById", "scrollIntoView", "behavior", "block", "inline", "navigateToLogin", "navigateToRegister", "toggleMobileMenu", "isMobileMenuOpen", "logout", "next", "error", "sections", "sectionElements", "map", "id", "el", "length", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "threshold", "rootMargin", "section", "observe", "isActiveSection", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\navigation\\navigation.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\navigation\\navigation.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';\r\nimport { Router, NavigationEnd, ActivatedRoute } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\nimport { AuthService } from '../../auth/services/auth.service';\r\nimport { TranslationService } from '../../core/i18n/translation.service';\r\n\r\n@Component({\r\n  selector: 'app-navigation',\r\n  templateUrl: './navigation.component.html',\r\n  styleUrls: ['./navigation.component.css']\r\n})\r\nexport class NavigationComponent implements OnInit, OnDestroy {\r\n  currentRoute = '';\r\n  isMobileMenuOpen = false;\r\n  isHomePage = false;\r\n  activeSection = 'hero';\r\n  private intersectionObserver?: IntersectionObserver;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    public authService: AuthService,\r\n    public t: TranslationService\r\n  ) {\r\n    // Track current route\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe((event) => {\r\n      this.currentRoute = (event as NavigationEnd).url;\r\n      const wasHomePage = this.isHomePage;\r\n      this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\r\n      this.closeMobileMenu();\r\n\r\n      // Initialize scroll detection when navigating to home page\r\n      if (this.isHomePage && !wasHomePage) {\r\n        // Clean up previous observer if it exists\r\n        if (this.intersectionObserver) {\r\n          this.intersectionObserver.disconnect();\r\n        }\r\n        this.initializeScrollDetection();\r\n      } else if (!this.isHomePage && this.intersectionObserver) {\r\n        // Clean up observer when leaving home page\r\n        this.intersectionObserver.disconnect();\r\n        this.activeSection = 'hero'; // Reset to default\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Check initial route\r\n    this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\r\n\r\n    // Handle fragment navigation when arriving at home page\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe(() => {\r\n      // Check for fragment after navigation\r\n      const fragment = this.route.snapshot.fragment;\r\n      if (fragment && this.isHomePage) {\r\n        setTimeout(() => {\r\n          this.scrollToSection(fragment);\r\n        }, 300); // Increased delay to ensure DOM is ready\r\n      }\r\n    });\r\n\r\n    // Initialize scroll detection when on home page\r\n    if (this.isHomePage) {\r\n      this.initializeScrollDetection();\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up intersection observer\r\n    if (this.intersectionObserver) {\r\n      this.intersectionObserver.disconnect();\r\n    }\r\n  }\r\n\r\n  // Check if user is authenticated\r\n  isAuthenticated(): boolean {\r\n    return this.authService.isAuthenticated();\r\n  }\r\n\r\n  // Navigation methods - adapt behavior based on current page\r\n  navigateToSection(sectionId: string): void {\r\n    this.closeMobileMenu();\r\n\r\n    if (this.isHomePage) {\r\n      // If on home page, scroll to section\r\n      this.scrollToSection(sectionId);\r\n      // Update active section immediately for better UX\r\n      this.activeSection = sectionId;\r\n    } else {\r\n      // If on other page, navigate to home with fragment\r\n      this.router.navigate(['/home'], { fragment: sectionId });\r\n    }\r\n  }\r\n\r\n  // Smooth scroll to section (only works when on home page)\r\n  private scrollToSection(sectionId: string): void {\r\n    const element = document.getElementById(sectionId);\r\n    if (element) {\r\n      element.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n    }\r\n  }\r\n\r\n  // Authentication navigation\r\n  navigateToLogin(): void {\r\n    this.closeMobileMenu();\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    this.closeMobileMenu();\r\n    this.router.navigate(['/register']);\r\n  }\r\n\r\n  // Mobile menu controls\r\n  toggleMobileMenu(): void {\r\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\r\n  }\r\n\r\n  closeMobileMenu(): void {\r\n    this.isMobileMenuOpen = false;\r\n  }\r\n\r\n  // Logout functionality\r\n  logout(): void {\r\n    this.authService.logout().subscribe({\r\n      next: () => {\r\n        this.router.navigate(['/home']);\r\n      },\r\n      error: () => {\r\n        // Still navigate to home even if logout fails\r\n        this.router.navigate(['/home']);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Initialize scroll detection using Intersection Observer\r\n  private initializeScrollDetection(): void {\r\n    // Wait for DOM to be ready\r\n    setTimeout(() => {\r\n      const sections = ['hero', 'astrologers', 'articles', 'horoscope'];\r\n      const sectionElements = sections.map(id => document.getElementById(id)).filter(el => el !== null);\r\n\r\n      if (sectionElements.length === 0) {\r\n        return; // No sections found, skip initialization\r\n      }\r\n\r\n      // Create intersection observer\r\n      this.intersectionObserver = new IntersectionObserver(\r\n        (entries) => {\r\n          entries.forEach(entry => {\r\n            if (entry.isIntersecting) {\r\n              // Update active section when section comes into view\r\n              this.activeSection = entry.target.id;\r\n            }\r\n          });\r\n        },\r\n        {\r\n          // Trigger when section is 30% visible\r\n          threshold: 0.3,\r\n          // Offset from top to account for fixed navigation\r\n          rootMargin: '-80px 0px -50% 0px'\r\n        }\r\n      );\r\n\r\n      // Observe all sections\r\n      sectionElements.forEach(section => {\r\n        if (section) {\r\n          this.intersectionObserver!.observe(section);\r\n        }\r\n      });\r\n    }, 500);\r\n  }\r\n\r\n  // Check if a navigation link is active\r\n  isActiveSection(sectionId: string): boolean {\r\n    return this.isHomePage && this.activeSection === sectionId;\r\n  }\r\n}\r\n", "<!-- Unified Navigation Menu -->\r\n<nav class=\"unified-navigation\">\r\n  <div class=\"nav-container\">\r\n    <!-- Brand -->\r\n    <div class=\"nav-brand\" (click)=\"navigateToSection('hero')\">\r\n      <mat-icon>auto_awesome</mat-icon>\r\n      <span>{{ t.nav.brand }}</span>\r\n    </div>\r\n\r\n    <!-- Navigation Links -->\r\n    <div class=\"nav-links\">\r\n      <button mat-button (click)=\"navigateToSection('hero')\"\r\n              class=\"nav-link\"\r\n              [class.active]=\"isActiveSection('hero')\">\r\n        <mat-icon>home</mat-icon>\r\n        {{ t.home.nav.home }}\r\n      </button>\r\n      <button mat-button (click)=\"navigateToSection('astrologers')\"\r\n              class=\"nav-link\"\r\n              [class.active]=\"isActiveSection('astrologers')\">\r\n        <mat-icon>people</mat-icon>\r\n        {{ t.home.nav.astrologers }}\r\n      </button>\r\n      <button mat-button (click)=\"navigateToSection('articles')\"\r\n              class=\"nav-link\"\r\n              [class.active]=\"isActiveSection('articles')\">\r\n        <mat-icon>article</mat-icon>\r\n        {{ t.home.nav.articles }}\r\n      </button>\r\n      <button mat-button (click)=\"navigateToSection('horoscope')\"\r\n              class=\"nav-link\"\r\n              [class.active]=\"isActiveSection('horoscope')\">\r\n        <mat-icon>stars</mat-icon>\r\n        {{ t.home.nav.horoscope }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Authentication Actions for Anonymous Users -->\r\n    <div class=\"nav-actions\" *ngIf=\"!isAuthenticated()\">\r\n      <button mat-stroked-button color=\"primary\" (click)=\"navigateToLogin()\" class=\"login-btn\">\r\n        <mat-icon>login</mat-icon>\r\n        {{ t.common.login }}\r\n      </button>\r\n      <button mat-raised-button color=\"primary\" (click)=\"navigateToRegister()\" class=\"register-btn\">\r\n        <mat-icon>star</mat-icon>\r\n        {{ t.home.startJourney }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- User Menu for Authenticated Users -->\r\n    <div class=\"nav-user-menu\" *ngIf=\"isAuthenticated()\">\r\n      <!-- User Info -->\r\n      <span class=\"user-info\" *ngIf=\"authService.currentUser$ | async as user\">\r\n        {{ t.dashboard.welcome }}, {{ user.firstName }}!\r\n      </span>\r\n\r\n      <button mat-icon-button [matMenuTriggerFor]=\"userMenu\" class=\"user-avatar-button\">\r\n        <div class=\"user-avatar\" *ngIf=\"authService.currentUser$ | async as user\">\r\n          <img *ngIf=\"user.profilePictureUrl; else defaultAvatar\"\r\n               [src]=\"user.profilePictureUrl\"\r\n               [alt]=\"user.firstName + ' ' + user.lastName\"\r\n               class=\"avatar-image\">\r\n          <ng-template #defaultAvatar>\r\n            <mat-icon class=\"default-avatar\">account_circle</mat-icon>\r\n          </ng-template>\r\n        </div>\r\n      </button>\r\n      <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\r\n        <button mat-menu-item routerLink=\"/profile/edit\">\r\n          <mat-icon>person</mat-icon>\r\n          <span>{{ t.nav.myProfile }}</span>\r\n        </button>\r\n        <button mat-menu-item routerLink=\"/profiles/search\">\r\n          <mat-icon>search</mat-icon>\r\n          <span>{{ t.nav.findProfessionals }}</span>\r\n        </button>\r\n        <button mat-menu-item routerLink=\"/dashboard\">\r\n          <mat-icon>dashboard</mat-icon>\r\n          <span>{{ t.nav.dashboard }}</span>\r\n        </button>\r\n        <button mat-menu-item routerLink=\"/test-api\">\r\n          <mat-icon>api</mat-icon>\r\n          <span>Test API Connection</span>\r\n        </button>\r\n        <button mat-menu-item [matMenuTriggerFor]=\"themeMenu\">\r\n          <mat-icon>palette</mat-icon>\r\n          <span>{{ t.nav.themes }}</span>\r\n          <mat-icon class=\"submenu-arrow\">chevron_right</mat-icon>\r\n        </button>\r\n        <button mat-menu-item>\r\n          <mat-icon>settings</mat-icon>\r\n          <span>{{ t.common.settings }}</span>\r\n        </button>\r\n        <mat-divider></mat-divider>\r\n        <button mat-menu-item (click)=\"logout()\">\r\n          <mat-icon>logout</mat-icon>\r\n          <span>{{ t.nav.logout }}</span>\r\n        </button>\r\n      </mat-menu>\r\n\r\n      <!-- Theme submenu -->\r\n      <mat-menu #themeMenu=\"matMenu\" class=\"theme-menu\">\r\n        <div class=\"theme-menu-content\" (click)=\"$event.stopPropagation()\">\r\n          <app-theme-selector></app-theme-selector>\r\n        </div>\r\n      </mat-menu>\r\n    </div>\r\n\r\n    <!-- Mobile Menu Toggle -->\r\n    <button mat-icon-button class=\"mobile-menu-toggle\" (click)=\"toggleMobileMenu()\" [class.active]=\"isMobileMenuOpen\">\r\n      <mat-icon>{{ isMobileMenuOpen ? 'close' : 'menu' }}</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Mobile Menu -->\r\n  <div class=\"mobile-menu\" [class.open]=\"isMobileMenuOpen\">\r\n    <button mat-button (click)=\"navigateToSection('hero')\"\r\n            class=\"mobile-nav-link\"\r\n            [class.active]=\"isActiveSection('hero')\">\r\n      <mat-icon>home</mat-icon>\r\n      {{ t.home.nav.home }}\r\n    </button>\r\n    <button mat-button (click)=\"navigateToSection('astrologers')\"\r\n            class=\"mobile-nav-link\"\r\n            [class.active]=\"isActiveSection('astrologers')\">\r\n      <mat-icon>people</mat-icon>\r\n      {{ t.home.nav.astrologers }}\r\n    </button>\r\n    <button mat-button (click)=\"navigateToSection('articles')\"\r\n            class=\"mobile-nav-link\"\r\n            [class.active]=\"isActiveSection('articles')\">\r\n      <mat-icon>article</mat-icon>\r\n      {{ t.home.nav.articles }}\r\n    </button>\r\n    <button mat-button (click)=\"navigateToSection('horoscope')\"\r\n            class=\"mobile-nav-link\"\r\n            [class.active]=\"isActiveSection('horoscope')\">\r\n      <mat-icon>stars</mat-icon>\r\n      {{ t.home.nav.horoscope }}\r\n    </button>\r\n\r\n    <!-- Mobile Authentication Actions for Anonymous Users -->\r\n    <div class=\"mobile-actions\" *ngIf=\"!isAuthenticated()\">\r\n      <button mat-stroked-button color=\"primary\" (click)=\"navigateToLogin()\" class=\"mobile-login-btn\">\r\n        <mat-icon>login</mat-icon>\r\n        {{ t.common.login }}\r\n      </button>\r\n      <button mat-raised-button color=\"primary\" (click)=\"navigateToRegister()\" class=\"mobile-register-btn\">\r\n        <mat-icon>star</mat-icon>\r\n        {{ t.home.startJourney }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Mobile User Actions for Authenticated Users -->\r\n    <div class=\"mobile-user-actions\" *ngIf=\"isAuthenticated()\">\r\n      <button mat-button routerLink=\"/profile/edit\" class=\"mobile-nav-link\">\r\n        <mat-icon>person</mat-icon>\r\n        {{ t.nav.myProfile }}\r\n      </button>\r\n      <button mat-button routerLink=\"/profiles/search\" class=\"mobile-nav-link\">\r\n        <mat-icon>search</mat-icon>\r\n        {{ t.nav.findProfessionals }}\r\n      </button>\r\n      <button mat-button routerLink=\"/dashboard\" class=\"mobile-nav-link\">\r\n        <mat-icon>dashboard</mat-icon>\r\n        {{ t.nav.dashboard }}\r\n      </button>\r\n      <button mat-button (click)=\"logout()\" class=\"mobile-nav-link\">\r\n        <mat-icon>logout</mat-icon>\r\n        {{ t.nav.logout }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</nav>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}