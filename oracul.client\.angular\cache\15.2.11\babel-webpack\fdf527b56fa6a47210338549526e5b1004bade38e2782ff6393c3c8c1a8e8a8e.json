{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AvatarService {\n  constructor() {\n    this.defaultAvatarColors = ['#d2a6d0', '#67455c', '#e6dbec', '#a07ba0', '#3f2f4e', '#8e7cc3', '#b19cd9', '#6a4c93', '#9d4edd', '#c77dff' // light violet\n    ];\n  }\n  /**\r\n   * Get avatar URL with fallback to generated avatar\r\n   */\n  getAvatarUrl(profilePhotoUrl, firstName, lastName) {\n    if (profilePhotoUrl && profilePhotoUrl.trim()) {\n      return profilePhotoUrl;\n    }\n    return this.generateAvatarUrl(firstName, lastName);\n  }\n  /**\r\n   * Generate a data URL for an avatar with initials\r\n   */\n  generateAvatarUrl(firstName, lastName) {\n    const initials = this.getInitials(firstName, lastName);\n    const backgroundColor = this.getColorForName(firstName + lastName);\n    const textColor = this.getContrastColor(backgroundColor);\n    const svg = this.createAvatarSvg(initials, backgroundColor, textColor);\n    return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;\n  }\n  /**\r\n   * Get initials from first and last name\r\n   */\n  getInitials(firstName, lastName) {\n    const first = firstName?.charAt(0)?.toUpperCase() || '';\n    const last = lastName?.charAt(0)?.toUpperCase() || '';\n    return first + last || '?';\n  }\n  /**\r\n   * Get a consistent color based on the name\r\n   */\n  getColorForName(name) {\n    let hash = 0;\n    for (let i = 0; i < name.length; i++) {\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    const index = Math.abs(hash) % this.defaultAvatarColors.length;\n    return this.defaultAvatarColors[index];\n  }\n  /**\r\n   * Get contrasting text color (white or dark) based on background\r\n   */\n  getContrastColor(backgroundColor) {\n    // Convert hex to RGB\n    const hex = backgroundColor.replace('#', '');\n    const r = parseInt(hex.substr(0, 2), 16);\n    const g = parseInt(hex.substr(2, 2), 16);\n    const b = parseInt(hex.substr(4, 2), 16);\n    // Calculate luminance\n    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;\n    return luminance > 0.5 ? '#333333' : '#ffffff';\n  }\n  /**\r\n   * Create SVG avatar with initials\r\n   */\n  createAvatarSvg(initials, backgroundColor, textColor) {\n    return `\n      <svg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\">\n        <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"${backgroundColor}\"/>\n        <text x=\"50\" y=\"50\" font-family=\"Arial, sans-serif\" font-size=\"36\" font-weight=\"500\" \n              text-anchor=\"middle\" dominant-baseline=\"central\" fill=\"${textColor}\">\n          ${initials}\n        </text>\n      </svg>\n    `.trim();\n  }\n  /**\r\n   * Handle image error by setting a generated avatar\r\n   */\n  onImageError(event, firstName, lastName) {\n    const target = event.target;\n    if (target) {\n      target.src = this.generateAvatarUrl(firstName, lastName);\n    }\n  }\n  /**\r\n   * Get a mystical/astrological themed avatar for oracle profiles\r\n   */\n  getMysticalAvatarUrl(firstName, lastName) {\n    const initials = this.getInitials(firstName, lastName);\n    const backgroundColor = this.getColorForName(firstName + lastName);\n    const textColor = this.getContrastColor(backgroundColor);\n    const svg = this.createMysticalAvatarSvg(initials, backgroundColor, textColor);\n    return `data:image/svg+xml;base64,${btoa(svg)}`;\n  }\n  /**\r\n   * Create mystical-themed SVG avatar with stars and moon elements\r\n   */\n  createMysticalAvatarSvg(initials, backgroundColor, textColor) {\n    return `\n      <svg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\">\n        <defs>\n          <radialGradient id=\"mysticalGrad\" cx=\"50%\" cy=\"30%\" r=\"70%\">\n            <stop offset=\"0%\" style=\"stop-color:${backgroundColor};stop-opacity:1\" />\n            <stop offset=\"100%\" style=\"stop-color:${this.darkenColor(backgroundColor, 20)};stop-opacity:1\" />\n          </radialGradient>\n        </defs>\n        <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"url(#mysticalGrad)\"/>\n        \n        <!-- Stars -->\n        <polygon points=\"20,25 22,30 27,30 23,33 25,38 20,35 15,38 17,33 13,30 18,30\" \n                 fill=\"${textColor}\" opacity=\"0.3\" transform=\"scale(0.4)\"/>\n        <polygon points=\"75,20 77,25 82,25 78,28 80,33 75,30 70,33 72,28 68,25 73,25\" \n                 fill=\"${textColor}\" opacity=\"0.3\" transform=\"scale(0.3)\"/>\n        <polygon points=\"80,70 82,75 87,75 83,78 85,83 80,80 75,83 77,78 73,75 78,75\" \n                 fill=\"${textColor}\" opacity=\"0.2\" transform=\"scale(0.3)\"/>\n        \n        <!-- Crescent moon -->\n        <path d=\"M 15 15 A 8 8 0 1 1 15 25 A 6 6 0 1 0 15 15\" \n              fill=\"${textColor}\" opacity=\"0.4\"/>\n        \n        <text x=\"50\" y=\"50\" font-family=\"Arial, sans-serif\" font-size=\"32\" font-weight=\"600\" \n              text-anchor=\"middle\" dominant-baseline=\"central\" fill=\"${textColor}\">\n          ${initials}\n        </text>\n      </svg>\n    `.trim();\n  }\n  /**\r\n   * Darken a hex color by a percentage\r\n   */\n  darkenColor(color, percent) {\n    const hex = color.replace('#', '');\n    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.round(255 * percent / 100));\n    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.round(255 * percent / 100));\n    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.round(255 * percent / 100));\n    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\n  }\n  static {\n    this.ɵfac = function AvatarService_Factory(t) {\n      return new (t || AvatarService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AvatarService,\n      factory: AvatarService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,aAAa;EAcxBC;IAbiB,wBAAmB,GAAG,CACrC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;EAEc;EAEf;;;EAGAC,YAAY,CAACC,eAA0C,EAAEC,SAAiB,EAAEC,QAAgB;IAC1F,IAAIF,eAAe,IAAIA,eAAe,CAACG,IAAI,EAAE,EAAE;MAC7C,OAAOH,eAAe;;IAGxB,OAAO,IAAI,CAACI,iBAAiB,CAACH,SAAS,EAAEC,QAAQ,CAAC;EACpD;EAEA;;;EAGAE,iBAAiB,CAACH,SAAiB,EAAEC,QAAgB;IACnD,MAAMG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACL,SAAS,EAAEC,QAAQ,CAAC;IACtD,MAAMK,eAAe,GAAG,IAAI,CAACC,eAAe,CAACP,SAAS,GAAGC,QAAQ,CAAC;IAClE,MAAMO,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACH,eAAe,CAAC;IAExD,MAAMI,GAAG,GAAG,IAAI,CAACC,eAAe,CAACP,QAAQ,EAAEE,eAAe,EAAEE,SAAS,CAAC;IACtE,OAAO,6BAA6BI,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAACJ,GAAG,CAAC,CAAC,CAAC,EAAE;EAC/E;EAEA;;;EAGQL,WAAW,CAACL,SAAiB,EAAEC,QAAgB;IACrD,MAAMc,KAAK,GAAGf,SAAS,EAAEgB,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE,IAAI,EAAE;IACvD,MAAMC,IAAI,GAAGjB,QAAQ,EAAEe,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE,IAAI,EAAE;IACrD,OAAOF,KAAK,GAAGG,IAAI,IAAI,GAAG;EAC5B;EAEA;;;EAGQX,eAAe,CAACY,IAAY;IAClC,IAAIC,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCD,IAAI,GAAGD,IAAI,CAACI,UAAU,CAACF,CAAC,CAAC,IAAI,CAACD,IAAI,IAAI,CAAC,IAAIA,IAAI,CAAC;;IAElD,MAAMI,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,GAAG,IAAI,CAACO,mBAAmB,CAACL,MAAM;IAC9D,OAAO,IAAI,CAACK,mBAAmB,CAACH,KAAK,CAAC;EACxC;EAEA;;;EAGQf,gBAAgB,CAACH,eAAuB;IAC9C;IACA,MAAMsB,GAAG,GAAGtB,eAAe,CAACuB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC5C,MAAMC,CAAC,GAAGC,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACxC,MAAMC,CAAC,GAAGF,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACxC,MAAME,CAAC,GAAGH,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAExC;IACA,MAAMG,SAAS,GAAG,CAAC,KAAK,GAAGL,CAAC,GAAG,KAAK,GAAGG,CAAC,GAAG,KAAK,GAAGC,CAAC,IAAI,GAAG;IAE3D,OAAOC,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;EAChD;EAEA;;;EAGQxB,eAAe,CAACP,QAAgB,EAAEE,eAAuB,EAAEE,SAAiB;IAClF,OAAO;;+CAEoCF,eAAe;;uEAESE,SAAS;YACpEJ,QAAQ;;;KAGf,CAACF,IAAI,EAAE;EACV;EAEA;;;EAGAkC,YAAY,CAACC,KAAY,EAAErC,SAAiB,EAAEC,QAAgB;IAC5D,MAAMqC,MAAM,GAAGD,KAAK,CAACC,MAA0B;IAC/C,IAAIA,MAAM,EAAE;MACVA,MAAM,CAACC,GAAG,GAAG,IAAI,CAACpC,iBAAiB,CAACH,SAAS,EAAEC,QAAQ,CAAC;;EAE5D;EAEA;;;EAGAuC,oBAAoB,CAACxC,SAAiB,EAAEC,QAAgB;IACtD,MAAMG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACL,SAAS,EAAEC,QAAQ,CAAC;IACtD,MAAMK,eAAe,GAAG,IAAI,CAACC,eAAe,CAACP,SAAS,GAAGC,QAAQ,CAAC;IAClE,MAAMO,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACH,eAAe,CAAC;IAExD,MAAMI,GAAG,GAAG,IAAI,CAAC+B,uBAAuB,CAACrC,QAAQ,EAAEE,eAAe,EAAEE,SAAS,CAAC;IAC9E,OAAO,6BAA6BI,IAAI,CAACF,GAAG,CAAC,EAAE;EACjD;EAEA;;;EAGQ+B,uBAAuB,CAACrC,QAAgB,EAAEE,eAAuB,EAAEE,SAAiB;IAC1F,OAAO;;;;kDAIuCF,eAAe;oDACb,IAAI,CAACoC,WAAW,CAACpC,eAAe,EAAE,EAAE,CAAC;;;;;;;yBAOhEE,SAAS;;yBAETA,SAAS;;yBAETA,SAAS;;;;sBAIZA,SAAS;;;uEAGwCA,SAAS;YACpEJ,QAAQ;;;KAGf,CAACF,IAAI,EAAE;EACV;EAEA;;;EAGQwC,WAAW,CAACC,KAAa,EAAEC,OAAe;IAChD,MAAMhB,GAAG,GAAGe,KAAK,CAACd,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAClC,MAAMC,CAAC,GAAGL,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACqB,KAAK,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,CAAC,CAAC;IACvF,MAAMX,CAAC,GAAGR,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACqB,KAAK,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,CAAC,CAAC;IACvF,MAAMV,CAAC,GAAGT,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGP,IAAI,CAACqB,KAAK,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,CAAC,CAAC;IAEvF,OAAO,IAAId,CAAC,CAACiB,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGf,CAAC,CAACc,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGd,CAAC,CAACa,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClH;;;uBA7JWpD,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAqD,SAAbrD,aAAa;MAAAsD,YAFZ;IAAM;EAAA", "names": ["AvatarService", "constructor", "getAvatarUrl", "profilePhotoUrl", "firstName", "lastName", "trim", "generateAvatarUrl", "initials", "getInitials", "backgroundColor", "getColorForName", "textColor", "getContrastColor", "svg", "createAvatarSvg", "btoa", "unescape", "encodeURIComponent", "first", "char<PERSON>t", "toUpperCase", "last", "name", "hash", "i", "length", "charCodeAt", "index", "Math", "abs", "defaultAvatarColors", "hex", "replace", "r", "parseInt", "substr", "g", "b", "luminance", "onImageError", "event", "target", "src", "getMysticalAvatarUrl", "createMysticalAvatarSvg", "darkenColor", "color", "percent", "max", "round", "toString", "padStart", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\services\\avatar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AvatarService {\r\n  private readonly defaultAvatarColors = [\r\n    '#d2a6d0', // soft lilac\r\n    '#67455c', // dark purple\r\n    '#e6dbec', // very light lilac\r\n    '#a07ba0', // pink-lilac\r\n    '#3f2f4e', // deep mystical purple\r\n    '#8e7cc3', // lavender\r\n    '#b19cd9', // light purple\r\n    '#6a4c93', // royal purple\r\n    '#9d4edd', // bright purple\r\n    '#c77dff'  // light violet\r\n  ];\r\n\r\n  constructor() {}\r\n\r\n  /**\r\n   * Get avatar URL with fallback to generated avatar\r\n   */\r\n  getAvatarUrl(profilePhotoUrl: string | null | undefined, firstName: string, lastName: string): string {\r\n    if (profilePhotoUrl && profilePhotoUrl.trim()) {\r\n      return profilePhotoUrl;\r\n    }\r\n    \r\n    return this.generateAvatarUrl(firstName, lastName);\r\n  }\r\n\r\n  /**\r\n   * Generate a data URL for an avatar with initials\r\n   */\r\n  generateAvatarUrl(firstName: string, lastName: string): string {\r\n    const initials = this.getInitials(firstName, lastName);\r\n    const backgroundColor = this.getColorForName(firstName + lastName);\r\n    const textColor = this.getContrastColor(backgroundColor);\r\n    \r\n    const svg = this.createAvatarSvg(initials, backgroundColor, textColor);\r\n    return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;\r\n  }\r\n\r\n  /**\r\n   * Get initials from first and last name\r\n   */\r\n  private getInitials(firstName: string, lastName: string): string {\r\n    const first = firstName?.charAt(0)?.toUpperCase() || '';\r\n    const last = lastName?.charAt(0)?.toUpperCase() || '';\r\n    return first + last || '?';\r\n  }\r\n\r\n  /**\r\n   * Get a consistent color based on the name\r\n   */\r\n  private getColorForName(name: string): string {\r\n    let hash = 0;\r\n    for (let i = 0; i < name.length; i++) {\r\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\r\n    }\r\n    const index = Math.abs(hash) % this.defaultAvatarColors.length;\r\n    return this.defaultAvatarColors[index];\r\n  }\r\n\r\n  /**\r\n   * Get contrasting text color (white or dark) based on background\r\n   */\r\n  private getContrastColor(backgroundColor: string): string {\r\n    // Convert hex to RGB\r\n    const hex = backgroundColor.replace('#', '');\r\n    const r = parseInt(hex.substr(0, 2), 16);\r\n    const g = parseInt(hex.substr(2, 2), 16);\r\n    const b = parseInt(hex.substr(4, 2), 16);\r\n    \r\n    // Calculate luminance\r\n    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;\r\n    \r\n    return luminance > 0.5 ? '#333333' : '#ffffff';\r\n  }\r\n\r\n  /**\r\n   * Create SVG avatar with initials\r\n   */\r\n  private createAvatarSvg(initials: string, backgroundColor: string, textColor: string): string {\r\n    return `\r\n      <svg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"${backgroundColor}\"/>\r\n        <text x=\"50\" y=\"50\" font-family=\"Arial, sans-serif\" font-size=\"36\" font-weight=\"500\" \r\n              text-anchor=\"middle\" dominant-baseline=\"central\" fill=\"${textColor}\">\r\n          ${initials}\r\n        </text>\r\n      </svg>\r\n    `.trim();\r\n  }\r\n\r\n  /**\r\n   * Handle image error by setting a generated avatar\r\n   */\r\n  onImageError(event: Event, firstName: string, lastName: string): void {\r\n    const target = event.target as HTMLImageElement;\r\n    if (target) {\r\n      target.src = this.generateAvatarUrl(firstName, lastName);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get a mystical/astrological themed avatar for oracle profiles\r\n   */\r\n  getMysticalAvatarUrl(firstName: string, lastName: string): string {\r\n    const initials = this.getInitials(firstName, lastName);\r\n    const backgroundColor = this.getColorForName(firstName + lastName);\r\n    const textColor = this.getContrastColor(backgroundColor);\r\n    \r\n    const svg = this.createMysticalAvatarSvg(initials, backgroundColor, textColor);\r\n    return `data:image/svg+xml;base64,${btoa(svg)}`;\r\n  }\r\n\r\n  /**\r\n   * Create mystical-themed SVG avatar with stars and moon elements\r\n   */\r\n  private createMysticalAvatarSvg(initials: string, backgroundColor: string, textColor: string): string {\r\n    return `\r\n      <svg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <defs>\r\n          <radialGradient id=\"mysticalGrad\" cx=\"50%\" cy=\"30%\" r=\"70%\">\r\n            <stop offset=\"0%\" style=\"stop-color:${backgroundColor};stop-opacity:1\" />\r\n            <stop offset=\"100%\" style=\"stop-color:${this.darkenColor(backgroundColor, 20)};stop-opacity:1\" />\r\n          </radialGradient>\r\n        </defs>\r\n        <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"url(#mysticalGrad)\"/>\r\n        \r\n        <!-- Stars -->\r\n        <polygon points=\"20,25 22,30 27,30 23,33 25,38 20,35 15,38 17,33 13,30 18,30\" \r\n                 fill=\"${textColor}\" opacity=\"0.3\" transform=\"scale(0.4)\"/>\r\n        <polygon points=\"75,20 77,25 82,25 78,28 80,33 75,30 70,33 72,28 68,25 73,25\" \r\n                 fill=\"${textColor}\" opacity=\"0.3\" transform=\"scale(0.3)\"/>\r\n        <polygon points=\"80,70 82,75 87,75 83,78 85,83 80,80 75,83 77,78 73,75 78,75\" \r\n                 fill=\"${textColor}\" opacity=\"0.2\" transform=\"scale(0.3)\"/>\r\n        \r\n        <!-- Crescent moon -->\r\n        <path d=\"M 15 15 A 8 8 0 1 1 15 25 A 6 6 0 1 0 15 15\" \r\n              fill=\"${textColor}\" opacity=\"0.4\"/>\r\n        \r\n        <text x=\"50\" y=\"50\" font-family=\"Arial, sans-serif\" font-size=\"32\" font-weight=\"600\" \r\n              text-anchor=\"middle\" dominant-baseline=\"central\" fill=\"${textColor}\">\r\n          ${initials}\r\n        </text>\r\n      </svg>\r\n    `.trim();\r\n  }\r\n\r\n  /**\r\n   * Darken a hex color by a percentage\r\n   */\r\n  private darkenColor(color: string, percent: number): string {\r\n    const hex = color.replace('#', '');\r\n    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.round(255 * percent / 100));\r\n    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.round(255 * percent / 100));\r\n    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.round(255 * percent / 100));\r\n    \r\n    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}