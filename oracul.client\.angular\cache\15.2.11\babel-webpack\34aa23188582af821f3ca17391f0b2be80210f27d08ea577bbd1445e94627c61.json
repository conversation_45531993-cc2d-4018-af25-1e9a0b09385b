{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ExperienceManagementComponent {\n  static {\n    this.ɵfac = function ExperienceManagementComponent_Factory(t) {\n      return new (t || ExperienceManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExperienceManagementComponent,\n      selectors: [[\"app-experience-management\"]],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"experience-management\"]],\n      template: function ExperienceManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"work\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Manage Experience \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n          i0.ɵɵtext(9, \"Experience management component - Coming soon!\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".experience-management[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHJvZmlsZS9jb21wb25lbnRzL2V4cGVyaWVuY2UtbWFuYWdlbWVudC9leHBlcmllbmNlLW1hbmFnZW1lbnQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSSx5QkFBeUIsYUFBYSxFQUFFIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmV4cGVyaWVuY2UtbWFuYWdlbWVudCB7IHBhZGRpbmc6IDIwcHg7IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAuBA,OAAM,MAAOA,6BAA6B;;;uBAA7BA,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAlBtCC,8BAAmC;UAIjBA,oBAAI;UAAAA,iBAAW;UACzBA,mCACF;UAAAA,iBAAiB;UAEnBA,wCAAkB;UACbA,8DAA8C;UAAAA,iBAAI", "names": ["ExperienceManagementComponent", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\experience-management\\experience-management.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-experience-management',\r\n  template: `\r\n    <div class=\"experience-management\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>work</mat-icon>\r\n            Manage Experience\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <p>Experience management component - Coming soon!</p>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .experience-management { padding: 20px; }\r\n  `]\r\n})\r\nexport class ExperienceManagementComponent {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}