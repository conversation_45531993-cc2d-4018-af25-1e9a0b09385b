{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../theme.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/divider\";\nfunction ThemeSelectorComponent_div_6_mat_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 15);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ThemeSelectorComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function ThemeSelectorComponent_div_6_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const theme_r2 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.selectTheme(theme_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 8);\n    i0.ɵɵelement(2, \"div\", 9)(3, \"div\", 10)(4, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"span\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ThemeSelectorComponent_div_6_mat_icon_8_Template, 2, 0, \"mat-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const theme_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", theme_r2.name === ctx_r0.currentTheme.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", theme_r2.colors.primary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", theme_r2.colors.accent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background-color\", theme_r2.colors.surface);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getThemeDisplayName(theme_r2.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", theme_r2.name === ctx_r0.currentTheme.name);\n  }\n}\nexport class ThemeSelectorComponent {\n  constructor(themeService, snackBar) {\n    this.themeService = themeService;\n    this.snackBar = snackBar;\n    this.availableThemes = [];\n    this.currentTheme = this.themeService.getCurrentTheme();\n  }\n  ngOnInit() {\n    this.availableThemes = this.themeService.getAvailableThemes();\n    this.themeService.currentTheme$.subscribe(theme => {\n      this.currentTheme = theme;\n    });\n  }\n  selectTheme(theme) {\n    console.log('Selecting theme:', theme.name);\n    this.themeService.setTheme(theme);\n    // Show notification\n    this.snackBar.open(`Theme changed to ${this.getThemeDisplayName(theme.name)}`, 'Close', {\n      duration: 2000,\n      panelClass: ['success-snackbar']\n    });\n    // Force a visual update after a short delay\n    setTimeout(() => {\n      console.log('Theme applied, current CSS variables:', {\n        primary: getComputedStyle(document.documentElement).getPropertyValue('--theme-primary'),\n        accent: getComputedStyle(document.documentElement).getPropertyValue('--theme-accent'),\n        background: getComputedStyle(document.documentElement).getPropertyValue('--theme-background')\n      });\n    }, 200);\n  }\n  getThemeDisplayName(themeName) {\n    switch (themeName) {\n      case 'deep-purple-amber':\n        return 'Deep Purple & Amber';\n      case 'blue-orange':\n        return 'Blue & Orange';\n      case 'green-teal':\n        return 'Green & Teal';\n      case 'dark':\n        return 'Dark Theme';\n      case 'mystical-purple':\n        return 'Mystical Purple';\n      default:\n        return themeName.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n    }\n  }\n  exportTheme() {\n    const themeJson = this.themeService.exportTheme();\n    const blob = new Blob([themeJson], {\n      type: 'application/json'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${this.currentTheme.name}-theme.json`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        const content = e.target?.result;\n        if (this.themeService.importTheme(content)) {\n          // Theme imported successfully\n          console.log('Theme imported successfully');\n        } else {\n          console.error('Failed to import theme');\n        }\n      };\n      reader.readAsText(file);\n    }\n  }\n  static {\n    this.ɵfac = function ThemeSelectorComponent_Factory(t) {\n      return new (t || ThemeSelectorComponent)(i0.ɵɵdirectiveInject(i1.ThemeService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ThemeSelectorComponent,\n      selectors: [[\"app-theme-selector\"]],\n      decls: 19,\n      vars: 1,\n      consts: [[1, \"theme-selector\"], [1, \"theme-grid\"], [\"class\", \"theme-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"theme-actions\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".json\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [1, \"theme-option\", 3, \"click\"], [1, \"theme-preview\"], [1, \"color-bar\", \"primary\"], [1, \"color-bar\", \"accent\"], [1, \"color-bar\", \"surface\"], [1, \"theme-info\"], [1, \"theme-name\"], [\"class\", \"selected-icon\", 4, \"ngIf\"], [1, \"selected-icon\"]],\n      template: function ThemeSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r6 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\")(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"palette\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Choose Theme \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 1);\n          i0.ɵɵtemplate(6, ThemeSelectorComponent_div_6_Template, 9, 10, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"mat-divider\");\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ThemeSelectorComponent_Template_button_click_9_listener() {\n            return ctx.exportTheme();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" Export Current Theme \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"input\", 5, 6);\n          i0.ɵɵlistener(\"change\", function ThemeSelectorComponent_Template_input_change_13_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ThemeSelectorComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r6);\n            const _r1 = i0.ɵɵreference(14);\n            return i0.ɵɵresetView(_r1.click());\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Import Theme \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableThemes);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.MatButton, i5.MatIcon, i6.MatDivider],\n      styles: [\".theme-selector[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.theme-selector[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  margin-bottom: 20px;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.theme-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\r\\n  gap: 16px;\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.theme-option[_ngcontent-%COMP%] {\\r\\n  border: 2px solid transparent;\\r\\n  border-radius: 12px;\\r\\n  padding: 16px;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.3s ease;\\r\\n  background: var(--theme-surface);\\r\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.theme-option[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-2px);\\r\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.theme-option.selected[_ngcontent-%COMP%] {\\r\\n  border-color: var(--theme-primary);\\r\\n  box-shadow: 0 4px 16px rgba(103, 58, 183, 0.3);\\r\\n}\\r\\n\\r\\n.theme-preview[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  height: 40px;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.color-bar[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.color-bar.primary[_ngcontent-%COMP%] {\\r\\n  flex: 2;\\r\\n}\\r\\n\\r\\n.theme-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: space-between;\\r\\n}\\r\\n\\r\\n.theme-name[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.selected-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n.theme-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n  flex-wrap: wrap;\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n.theme-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 600px) {\\r\\n  .theme-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n  \\r\\n  .theme-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n  \\r\\n  .theme-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: center;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;IAqBQA,oCAAyE;IAAAA,4BAAY;IAAAA,iBAAW;;;;;;IAdpGA,8BAI+B;IAA7BA;MAAA;MAAA;MAAA;MAAA,OAASA,2CAAkB;IAAA,EAAC;IAE5BA,8BAA2B;IACzBA,yBAAqF;IAGvFA,iBAAM;IAENA,+BAAwB;IACGA,YAAqC;IAAAA,iBAAO;IACrEA,wFAAgG;IAClGA,iBAAM;;;;;IAZNA,sEAAmD;IAIlBA,eAA+C;IAA/CA,2DAA+C;IAChDA,eAA8C;IAA9CA,0DAA8C;IAC7CA,eAA+C;IAA/CA,2DAA+C;IAIrDA,eAAqC;IAArCA,+DAAqC;IACnDA,eAAsC;IAAtCA,iEAAsC;;;ACXzD,OAAM,MAAOC,sBAAsB;EAIjCC,YACUC,YAA0B,EAC1BC,QAAqB;IADrB,iBAAY,GAAZD,YAAY;IACZ,aAAQ,GAARC,QAAQ;IALlB,oBAAe,GAAkB,EAAE;IAOjC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACF,YAAY,CAACG,eAAe,EAAE;EACzD;EAEAC,QAAQ;IACN,IAAI,CAACC,eAAe,GAAG,IAAI,CAACL,YAAY,CAACM,kBAAkB,EAAE;IAE7D,IAAI,CAACN,YAAY,CAACO,aAAa,CAACC,SAAS,CAACC,KAAK,IAAG;MAChD,IAAI,CAACP,YAAY,GAAGO,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEAC,WAAW,CAACD,KAAkB;IAC5BE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,KAAK,CAACI,IAAI,CAAC;IAC3C,IAAI,CAACb,YAAY,CAACc,QAAQ,CAACL,KAAK,CAAC;IAEjC;IACA,IAAI,CAACR,QAAQ,CAACc,IAAI,CAAC,oBAAoB,IAAI,CAACC,mBAAmB,CAACP,KAAK,CAACI,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;MACtFI,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;IAEF;IACAC,UAAU,CAAC,MAAK;MACdR,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDQ,OAAO,EAAEC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC,CAACC,gBAAgB,CAAC,iBAAiB,CAAC;QACvFC,MAAM,EAAEJ,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;QACrFE,UAAU,EAAEL,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC,CAACC,gBAAgB,CAAC,oBAAoB;OAC7F,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAR,mBAAmB,CAACW,SAAiB;IACnC,QAAQA,SAAS;MACf,KAAK,mBAAmB;QACtB,OAAO,qBAAqB;MAC9B,KAAK,aAAa;QAChB,OAAO,eAAe;MACxB,KAAK,YAAY;QACf,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,YAAY;MACrB,KAAK,iBAAiB;QACpB,OAAO,iBAAiB;MAC1B;QACE,OAAOA,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,EAAE,CAAC;IAAC;EAEjF;EAEAC,WAAW;IACT,MAAMC,SAAS,GAAG,IAAI,CAAChC,YAAY,CAAC+B,WAAW,EAAE;IACjD,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAkB,CAAE,CAAC;IAChE,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,IAAI,GAAGlB,QAAQ,CAACmB,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACE,IAAI,GAAGN,GAAG;IACfI,IAAI,CAACG,QAAQ,GAAG,GAAG,IAAI,CAACzC,YAAY,CAACW,IAAI,aAAa;IACtD2B,IAAI,CAACI,KAAK,EAAE;IACZP,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;EACjC;EAEAU,cAAc,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,MAAMC,OAAO,GAAGD,CAAC,CAACL,MAAM,EAAEO,MAAgB;QAC1C,IAAI,IAAI,CAACxD,YAAY,CAACyD,WAAW,CAACF,OAAO,CAAC,EAAE;UAC1C;UACA5C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;SAC3C,MAAM;UACLD,OAAO,CAAC+C,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,CAAC;MACDP,MAAM,CAACQ,UAAU,CAACX,IAAI,CAAC;;EAE3B;;;uBAlFWlD,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAA8D;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;;UDVnCnE,8BAA4B;UAEdA,uBAAO;UAAAA,iBAAW;UAC5BA,8BACF;UAAAA,iBAAK;UAELA,8BAAwB;UACtBA,wEAgBM;UACRA,iBAAM;UAENA,8BAA2B;UAE3BA,8BAA2B;UACEA;YAAA,OAASoE,iBAAa;UAAA,EAAC;UAChDpE,iCAAU;UAAAA,yBAAQ;UAAAA,iBAAW;UAC7BA,uCACF;UAAAA,iBAAS;UAETA,oCAKyB;UAFvBA;YAAA,OAAUoE,0BAAsB;UAAA,EAAC;UAHnCpE,iBAKyB;UAEzBA,kCAAuD;UAA5BA;YAAAA;YAAA;YAAA,OAASA,0BAAiB;UAAA,EAAC;UACpDA,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,+BACF;UAAAA,iBAAS;;;UApCWA,eAAkB;UAAlBA,6CAAkB", "names": ["i0", "ThemeSelectorComponent", "constructor", "themeService", "snackBar", "currentTheme", "getCurrentTheme", "ngOnInit", "availableThemes", "getAvailableThemes", "currentTheme$", "subscribe", "theme", "selectTheme", "console", "log", "name", "setTheme", "open", "getThemeDisplayName", "duration", "panelClass", "setTimeout", "primary", "getComputedStyle", "document", "documentElement", "getPropertyValue", "accent", "background", "themeName", "replace", "l", "toUpperCase", "exportTheme", "themeJson", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "link", "createElement", "href", "download", "click", "revokeObjectURL", "onFileSelected", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "content", "result", "importTheme", "error", "readAsText", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\theme\\theme-selector\\theme-selector.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\theme\\theme-selector\\theme-selector.component.ts"], "sourcesContent": ["<div class=\"theme-selector\">\r\n  <h3>\r\n    <mat-icon>palette</mat-icon>\r\n    Choose Theme\r\n  </h3>\r\n  \r\n  <div class=\"theme-grid\">\r\n    <div \r\n      *ngFor=\"let theme of availableThemes\" \r\n      class=\"theme-option\"\r\n      [class.selected]=\"theme.name === currentTheme.name\"\r\n      (click)=\"selectTheme(theme)\">\r\n      \r\n      <div class=\"theme-preview\">\r\n        <div class=\"color-bar primary\" [style.background-color]=\"theme.colors.primary\"></div>\r\n        <div class=\"color-bar accent\" [style.background-color]=\"theme.colors.accent\"></div>\r\n        <div class=\"color-bar surface\" [style.background-color]=\"theme.colors.surface\"></div>\r\n      </div>\r\n      \r\n      <div class=\"theme-info\">\r\n        <span class=\"theme-name\">{{ getThemeDisplayName(theme.name) }}</span>\r\n        <mat-icon *ngIf=\"theme.name === currentTheme.name\" class=\"selected-icon\">check_circle</mat-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <mat-divider></mat-divider>\r\n\r\n  <div class=\"theme-actions\">\r\n    <button mat-stroked-button (click)=\"exportTheme()\">\r\n      <mat-icon>download</mat-icon>\r\n      Export Current Theme\r\n    </button>\r\n\r\n    <input \r\n      type=\"file\" \r\n      accept=\".json\" \r\n      (change)=\"onFileSelected($event)\" \r\n      #fileInput \r\n      style=\"display: none;\">\r\n    \r\n    <button mat-stroked-button (click)=\"fileInput.click()\">\r\n      <mat-icon>upload</mat-icon>\r\n      Import Theme\r\n    </button>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { ThemeService } from '../theme.service';\r\nimport { ThemeConfig } from '../theme.config';\r\n\r\n@Component({\r\n  selector: 'app-theme-selector',\r\n  templateUrl: './theme-selector.component.html',\r\n  styleUrls: ['./theme-selector.component.css']\r\n})\r\nexport class ThemeSelectorComponent implements OnInit {\r\n  availableThemes: ThemeConfig[] = [];\r\n  currentTheme: ThemeConfig;\r\n\r\n  constructor(\r\n    private themeService: ThemeService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.currentTheme = this.themeService.getCurrentTheme();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.availableThemes = this.themeService.getAvailableThemes();\r\n\r\n    this.themeService.currentTheme$.subscribe(theme => {\r\n      this.currentTheme = theme;\r\n    });\r\n  }\r\n\r\n  selectTheme(theme: ThemeConfig): void {\r\n    console.log('Selecting theme:', theme.name);\r\n    this.themeService.setTheme(theme);\r\n\r\n    // Show notification\r\n    this.snackBar.open(`Theme changed to ${this.getThemeDisplayName(theme.name)}`, 'Close', {\r\n      duration: 2000,\r\n      panelClass: ['success-snackbar']\r\n    });\r\n\r\n    // Force a visual update after a short delay\r\n    setTimeout(() => {\r\n      console.log('Theme applied, current CSS variables:', {\r\n        primary: getComputedStyle(document.documentElement).getPropertyValue('--theme-primary'),\r\n        accent: getComputedStyle(document.documentElement).getPropertyValue('--theme-accent'),\r\n        background: getComputedStyle(document.documentElement).getPropertyValue('--theme-background')\r\n      });\r\n    }, 200);\r\n  }\r\n\r\n  getThemeDisplayName(themeName: string): string {\r\n    switch (themeName) {\r\n      case 'deep-purple-amber':\r\n        return 'Deep Purple & Amber';\r\n      case 'blue-orange':\r\n        return 'Blue & Orange';\r\n      case 'green-teal':\r\n        return 'Green & Teal';\r\n      case 'dark':\r\n        return 'Dark Theme';\r\n      case 'mystical-purple':\r\n        return 'Mystical Purple';\r\n      default:\r\n        return themeName.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\r\n    }\r\n  }\r\n\r\n  exportTheme(): void {\r\n    const themeJson = this.themeService.exportTheme();\r\n    const blob = new Blob([themeJson], { type: 'application/json' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = `${this.currentTheme.name}-theme.json`;\r\n    link.click();\r\n    window.URL.revokeObjectURL(url);\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const content = e.target?.result as string;\r\n        if (this.themeService.importTheme(content)) {\r\n          // Theme imported successfully\r\n          console.log('Theme imported successfully');\r\n        } else {\r\n          console.error('Failed to import theme');\r\n        }\r\n      };\r\n      reader.readAsText(file);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}