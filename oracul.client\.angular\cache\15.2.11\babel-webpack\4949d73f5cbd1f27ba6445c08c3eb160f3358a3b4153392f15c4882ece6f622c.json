{"ast": null, "code": "import { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/token.service\";\nexport class AuthInterceptor {\n  constructor(tokenService, injector) {\n    this.tokenService = tokenService;\n    this.injector = injector;\n    this.isRefreshing = false;\n    this.refreshTokenSubject = new BehaviorSubject(null);\n  }\n  intercept(req, next) {\n    // Add auth header if user is authenticated\n    const authReq = this.addAuthHeader(req);\n    return next.handle(authReq).pipe(catchError(error => {\n      if (error.status === 401 && !authReq.url.includes('/auth/')) {\n        return this.handle401Error(authReq, next);\n      }\n      return throwError(error);\n    }));\n  }\n  addAuthHeader(req) {\n    const token = this.authService.getToken();\n    if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {\n      return req.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    return req;\n  }\n  handle401Error(req, next) {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n      return this.authService.refreshToken().pipe(switchMap(response => {\n        this.isRefreshing = false;\n        this.refreshTokenSubject.next(response.accessToken);\n        return next.handle(this.addAuthHeader(req));\n      }), catchError(error => {\n        this.isRefreshing = false;\n        // Refresh failed, redirect to login\n        return throwError(error);\n      }));\n    } else {\n      // Wait for refresh to complete\n      return this.refreshTokenSubject.pipe(filter(token => token != null), take(1), switchMap(() => next.handle(this.addAuthHeader(req))));\n    }\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.TokenService), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAAqBA,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAC9D,SAASC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAa,gBAAgB;;;AAKzE,OAAM,MAAOC,eAAe;EAK1BC,YACUC,YAA0B,EAC1BC,QAAkB;IADlB,iBAAY,GAAZD,YAAY;IACZ,aAAQ,GAARC,QAAQ;IANV,iBAAY,GAAG,KAAK;IACpB,wBAAmB,GAAyB,IAAIR,eAAe,CAAM,IAAI,CAAC;EAM/E;EAEHS,SAAS,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,GAAG,CAAC;IAEvC,OAAOC,IAAI,CAACG,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9Bd,UAAU,CAAEe,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAI,CAACL,OAAO,CAACM,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3D,OAAO,IAAI,CAACC,cAAc,CAACR,OAAO,EAAED,IAAI,CAAC;;MAE3C,OAAOZ,UAAU,CAACiB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEQH,aAAa,CAACH,GAAqB;IACzC,MAAMW,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,QAAQ,EAAE;IAEzC,IAAIF,KAAK,IAAI,CAACX,GAAG,CAACQ,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACT,GAAG,CAACQ,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACpF,OAAOT,GAAG,CAACc,KAAK,CAAC;QACfC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUL,KAAK;;OAEjC,CAAC;;IAGJ,OAAOX,GAAG;EACZ;EAEQU,cAAc,CAACV,GAAqB,EAAEC,IAAiB;IAC7D,IAAI,CAAC,IAAI,CAACgB,YAAY,EAAE;MACtB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,mBAAmB,CAACjB,IAAI,CAAC,IAAI,CAAC;MAEnC,OAAO,IAAI,CAACW,WAAW,CAACO,YAAY,EAAE,CAACd,IAAI,CACzCX,SAAS,CAAE0B,QAAa,IAAI;QAC1B,IAAI,CAACH,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,mBAAmB,CAACjB,IAAI,CAACmB,QAAQ,CAACC,WAAW,CAAC;QACnD,OAAOpB,IAAI,CAACG,MAAM,CAAC,IAAI,CAACD,aAAa,CAACH,GAAG,CAAC,CAAC;MAC7C,CAAC,CAAC,EACFT,UAAU,CAAEe,KAAK,IAAI;QACnB,IAAI,CAACW,YAAY,GAAG,KAAK;QACzB;QACA,OAAO5B,UAAU,CAACiB,KAAK,CAAC;MAC1B,CAAC,CAAC,CACH;KACF,MAAM;MACL;MACA,OAAO,IAAI,CAACY,mBAAmB,CAACb,IAAI,CAClCb,MAAM,CAACmB,KAAK,IAAIA,KAAK,IAAI,IAAI,CAAC,EAC9BlB,IAAI,CAAC,CAAC,CAAC,EACPC,SAAS,CAAC,MAAMO,IAAI,CAACG,MAAM,CAAC,IAAI,CAACD,aAAa,CAACH,GAAG,CAAC,CAAC,CAAC,CACtD;;EAEL;;;uBA/DWL,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAA2B,SAAf3B,eAAe;IAAA;EAAA", "names": ["throwError", "BehaviorSubject", "catchError", "filter", "take", "switchMap", "AuthInterceptor", "constructor", "tokenService", "injector", "intercept", "req", "next", "authReq", "addAuthHeader", "handle", "pipe", "error", "status", "url", "includes", "handle401Error", "token", "authService", "getToken", "clone", "setHeaders", "Authorization", "isRefreshing", "refreshTokenSubject", "refreshToken", "response", "accessToken", "factory"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable, Injector } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpClient } from '@angular/common/http';\r\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\r\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\r\nimport { TokenService } from '../services/token.service';\r\nimport { AuthResponse } from '../models/auth.models';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n  private isRefreshing = false;\r\n  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);\r\n  private httpClient?: HttpClient;\r\n\r\n  constructor(\r\n    private tokenService: TokenService,\r\n    private injector: Injector\r\n  ) {}\r\n\r\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    // Add auth header if user is authenticated\r\n    const authReq = this.addAuthHeader(req);\r\n\r\n    return next.handle(authReq).pipe(\r\n      catchError((error: HttpErrorResponse) => {\r\n        if (error.status === 401 && !authReq.url.includes('/auth/')) {\r\n          return this.handle401Error(authReq, next);\r\n        }\r\n        return throwError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  private addAuthHeader(req: HttpRequest<any>): HttpRequest<any> {\r\n    const token = this.authService.getToken();\r\n    \r\n    if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {\r\n      return req.clone({\r\n        setHeaders: {\r\n          Authorization: `Bearer ${token}`\r\n        }\r\n      });\r\n    }\r\n    \r\n    return req;\r\n  }\r\n\r\n  private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    if (!this.isRefreshing) {\r\n      this.isRefreshing = true;\r\n      this.refreshTokenSubject.next(null);\r\n\r\n      return this.authService.refreshToken().pipe(\r\n        switchMap((response: any) => {\r\n          this.isRefreshing = false;\r\n          this.refreshTokenSubject.next(response.accessToken);\r\n          return next.handle(this.addAuthHeader(req));\r\n        }),\r\n        catchError((error) => {\r\n          this.isRefreshing = false;\r\n          // Refresh failed, redirect to login\r\n          return throwError(error);\r\n        })\r\n      );\r\n    } else {\r\n      // Wait for refresh to complete\r\n      return this.refreshTokenSubject.pipe(\r\n        filter(token => token != null),\r\n        take(1),\r\n        switchMap(() => next.handle(this.addAuthHeader(req)))\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}