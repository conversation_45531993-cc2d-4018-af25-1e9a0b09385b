{"ast": null, "code": "import { FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"../../../auth/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/slide-toggle\";\nfunction ProfileEditComponent_div_0_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getErrorMessage(\"contactInfo.email\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_122_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r14.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 23)(2, \"mat-form-field\", 60)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 62)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 63);\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_div_122_mat_option_10_Template, 2, 2, \"mat-option\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"mat-checkbox\", 51);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-checkbox\", 66);\n    i0.ɵɵtext(15, \"Primary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_122_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const i_r12 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.removePhoneNumber(i_r12));\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r12 = ctx.index;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r12);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.getPhoneTypeOptions());\n  }\n}\nfunction ProfileEditComponent_div_0_div_161_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const platform_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", platform_r20.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", platform_r20.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_161_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 23)(2, \"mat-form-field\", 70)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Platform\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-select\", 71);\n    i0.ɵɵtemplate(6, ProfileEditComponent_div_0_div_161_mat_option_6_Template, 2, 2, \"mat-option\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 72)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 74)(12, \"mat-checkbox\", 51);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_161_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const i_r18 = restoredCtx.index;\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.removeSocialLink(i_r18));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r18 = ctx.index;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r18);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getPlatformOptions());\n  }\n}\nfunction ProfileEditComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h1\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onCancel());\n    });\n    i0.ɵɵtext(8, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onSubmit());\n    });\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_mat_icon_10_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(11, ProfileEditComponent_div_0_mat_icon_11_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"form\", 8)(14, \"mat-card\", 9)(15, \"mat-card-header\")(16, \"mat-card-title\");\n    i0.ɵɵtext(17, \"Profile Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12);\n    i0.ɵɵelement(22, \"img\", 13);\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"camera_alt\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"input\", 15, 16);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onProfilePhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const _r4 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(_r4.click());\n    });\n    i0.ɵɵtext(29, \" Change Profile Photo \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 18)(31, \"div\", 19)(32, \"div\", 20)(33, \"div\", 14)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"camera_alt\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"input\", 15, 21);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_36_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onCoverPhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const _r5 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(_r5.click());\n    });\n    i0.ɵɵtext(39, \" Change Cover Photo \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(40, \"mat-card\", 22)(41, \"mat-card-header\")(42, \"mat-card-title\");\n    i0.ɵɵtext(43, \"Basic Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"mat-card-content\")(45, \"div\", 23)(46, \"mat-form-field\", 24)(47, \"mat-label\");\n    i0.ɵɵtext(48, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(49, \"input\", 25);\n    i0.ɵɵtemplate(50, ProfileEditComponent_div_0_mat_error_50_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"mat-form-field\", 24)(52, \"mat-label\");\n    i0.ɵɵtext(53, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"input\", 26);\n    i0.ɵɵtemplate(55, ProfileEditComponent_div_0_mat_error_55_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"mat-form-field\", 27)(57, \"mat-label\");\n    i0.ɵɵtext(58, \"Professional Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"mat-form-field\", 27)(61, \"mat-label\");\n    i0.ɵɵtext(62, \"Professional Headline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(63, \"textarea\", 29);\n    i0.ɵɵelementStart(64, \"mat-hint\", 30);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"mat-form-field\", 27)(67, \"mat-label\");\n    i0.ɵɵtext(68, \"Professional Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(69, \"textarea\", 31);\n    i0.ɵɵelementStart(70, \"mat-hint\", 30);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(72, \"mat-card\", 32)(73, \"mat-card-header\")(74, \"mat-card-title\");\n    i0.ɵɵtext(75, \"Location\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(76, \"mat-card-content\")(77, \"div\", 23)(78, \"mat-form-field\", 24)(79, \"mat-label\");\n    i0.ɵɵtext(80, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(81, \"input\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"mat-form-field\", 24)(83, \"mat-label\");\n    i0.ɵɵtext(84, \"State/Province\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(85, \"input\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 23)(87, \"mat-form-field\", 24)(88, \"mat-label\");\n    i0.ɵɵtext(89, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(90, \"input\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"mat-form-field\", 24)(92, \"mat-label\");\n    i0.ɵɵtext(93, \"Display Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(95, \"mat-card\", 37)(96, \"mat-card-header\")(97, \"mat-card-title\");\n    i0.ɵɵtext(98, \"Contact Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(99, \"mat-card-content\")(100, \"div\", 23)(101, \"mat-form-field\", 24)(102, \"mat-label\");\n    i0.ɵɵtext(103, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(104, \"input\", 38);\n    i0.ɵɵtemplate(105, ProfileEditComponent_div_0_mat_error_105_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"div\", 39)(107, \"mat-checkbox\", 40);\n    i0.ɵɵtext(108, \" Make email public \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 23)(110, \"mat-form-field\", 24)(111, \"mat-label\");\n    i0.ɵɵtext(112, \"Website\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(113, \"input\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"mat-form-field\", 24)(115, \"mat-label\");\n    i0.ɵɵtext(116, \"Portfolio URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(117, \"input\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(118, \"div\", 43)(119, \"h4\");\n    i0.ɵɵtext(120, \"Phone Numbers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"div\", 44);\n    i0.ɵɵtemplate(122, ProfileEditComponent_div_0_div_122_Template, 19, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_123_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.addPhoneNumber());\n    });\n    i0.ɵɵelementStart(124, \"mat-icon\");\n    i0.ɵɵtext(125, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(126, \" Add Phone Number \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(127, \"div\", 47)(128, \"h4\");\n    i0.ɵɵtext(129, \"Business Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"mat-form-field\", 27)(131, \"mat-label\");\n    i0.ɵɵtext(132, \"Street Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(133, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(134, \"div\", 23)(135, \"mat-form-field\", 49)(136, \"mat-label\");\n    i0.ɵɵtext(137, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(138, \"input\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"mat-form-field\", 49)(140, \"mat-label\");\n    i0.ɵɵtext(141, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(142, \"input\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(143, \"mat-form-field\", 49)(144, \"mat-label\");\n    i0.ɵɵtext(145, \"Postal Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(146, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(147, \"div\", 23)(148, \"mat-form-field\", 24)(149, \"mat-label\");\n    i0.ɵɵtext(150, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(151, \"input\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(152, \"div\", 39)(153, \"mat-checkbox\", 51);\n    i0.ɵɵtext(154, \" Make business address public \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(155, \"mat-card\", 22)(156, \"mat-card-header\")(157, \"mat-card-title\");\n    i0.ɵɵtext(158, \"Social Links\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(159, \"mat-card-content\")(160, \"div\", 52);\n    i0.ɵɵtemplate(161, ProfileEditComponent_div_0_div_161_Template, 17, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(162, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_162_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.addSocialLink());\n    });\n    i0.ɵɵelementStart(163, \"mat-icon\");\n    i0.ɵɵtext(164, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(165, \" Add Social Link \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(166, \"mat-card\", 22)(167, \"mat-card-header\")(168, \"mat-card-title\");\n    i0.ɵɵtext(169, \"Privacy Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(170, \"mat-card-content\")(171, \"div\", 54)(172, \"mat-slide-toggle\", 55)(173, \"span\", 56);\n    i0.ɵɵtext(174, \"Make profile public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(175, \"p\", 57);\n    i0.ɵɵtext(176, \" When enabled, your profile will be visible to everyone and searchable. When disabled, only you can see your profile. \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving || ctx_r0.profileForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isSaving ? \"Saving...\" : \"Save Changes\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"src\", (ctx_r0.profile == null ? null : ctx_r0.profile.profilePhotoUrl) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(10);\n    i0.ɵɵstyleProp(\"background-image\", ctx_r0.profile && ctx_r0.profile.coverPhotoUrl ? \"url(\" + ctx_r0.profile.coverPhotoUrl + \")\" : \"var(--theme-gradient-primary)\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_10_0 = ctx_r0.profileForm.get(\"headline\")) == null ? null : tmp_10_0.value == null ? null : tmp_10_0.value.length) || 0, \"/220\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_11_0 = ctx_r0.profileForm.get(\"summary\")) == null ? null : tmp_11_0.value == null ? null : tmp_11_0.value.length) || 0, \"/2000\");\n    i0.ɵɵadvance(34);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.phoneNumbers.controls);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.socialLinks.controls);\n  }\n}\nfunction ProfileEditComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"mat-spinner\", 76);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProfileEditComponent {\n  constructor(formBuilder, profileService, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.profileService = profileService;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.profile = null;\n    this.isLoading = true;\n    this.isSaving = false;\n    this.destroy$ = new Subject();\n    this.profileForm = this.createForm();\n  }\n  ngOnInit() {\n    this.loadProfile();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createForm() {\n    return this.formBuilder.group({\n      // Basic Information\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      professionalTitle: [''],\n      headline: ['', Validators.maxLength(220)],\n      summary: ['', Validators.maxLength(2000)],\n      // Location\n      location: this.formBuilder.group({\n        city: [''],\n        state: [''],\n        country: [''],\n        displayLocation: ['']\n      }),\n      // Contact Information\n      contactInfo: this.formBuilder.group({\n        email: ['', Validators.email],\n        isEmailPublic: [false],\n        website: [''],\n        portfolioUrl: [''],\n        phoneNumbers: this.formBuilder.array([]),\n        businessAddress: this.formBuilder.group({\n          street: [''],\n          city: [''],\n          state: [''],\n          postalCode: [''],\n          country: [''],\n          isPublic: [false]\n        })\n      }),\n      // Privacy Settings\n      isPublic: [true],\n      // Social Links\n      socialLinks: this.formBuilder.array([])\n    });\n  }\n  loadProfile() {\n    this.profileService.getCurrentUserProfile().pipe(takeUntil(this.destroy$)).subscribe({\n      next: profile => {\n        this.profile = profile;\n        this.populateForm(profile);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading profile:', error);\n        this.snackBar.open('Error loading profile', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/dashboard']);\n        this.isLoading = false;\n      }\n    });\n  }\n  populateForm(profile) {\n    this.profileForm.patchValue({\n      firstName: profile.firstName,\n      lastName: profile.lastName,\n      professionalTitle: profile.professionalTitle || '',\n      headline: profile.headline || '',\n      summary: profile.summary || '',\n      location: {\n        city: profile.location?.city || '',\n        state: profile.location?.state || '',\n        country: profile.location?.country || '',\n        displayLocation: profile.location?.displayLocation || ''\n      },\n      contactInfo: {\n        email: profile.contactInfo.email || '',\n        isEmailPublic: profile.contactInfo.isEmailPublic,\n        website: profile.contactInfo.website || '',\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\n        businessAddress: {\n          street: profile.contactInfo.businessAddress?.street || '',\n          city: profile.contactInfo.businessAddress?.city || '',\n          state: profile.contactInfo.businessAddress?.state || '',\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\n          country: profile.contactInfo.businessAddress?.country || '',\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\n        }\n      },\n      isPublic: profile.isPublic\n    });\n    // Populate phone numbers\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\n    // Populate social links\n    this.setSocialLinks(profile.socialLinks || []);\n  }\n  // Phone Numbers Management\n  get phoneNumbers() {\n    return this.profileForm.get('contactInfo.phoneNumbers');\n  }\n  setPhoneNumbers(phones) {\n    const phoneArray = this.phoneNumbers;\n    phoneArray.clear();\n    phones.forEach(phone => {\n      phoneArray.push(this.formBuilder.group({\n        id: [phone.id],\n        number: [phone.number, Validators.required],\n        type: [phone.type, Validators.required],\n        isPublic: [phone.isPublic],\n        isPrimary: [phone.isPrimary]\n      }));\n    });\n  }\n  addPhoneNumber() {\n    const phoneGroup = this.formBuilder.group({\n      id: [null],\n      number: ['', Validators.required],\n      type: ['mobile', Validators.required],\n      isPublic: [false],\n      isPrimary: [false]\n    });\n    this.phoneNumbers.push(phoneGroup);\n  }\n  removePhoneNumber(index) {\n    this.phoneNumbers.removeAt(index);\n  }\n  // Social Links Management\n  get socialLinks() {\n    return this.profileForm.get('socialLinks');\n  }\n  setSocialLinks(links) {\n    const linksArray = this.socialLinks;\n    linksArray.clear();\n    links.forEach(link => {\n      linksArray.push(this.formBuilder.group({\n        id: [link.id],\n        platform: [link.platform, Validators.required],\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\n        displayName: [link.displayName],\n        isPublic: [link.isPublic]\n      }));\n    });\n  }\n  addSocialLink() {\n    const linkGroup = this.formBuilder.group({\n      id: [null],\n      platform: ['linkedin', Validators.required],\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      displayName: [''],\n      isPublic: [true]\n    });\n    this.socialLinks.push(linkGroup);\n  }\n  removeSocialLink(index) {\n    this.socialLinks.removeAt(index);\n  }\n  // Form Submission\n  onSubmit() {\n    if (this.profileForm.valid) {\n      this.isSaving = true;\n      const formValue = this.profileForm.value;\n      const updateRequest = {\n        professionalTitle: formValue.professionalTitle,\n        headline: formValue.headline,\n        location: formValue.location,\n        contactInfo: formValue.contactInfo,\n        summary: formValue.summary,\n        isPublic: formValue.isPublic\n      };\n      this.profileService.updateProfile(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: updatedProfile => {\n          this.isSaving = false;\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate(['/profile', updatedProfile.slug]);\n        },\n        error: error => {\n          this.isSaving = false;\n          console.error('Error updating profile:', error);\n          this.snackBar.open('Error updating profile. Please try again.', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n      this.snackBar.open('Please fix the errors in the form', 'Close', {\n        duration: 3000\n      });\n    }\n  }\n  onCancel() {\n    if (this.profile) {\n      this.router.navigate(['/profile', this.profile.slug]);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  // File Upload Methods\n  onProfilePhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.uploadProfilePhoto(file);\n    }\n  }\n  onCoverPhotoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.uploadCoverPhoto(file);\n    }\n  }\n  uploadProfilePhoto(file) {\n    this.profileService.uploadProfilePhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (this.profile) {\n          this.profile.profilePhotoUrl = response.url;\n        }\n        this.snackBar.open('Profile photo updated!', 'Close', {\n          duration: 2000\n        });\n      },\n      error: error => {\n        console.error('Error uploading profile photo:', error);\n        this.snackBar.open('Error uploading photo', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  uploadCoverPhoto(file) {\n    this.profileService.uploadCoverPhoto(file).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (this.profile) {\n          this.profile.coverPhotoUrl = response.url;\n        }\n        this.snackBar.open('Cover photo updated!', 'Close', {\n          duration: 2000\n        });\n      },\n      error: error => {\n        console.error('Error uploading cover photo:', error);\n        this.snackBar.open('Error uploading photo', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Utility Methods\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormArray) {\n        control.controls.forEach(arrayControl => {\n          if (arrayControl instanceof FormGroup) {\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\n              arrayControl.get(arrayKey)?.markAsTouched();\n            });\n          }\n        });\n      }\n    });\n  }\n  getErrorMessage(fieldName) {\n    const control = this.profileForm.get(fieldName);\n    if (control?.hasError('required')) {\n      return `${fieldName} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      return `${fieldName} must be at least ${control.errors?.['minlength'].requiredLength} characters`;\n    }\n    if (control?.hasError('maxlength')) {\n      return `${fieldName} must be no more than ${control.errors?.['maxlength'].requiredLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid URL';\n    }\n    return '';\n  }\n  // Platform options for social links\n  getPlatformOptions() {\n    return [{\n      value: 'linkedin',\n      label: 'LinkedIn'\n    }, {\n      value: 'twitter',\n      label: 'Twitter'\n    }, {\n      value: 'github',\n      label: 'GitHub'\n    }, {\n      value: 'behance',\n      label: 'Behance'\n    }, {\n      value: 'dribbble',\n      label: 'Dribbble'\n    }, {\n      value: 'instagram',\n      label: 'Instagram'\n    }, {\n      value: 'facebook',\n      label: 'Facebook'\n    }, {\n      value: 'youtube',\n      label: 'YouTube'\n    }, {\n      value: 'other',\n      label: 'Other'\n    }];\n  }\n  // Phone type options\n  getPhoneTypeOptions() {\n    return [{\n      value: 'mobile',\n      label: 'Mobile'\n    }, {\n      value: 'business',\n      label: 'Business'\n    }, {\n      value: 'home',\n      label: 'Home'\n    }];\n  }\n  static {\n    this.ɵfac = function ProfileEditComponent_Factory(t) {\n      return new (t || ProfileEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileEditComponent,\n      selectors: [[\"app-profile-edit\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"profile-edit-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"profile-edit-container\"], [1, \"edit-header\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"profile-form\", 3, \"formGroup\"], [1, \"photo-section\"], [1, \"photo-uploads\"], [1, \"profile-photo-upload\"], [1, \"photo-preview\"], [\"alt\", \"Profile Photo\", 1, \"profile-photo\", 3, \"src\"], [1, \"photo-overlay\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"profilePhotoInput\", \"\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"cover-photo-upload\"], [1, \"cover-preview\"], [1, \"cover-photo\"], [\"coverPhotoInput\", \"\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"required\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"professionalTitle\", \"placeholder\", \"e.g., Senior Software Engineer, UX Designer\"], [\"matInput\", \"\", \"formControlName\", \"headline\", \"rows\", \"2\", \"placeholder\", \"A brief, compelling description of what you do\", \"maxlength\", \"220\"], [\"align\", \"end\"], [\"matInput\", \"\", \"formControlName\", \"summary\", \"rows\", \"6\", \"placeholder\", \"Describe your expertise, experience, and what makes you unique\", \"maxlength\", \"2000\"], [\"formGroupName\", \"location\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"state\"], [\"matInput\", \"\", \"formControlName\", \"country\"], [\"matInput\", \"\", \"formControlName\", \"displayLocation\", \"placeholder\", \"e.g., San Francisco, CA\"], [\"formGroupName\", \"contactInfo\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\"], [1, \"checkbox-field\"], [\"formControlName\", \"isEmailPublic\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"https://yourwebsite.com\"], [\"matInput\", \"\", \"formControlName\", \"portfolioUrl\", \"placeholder\", \"https://portfolio.com\"], [1, \"phone-numbers-section\"], [\"formArrayName\", \"phoneNumbers\"], [\"class\", \"phone-number-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formGroupName\", \"businessAddress\", 1, \"business-address-section\"], [\"matInput\", \"\", \"formControlName\", \"street\"], [\"appearance\", \"outline\", 1, \"third-width\"], [\"matInput\", \"\", \"formControlName\", \"postalCode\"], [\"formControlName\", \"isPublic\"], [\"formArrayName\", \"socialLinks\"], [\"class\", \"social-link-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"privacy-controls\"], [\"formControlName\", \"isPublic\", \"color\", \"primary\"], [1, \"toggle-label\"], [1, \"toggle-description\"], [\"diameter\", \"20\"], [1, \"phone-number-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"phone-input\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"placeholder\", \"+****************\"], [\"appearance\", \"outline\", 1, \"phone-type\"], [\"formControlName\", \"type\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"phone-controls\"], [\"formControlName\", \"isPrimary\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [3, \"value\"], [1, \"social-link-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"platform-select\"], [\"formControlName\", \"platform\"], [\"appearance\", \"outline\", 1, \"url-input\"], [\"matInput\", \"\", \"formControlName\", \"url\", \"placeholder\", \"https://linkedin.com/in/yourname\"], [1, \"link-controls\"], [1, \"loading-container\"], [\"diameter\", \"50\"]],\n      template: function ProfileEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProfileEditComponent_div_0_Template, 177, 16, \"div\", 0);\n          i0.ɵɵtemplate(1, ProfileEditComponent_div_1_Template, 4, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatHint, i10.MatError, i11.MatInput, i12.MatSelect, i13.MatOption, i14.MatCheckbox, i15.MatProgressSpinner, i16.MatSlideToggle],\n      styles: [\".profile-edit-container[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n  padding: 20px;\\r\\n  background-color: var(--theme-background);\\r\\n}\\r\\n\\r\\n.edit-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  margin-bottom: 24px;\\r\\n  padding: 20px 0;\\r\\n}\\r\\n\\r\\n.edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  margin: 0;\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 2rem;\\r\\n}\\r\\n\\r\\n.header-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.profile-form[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 0;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\r\\n  padding-bottom: 16px;\\r\\n}\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 1.3rem;\\r\\n}\\r\\n\\r\\n\\r\\n.form-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  align-items: flex-start;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.half-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n\\r\\n.third-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n\\r\\n.checkbox-field[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  min-height: 56px;\\r\\n}\\r\\n\\r\\n\\r\\n.photo-section[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n}\\r\\n\\r\\n.photo-uploads[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 32px;\\r\\n  align-items: flex-start;\\r\\n}\\r\\n\\r\\n.profile-photo-upload[_ngcontent-%COMP%], .cover-photo-upload[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  cursor: pointer;\\r\\n  border-radius: 12px;\\r\\n  overflow: hidden;\\r\\n  transition: transform 0.3s ease;\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%]:hover {\\r\\n  transform: scale(1.02);\\r\\n}\\r\\n\\r\\n.profile-photo[_ngcontent-%COMP%] {\\r\\n  width: 150px;\\r\\n  height: 150px;\\r\\n  border-radius: 50%;\\r\\n  object-fit: cover;\\r\\n  border: 4px solid var(--theme-surface);\\r\\n  box-shadow: 0 4px 16px rgba(0,0,0,0.1);\\r\\n}\\r\\n\\r\\n.cover-preview[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  width: 300px;\\r\\n  height: 120px;\\r\\n  border-radius: 12px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.cover-photo[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-size: cover;\\r\\n  background-position: center;\\r\\n  background-repeat: no-repeat;\\r\\n}\\r\\n\\r\\n.photo-overlay[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0,0,0,0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.photo-preview[_ngcontent-%COMP%]:hover   .photo-overlay[_ngcontent-%COMP%] {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.photo-overlay[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: white;\\r\\n  font-size: 32px;\\r\\n  width: 32px;\\r\\n  height: 32px;\\r\\n}\\r\\n\\r\\n\\r\\n.phone-numbers-section[_ngcontent-%COMP%] {\\r\\n  margin-top: 24px;\\r\\n}\\r\\n\\r\\n.phone-numbers-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-size: 1.1rem;\\r\\n}\\r\\n\\r\\n.phone-number-item[_ngcontent-%COMP%] {\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n  padding: 16px;\\r\\n  margin-bottom: 12px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.phone-input[_ngcontent-%COMP%] {\\r\\n  flex: 2;\\r\\n}\\r\\n\\r\\n.phone-type[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 120px;\\r\\n}\\r\\n\\r\\n.phone-controls[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n  align-items: flex-start;\\r\\n  min-width: 120px;\\r\\n}\\r\\n\\r\\n\\r\\n.business-address-section[_ngcontent-%COMP%] {\\r\\n  margin-top: 24px;\\r\\n}\\r\\n\\r\\n.business-address-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-size: 1.1rem;\\r\\n}\\r\\n\\r\\n\\r\\n.social-link-item[_ngcontent-%COMP%] {\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n  padding: 16px;\\r\\n  margin-bottom: 12px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.platform-select[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 140px;\\r\\n}\\r\\n\\r\\n.url-input[_ngcontent-%COMP%] {\\r\\n  flex: 2;\\r\\n}\\r\\n\\r\\n.link-controls[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n  align-items: flex-start;\\r\\n  min-width: 100px;\\r\\n}\\r\\n\\r\\n\\r\\n.privacy-controls[_ngcontent-%COMP%] {\\r\\n  padding: 16px;\\r\\n}\\r\\n\\r\\n.toggle-label[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.toggle-description[_ngcontent-%COMP%] {\\r\\n  margin: 8px 0 0 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-size: 0.9rem;\\r\\n  line-height: 1.4;\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 50vh;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n.mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-error);\\r\\n}\\r\\n\\r\\n.mat-error[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .profile-edit-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n  \\r\\n  .edit-header[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 16px;\\r\\n    text-align: center;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: center;\\r\\n  }\\r\\n  \\r\\n  .form-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 0;\\r\\n  }\\r\\n  \\r\\n  .half-width[_ngcontent-%COMP%], .third-width[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n  }\\r\\n  \\r\\n  .photo-uploads[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n    gap: 24px;\\r\\n  }\\r\\n  \\r\\n  .cover-preview[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    max-width: 300px;\\r\\n  }\\r\\n  \\r\\n  .phone-controls[_ngcontent-%COMP%], .link-controls[_ngcontent-%COMP%] {\\r\\n    flex-direction: row;\\r\\n    align-items: center;\\r\\n    justify-content: space-between;\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\r\\n    font-size: 1.5rem;\\r\\n  }\\r\\n  \\r\\n  .profile-photo[_ngcontent-%COMP%] {\\r\\n    width: 120px;\\r\\n    height: 120px;\\r\\n  }\\r\\n  \\r\\n  .cover-preview[_ngcontent-%COMP%] {\\r\\n    height: 100px;\\r\\n  }\\r\\n  \\r\\n  .phone-number-item[_ngcontent-%COMP%], .social-link-item[_ngcontent-%COMP%] {\\r\\n    padding: 12px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.form-section[_ngcontent-%COMP%] {\\r\\n  animation: _ngcontent-%COMP%_slideInUp 0.3s ease-out;\\r\\n}\\r\\n\\r\\n@keyframes _ngcontent-%COMP%_slideInUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(20px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.form-section[_ngcontent-%COMP%]:hover {\\r\\n  box-shadow: 0 4px 16px rgba(0,0,0,0.1);\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\nbutton[mat-stroked-button][_ngcontent-%COMP%] {\\r\\n  border-color: var(--theme-primary);\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\nbutton[mat-stroked-button][_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(103, 58, 183, 0.04);\\r\\n}\\r\\n\\r\\n\\r\\n.mat-chip[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-progress-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\r\\n  stroke: var(--theme-primary);\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAE9E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICYjCC,gCAA2B;IACzBA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA4B;IAAAA,oBAAI;IAAAA,iBAAW;;;;;IA0DvCA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,oEACF;;;;;IAMAA,iCAAgG;IAC9FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,mEACF;;;;;IAuEAA,iCAAkH;IAChHA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,4EACF;;;;;IAqCQA,sCAA4E;IAC1EA,YACF;IAAAA,iBAAa;;;;IAF0CA,sCAAoB;IACzEA,eACF;IADEA,+CACF;;;;;;IAbRA,+BACmD;IAGlCA,4BAAY;IAAAA,iBAAY;IACnCA,4BAAyE;IAC3EA,iBAAiB;IAEjBA,0CAAwD;IAC3CA,oBAAI;IAAAA,iBAAY;IAC3BA,sCAAmC;IACjCA,oGAEa;IACfA,iBAAa;IAGfA,gCAA4B;IACeA,uBAAM;IAAAA,iBAAe;IAC9DA,yCAA0C;IAAAA,wBAAO;IAAAA,iBAAe;IAChEA,mCAAkF;IAA7CA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACjEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;IApB9BA,qCAAmB;IAUaA,gBAAwB;IAAxBA,sDAAwB;;;;;IA4EvDA,sCAAmF;IACjFA,YACF;IAAAA,iBAAa;;;;IAF6CA,0CAAwB;IAChFA,eACF;IADEA,mDACF;;;;;;IARRA,+BACkD;IAGjCA,wBAAQ;IAAAA,iBAAY;IAC/BA,sCAAuC;IACrCA,kGAEa;IACfA,iBAAa;IAGfA,0CAAuD;IAC1CA,mBAAG;IAAAA,iBAAY;IAC1BA,6BAAqF;IACvFA,iBAAiB;IAEjBA,gCAA2B;IACgBA,uBAAM;IAAAA,iBAAe;IAC9DA,mCAAiF;IAA5CA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;;;;;IAnB9BA,qCAAmB;IAKiBA,eAAuB;IAAvBA,sDAAuB;;;;;;IAhR1EA,8BAAuD;IAGvCA,oBAAI;IAAAA,iBAAW;IACzBA,8BACF;IAAAA,iBAAK;IACLA,8BAA4B;IACCA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAC7CA,wBACF;IAAAA,iBAAS;IACTA,iCAI+C;IAD7CA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAEpBA,uFAEW;IACXA,uFAA2C;IAC3CA,aACF;IAAAA,iBAAS;IAIbA,gCAAqD;IAI/BA,+BAAc;IAAAA,iBAAiB;IAEjDA,yCAAkB;IAIVA,2BAC+C;IAC/CA,gCAA2B;IACfA,2BAAU;IAAAA,iBAAW;IAGnCA,sCAC+C;IADTA;MAAAA;MAAA;MAAA,OAAUA,qDAA8B;IAAA,EAAC;IAA/EA,iBAC+C;IAC/CA,mCAA+D;IAApCA;MAAAA;MAAA;MAAA,OAASA,0BAAyB;IAAA,EAAC;IAC5DA,uCACF;IAAAA,iBAAS;IAGXA,gCAAgC;IAKdA,2BAAU;IAAAA,iBAAW;IAIrCA,sCAC+C;IADXA;MAAAA;MAAA;MAAA,OAAUA,mDAA4B;IAAA,EAAC;IAA3EA,iBAC+C;IAC/CA,mCAA6D;IAAlCA;MAAAA;MAAA;MAAA,OAASA,0BAAuB;IAAA,EAAC;IAC1DA,qCACF;IAAAA,iBAAS;IAOjBA,qCAA+B;IAEXA,kCAAiB;IAAAA,iBAAiB;IAEpDA,yCAAkB;IAGDA,2BAAU;IAAAA,iBAAY;IACjCA,6BAAqD;IACrDA,yFAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,0BAAS;IAAAA,iBAAY;IAChCA,6BAAoD;IACpDA,yFAEY;IACdA,iBAAiB;IAGnBA,2CAAwD;IAC3CA,mCAAkB;IAAAA,iBAAY;IACzCA,6BACiE;IACnEA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,sCAAqB;IAAAA,iBAAY;IAC5CA,gCAEqC;IACrCA,qCAAsB;IAAAA,aAAyD;IAAAA,iBAAW;IAG5FA,2CAAwD;IAC3CA,qCAAoB;IAAAA,iBAAY;IAC3CA,gCAEsC;IACtCA,qCAAsB;IAAAA,aAAyD;IAAAA,iBAAW;IAMhGA,qCAAwD;IAEpCA,yBAAQ;IAAAA,iBAAiB;IAE3CA,yCAAkB;IAGDA,qBAAI;IAAAA,iBAAY;IAC3BA,6BAAuC;IACzCA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,+BAAc;IAAAA,iBAAY;IACrCA,6BAAwC;IAC1CA,iBAAiB;IAGnBA,gCAAsB;IAEPA,wBAAO;IAAAA,iBAAY;IAC9BA,6BAA0C;IAC5CA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,iCAAgB;IAAAA,iBAAY;IACvCA,6BAC6C;IAC/CA,iBAAiB;IAMvBA,qCAA2D;IAEvCA,oCAAmB;IAAAA,iBAAiB;IAEtDA,yCAAkB;IAGDA,uBAAK;IAAAA,iBAAY;IAC5BA,8BAAqD;IACrDA,2FAEY;IACdA,iBAAiB;IAEjBA,iCAA4B;IAExBA,qCACF;IAAAA,iBAAe;IAInBA,iCAAsB;IAEPA,yBAAO;IAAAA,iBAAY;IAC9BA,8BAAgF;IAClFA,iBAAiB;IAEjBA,4CAAwD;IAC3CA,+BAAa;IAAAA,iBAAY;IACpCA,8BAAmF;IACrFA,iBAAiB;IAInBA,iCAAmC;IAC7BA,+BAAa;IAAAA,iBAAK;IACtBA,iCAAkC;IAChCA,iFAyBM;IACRA,iBAAM;IACNA,oCAAoE;IAAzCA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IACnDA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,oCACF;IAAAA,iBAAS;IAIXA,iCAAsE;IAChEA,kCAAgB;IAAAA,iBAAK;IACzBA,4CAAwD;IAC3CA,gCAAc;IAAAA,iBAAY;IACrCA,8BAAyC;IAC3CA,iBAAiB;IAEjBA,iCAAsB;IAEPA,sBAAI;IAAAA,iBAAY;IAC3BA,8BAAuC;IACzCA,iBAAiB;IAEjBA,4CAAyD;IAC5CA,uBAAK;IAAAA,iBAAY;IAC5BA,8BAAwC;IAC1CA,iBAAiB;IAEjBA,4CAAyD;IAC5CA,6BAAW;IAAAA,iBAAY;IAClCA,8BAA6C;IAC/CA,iBAAiB;IAGnBA,iCAAsB;IAEPA,yBAAO;IAAAA,iBAAY;IAC9BA,8BAA0C;IAC5CA,iBAAiB;IAEjBA,iCAA4B;IAExBA,gDACF;IAAAA,iBAAe;IAQzBA,sCAA+B;IAEXA,8BAAY;IAAAA,iBAAiB;IAE/CA,0CAAkB;IAEdA,iFAwBM;IACRA,iBAAM;IACNA,oCAAmE;IAAxCA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IAClDA,kCAAU;IAAAA,qBAAG;IAAAA,iBAAW;IACxBA,mCACF;IAAAA,iBAAS;IAKbA,sCAA+B;IAEXA,kCAAgB;IAAAA,iBAAiB;IAEnDA,0CAAkB;IAGeA,qCAAmB;IAAAA,iBAAO;IACrDA,+BAA8B;IAC5BA,wIAEF;IAAAA,iBAAI;;;;;;;;;IAhTsCA,eAAqB;IAArBA,0CAAqB;IAOnEA,eAA4C;IAA5CA,wEAA4C;IACjCA,eAAc;IAAdA,sCAAc;IAGdA,eAAe;IAAfA,uCAAe;IAC1BA,eACF;IADEA,+EACF;IAIEA,eAAyB;IAAzBA,8CAAyB;IAUdA,eAAuE;IAAvEA,+IAAuE;IAgBvEA,gBAAwI;IAAxIA,kKAAwI;IA0BnIA,gBAAoF;IAApFA,+LAAoF;IAQpFA,eAAkF;IAAlFA,6LAAkF;IAiB1EA,gBAAyD;IAAzDA,gKAAyD;IAQzDA,eAAyD;IAAzDA,gKAAyD;IAgDjEA,gBAAoG;IAApGA,mNAAoG;IA4BzFA,gBAA0B;IAA1BA,sDAA0B;IAiF7BA,gBAAyB;IAAzBA,qDAAyB;;;;;IAsDzDA,+BAAiD;IAC/CA,kCAAyC;IACzCA,yBAAG;IAAAA,kCAAkB;IAAAA,iBAAI;;;ADnT3B,OAAM,MAAOC,oBAAoB;EAQ/BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAJrB,gBAAW,GAAXJ,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAXlB,YAAO,GAAuB,IAAI;IAClC,cAAS,GAAG,IAAI;IAChB,aAAQ,GAAG,KAAK;IAER,aAAQ,GAAG,IAAIT,OAAO,EAAQ;IASpC,IAAI,CAACU,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;EACtC;EAEAC,QAAQ;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAW;IACT,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;IACpB,IAAI,CAACD,QAAQ,CAACE,QAAQ,EAAE;EAC1B;EAEQN,UAAU;IAChB,OAAO,IAAI,CAACN,WAAW,CAACa,KAAK,CAAC;MAC5B;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,QAAQ,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,SAAS,CAAC,GAAG,CAAC,CAAC;MACzCC,OAAO,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,SAAS,CAAC,IAAI,CAAC,CAAC;MAEzC;MACAE,QAAQ,EAAE,IAAI,CAACtB,WAAW,CAACa,KAAK,CAAC;QAC/BU,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,eAAe,EAAE,CAAC,EAAE;OACrB,CAAC;MAEF;MACAC,WAAW,EAAE,IAAI,CAAC3B,WAAW,CAACa,KAAK,CAAC;QAClCe,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACmC,KAAK,CAAC;QAC7BC,aAAa,EAAE,CAAC,KAAK,CAAC;QACtBC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,YAAY,EAAE,IAAI,CAAChC,WAAW,CAACiC,KAAK,CAAC,EAAE,CAAC;QACxCC,eAAe,EAAE,IAAI,CAAClC,WAAW,CAACa,KAAK,CAAC;UACtCsB,MAAM,EAAE,CAAC,EAAE,CAAC;UACZZ,IAAI,EAAE,CAAC,EAAE,CAAC;UACVC,KAAK,EAAE,CAAC,EAAE,CAAC;UACXY,UAAU,EAAE,CAAC,EAAE,CAAC;UAChBX,OAAO,EAAE,CAAC,EAAE,CAAC;UACbY,QAAQ,EAAE,CAAC,KAAK;SACjB;OACF,CAAC;MAEF;MACAA,QAAQ,EAAE,CAAC,IAAI,CAAC;MAEhB;MACAC,WAAW,EAAE,IAAI,CAACtC,WAAW,CAACiC,KAAK,CAAC,EAAE;KACvC,CAAC;EACJ;EAEQzB,WAAW;IACjB,IAAI,CAACP,cAAc,CAACsC,qBAAqB,EAAE,CACxCC,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9B+B,SAAS,CAAC;MACT9B,IAAI,EAAG+B,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACC,YAAY,CAACD,OAAO,CAAC;QAC1B,IAAI,CAACE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACzC,QAAQ,CAAC2C,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACxE,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACpC,IAAI,CAACL,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQD,YAAY,CAACD,OAAoB;IACvC,IAAI,CAACrC,WAAW,CAAC6C,UAAU,CAAC;MAC1BpC,SAAS,EAAE4B,OAAO,CAAC5B,SAAS;MAC5BG,QAAQ,EAAEyB,OAAO,CAACzB,QAAQ;MAC1BC,iBAAiB,EAAEwB,OAAO,CAACxB,iBAAiB,IAAI,EAAE;MAClDC,QAAQ,EAAEuB,OAAO,CAACvB,QAAQ,IAAI,EAAE;MAChCE,OAAO,EAAEqB,OAAO,CAACrB,OAAO,IAAI,EAAE;MAC9BC,QAAQ,EAAE;QACRC,IAAI,EAAEmB,OAAO,CAACpB,QAAQ,EAAEC,IAAI,IAAI,EAAE;QAClCC,KAAK,EAAEkB,OAAO,CAACpB,QAAQ,EAAEE,KAAK,IAAI,EAAE;QACpCC,OAAO,EAAEiB,OAAO,CAACpB,QAAQ,EAAEG,OAAO,IAAI,EAAE;QACxCC,eAAe,EAAEgB,OAAO,CAACpB,QAAQ,EAAEI,eAAe,IAAI;OACvD;MACDC,WAAW,EAAE;QACXC,KAAK,EAAEc,OAAO,CAACf,WAAW,CAACC,KAAK,IAAI,EAAE;QACtCC,aAAa,EAAEa,OAAO,CAACf,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAEY,OAAO,CAACf,WAAW,CAACG,OAAO,IAAI,EAAE;QAC1CC,YAAY,EAAEW,OAAO,CAACf,WAAW,CAACI,YAAY,IAAI,EAAE;QACpDG,eAAe,EAAE;UACfC,MAAM,EAAEO,OAAO,CAACf,WAAW,CAACO,eAAe,EAAEC,MAAM,IAAI,EAAE;UACzDZ,IAAI,EAAEmB,OAAO,CAACf,WAAW,CAACO,eAAe,EAAEX,IAAI,IAAI,EAAE;UACrDC,KAAK,EAAEkB,OAAO,CAACf,WAAW,CAACO,eAAe,EAAEV,KAAK,IAAI,EAAE;UACvDY,UAAU,EAAEM,OAAO,CAACf,WAAW,CAACO,eAAe,EAAEE,UAAU,IAAI,EAAE;UACjEX,OAAO,EAAEiB,OAAO,CAACf,WAAW,CAACO,eAAe,EAAET,OAAO,IAAI,EAAE;UAC3DY,QAAQ,EAAEK,OAAO,CAACf,WAAW,CAACO,eAAe,EAAEG,QAAQ,IAAI;;OAE9D;MACDA,QAAQ,EAAEK,OAAO,CAACL;KACnB,CAAC;IAEF;IACA,IAAI,CAACc,eAAe,CAACT,OAAO,CAACf,WAAW,CAACK,YAAY,IAAI,EAAE,CAAC;IAE5D;IACA,IAAI,CAACoB,cAAc,CAACV,OAAO,CAACJ,WAAW,IAAI,EAAE,CAAC;EAChD;EAEA;EACA,IAAIN,YAAY;IACd,OAAO,IAAI,CAAC3B,WAAW,CAACgD,GAAG,CAAC,0BAA0B,CAAc;EACtE;EAEQF,eAAe,CAACG,MAAa;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACvB,YAAY;IACpCuB,UAAU,CAACC,KAAK,EAAE;IAElBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAG;MACrBH,UAAU,CAACI,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACa,KAAK,CAAC;QACrC+C,EAAE,EAAE,CAACF,KAAK,CAACE,EAAE,CAAC;QACdC,MAAM,EAAE,CAACH,KAAK,CAACG,MAAM,EAAEpE,UAAU,CAACsB,QAAQ,CAAC;QAC3C+C,IAAI,EAAE,CAACJ,KAAK,CAACI,IAAI,EAAErE,UAAU,CAACsB,QAAQ,CAAC;QACvCsB,QAAQ,EAAE,CAACqB,KAAK,CAACrB,QAAQ,CAAC;QAC1B0B,SAAS,EAAE,CAACL,KAAK,CAACK,SAAS;OAC5B,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAC,cAAc;IACZ,MAAMC,UAAU,GAAG,IAAI,CAACjE,WAAW,CAACa,KAAK,CAAC;MACxC+C,EAAE,EAAE,CAAC,IAAI,CAAC;MACVC,MAAM,EAAE,CAAC,EAAE,EAAEpE,UAAU,CAACsB,QAAQ,CAAC;MACjC+C,IAAI,EAAE,CAAC,QAAQ,EAAErE,UAAU,CAACsB,QAAQ,CAAC;MACrCsB,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB0B,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IAEF,IAAI,CAAC/B,YAAY,CAAC2B,IAAI,CAACM,UAAU,CAAC;EACpC;EAEAC,iBAAiB,CAACC,KAAa;IAC7B,IAAI,CAACnC,YAAY,CAACoC,QAAQ,CAACD,KAAK,CAAC;EACnC;EAEA;EACA,IAAI7B,WAAW;IACb,OAAO,IAAI,CAACjC,WAAW,CAACgD,GAAG,CAAC,aAAa,CAAc;EACzD;EAEQD,cAAc,CAACiB,KAAY;IACjC,MAAMC,UAAU,GAAG,IAAI,CAAChC,WAAW;IACnCgC,UAAU,CAACd,KAAK,EAAE;IAElBa,KAAK,CAACZ,OAAO,CAACc,IAAI,IAAG;MACnBD,UAAU,CAACX,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACa,KAAK,CAAC;QACrC+C,EAAE,EAAE,CAACW,IAAI,CAACX,EAAE,CAAC;QACbY,QAAQ,EAAE,CAACD,IAAI,CAACC,QAAQ,EAAE/E,UAAU,CAACsB,QAAQ,CAAC;QAC9C0D,GAAG,EAAE,CAACF,IAAI,CAACE,GAAG,EAAE,CAAChF,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACiF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;QACzEC,WAAW,EAAE,CAACJ,IAAI,CAACI,WAAW,CAAC;QAC/BtC,QAAQ,EAAE,CAACkC,IAAI,CAAClC,QAAQ;OACzB,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EAEAuC,aAAa;IACX,MAAMC,SAAS,GAAG,IAAI,CAAC7E,WAAW,CAACa,KAAK,CAAC;MACvC+C,EAAE,EAAE,CAAC,IAAI,CAAC;MACVY,QAAQ,EAAE,CAAC,UAAU,EAAE/E,UAAU,CAACsB,QAAQ,CAAC;MAC3C0D,GAAG,EAAE,CAAC,EAAE,EAAE,CAAChF,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACiF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACnEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBtC,QAAQ,EAAE,CAAC,IAAI;KAChB,CAAC;IAEF,IAAI,CAACC,WAAW,CAACqB,IAAI,CAACkB,SAAS,CAAC;EAClC;EAEAC,gBAAgB,CAACX,KAAa;IAC5B,IAAI,CAAC7B,WAAW,CAAC8B,QAAQ,CAACD,KAAK,CAAC;EAClC;EAEA;EACAY,QAAQ;IACN,IAAI,IAAI,CAAC1E,WAAW,CAAC2E,KAAK,EAAE;MAC1B,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,MAAMC,SAAS,GAAG,IAAI,CAAC7E,WAAW,CAAC8E,KAAK;MACxC,MAAMC,aAAa,GAAyB;QAC1ClE,iBAAiB,EAAEgE,SAAS,CAAChE,iBAAiB;QAC9CC,QAAQ,EAAE+D,SAAS,CAAC/D,QAAQ;QAC5BG,QAAQ,EAAE4D,SAAS,CAAC5D,QAAQ;QAC5BK,WAAW,EAAEuD,SAAS,CAACvD,WAAW;QAClCN,OAAO,EAAE6D,SAAS,CAAC7D,OAAO;QAC1BgB,QAAQ,EAAE6C,SAAS,CAAC7C;OACrB;MAED,IAAI,CAACpC,cAAc,CAACoF,aAAa,CAACD,aAAa,CAAC,CAC7C5C,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9B+B,SAAS,CAAC;QACT9B,IAAI,EAAG2E,cAAc,IAAI;UACvB,IAAI,CAACL,QAAQ,GAAG,KAAK;UACrB,IAAI,CAAC7E,QAAQ,CAAC2C,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAChF,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,UAAU,EAAEqC,cAAc,CAACC,IAAI,CAAC,CAAC;QACzD,CAAC;QACD1C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACoC,QAAQ,GAAG,KAAK;UACrBnC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACzC,QAAQ,CAAC2C,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9F;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAACwC,oBAAoB,EAAE;MAC3B,IAAI,CAACpF,QAAQ,CAAC2C,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;;EAExF;EAEAyC,QAAQ;IACN,IAAI,IAAI,CAAC/C,OAAO,EAAE;MAChB,IAAI,CAACvC,MAAM,CAAC8C,QAAQ,CAAC,CAAC,UAAU,EAAE,IAAI,CAACP,OAAO,CAAC6C,IAAI,CAAC,CAAC;KACtD,MAAM;MACL,IAAI,CAACpF,MAAM,CAAC8C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;EACAyC,sBAAsB,CAACC,KAAU;IAC/B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACG,kBAAkB,CAACH,IAAI,CAAC;;EAEjC;EAEAI,oBAAoB,CAACL,KAAU;IAC7B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACK,gBAAgB,CAACL,IAAI,CAAC;;EAE/B;EAEQG,kBAAkB,CAACH,IAAU;IACnC,IAAI,CAAC3F,cAAc,CAAC8F,kBAAkB,CAACH,IAAI,CAAC,CACzCpD,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9B+B,SAAS,CAAC;MACT9B,IAAI,EAAGuF,QAAQ,IAAI;QACjB,IAAI,IAAI,CAACxD,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAACyD,eAAe,GAAGD,QAAQ,CAACzB,GAAG;;QAE7C,IAAI,CAACrE,QAAQ,CAAC2C,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC3E,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACzC,QAAQ,CAAC2C,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC1E;KACD,CAAC;EACN;EAEQiD,gBAAgB,CAACL,IAAU;IACjC,IAAI,CAAC3F,cAAc,CAACgG,gBAAgB,CAACL,IAAI,CAAC,CACvCpD,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CAAC,CAC9B+B,SAAS,CAAC;MACT9B,IAAI,EAAGuF,QAAQ,IAAI;QACjB,IAAI,IAAI,CAACxD,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAAC0D,aAAa,GAAGF,QAAQ,CAACzB,GAAG;;QAE3C,IAAI,CAACrE,QAAQ,CAAC2C,IAAI,CAAC,sBAAsB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACzE,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACzC,QAAQ,CAAC2C,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC1E;KACD,CAAC;EACN;EAEA;EACQwC,oBAAoB;IAC1Ba,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjG,WAAW,CAACkG,QAAQ,CAAC,CAAC9C,OAAO,CAAC+C,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAACpG,WAAW,CAACgD,GAAG,CAACmD,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAY/G,SAAS,EAAE;QAChC+G,OAAO,CAACF,QAAQ,CAAC9C,OAAO,CAACkD,YAAY,IAAG;UACtC,IAAIA,YAAY,YAAYnH,SAAS,EAAE;YACrC6G,MAAM,CAACC,IAAI,CAACK,YAAY,CAACJ,QAAQ,CAAC,CAAC9C,OAAO,CAACmD,QAAQ,IAAG;cACpDD,YAAY,CAACtD,GAAG,CAACuD,QAAQ,CAAC,EAAEF,aAAa,EAAE;YAC7C,CAAC,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAG,eAAe,CAACC,SAAiB;IAC/B,MAAML,OAAO,GAAG,IAAI,CAACpG,WAAW,CAACgD,GAAG,CAACyD,SAAS,CAAC;IAC/C,IAAIL,OAAO,EAAEM,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAGD,SAAS,cAAc;;IAEnC,IAAIL,OAAO,EAAEM,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,IAAIN,OAAO,EAAEM,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,GAAGD,SAAS,qBAAqBL,OAAO,CAACO,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc,aAAa;;IAEnG,IAAIR,OAAO,EAAEM,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,GAAGD,SAAS,yBAAyBL,OAAO,CAACO,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc,aAAa;;IAEvG,IAAIR,OAAO,EAAEM,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,0BAA0B;;IAEnC,OAAO,EAAE;EACX;EAEA;EACAG,kBAAkB;IAChB,OAAO,CACL;MAAE/B,KAAK,EAAE,UAAU;MAAEgC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEhC,KAAK,EAAE,SAAS;MAAEgC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEhC,KAAK,EAAE,QAAQ;MAAEgC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEhC,KAAK,EAAE,SAAS;MAAEgC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEhC,KAAK,EAAE,UAAU;MAAEgC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEhC,KAAK,EAAE,WAAW;MAAEgC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEhC,KAAK,EAAE,UAAU;MAAEgC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEhC,KAAK,EAAE,SAAS;MAAEgC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEhC,KAAK,EAAE,OAAO;MAAEgC,KAAK,EAAE;IAAO,CAAE,CACnC;EACH;EAEA;EACAC,mBAAmB;IACjB,OAAO,CACL;MAAEjC,KAAK,EAAE,QAAQ;MAAEgC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEhC,KAAK,EAAE,UAAU;MAAEgC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEhC,KAAK,EAAE,MAAM;MAAEgC,KAAK,EAAE;IAAM,CAAE,CACjC;EACH;;;uBA5VWrH,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAuH;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCfjC5H,wEA6TM;UAGNA,qEAGM;;;UAnU+BA,qCAAgB;UAgUrBA,eAAe;UAAfA,oCAAe", "names": ["FormGroup", "Validators", "FormArray", "Subject", "takeUntil", "i0", "ProfileEditComponent", "constructor", "formBuilder", "profileService", "authService", "router", "snackBar", "profileForm", "createForm", "ngOnInit", "loadProfile", "ngOnDestroy", "destroy$", "next", "complete", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "professional<PERSON>itle", "headline", "max<PERSON><PERSON><PERSON>", "summary", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "array", "businessAddress", "street", "postalCode", "isPublic", "socialLinks", "getCurrentUserProfile", "pipe", "subscribe", "profile", "populateForm", "isLoading", "error", "console", "open", "duration", "navigate", "patchValue", "setPhoneNumbers", "setSocialLinks", "get", "phones", "phoneArray", "clear", "for<PERSON>ach", "phone", "push", "id", "number", "type", "isPrimary", "addPhoneNumber", "phoneGroup", "removePhoneNumber", "index", "removeAt", "links", "linksArray", "link", "platform", "url", "pattern", "displayName", "addSocialLink", "linkGroup", "removeSocialLink", "onSubmit", "valid", "isSaving", "formValue", "value", "updateRequest", "updateProfile", "updatedProfile", "slug", "markFormGroupTouched", "onCancel", "onProfilePhotoSelected", "event", "file", "target", "files", "uploadProfilePhoto", "onCoverPhotoSelected", "uploadCoverPhoto", "response", "profilePhotoUrl", "coverPhotoUrl", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "arrayControl", "<PERSON><PERSON><PERSON>", "getErrorMessage", "fieldName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "<PERSON><PERSON><PERSON><PERSON>", "getPlatformOptions", "label", "getPhoneTypeOptions", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-edit\\profile-edit.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { AuthService } from '../../../auth/services/auth.service';\r\nimport { UserProfile, ProfileUpdateRequest } from '../../models/profile.models';\r\n\r\n@Component({\r\n  selector: 'app-profile-edit',\r\n  templateUrl: './profile-edit.component.html',\r\n  styleUrls: ['./profile-edit.component.css']\r\n})\r\nexport class ProfileEditComponent implements OnInit, OnDestroy {\r\n  profileForm: FormGroup;\r\n  profile: UserProfile | null = null;\r\n  isLoading = true;\r\n  isSaving = false;\r\n  \r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private profileService: ProfileService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.profileForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadProfile();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private createForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      // Basic Information\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      professionalTitle: [''],\r\n      headline: ['', Validators.maxLength(220)],\r\n      summary: ['', Validators.maxLength(2000)],\r\n      \r\n      // Location\r\n      location: this.formBuilder.group({\r\n        city: [''],\r\n        state: [''],\r\n        country: [''],\r\n        displayLocation: ['']\r\n      }),\r\n      \r\n      // Contact Information\r\n      contactInfo: this.formBuilder.group({\r\n        email: ['', Validators.email],\r\n        isEmailPublic: [false],\r\n        website: [''],\r\n        portfolioUrl: [''],\r\n        phoneNumbers: this.formBuilder.array([]),\r\n        businessAddress: this.formBuilder.group({\r\n          street: [''],\r\n          city: [''],\r\n          state: [''],\r\n          postalCode: [''],\r\n          country: [''],\r\n          isPublic: [false]\r\n        })\r\n      }),\r\n      \r\n      // Privacy Settings\r\n      isPublic: [true],\r\n      \r\n      // Social Links\r\n      socialLinks: this.formBuilder.array([])\r\n    });\r\n  }\r\n\r\n  private loadProfile(): void {\r\n    this.profileService.getCurrentUserProfile()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (profile) => {\r\n          this.profile = profile;\r\n          this.populateForm(profile);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading profile:', error);\r\n          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });\r\n          this.router.navigate(['/dashboard']);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private populateForm(profile: UserProfile): void {\r\n    this.profileForm.patchValue({\r\n      firstName: profile.firstName,\r\n      lastName: profile.lastName,\r\n      professionalTitle: profile.professionalTitle || '',\r\n      headline: profile.headline || '',\r\n      summary: profile.summary || '',\r\n      location: {\r\n        city: profile.location?.city || '',\r\n        state: profile.location?.state || '',\r\n        country: profile.location?.country || '',\r\n        displayLocation: profile.location?.displayLocation || ''\r\n      },\r\n      contactInfo: {\r\n        email: profile.contactInfo.email || '',\r\n        isEmailPublic: profile.contactInfo.isEmailPublic,\r\n        website: profile.contactInfo.website || '',\r\n        portfolioUrl: profile.contactInfo.portfolioUrl || '',\r\n        businessAddress: {\r\n          street: profile.contactInfo.businessAddress?.street || '',\r\n          city: profile.contactInfo.businessAddress?.city || '',\r\n          state: profile.contactInfo.businessAddress?.state || '',\r\n          postalCode: profile.contactInfo.businessAddress?.postalCode || '',\r\n          country: profile.contactInfo.businessAddress?.country || '',\r\n          isPublic: profile.contactInfo.businessAddress?.isPublic || false\r\n        }\r\n      },\r\n      isPublic: profile.isPublic\r\n    });\r\n\r\n    // Populate phone numbers\r\n    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\r\n    \r\n    // Populate social links\r\n    this.setSocialLinks(profile.socialLinks || []);\r\n  }\r\n\r\n  // Phone Numbers Management\r\n  get phoneNumbers(): FormArray {\r\n    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;\r\n  }\r\n\r\n  private setPhoneNumbers(phones: any[]): void {\r\n    const phoneArray = this.phoneNumbers;\r\n    phoneArray.clear();\r\n    \r\n    phones.forEach(phone => {\r\n      phoneArray.push(this.formBuilder.group({\r\n        id: [phone.id],\r\n        number: [phone.number, Validators.required],\r\n        type: [phone.type, Validators.required],\r\n        isPublic: [phone.isPublic],\r\n        isPrimary: [phone.isPrimary]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addPhoneNumber(): void {\r\n    const phoneGroup = this.formBuilder.group({\r\n      id: [null],\r\n      number: ['', Validators.required],\r\n      type: ['mobile', Validators.required],\r\n      isPublic: [false],\r\n      isPrimary: [false]\r\n    });\r\n    \r\n    this.phoneNumbers.push(phoneGroup);\r\n  }\r\n\r\n  removePhoneNumber(index: number): void {\r\n    this.phoneNumbers.removeAt(index);\r\n  }\r\n\r\n  // Social Links Management\r\n  get socialLinks(): FormArray {\r\n    return this.profileForm.get('socialLinks') as FormArray;\r\n  }\r\n\r\n  private setSocialLinks(links: any[]): void {\r\n    const linksArray = this.socialLinks;\r\n    linksArray.clear();\r\n    \r\n    links.forEach(link => {\r\n      linksArray.push(this.formBuilder.group({\r\n        id: [link.id],\r\n        platform: [link.platform, Validators.required],\r\n        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\r\n        displayName: [link.displayName],\r\n        isPublic: [link.isPublic]\r\n      }));\r\n    });\r\n  }\r\n\r\n  addSocialLink(): void {\r\n    const linkGroup = this.formBuilder.group({\r\n      id: [null],\r\n      platform: ['linkedin', Validators.required],\r\n      url: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n      displayName: [''],\r\n      isPublic: [true]\r\n    });\r\n    \r\n    this.socialLinks.push(linkGroup);\r\n  }\r\n\r\n  removeSocialLink(index: number): void {\r\n    this.socialLinks.removeAt(index);\r\n  }\r\n\r\n  // Form Submission\r\n  onSubmit(): void {\r\n    if (this.profileForm.valid) {\r\n      this.isSaving = true;\r\n      \r\n      const formValue = this.profileForm.value;\r\n      const updateRequest: ProfileUpdateRequest = {\r\n        professionalTitle: formValue.professionalTitle,\r\n        headline: formValue.headline,\r\n        location: formValue.location,\r\n        contactInfo: formValue.contactInfo,\r\n        summary: formValue.summary,\r\n        isPublic: formValue.isPublic\r\n      };\r\n\r\n      this.profileService.updateProfile(updateRequest)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (updatedProfile) => {\r\n            this.isSaving = false;\r\n            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\r\n            this.router.navigate(['/profile', updatedProfile.slug]);\r\n          },\r\n          error: (error) => {\r\n            this.isSaving = false;\r\n            console.error('Error updating profile:', error);\r\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });\r\n          }\r\n        });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n      this.snackBar.open('Please fix the errors in the form', 'Close', { duration: 3000 });\r\n    }\r\n  }\r\n\r\n  onCancel(): void {\r\n    if (this.profile) {\r\n      this.router.navigate(['/profile', this.profile.slug]);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  // File Upload Methods\r\n  onProfilePhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      this.uploadProfilePhoto(file);\r\n    }\r\n  }\r\n\r\n  onCoverPhotoSelected(event: any): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      this.uploadCoverPhoto(file);\r\n    }\r\n  }\r\n\r\n  private uploadProfilePhoto(file: File): void {\r\n    this.profileService.uploadProfilePhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (this.profile) {\r\n            this.profile.profilePhotoUrl = response.url;\r\n          }\r\n          this.snackBar.open('Profile photo updated!', 'Close', { duration: 2000 });\r\n        },\r\n        error: (error) => {\r\n          console.error('Error uploading profile photo:', error);\r\n          this.snackBar.open('Error uploading photo', 'Close', { duration: 3000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  private uploadCoverPhoto(file: File): void {\r\n    this.profileService.uploadCoverPhoto(file)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (this.profile) {\r\n            this.profile.coverPhotoUrl = response.url;\r\n          }\r\n          this.snackBar.open('Cover photo updated!', 'Close', { duration: 2000 });\r\n        },\r\n        error: (error) => {\r\n          console.error('Error uploading cover photo:', error);\r\n          this.snackBar.open('Error uploading photo', 'Close', { duration: 3000 });\r\n        }\r\n      });\r\n  }\r\n\r\n  // Utility Methods\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.profileForm.controls).forEach(key => {\r\n      const control = this.profileForm.get(key);\r\n      control?.markAsTouched();\r\n      \r\n      if (control instanceof FormArray) {\r\n        control.controls.forEach(arrayControl => {\r\n          if (arrayControl instanceof FormGroup) {\r\n            Object.keys(arrayControl.controls).forEach(arrayKey => {\r\n              arrayControl.get(arrayKey)?.markAsTouched();\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getErrorMessage(fieldName: string): string {\r\n    const control = this.profileForm.get(fieldName);\r\n    if (control?.hasError('required')) {\r\n      return `${fieldName} is required`;\r\n    }\r\n    if (control?.hasError('email')) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return `${fieldName} must be at least ${control.errors?.['minlength'].requiredLength} characters`;\r\n    }\r\n    if (control?.hasError('maxlength')) {\r\n      return `${fieldName} must be no more than ${control.errors?.['maxlength'].requiredLength} characters`;\r\n    }\r\n    if (control?.hasError('pattern')) {\r\n      return 'Please enter a valid URL';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Platform options for social links\r\n  getPlatformOptions() {\r\n    return [\r\n      { value: 'linkedin', label: 'LinkedIn' },\r\n      { value: 'twitter', label: 'Twitter' },\r\n      { value: 'github', label: 'GitHub' },\r\n      { value: 'behance', label: 'Behance' },\r\n      { value: 'dribbble', label: 'Dribbble' },\r\n      { value: 'instagram', label: 'Instagram' },\r\n      { value: 'facebook', label: 'Facebook' },\r\n      { value: 'youtube', label: 'YouTube' },\r\n      { value: 'other', label: 'Other' }\r\n    ];\r\n  }\r\n\r\n  // Phone type options\r\n  getPhoneTypeOptions() {\r\n    return [\r\n      { value: 'mobile', label: 'Mobile' },\r\n      { value: 'business', label: 'Business' },\r\n      { value: 'home', label: 'Home' }\r\n    ];\r\n  }\r\n}\r\n", "<div class=\"profile-edit-container\" *ngIf=\"!isLoading\">\r\n  <div class=\"edit-header\">\r\n    <h1>\r\n      <mat-icon>edit</mat-icon>\r\n      Edit Profile\r\n    </h1>\r\n    <div class=\"header-actions\">\r\n      <button mat-stroked-button (click)=\"onCancel()\" [disabled]=\"isSaving\">\r\n        Cancel\r\n      </button>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        (click)=\"onSubmit()\"\r\n        [disabled]=\"isSaving || profileForm.invalid\">\r\n        <mat-icon *ngIf=\"isSaving\">\r\n          <mat-spinner diameter=\"20\"></mat-spinner>\r\n        </mat-icon>\r\n        <mat-icon *ngIf=\"!isSaving\">save</mat-icon>\r\n        {{ isSaving ? 'Saving...' : 'Save Changes' }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <form [formGroup]=\"profileForm\" class=\"profile-form\">\r\n    <!-- Photo Upload Section -->\r\n    <mat-card class=\"photo-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Profile Photos</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"photo-uploads\">\r\n          <div class=\"profile-photo-upload\">\r\n            <div class=\"photo-preview\">\r\n              <img [src]=\"profile?.profilePhotoUrl || '/assets/images/default-avatar.png'\"\r\n                   alt=\"Profile Photo\" class=\"profile-photo\">\r\n              <div class=\"photo-overlay\">\r\n                <mat-icon>camera_alt</mat-icon>\r\n              </div>\r\n            </div>\r\n            <input type=\"file\" #profilePhotoInput (change)=\"onProfilePhotoSelected($event)\"\r\n                   accept=\"image/*\" style=\"display: none;\">\r\n            <button mat-stroked-button (click)=\"profilePhotoInput.click()\">\r\n              Change Profile Photo\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"cover-photo-upload\">\r\n            <div class=\"cover-preview\">\r\n              <div class=\"cover-photo\"\r\n                   [style.background-image]=\"(profile && profile.coverPhotoUrl) ? ('url(' + profile.coverPhotoUrl + ')') : 'var(--theme-gradient-primary)'\">\r\n                <div class=\"photo-overlay\">\r\n                  <mat-icon>camera_alt</mat-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <input type=\"file\" #coverPhotoInput (change)=\"onCoverPhotoSelected($event)\"\r\n                   accept=\"image/*\" style=\"display: none;\">\r\n            <button mat-stroked-button (click)=\"coverPhotoInput.click()\">\r\n              Change Cover Photo\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Basic Information -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Basic Information</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>First Name</mat-label>\r\n            <input matInput formControlName=\"firstName\" required>\r\n            <mat-error *ngIf=\"profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched\">\r\n              {{ getErrorMessage('firstName') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Last Name</mat-label>\r\n            <input matInput formControlName=\"lastName\" required>\r\n            <mat-error *ngIf=\"profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched\">\r\n              {{ getErrorMessage('lastName') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Title</mat-label>\r\n          <input matInput formControlName=\"professionalTitle\"\r\n                 placeholder=\"e.g., Senior Software Engineer, UX Designer\">\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Headline</mat-label>\r\n          <textarea matInput formControlName=\"headline\" rows=\"2\"\r\n                    placeholder=\"A brief, compelling description of what you do\"\r\n                    maxlength=\"220\"></textarea>\r\n          <mat-hint align=\"end\">{{ profileForm.get('headline')?.value?.length || 0 }}/220</mat-hint>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>Professional Summary</mat-label>\r\n          <textarea matInput formControlName=\"summary\" rows=\"6\"\r\n                    placeholder=\"Describe your expertise, experience, and what makes you unique\"\r\n                    maxlength=\"2000\"></textarea>\r\n          <mat-hint align=\"end\">{{ profileForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>\r\n        </mat-form-field>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Location Information -->\r\n    <mat-card class=\"form-section\" formGroupName=\"location\">\r\n      <mat-card-header>\r\n        <mat-card-title>Location</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>City</mat-label>\r\n            <input matInput formControlName=\"city\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>State/Province</mat-label>\r\n            <input matInput formControlName=\"state\">\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Country</mat-label>\r\n            <input matInput formControlName=\"country\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Display Location</mat-label>\r\n            <input matInput formControlName=\"displayLocation\"\r\n                   placeholder=\"e.g., San Francisco, CA\">\r\n          </mat-form-field>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Contact Information -->\r\n    <mat-card class=\"form-section\" formGroupName=\"contactInfo\">\r\n      <mat-card-header>\r\n        <mat-card-title>Contact Information</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Email</mat-label>\r\n            <input matInput formControlName=\"email\" type=\"email\">\r\n            <mat-error *ngIf=\"profileForm.get('contactInfo.email')?.invalid && profileForm.get('contactInfo.email')?.touched\">\r\n              {{ getErrorMessage('contactInfo.email') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <div class=\"checkbox-field\">\r\n            <mat-checkbox formControlName=\"isEmailPublic\">\r\n              Make email public\r\n            </mat-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Website</mat-label>\r\n            <input matInput formControlName=\"website\" placeholder=\"https://yourwebsite.com\">\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n            <mat-label>Portfolio URL</mat-label>\r\n            <input matInput formControlName=\"portfolioUrl\" placeholder=\"https://portfolio.com\">\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Phone Numbers -->\r\n        <div class=\"phone-numbers-section\">\r\n          <h4>Phone Numbers</h4>\r\n          <div formArrayName=\"phoneNumbers\">\r\n            <div *ngFor=\"let phone of phoneNumbers.controls; let i = index\"\r\n                 [formGroupName]=\"i\" class=\"phone-number-item\">\r\n              <div class=\"form-row\">\r\n                <mat-form-field appearance=\"outline\" class=\"phone-input\">\r\n                  <mat-label>Phone Number</mat-label>\r\n                  <input matInput formControlName=\"number\" placeholder=\"+****************\">\r\n                </mat-form-field>\r\n\r\n                <mat-form-field appearance=\"outline\" class=\"phone-type\">\r\n                  <mat-label>Type</mat-label>\r\n                  <mat-select formControlName=\"type\">\r\n                    <mat-option *ngFor=\"let type of getPhoneTypeOptions()\" [value]=\"type.value\">\r\n                      {{ type.label }}\r\n                    </mat-option>\r\n                  </mat-select>\r\n                </mat-form-field>\r\n\r\n                <div class=\"phone-controls\">\r\n                  <mat-checkbox formControlName=\"isPublic\">Public</mat-checkbox>\r\n                  <mat-checkbox formControlName=\"isPrimary\">Primary</mat-checkbox>\r\n                  <button mat-icon-button color=\"warn\" (click)=\"removePhoneNumber(i)\" type=\"button\">\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button mat-stroked-button (click)=\"addPhoneNumber()\" type=\"button\">\r\n            <mat-icon>add</mat-icon>\r\n            Add Phone Number\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Business Address -->\r\n        <div class=\"business-address-section\" formGroupName=\"businessAddress\">\r\n          <h4>Business Address</h4>\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Street Address</mat-label>\r\n            <input matInput formControlName=\"street\">\r\n          </mat-form-field>\r\n\r\n          <div class=\"form-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>City</mat-label>\r\n              <input matInput formControlName=\"city\">\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>State</mat-label>\r\n              <input matInput formControlName=\"state\">\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"third-width\">\r\n              <mat-label>Postal Code</mat-label>\r\n              <input matInput formControlName=\"postalCode\">\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Country</mat-label>\r\n              <input matInput formControlName=\"country\">\r\n            </mat-form-field>\r\n\r\n            <div class=\"checkbox-field\">\r\n              <mat-checkbox formControlName=\"isPublic\">\r\n                Make business address public\r\n              </mat-checkbox>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Social Links -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Social Links</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div formArrayName=\"socialLinks\">\r\n          <div *ngFor=\"let link of socialLinks.controls; let i = index\"\r\n               [formGroupName]=\"i\" class=\"social-link-item\">\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"platform-select\">\r\n                <mat-label>Platform</mat-label>\r\n                <mat-select formControlName=\"platform\">\r\n                  <mat-option *ngFor=\"let platform of getPlatformOptions()\" [value]=\"platform.value\">\r\n                    {{ platform.label }}\r\n                  </mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"url-input\">\r\n                <mat-label>URL</mat-label>\r\n                <input matInput formControlName=\"url\" placeholder=\"https://linkedin.com/in/yourname\">\r\n              </mat-form-field>\r\n\r\n              <div class=\"link-controls\">\r\n                <mat-checkbox formControlName=\"isPublic\">Public</mat-checkbox>\r\n                <button mat-icon-button color=\"warn\" (click)=\"removeSocialLink(i)\" type=\"button\">\r\n                  <mat-icon>delete</mat-icon>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <button mat-stroked-button (click)=\"addSocialLink()\" type=\"button\">\r\n          <mat-icon>add</mat-icon>\r\n          Add Social Link\r\n        </button>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <!-- Privacy Settings -->\r\n    <mat-card class=\"form-section\">\r\n      <mat-card-header>\r\n        <mat-card-title>Privacy Settings</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <div class=\"privacy-controls\">\r\n          <mat-slide-toggle formControlName=\"isPublic\" color=\"primary\">\r\n            <span class=\"toggle-label\">Make profile public</span>\r\n            <p class=\"toggle-description\">\r\n              When enabled, your profile will be visible to everyone and searchable.\r\n              When disabled, only you can see your profile.\r\n            </p>\r\n          </mat-slide-toggle>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </form>\r\n</div>\r\n\r\n<!-- Loading State -->\r\n<div class=\"loading-container\" *ngIf=\"isLoading\">\r\n  <mat-spinner diameter=\"50\"></mat-spinner>\r\n  <p>Loading profile...</p>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}