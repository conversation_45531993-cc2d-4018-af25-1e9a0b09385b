import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private readonly TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user_info';
  private readonly REMEMBER_ME_KEY = 'remember_me';

  // Token management
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY) || sessionStorage.getItem(this.TOKEN_KEY);
  }

  setToken(token: string, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem(this.TOKEN_KEY, token);
      sessionStorage.removeItem(this.TOKEN_KEY);
    } else {
      sessionStorage.setItem(this.TOKEN_KEY, token);
      localStorage.removeItem(this.TOKEN_KEY);
    }
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY) || sessionStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  setRefreshToken(token: string, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
    } else {
      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, token);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
  }

  setUser(user: any, rememberMe: boolean = false): void {
    const userStr = JSON.stringify(user);
    if (rememberMe) {
      localStorage.setItem(this.USER_KEY, userStr);
      sessionStorage.removeItem(this.USER_KEY);
    } else {
      sessionStorage.setItem(this.USER_KEY, userStr);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  getStoredUser(): any | null {
    const userStr = localStorage.getItem(this.USER_KEY) || sessionStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  setRememberMe(rememberMe: boolean): void {
    if (rememberMe) {
      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');
    } else {
      localStorage.removeItem(this.REMEMBER_ME_KEY);
    }
  }

  getRememberMe(): boolean {
    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(this.decodeJwtPayload(token) as string);
      const exp = payload.exp;
      if (!exp) return true;
      const now = Math.floor(Date.now() / 1000);
      return now > exp;
    } catch (error) {
      console.warn("Invalid token format:", error);
      return true; // Treat invalid token as expired
    }
  }

  decodeJwtPayload(token: string): string | null {
    try {
      const payload = token.split('.')[1];
      if (!payload) return null;

      // Base64url decode: convert to base64
      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
      const padded = base64 + '='.repeat((4 - base64.length % 4) % 4);

      // Decode UTF-8 payload
      const binary = atob(padded);
      const utf8 = decodeURIComponent(
        Array.prototype.map
          .call(binary, c => '%' + c.charCodeAt(0).toString(16).padStart(2, '0'))
          .join('')
      );

        return utf8;
      } catch (e) {
        console.error("Failed to decode JWT payload:", e);
        return null;
      }
    }

  clearAllTokens(): void {
    // Clear from both localStorage and sessionStorage
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem(this.REMEMBER_ME_KEY);
    
    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
    sessionStorage.removeItem(this.USER_KEY);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token && !this.isTokenExpired(token);
  }
}
