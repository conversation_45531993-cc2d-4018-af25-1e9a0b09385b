# 🎨 Oracul Theme Customization Guide

This guide explains how to customize the color scheme and themes in the Oracul application.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Built-in Themes](#built-in-themes)
3. [Using the Theme Selector](#using-the-theme-selector)
4. [Creating Custom Themes](#creating-custom-themes)
5. [CSS Custom Properties](#css-custom-properties)
6. [Programmatic Theme Changes](#programmatic-theme-changes)
7. [Exporting and Importing Themes](#exporting-and-importing-themes)
8. [Advanced Customization](#advanced-customization)

## 🎯 Overview

The Oracul application features a comprehensive theming system that allows you to:

- **Switch between built-in themes** instantly
- **Create custom themes** with your brand colors
- **Export and import** theme configurations
- **Persist theme preferences** across sessions
- **Apply themes** to all components consistently

## 🌈 Built-in Themes

The application comes with 4 pre-built themes:

### 1. **Deep Purple & Amber** (Default)
- **Primary**: Deep Purple (#673ab7)
- **Accent**: <PERSON> (#ffc107)
- **Best for**: Professional, modern look

### 2. **Blue & Orange**
- **Primary**: Blue (#2196f3)
- **Accent**: Orange (#ff9800)
- **Best for**: Tech companies, startups

### 3. **Green & Teal**
- **Primary**: Green (#4caf50)
- **Accent**: Teal (#009688)
- **Best for**: Environmental, health apps

### 4. **Dark Theme**
- **Primary**: Purple (#bb86fc)
- **Accent**: Teal (#03dac6)
- **Best for**: Night mode, developer tools

## 🎛️ Using the Theme Selector

### Access the Theme Selector:
1. **Login** to the application
2. Click the **user menu** (account icon) in the top-right
3. Select **"Themes"** from the dropdown
4. Choose your preferred theme from the visual grid

### Features:
- **Visual preview** of each theme's colors
- **Instant application** when selected
- **Automatic persistence** of your choice
- **Export/Import** functionality

## 🛠️ Creating Custom Themes

### Method 1: Modify Configuration Files

Edit the theme configuration in `oracul.client/src/app/core/theme/theme.config.ts`:

```typescript
export const MY_CUSTOM_THEME: ThemeConfig = {
  name: 'my-custom-theme',
  colors: {
    primary: '#your-primary-color',
    primaryLight: '#your-primary-light',
    primaryDark: '#your-primary-dark',
    accent: '#your-accent-color',
    accentLight: '#your-accent-light',
    accentDark: '#your-accent-dark',
    warn: '#f44336',
    success: '#4caf50',
    error: '#f44336',
    background: '#fafafa',
    surface: '#ffffff',
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
      hint: 'rgba(0, 0, 0, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #your-color1 0%, #your-color2 100%)',
      secondary: 'linear-gradient(135deg, #your-color3 0%, #your-color4 100%)',
      auth: 'linear-gradient(135deg, #your-auth1 0%, #your-auth2 100%)'
    },
    oauth: {
      google: {
        background: '#ffffff',
        border: '#dadce0',
        text: '#3c4043',
        hover: '#f8f9fa'
      },
      facebook: {
        background: '#ffffff',
        border: '#1877f2',
        text: '#1877f2',
        hover: '#f0f2f5'
      }
    }
  }
};
```

Then add it to the `AVAILABLE_THEMES` array:

```typescript
export const AVAILABLE_THEMES: ThemeConfig[] = [
  DEFAULT_THEME,
  BLUE_ORANGE_THEME,
  GREEN_TEAL_THEME,
  DARK_THEME,
  MY_CUSTOM_THEME  // Add your theme here
];
```

### Method 2: Programmatic Creation

Use the ThemeService to create themes dynamically:

```typescript
import { ThemeService } from './core/theme/theme.service';

constructor(private themeService: ThemeService) {}

createMyTheme() {
  const customTheme = this.themeService.createCustomTheme('my-brand', {
    primary: '#your-brand-primary',
    accent: '#your-brand-accent',
    gradient: {
      auth: 'linear-gradient(135deg, #brand1 0%, #brand2 100%)'
    }
  });
  
  this.themeService.setTheme(customTheme);
}
```

## 🎨 CSS Custom Properties

The theming system uses CSS custom properties (variables) that you can reference in your styles:

### Available Variables:

```css
/* Primary Colors */
--theme-primary
--theme-primary-light
--theme-primary-dark

/* Accent Colors */
--theme-accent
--theme-accent-light
--theme-accent-dark

/* Status Colors */
--theme-warn
--theme-success
--theme-error

/* Background Colors */
--theme-background
--theme-surface

/* Text Colors */
--theme-text-primary
--theme-text-secondary
--theme-text-disabled
--theme-text-hint

/* Gradients */
--theme-gradient-primary
--theme-gradient-secondary
--theme-gradient-auth

/* OAuth Button Colors */
--theme-google-bg
--theme-google-border
--theme-google-text
--theme-google-hover
--theme-facebook-bg
--theme-facebook-border
--theme-facebook-text
--theme-facebook-hover
```

### Usage Example:

```css
.my-component {
  background-color: var(--theme-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-accent);
}

.my-gradient-background {
  background: var(--theme-gradient-primary);
}
```

## 💻 Programmatic Theme Changes

### In Components:

```typescript
import { ThemeService } from './core/theme/theme.service';

export class MyComponent {
  constructor(private themeService: ThemeService) {}

  switchToBlueTheme() {
    this.themeService.setThemeByName('blue-orange');
  }

  getCurrentTheme() {
    return this.themeService.getCurrentTheme();
  }

  getAvailableThemes() {
    return this.themeService.getAvailableThemes();
  }
}
```

### Subscribe to Theme Changes:

```typescript
ngOnInit() {
  this.themeService.currentTheme$.subscribe(theme => {
    console.log('Theme changed to:', theme.name);
    // React to theme changes
  });
}
```

## 📁 Exporting and Importing Themes

### Export Current Theme:
1. Open the **Theme Selector**
2. Click **"Export Current Theme"**
3. A JSON file will be downloaded with your theme configuration

### Import Custom Theme:
1. Open the **Theme Selector**
2. Click **"Import Theme"**
3. Select a JSON theme file
4. The theme will be applied immediately

### Theme File Format:
```json
{
  "name": "my-custom-theme",
  "colors": {
    "primary": "#673ab7",
    "accent": "#ffc107",
    // ... other color properties
  }
}
```

## 🔧 Advanced Customization

### Adding New CSS Variables:

1. **Update theme.config.ts** with new color properties
2. **Modify theme.service.ts** to apply the new variables
3. **Add to theme.css** for global usage

### Creating Theme-Specific Components:

```css
/* Component will look different in dark theme */
.theme-dark .my-component {
  background-color: var(--theme-surface);
  border: 1px solid var(--theme-text-disabled);
}

.theme-blue-orange .my-component {
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}
```

### Environment-Specific Themes:

```typescript
// In environment.ts
export const environment = {
  production: false,
  defaultTheme: 'deep-purple-amber',
  allowThemeCustomization: true
};

// In environment.prod.ts
export const environment = {
  production: true,
  defaultTheme: 'my-brand-theme',
  allowThemeCustomization: false
};
```

## 🚀 Quick Start Checklist

- [ ] **Choose a built-in theme** from the Theme Selector
- [ ] **Test all themes** to see which fits your brand
- [ ] **Create a custom theme** with your brand colors
- [ ] **Export your theme** for backup/sharing
- [ ] **Update CSS** to use theme variables instead of hardcoded colors
- [ ] **Test theme switching** across all components

## 📝 Best Practices

1. **Always use CSS custom properties** instead of hardcoded colors
2. **Test your themes** in both light and dark variants
3. **Ensure sufficient contrast** for accessibility
4. **Export themes** before making major changes
5. **Use semantic color names** (primary, accent, etc.) rather than specific colors
6. **Test OAuth buttons** appearance in your custom themes

## 🎨 Color Palette Tools

Recommended tools for creating harmonious color schemes:

- **Material Design Color Tool**: https://material.io/resources/color/
- **Coolors.co**: https://coolors.co/
- **Adobe Color**: https://color.adobe.com/
- **Paletton**: http://paletton.com/

---

**Happy Theming! 🎨**

For questions or issues, please refer to the main project documentation or create an issue in the repository.
