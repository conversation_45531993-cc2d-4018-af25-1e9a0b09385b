import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../services/auth.service';
import { OAuthService, OAuthUser } from '../services/oauth.service';
import { LoginRequest, OAuthLoginRequest } from '../models/auth.models';
import { TranslationService } from '../../core/i18n/translation.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  returnUrl = '/';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private oauthService: OAuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    public t: TranslationService
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';

    // Redirect if already logged in
    this.authService.isAuthenticated$.subscribe(isAuth => {
      if (isAuth) {
        this.router.navigate([this.returnUrl]);
      }
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;

      const loginRequest: LoginRequest = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password,
        rememberMe: this.loginForm.value.rememberMe
      };

      this.authService.login(loginRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open(this.t.auth.loginSuccess, this.t.common.close, {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
            this.router.navigate([this.returnUrl]);
          } else {
            this.snackBar.open(response.message || this.t.auth.loginError, this.t.common.close, {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
          }
        },
        error: (error) => {
          this.isLoading = false;
          const errorMessage = this.getErrorMessage(error);
          this.snackBar.open(errorMessage, this.t.common.close, {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getEmailErrorMessage(): string {
    const emailControl = this.loginForm.get('email');
    if (emailControl?.hasError('required')) {
      return this.t.auth.emailRequired;
    }
    if (emailControl?.hasError('email')) {
      return this.t.auth.invalidEmail;
    }
    return '';
  }

  getPasswordErrorMessage(): string {
    const passwordControl = this.loginForm.get('password');
    if (passwordControl?.hasError('required')) {
      return this.t.auth.passwordRequired;
    }
    if (passwordControl?.hasError('minlength')) {
      return this.t.auth.passwordTooShort;
    }
    return '';
  }

  private getErrorMessage(error: any): string {
    // Handle different types of errors
    if (typeof error === 'string') {
      return error;
    }

    if (error?.error?.message) {
      const message = error.error.message.toLowerCase();

      // Account locked
      if (message.includes('locked') || message.includes('temporarily locked')) {
        return 'Акаунтът е временно заключен поради множество неуспешни опити за вход. Моля, опитайте отново след 30 минути.';
      }

      // Invalid credentials
      if (message.includes('invalid') || message.includes('password') || message.includes('email')) {
        return 'Невалиден имейл или парола. Моля, проверете данните си и опитайте отново.';
      }

      // Account deactivated
      if (message.includes('deactivated') || message.includes('inactive')) {
        return 'Акаунтът е деактивиран. Моля, свържете се с поддръжката.';
      }

      return error.error.message;
    }

    // Network errors
    if (error?.status === 0 || error?.name === 'HttpErrorResponse') {
      return 'Проблем с мрежовата връзка. Моля, проверете интернет връзката си и опитайте отново.';
    }

    // Server errors
    if (error?.status >= 500) {
      return 'Възникна проблем със сървъра. Моля, опитайте отново след малко.';
    }

    // Default error message
    return this.t.auth.loginError;
  }

  navigateToRegister(): void {
    this.router.navigate(['/register'], { queryParams: { returnUrl: this.returnUrl } });
  }

  navigateToForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  signInWithGoogle(): void {
    this.isLoading = true;

    this.oauthService.signInWithGooglePopup().subscribe({
      next: (oauthUser: OAuthUser) => {
        this.handleOAuthLogin(oauthUser);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Неуспешен вход с Google. Моля, опитайте отново.', this.t.common.close, {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  signInWithFacebook(): void {
    this.isLoading = true;

    this.oauthService.signInWithFacebook().subscribe({
      next: (oauthUser: OAuthUser) => {
        this.handleOAuthLogin(oauthUser);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Неуспешен вход с Facebook. Моля, опитайте отново.', this.t.common.close, {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private handleOAuthLogin(oauthUser: OAuthUser): void {
    const oauthRequest: OAuthLoginRequest = {
      provider: oauthUser.provider,
      accessToken: oauthUser.accessToken,
      email: oauthUser.email,
      firstName: oauthUser.firstName,
      lastName: oauthUser.lastName,
      profilePictureUrl: oauthUser.profilePictureUrl
    };

    this.authService.loginWithOAuth(oauthRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.snackBar.open(`Добре дошли! Влязохте с ${oauthUser.provider}`, this.t.common.close, {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate([this.returnUrl]);
        } else {
          this.snackBar.open(response.message || 'Неуспешен OAuth вход', this.t.common.close, {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error || 'Неуспешен OAuth вход. Моля, опитайте отново.', this.t.common.close, {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }
}
