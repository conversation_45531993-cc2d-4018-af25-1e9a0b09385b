import { Component, OnInit } from '@angular/core';
import { ThemeService } from './core/theme/theme.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'oracul.client';

  constructor(
    private themeService: ThemeService
  ) {}

  ngOnInit() {
    // Initialize theme
    this.themeService.getCurrentTheme();
  }
}
