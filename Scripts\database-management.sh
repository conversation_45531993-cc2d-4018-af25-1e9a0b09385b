#!/bin/bash

# Database Management Scripts for Oracul Application
# Bash script for Linux/Mac environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

echo -e "${MAGENTA}🔮 Oracul Database Management Script${NC}"
echo -e "${CYAN}Project Root: $PROJECT_ROOT${NC}"
echo ""

show_usage() {
    echo -e "${<PERSON>YAN}Available actions:${NC}"
    echo -e "  ${WHITE}status${NC}   - Check database status and migrations"
    echo -e "  ${WHITE}migrate${NC}  - Apply pending migrations"
    echo -e "  ${WHITE}seed${NC}     - Seed database with oracle profiles"
    echo -e "  ${WHITE}recreate${NC} - Drop and recreate empty database"
    echo -e "  ${WHITE}reset${NC}    - Full reset (drop + migrate + seed)"
    echo -e "  ${WHITE}drop${NC}     - Drop database (WARNING: deletes all data)"
    echo ""
    echo -e "${YELLOW}Usage examples:${NC}"
    echo -e "  ${GRAY}./Scripts/database-management.sh status${NC}"
    echo -e "  ${GRAY}./Scripts/database-management.sh reset${NC}"
    echo -e "  ${GRAY}./Scripts/database-management.sh seed${NC}"
}

show_status() {
    echo -e "${YELLOW}📊 Database Status Check${NC}"
    
    # Check if database exists
    if dotnet ef database list --project Oracul.Data --startup-project Oracul.Server > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Database connection successful${NC}"
        dotnet ef database list --project Oracul.Data --startup-project Oracul.Server
        
        # Check for pending migrations
        echo -e "${CYAN}📋 Migration Status:${NC}"
        dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server
    else
        echo -e "${RED}❌ Database connection failed${NC}"
        dotnet ef database list --project Oracul.Data --startup-project Oracul.Server
    fi
}

drop_database() {
    echo -e "${RED}🗑️ Dropping Database...${NC}"
    
    if dotnet ef database drop --project Oracul.Data --startup-project Oracul.Server --force; then
        echo -e "${GREEN}✅ Database dropped successfully${NC}"
    else
        echo -e "${RED}❌ Failed to drop database${NC}"
        return 1
    fi
}

run_migrations() {
    echo -e "${BLUE}🔄 Running Database Migrations...${NC}"
    
    if dotnet ef database update --project Oracul.Data --startup-project Oracul.Server; then
        echo -e "${GREEN}✅ Migrations applied successfully${NC}"
    else
        echo -e "${RED}❌ Failed to apply migrations${NC}"
        return 1
    fi
}

seed_database() {
    echo -e "${GREEN}🌱 Seeding Database with Oracle Profiles...${NC}"
    
    # Build the project first
    echo -e "${YELLOW}🔨 Building project...${NC}"
    if ! dotnet build Oracul.Server; then
        echo -e "${RED}❌ Build failed${NC}"
        return 1
    fi
    
    # Run the seeding command
    echo -e "${YELLOW}🌱 Running seeding service...${NC}"
    if dotnet run --project Oracul.Server --seed-only; then
        echo -e "${GREEN}✅ Database seeded successfully with 5 oracle profiles${NC}"
    else
        echo -e "${RED}❌ Failed to seed database${NC}"
        return 1
    fi
}

reset_database() {
    echo -e "${MAGENTA}🔄 Resetting Database (Drop + Migrate + Seed)...${NC}"
    
    drop_database
    sleep 2
    run_migrations
    sleep 2
    seed_database
    
    echo ""
    echo -e "${GREEN}🎉 Database reset complete!${NC}"
    echo -e "${CYAN}The database now contains 5 fresh oracle profiles:${NC}"
    echo -e "  ${WHITE}• Luna Starweaver (Astrologer)${NC}"
    echo -e "  ${WHITE}• Sage Moonchild (Crystal Healer)${NC}"
    echo -e "  ${WHITE}• River Palmistry (Palm Reader)${NC}"
    echo -e "  ${WHITE}• Aurora Wisdom (Spiritual Counselor)${NC}"
    echo -e "  ${WHITE}• Cosmic Dawn (Numerologist)${NC}"
}

recreate_database() {
    echo -e "${BLUE}🏗️ Recreating Database (Drop + Migrate only)...${NC}"
    
    drop_database
    sleep 2
    run_migrations
    
    echo ""
    echo -e "${GREEN}✅ Database recreated with empty tables${NC}"
    echo -e "${CYAN}Run 'seed' action to populate with oracle profiles${NC}"
}

confirm_action() {
    local message="$1"
    echo -e "${RED}⚠️ WARNING: $message${NC}"
    read -p "Type 'YES' to confirm: " confirm
    if [ "$confirm" != "YES" ]; then
        echo -e "${YELLOW}❌ Operation cancelled${NC}"
        return 1
    fi
    return 0
}

# Main script logic
case "${1,,}" in
    "status")
        show_status
        ;;
    "drop")
        if confirm_action "This will permanently delete all data!"; then
            drop_database
        fi
        ;;
    "migrate")
        run_migrations
        ;;
    "seed")
        seed_database
        ;;
    "recreate")
        if confirm_action "This will drop and recreate the database!"; then
            recreate_database
        fi
        ;;
    "reset")
        if confirm_action "This will completely reset the database with fresh oracle data!"; then
            reset_database
        fi
        ;;
    *)
        echo -e "${RED}❌ Invalid or missing action: $1${NC}"
        echo ""
        show_usage
        exit 1
        ;;
esac

echo ""
echo -e "${GRAY}Script completed.${NC}"
