export interface ThemeColors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  accent: string;
  accentLight: string;
  accentDark: string;
  warn: string;
  success: string;
  error: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
    hint: string;
  };
  gradient: {
    primary: string;
    secondary: string;
    auth: string;
  };
  oauth: {
    google: {
      background: string;
      border: string;
      text: string;
      hover: string;
    };
    facebook: {
      background: string;
      border: string;
      text: string;
      hover: string;
    };
  };
}

export interface ThemeConfig {
  name: string;
  colors: ThemeColors;
}

// Default Deep Purple & Amber Theme
export const DEFAULT_THEME: ThemeConfig = {
  name: 'deep-purple-amber',
  colors: {
    primary: '#673ab7',
    primaryLight: '#9c64e2',
    primaryDark: '#320b86',
    accent: '#ffc107',
    accentLight: '#fff350',
    accentDark: '#c79100',
    warn: '#f44336',
    success: '#4caf50',
    error: '#f44336',
    background: '#fafafa',
    surface: '#ffffff',
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
      hint: 'rgba(0, 0, 0, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      auth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    oauth: {
      google: {
        background: '#ffffff',
        border: '#dadce0',
        text: '#3c4043',
        hover: '#f8f9fa'
      },
      facebook: {
        background: '#ffffff',
        border: '#1877f2',
        text: '#1877f2',
        hover: '#f0f2f5'
      }
    }
  }
};

// Alternative Blue & Orange Theme
export const BLUE_ORANGE_THEME: ThemeConfig = {
  name: 'blue-orange',
  colors: {
    primary: '#2196f3',
    primaryLight: '#64b5f6',
    primaryDark: '#1976d2',
    accent: '#ff9800',
    accentLight: '#ffb74d',
    accentDark: '#f57c00',
    warn: '#f44336',
    success: '#4caf50',
    error: '#f44336',
    background: '#fafafa',
    surface: '#ffffff',
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
      hint: 'rgba(0, 0, 0, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)',
      secondary: 'linear-gradient(135deg, #ff9800 0%, #ff5722 100%)',
      auth: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)'
    },
    oauth: {
      google: {
        background: '#ffffff',
        border: '#dadce0',
        text: '#3c4043',
        hover: '#f8f9fa'
      },
      facebook: {
        background: '#ffffff',
        border: '#1877f2',
        text: '#1877f2',
        hover: '#f0f2f5'
      }
    }
  }
};

// Green & Teal Theme
export const GREEN_TEAL_THEME: ThemeConfig = {
  name: 'green-teal',
  colors: {
    primary: '#4caf50',
    primaryLight: '#81c784',
    primaryDark: '#388e3c',
    accent: '#009688',
    accentLight: '#4db6ac',
    accentDark: '#00695c',
    warn: '#f44336',
    success: '#4caf50',
    error: '#f44336',
    background: '#fafafa',
    surface: '#ffffff',
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)',
      hint: 'rgba(0, 0, 0, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
      secondary: 'linear-gradient(135deg, #009688 0%, #4caf50 100%)',
      auth: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'
    },
    oauth: {
      google: {
        background: '#ffffff',
        border: '#dadce0',
        text: '#3c4043',
        hover: '#f8f9fa'
      },
      facebook: {
        background: '#ffffff',
        border: '#1877f2',
        text: '#1877f2',
        hover: '#f0f2f5'
      }
    }
  }
};

// Dark Theme
export const DARK_THEME: ThemeConfig = {
  name: 'dark',
  colors: {
    primary: '#bb86fc',
    primaryLight: '#d7b3ff',
    primaryDark: '#985eff',
    accent: '#03dac6',
    accentLight: '#66fff9',
    accentDark: '#00a896',
    warn: '#cf6679',
    success: '#4caf50',
    error: '#cf6679',
    background: '#121212',
    surface: '#1e1e1e',
    text: {
      primary: 'rgba(255, 255, 255, 0.87)',
      secondary: 'rgba(255, 255, 255, 0.6)',
      disabled: 'rgba(255, 255, 255, 0.38)',
      hint: 'rgba(255, 255, 255, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)',
      secondary: 'linear-gradient(135deg, #03dac6 0%, #018786 100%)',
      auth: 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)'
    },
    oauth: {
      google: {
        background: '#2d2d2d',
        border: '#5f6368',
        text: '#e8eaed',
        hover: '#3c4043'
      },
      facebook: {
        background: '#2d2d2d',
        border: '#1877f2',
        text: '#1877f2',
        hover: '#3c4043'
      }
    }
  }
};

// Mystical Purple Theme
export const MYSTICAL_PURPLE_THEME: ThemeConfig = {
  name: 'mystical-purple',
  colors: {
    primary: '#67455c', // тъмен пурпурен
    primaryLight: '#a07ba0', // розово-лилаво
    primaryDark: '#3f2f4e', // дълбоко мистично лилаво
    accent: '#d2a6d0', // нежен лилав
    accentLight: '#e6dbec', // много светло лилаво
    accentDark: '#a07ba0', // розово-лилаво
    warn: '#d2869d',
    success: '#8fbc8f',
    error: '#d2869d',
    background: '#e6dbec', // много светло лилаво
    surface: '#ffffff',
    text: {
      primary: '#3f2f4e', // дълбоко мистично лилаво
      secondary: '#67455c', // тъмен пурпурен
      disabled: 'rgba(63, 47, 78, 0.38)',
      hint: 'rgba(63, 47, 78, 0.38)'
    },
    gradient: {
      primary: 'linear-gradient(135deg, #d2a6d0 0%, #67455c 100%)',
      secondary: 'linear-gradient(135deg, #a07ba0 0%, #3f2f4e 100%)',
      auth: 'linear-gradient(135deg, #d2a6d0 0%, #a07ba0 100%)'
    },
    oauth: {
      google: {
        background: '#ffffff',
        border: '#d2a6d0',
        text: '#67455c',
        hover: '#e6dbec'
      },
      facebook: {
        background: '#ffffff',
        border: '#67455c',
        text: '#67455c',
        hover: '#e6dbec'
      }
    }
  }
};

export const AVAILABLE_THEMES: ThemeConfig[] = [
  MYSTICAL_PURPLE_THEME, // Set as first (default) theme
  DEFAULT_THEME,
  BLUE_ORANGE_THEME,
  GREEN_TEAL_THEME,
  DARK_THEME
];
