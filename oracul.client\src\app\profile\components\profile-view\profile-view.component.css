.profile-container {
  min-height: 100vh;
  background-color: var(--theme-background);
}

/* Cover Section */
.cover-section {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.cover-photo {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
}

/* Profile Header */
.profile-header {
  background: var(--theme-surface);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  margin-top: -100px;
  z-index: 10;
  border-radius: 16px 16px 0 0;
  margin-left: 20px;
  margin-right: 20px;
}

.header-content {
  display: flex;
  align-items: flex-start;
  padding: 24px;
  gap: 24px;
}

.profile-photo-container {
  position: relative;
  flex-shrink: 0;
}

.profile-photo {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 4px solid var(--theme-surface);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background-color: #4caf50;
  border: 3px solid var(--theme-surface);
  border-radius: 50%;
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme-text-primary);
}

.profile-title {
  font-size: 1.5rem;
  font-weight: 400;
  margin: 0 0 8px 0;
  color: var(--theme-primary);
}

.profile-headline {
  font-size: 1.1rem;
  color: var(--theme-text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.profile-meta > div {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--theme-text-secondary);
  font-size: 0.9rem;
}

.profile-meta mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.contact-btn {
  min-width: 140px;
}

.profile-completion {
  padding: 0 24px 24px;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
}

.completion-percentage {
  font-weight: 600;
  color: var(--theme-primary);
}

/* Main Content */
.profile-content {
  padding: 24px;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-section {
  margin-bottom: 24px;
}

.profile-section .mat-card-header {
  padding-bottom: 16px;
}

.profile-section .mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--theme-primary);
  font-size: 1.3rem;
}

/* About Section */
.summary-text {
  line-height: 1.6;
  color: var(--theme-text-primary);
}

/* Experience Section */
.experience-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.experience-item {
  border-left: 3px solid var(--theme-accent);
  padding-left: 20px;
  position: relative;
}

.experience-item::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 8px;
  width: 12px;
  height: 12px;
  background-color: var(--theme-accent);
  border-radius: 50%;
}

.experience-header {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.company-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.experience-info h3 {
  margin: 0 0 4px 0;
  font-size: 1.2rem;
  color: var(--theme-text-primary);
}

.experience-info h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  color: var(--theme-primary);
}

.duration {
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
  margin: 0;
}

.experience-description {
  margin-bottom: 12px;
  line-height: 1.5;
}

.experience-achievements ul {
  margin: 0;
  padding-left: 20px;
}

.experience-achievements li {
  margin-bottom: 4px;
  color: var(--theme-text-secondary);
}

/* Portfolio Section */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.portfolio-item {
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.portfolio-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.portfolio-image {
  height: 200px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.portfolio-info {
  padding: 16px;
}

.portfolio-info h3 {
  margin: 0 0 8px 0;
  color: var(--theme-text-primary);
}

.portfolio-info p {
  margin: 0 0 12px 0;
  color: var(--theme-text-secondary);
  line-height: 1.4;
}

.portfolio-tech {
  margin-bottom: 12px;
}

.portfolio-links {
  display: flex;
  gap: 8px;
}

.tech-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-chip {
  background-color: var(--theme-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Blog Section */
.blog-posts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.blog-post {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.blog-post:hover {
  background-color: rgba(0,0,0,0.02);
}

.blog-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blog-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: var(--theme-text-primary);
}

.blog-content p {
  margin: 0 0 8px 0;
  color: var(--theme-text-secondary);
  line-height: 1.4;
}

.blog-meta {
  display: flex;
  gap: 16px;
  font-size: 0.85rem;
  color: var(--theme-text-hint);
}

/* Contact Section */
.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.contact-item:hover {
  background-color: rgba(0,0,0,0.02);
}

.contact-item mat-icon {
  color: var(--theme-primary);
  font-size: 20px;
}

.contact-item a {
  color: var(--theme-text-primary);
  text-decoration: none;
}

.contact-item a:hover {
  color: var(--theme-primary);
}

/* Skills Section */
.skill-category {
  margin: 0 0 12px 0;
  color: var(--theme-primary);
  font-size: 1.1rem;
}

.skills-list {
  margin-bottom: 20px;
}

.skill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: rgba(0,0,0,0.02);
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.skill-item:hover {
  background-color: rgba(0,0,0,0.04);
}

.skill-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.skill-name {
  font-weight: 500;
  color: var(--theme-text-primary);
}

.endorsement-count {
  font-size: 0.8rem;
  color: var(--theme-text-secondary);
}

.endorse-btn {
  color: var(--theme-primary);
}

/* Achievements & Certifications */
.achievements-list,
.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.achievement-item,
.certification-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 8px;
}

.achievement-icon,
.certification-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.achievement-icon img,
.certification-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.achievement-info h4,
.certification-info h4 {
  margin: 0 0 4px 0;
  color: var(--theme-text-primary);
}

.achievement-info p,
.certification-info p {
  margin: 0 0 8px 0;
  color: var(--theme-text-secondary);
  font-size: 0.9rem;
}

.achievement-date,
.certification-date {
  font-size: 0.8rem;
  color: var(--theme-text-hint);
}

.credential-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  color: var(--theme-primary);
  text-decoration: none;
  font-size: 0.9rem;
}

/* Analytics Section */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.analytics-item {
  text-align: center;
  padding: 16px;
  background-color: rgba(0,0,0,0.02);
  border-radius: 8px;
}

.analytics-value {
  display: block;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--theme-primary);
}

.analytics-label {
  font-size: 0.9rem;
  color: var(--theme-text-secondary);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 16px;
}

.loading-container p {
  color: var(--theme-text-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .right-column {
    order: -1;
  }
}

@media (max-width: 768px) {
  .profile-header {
    margin-left: 10px;
    margin-right: 10px;
  }

  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 16px;
  }

  .profile-photo {
    width: 120px;
    height: 120px;
  }

  .profile-actions {
    flex-direction: row;
    align-items: center;
  }

  .profile-content {
    padding: 16px;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profile-name {
    font-size: 2rem;
  }

  .profile-title {
    font-size: 1.2rem;
  }

  .profile-meta {
    justify-content: center;
  }

  .experience-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .blog-post {
    flex-direction: column;
  }

  .blog-image {
    width: 100%;
    height: 120px;
  }
}
