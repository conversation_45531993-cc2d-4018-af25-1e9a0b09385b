{"ast": null, "code": "import * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Directive, Inject, EventEmitter, Optional, Output, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, ContentChild, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i5 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortalOutlet, CdkPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i5$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { Subscription, Subject, fromEvent, of, merge, EMPTY, Observable, timer, BehaviorSubject } from 'rxjs';\nimport { startWith, distinctUntilChanged, takeUntil, take, switchMap, skip, filter } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c0 = function (a0) {\n  return {\n    animationDuration: a0\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c2 = [\"*\"];\nconst _c3 = [\"tabListContainer\"];\nconst _c4 = [\"tabList\"];\nconst _c5 = [\"tabListInner\"];\nconst _c6 = [\"nextPaginator\"];\nconst _c7 = [\"previousPaginator\"];\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\nfunction MatTabGroup_div_2_ng_template_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_div_2_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_div_2_ng_template_6_ng_template_0_Template, 0, 0, \"ng-template\", 14);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_div_2_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6, 7);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const tab_r4 = restoredCtx.$implicit;\n      const i_r5 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r13._handleClick(tab_r4, _r0, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_div_2_Template_div_cdkFocusChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const i_r5 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵtemplate(6, MatTabGroup_div_2_ng_template_6_Template, 1, 1, \"ng-template\", 12);\n    i0.ɵɵtemplate(7, MatTabGroup_div_2_ng_template_7_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const _r6 = i0.ɵɵreference(1);\n    const _r8 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r1.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r1._getTabLabelId(i_r5))(\"ngClass\", tab_r4.labelClass)(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r1.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r1._getTabIndex(i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r1._tabs.length)(\"aria-controls\", ctx_r1._getTabContentId(i_r5))(\"aria-selected\", ctx_r1.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", _r6)(\"matRippleDisabled\", tab_r4.disabled || ctx_r1.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", tab_r4.templateLabel)(\"ngIfElse\", _r8);\n  }\n}\nfunction MatTabGroup_mat_tab_body_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 15);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-mdc-tab-body-active\", ctx_r3.selectedIndex === i_r17);\n    i0.ɵɵproperty(\"id\", ctx_r3._getTabContentId(i_r17))(\"ngClass\", tab_r16.bodyClass)(\"content\", tab_r16.content)(\"position\", tab_r16.position)(\"origin\", tab_r16.origin)(\"animationDuration\", ctx_r3.animationDuration)(\"preserveContent\", ctx_r3.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r3.contentTabIndex != null && ctx_r3.selectedIndex === i_r17 ? ctx_r3.contentTabIndex : null)(\"aria-labelledby\", ctx_r3._getTabLabelId(i_r17));\n  }\n}\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\nconst _c11 = [\"mat-tab-link\", \"\"];\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: trigger('translateTab', [\n  // Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', style({\n    transform: 'none'\n  })),\n  // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), state('right', style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), transition('void => left-origin-center', [style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), transition('void => right-origin-center', [style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n    super(componentFactoryResolver, viewContainerRef, _document);\n    this._host = _host;\n    /** Subscription to events for when the tab body begins centering. */\n    this._centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    this._leavingSub = Subscription.EMPTY;\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n      if (isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n}\nMatTabBodyPortal.ɵfac = function MatTabBodyPortal_Factory(t) {\n  return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nMatTabBodyPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabBodyPortal,\n  selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: MatTabBody,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatTabBody)]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\nclass _MatTabBodyBase {\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor(_elementRef, _dir, changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    /** Subscription to the directionality change observable. */\n    this._dirChangeSubscription = Subscription.EMPTY;\n    /** Emits when an animation on the tab is complete. */\n    this._translateTabComplete = new Subject();\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    this._onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n    this._onCentered = new EventEmitter(true);\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    this.animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    this.preserveContent = false;\n    if (_dir) {\n      this._dirChangeSubscription = _dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n    // Ensure that we get unique animation events, because the `.done` callback can get\n    // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n    this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      // If the transition to the center is complete, emit an event.\n      if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n        this._onCentered.emit();\n      }\n      if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n        this._afterLeavingCenter.emit();\n      }\n    });\n  }\n  /**\n   * After initialized, check if the content is centered and has an origin. If so, set the\n   * special position states that transition the tab from the left or right before centering.\n   */\n  ngOnInit() {\n    if (this._position == 'center' && this.origin != null) {\n      this._position = this._computePositionFromOrigin(this.origin);\n    }\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._translateTabComplete.complete();\n  }\n  _onTranslateTabStarted(event) {\n    const isCentering = this._isCenterPosition(event.toState);\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition(position) {\n    return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n  }\n  /**\n   * Computes the position state based on the specified origin position. This is used if the\n   * tab is becoming visible immediately after creation.\n   */\n  _computePositionFromOrigin(origin) {\n    const dir = this._getLayoutDirection();\n    if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n      return 'left-origin-center';\n    }\n    return 'right-origin-center';\n  }\n}\n_MatTabBodyBase.ɵfac = function _MatTabBodyBase_Factory(t) {\n  return new (t || _MatTabBodyBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n_MatTabBodyBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabBodyBase,\n  inputs: {\n    _content: [\"content\", \"_content\"],\n    origin: \"origin\",\n    animationDuration: \"animationDuration\",\n    preserveContent: \"preserveContent\",\n    position: \"position\"\n  },\n  outputs: {\n    _onCentering: \"_onCentering\",\n    _beforeCentering: \"_beforeCentering\",\n    _afterLeavingCenter: \"_afterLeavingCenter\",\n    _onCentered: \"_onCentered\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabBodyBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _afterLeavingCenter: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    origin: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody extends _MatTabBodyBase {\n  constructor(elementRef, dir, changeDetectorRef) {\n    super(elementRef, dir, changeDetectorRef);\n  }\n}\nMatTabBody.ɵfac = function MatTabBody_Factory(t) {\n  return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nMatTabBody.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabBody,\n  selectors: [[\"mat-tab-body\"]],\n  viewQuery: function MatTabBody_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-body\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 6,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"content\", \"\"], [\"matTabBodyHost\", \"\"]],\n  template: function MatTabBody_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n        return ctx._onTranslateTabStarted($event);\n      })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n        return ctx._translateTabComplete.next($event);\n      });\n      i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c1, ctx._position, i0.ɵɵpureFunction1(1, _c0, ctx.animationDuration)));\n    }\n  },\n  dependencies: [MatTabBodyPortal],\n  styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matTabsAnimations.translateTab]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      animations: [matTabsAnimations.translateTab],\n      host: {\n        'class': 'mat-mdc-tab-body'\n      },\n      template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _portalHost: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  constructor( /** Content for the tab. */template) {\n    this.template = template;\n  }\n}\nMatTabContent.ɵfac = function MatTabContent_Factory(t) {\n  return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nMatTabContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabContent,\n  selectors: [[\"\", \"matTabContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_CONTENT,\n    useExisting: MatTabContent\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  constructor(templateRef, viewContainerRef, _closestTab) {\n    super(templateRef, viewContainerRef);\n    this._closestTab = _closestTab;\n  }\n}\nMatTabLabel.ɵfac = function MatTabLabel_Factory(t) {\n  return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n};\nMatTabLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabLabel,\n  selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_LABEL,\n    useExisting: MatTabLabel\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const clientRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `ClientRect` of the previous item.\n      correspondingItem.activateInkBar(clientRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\n/**\n * Mixin that can be used to apply the `MatInkBarItem` behavior to a class.\n * Base on MDC's `MDCSlidingTabIndicatorFoundation`:\n * https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-tab-indicator/sliding-foundation.ts\n * @docs-private\n */\nfunction mixinInkBarItem(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._fitToContent = false;\n    }\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n      return this._fitToContent;\n    }\n    set fitInkBarToContent(v) {\n      const newValue = coerceBooleanProperty(v);\n      if (this._fitToContent !== newValue) {\n        this._fitToContent = newValue;\n        if (this._inkBarElement) {\n          this._appendInkBarElement();\n        }\n      }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n      const element = this.elementRef.nativeElement;\n      // Early exit if no indicator is present to handle cases where an indicator\n      // may be activated without a prior indicator state\n      if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n        element.classList.add(ACTIVE_CLASS);\n        return;\n      }\n      // This animation uses the FLIP approach. You can read more about it at the link below:\n      // https://aerotwist.com/blog/flip-your-animations/\n      // Calculate the dimensions based on the dimensions of the previous indicator\n      const currentClientRect = element.getBoundingClientRect();\n      const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n      const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n      element.classList.add(NO_TRANSITION_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n      // Force repaint before updating classes and transform to ensure the transform properly takes effect\n      element.getBoundingClientRect();\n      element.classList.remove(NO_TRANSITION_CLASS);\n      element.classList.add(ACTIVE_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n      this.elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n      this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n      this._inkBarElement?.remove();\n      this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n      const documentNode = this.elementRef.nativeElement.ownerDocument || document;\n      this._inkBarElement = documentNode.createElement('span');\n      this._inkBarContentElement = documentNode.createElement('span');\n      this._inkBarElement.className = 'mdc-tab-indicator';\n      this._inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n      this._inkBarElement.appendChild(this._inkBarContentElement);\n      this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n      if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Ink bar element has not been created and cannot be appended');\n      }\n      const parentElement = this._fitToContent ? this.elementRef.nativeElement.querySelector('.mdc-tab__content') : this.elementRef.nativeElement;\n      if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Missing element to host the ink bar');\n      }\n      parentElement.appendChild(this._inkBarElement);\n    }\n  };\n}\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n/** @docs-private */\nconst _MatTabLabelWrapperMixinBase = mixinDisabled(class {});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass _MatTabLabelWrapperBase extends _MatTabLabelWrapperMixinBase {\n  constructor(elementRef) {\n    super();\n    this.elementRef = elementRef;\n  }\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n}\n_MatTabLabelWrapperBase.ɵfac = function _MatTabLabelWrapperBase_Factory(t) {\n  return new (t || _MatTabLabelWrapperBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n_MatTabLabelWrapperBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabLabelWrapperBase,\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabLabelWrapperBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nconst _MatTabLabelWrapperBaseWithInkBarItem = mixinInkBarItem(_MatTabLabelWrapperBase);\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBaseWithInkBarItem {}\nMatTabLabelWrapper.ɵfac = /* @__PURE__ */function () {\n  let ɵMatTabLabelWrapper_BaseFactory;\n  return function MatTabLabelWrapper_Factory(t) {\n    return (ɵMatTabLabelWrapper_BaseFactory || (ɵMatTabLabelWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabelWrapper)))(t || MatTabLabelWrapper);\n  };\n}();\nMatTabLabelWrapper.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabLabelWrapper,\n  selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n  hostVars: 3,\n  hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n      i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    fitInkBarToContent: \"fitInkBarToContent\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      inputs: ['disabled', 'fitInkBarToContent'],\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTab.\n/** @docs-private */\nconst _MatTabMixinBase = mixinDisabled(class {});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\n/** @docs-private */\nclass _MatTabBase extends _MatTabMixinBase {\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  constructor(_viewContainerRef, _closestTabGroup) {\n    super();\n    this._viewContainerRef = _viewContainerRef;\n    this._closestTabGroup = _closestTabGroup;\n    /** Plain text label for the tab, used when there is no template label. */\n    this.textLabel = '';\n    /** Portal that will be the hosted content of the tab */\n    this._contentPortal = null;\n    /** Emits whenever the internal state of the tab changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    this.position = null;\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    this.origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n    this.isActive = false;\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n}\n_MatTabBase.ɵfac = function _MatTabBase_Factory(t) {\n  return new (t || _MatTabBase)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n};\n_MatTabBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabBase,\n  viewQuery: function _MatTabBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n    }\n  },\n  inputs: {\n    textLabel: [\"label\", \"textLabel\"],\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    labelClass: \"labelClass\",\n    bodyClass: \"bodyClass\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB_GROUP]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTab extends _MatTabBase {\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n}\nMatTab.ɵfac = /* @__PURE__ */function () {\n  let ɵMatTab_BaseFactory;\n  return function MatTab_Factory(t) {\n    return (ɵMatTab_BaseFactory || (ɵMatTab_BaseFactory = i0.ɵɵgetInheritedFactory(MatTab)))(t || MatTab);\n  };\n}();\nMatTab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTab,\n  selectors: [[\"mat-tab\"]],\n  contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matTab\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB,\n    useExisting: MatTab\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c2,\n  decls: 1,\n  vars: 0,\n  template: function MatTab_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n    }\n  },\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      inputs: ['disabled'],\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], null, {\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  get disablePagination() {\n    return this._disablePagination;\n  }\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    value = coerceNumberProperty(value);\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this._dir = _dir;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._animationMode = _animationMode;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    this._scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    this._selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n    this._showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    this._disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    this._disableScrollBefore = true;\n    /** Stream that will stop the automated scrolling. */\n    this._stopScrolling = new Subject();\n    this._disablePagination = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n    this.selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n    this.indexFocused = new EventEmitter();\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    _ngZone.runOutsideAngular(() => {\n      fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._stopInterval();\n      });\n    });\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('before');\n    });\n    fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('after');\n    });\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    const resize = this._viewportRuler.change(150);\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    this._keyManager.updateActiveItem(this._selectedIndex);\n    // Defer the first call in order to allow for slower browsers to lay out the elements.\n    // This helps in cases where the user lands directly on a page with paginated tabs.\n    // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n    // can hold up tests that are in a background tab.\n    this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n    // On dir change or window resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._changeDetectorRef.markForCheck();\n      }\n      this._showPaginationControls = isEnabled;\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n}\nMatPaginatedTabHeader.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n  return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatPaginatedTabHeader.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatPaginatedTabHeader,\n  inputs: {\n    disablePagination: \"disablePagination\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disablePagination: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n}\n_MatTabHeaderBase.ɵfac = function _MatTabHeaderBase_Factory(t) {\n  return new (t || _MatTabHeaderBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n_MatTabHeaderBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabHeaderBase,\n  inputs: {\n    disableRipple: \"disableRipple\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabHeaderBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disableRipple: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends _MatTabHeaderBase {\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n}\nMatTabHeader.ɵfac = function MatTabHeader_Factory(t) {\n  return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatTabHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabHeader,\n  selectors: [[\"mat-tab-header\"]],\n  contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function MatTabHeader_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 7);\n      i0.ɵɵviewQuery(_c5, 7);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-header\"],\n  hostVars: 4,\n  hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n    }\n  },\n  inputs: {\n    selectedIndex: \"selectedIndex\"\n  },\n  outputs: {\n    selectFocusedIndex: \"selectFocusedIndex\",\n    indexFocused: \"indexFocused\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c2,\n  decls: 13,\n  vars: 10,\n  consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-mdc-tab-labels\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n  template: function MatTabHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n        return ctx._handlePaginatorClick(\"before\");\n      })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n        return ctx._handlePaginatorPress(\"before\", $event);\n      })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(2, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n        return ctx._handleKeydown($event);\n      });\n      i0.ɵɵelementStart(5, \"div\", 5, 6);\n      i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n        return ctx._onContentChanges();\n      });\n      i0.ɵɵelementStart(7, \"div\", 7, 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"button\", 9, 10);\n      i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_10_listener($event) {\n        return ctx._handlePaginatorPress(\"after\", $event);\n      })(\"click\", function MatTabHeader_Template_button_click_10_listener() {\n        return ctx._handlePaginatorClick(\"after\");\n      })(\"touchend\", function MatTabHeader_Template_button_touchend_10_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(12, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n      i0.ɵɵadvance(3);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(7);\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n    }\n  },\n  dependencies: [i5.MatRipple, i5$1.CdkObserveContent],\n  styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      inputs: ['selectedIndex'],\n      outputs: ['selectFocusedIndex', 'indexFocused'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n// Boilerplate for applying mixins to MatTabGroup.\n/** @docs-private */\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n  /** Whether the tab group should grow to the size of the active tab. */\n  get dynamicHeight() {\n    return this._dynamicHeight;\n  }\n  set dynamicHeight(value) {\n    this._dynamicHeight = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = coerceNumberProperty(value, null);\n  }\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n  }\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = coerceNumberProperty(value, null);\n  }\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  get disablePagination() {\n    return this._disablePagination;\n  }\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  get preserveContent() {\n    return this._preserveContent;\n  }\n  set preserveContent(value) {\n    this._preserveContent = coerceBooleanProperty(value);\n  }\n  /** Background color of the tab group. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n    super(elementRef);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** All of the tabs that belong to the group. */\n    this._tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n    this._indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n    this._lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    this._tabBodyWrapperHeight = 0;\n    /** Subscription to tabs being added/removed. */\n    this._tabsSubscription = Subscription.EMPTY;\n    /** Subscription to changes in the tab labels. */\n    this._tabLabelSubscription = Subscription.EMPTY;\n    this._dynamicHeight = false;\n    this._selectedIndex = null;\n    /** Position of the tab header. */\n    this.headerPosition = 'above';\n    this._disablePagination = false;\n    this._preserveContent = false;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    this.selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n    this.focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n    this.animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n    this.selectedTabChange = new EventEmitter(true);\n    this._groupId = nextId++;\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n    this.preserveContent = !!defaultConfig?.preserveContent;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(i) {\n    return `mat-tab-label-${this._groupId}-${i}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(i) {\n    return `mat-tab-content-${this._groupId}-${i}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this.animationDone.emit();\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n}\n_MatTabGroupBase.ɵfac = function _MatTabGroupBase_Factory(t) {\n  return new (t || _MatTabGroupBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n_MatTabGroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabGroupBase,\n  inputs: {\n    dynamicHeight: \"dynamicHeight\",\n    selectedIndex: \"selectedIndex\",\n    headerPosition: \"headerPosition\",\n    animationDuration: \"animationDuration\",\n    contentTabIndex: \"contentTabIndex\",\n    disablePagination: \"disablePagination\",\n    preserveContent: \"preserveContent\",\n    backgroundColor: \"backgroundColor\"\n  },\n  outputs: {\n    selectedIndexChange: \"selectedIndexChange\",\n    focusChange: \"focusChange\",\n    animationDone: \"animationDone\",\n    selectedTabChange: \"selectedTabChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabGroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    dynamicHeight: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input\n    }],\n    disablePagination: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup extends _MatTabGroupBase {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(v) {\n    this._fitInkBarToContent = coerceBooleanProperty(v);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Whether tabs should be stretched to fill the header. */\n  get stretchTabs() {\n    return this._stretchTabs;\n  }\n  set stretchTabs(v) {\n    this._stretchTabs = coerceBooleanProperty(v);\n  }\n  constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n    super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n    this._fitInkBarToContent = false;\n    this._stretchTabs = true;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n  }\n}\nMatTabGroup.ɵfac = function MatTabGroup_Factory(t) {\n  return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatTabGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabGroup,\n  selectors: [[\"mat-tab-group\"]],\n  contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n    }\n  },\n  viewQuery: function MatTabGroup_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c8, 5);\n      i0.ɵɵviewQuery(_c9, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-group\"],\n  hostVars: 6,\n  hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    disableRipple: \"disableRipple\",\n    fitInkBarToContent: \"fitInkBarToContent\",\n    stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"]\n  },\n  exportAs: [\"matTabGroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_GROUP,\n    useExisting: MatTabGroup\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 6,\n  vars: 7,\n  consts: [[3, \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"indexFocused\", \"selectFocusedIndex\"], [\"tabHeader\", \"\"], [\"class\", \"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\", \"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 3, \"id\", \"mdc-tab--active\", \"ngClass\", \"disabled\", \"fitInkBarToContent\", \"click\", \"cdkFocusChange\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"tabBodyWrapper\", \"\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-mdc-tab-body-active\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"id\", \"ngClass\", \"disabled\", \"fitInkBarToContent\", \"click\", \"cdkFocusChange\"], [\"tabNode\", \"\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"ngIf\", \"ngIfElse\"], [\"tabTextLabel\", \"\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"id\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\"]],\n  template: function MatTabGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"mat-tab-header\", 0, 1);\n      i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n        return ctx._focusChanged($event);\n      })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n        return ctx.selectedIndex = $event;\n      });\n      i0.ɵɵtemplate(2, MatTabGroup_div_2_Template, 9, 17, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵtemplate(5, MatTabGroup_mat_tab_body_5_Template, 1, 11, \"mat-tab-body\", 5);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n    }\n  },\n  dependencies: [i1$2.NgClass, i1$2.NgForOf, i1$2.NgIf, i2.CdkPortalOutlet, i5.MatRipple, i4.CdkMonitorFocus, MatTabBody, MatTabLabelWrapper, MatTabHeader],\n  styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-disabled{opacity:.4}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab-indicator__content--underline,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      inputs: ['color', 'disableRipple'],\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-mdc-tab-group',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs'\n      },\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-disabled{opacity:.4}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab-indicator__content--underline,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    fitInkBarToContent: [{\n      type: Input\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: ['mat-stretch-tabs']\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n  /** Background color of the tab nav. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n    /** Theme color of the nav bar. */\n    this.color = 'primary';\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this.updateActiveLink();\n    });\n    super.ngAfterContentInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        this._changeDetectorRef.markForCheck();\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        return;\n      }\n    }\n    // The ink bar should hide itself if no items are active.\n    this.selectedIndex = -1;\n    this._inkBar.hide();\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n}\n_MatTabNavBase.ɵfac = function _MatTabNavBase_Factory(t) {\n  return new (t || _MatTabNavBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n_MatTabNavBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabNavBase,\n  inputs: {\n    backgroundColor: \"backgroundColor\",\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    tabPanel: \"tabPanel\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabNavBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }]\n  });\n})();\n// Boilerplate for applying mixins to MatTabLink.\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {})));\n/** Base class with all of the `MatTabLink` functionality. */\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this._isActive) {\n      this._isActive = newValue;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  constructor(_tabNavBar, /** @docs-private */elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n    super();\n    this._tabNavBar = _tabNavBar;\n    this.elementRef = elementRef;\n    this._focusMonitor = _focusMonitor;\n    /** Whether the tab link is active or not. */\n    this._isActive = false;\n    /** Unique id for the tab. */\n    this.id = `mat-tab-link-${nextUniqueId++}`;\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = parseInt(tabIndex) || 0;\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (this._tabNavBar.tabPanel && event.keyCode === SPACE) {\n      this.elementRef.nativeElement.click();\n    }\n  }\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n  _getTabIndex() {\n    if (this._tabNavBar.tabPanel) {\n      return this._isActive && !this.disabled ? 0 : -1;\n    } else {\n      return this.tabIndex;\n    }\n  }\n}\n_MatTabLinkBase.ɵfac = function _MatTabLinkBase_Factory(t) {\n  return new (t || _MatTabLinkBase)(i0.ɵɵdirectiveInject(_MatTabNavBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n_MatTabLinkBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabLinkBase,\n  inputs: {\n    active: \"active\",\n    id: \"id\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabLinkBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: _MatTabNavBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    active: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\nconst _MatTabLinkBaseWithInkBarItem = mixinInkBarItem(_MatTabLinkBase);\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends _MatTabNavBase {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(v) {\n    this._fitInkBarToContent.next(coerceBooleanProperty(v));\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Whether tabs should be stretched to fill the header. */\n  get stretchTabs() {\n    return this._stretchTabs;\n  }\n  set stretchTabs(v) {\n    this._stretchTabs = coerceBooleanProperty(v);\n  }\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n    super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n    this._fitInkBarToContent = new BehaviorSubject(false);\n    this._stretchTabs = true;\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n}\nMatTabNav.ɵfac = function MatTabNav_Factory(t) {\n  return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8));\n};\nMatTabNav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabNav,\n  selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n  contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function MatTabNav_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 7);\n      i0.ɵɵviewQuery(_c5, 7);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n  hostVars: 15,\n  hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._getRole());\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    color: \"color\",\n    fitInkBarToContent: \"fitInkBarToContent\",\n    stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"]\n  },\n  exportAs: [\"matTabNavBar\", \"matTabNav\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c10,\n  ngContentSelectors: _c2,\n  decls: 13,\n  vars: 8,\n  consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-mdc-tab-links\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n  template: function MatTabNav_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n        return ctx._handlePaginatorClick(\"before\");\n      })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n        return ctx._handlePaginatorPress(\"before\", $event);\n      })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(2, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n        return ctx._handleKeydown($event);\n      });\n      i0.ɵɵelementStart(5, \"div\", 5, 6);\n      i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n        return ctx._onContentChanges();\n      });\n      i0.ɵɵelementStart(7, \"div\", 7, 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"button\", 9, 10);\n      i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_10_listener($event) {\n        return ctx._handlePaginatorPress(\"after\", $event);\n      })(\"click\", function MatTabNav_Template_button_click_10_listener() {\n        return ctx._handlePaginatorClick(\"after\");\n      })(\"touchend\", function MatTabNav_Template_button_touchend_10_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(12, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n      i0.ɵɵadvance(10);\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n    }\n  },\n  dependencies: [i5.MatRipple, i5$1.CdkObserveContent],\n  styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      inputs: ['color'],\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }]\n    }];\n  }, {\n    fitInkBarToContent: [{\n      type: Input\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: ['mat-stretch-tabs']\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends _MatTabLinkBaseWithInkBarItem {\n  constructor(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n    super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n    this._destroyed = new Subject();\n    tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n  }\n}\nMatTabLink.ɵfac = function MatTabLink_Factory(t) {\n  return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatTabLink.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabLink,\n  selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n  hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-mdc-focus-indicator\"],\n  hostVars: 11,\n  hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n      i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\",\n    active: \"active\",\n    id: \"id\"\n  },\n  exportAs: [\"matTabLink\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c11,\n  ngContentSelectors: _c2,\n  decls: 5,\n  vars: 2,\n  consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n  template: function MatTabLink_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n      i0.ɵɵprojection(4);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n    }\n  },\n  dependencies: [i5.MatRipple],\n  styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab-link.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab-link .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12}.mat-mdc-tab-link.mat-mdc-tab-disabled{pointer-events:none;opacity:.4}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      inputs: ['disabled', 'disableRipple', 'tabIndex', 'active', 'id'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_getTabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab-link.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab-link .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12}.mat-mdc-tab-link.mat-mdc-tab-disabled{pointer-events:none;opacity:.4}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatTabNav\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  constructor() {\n    /** Unique id for the tab panel. */\n    this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n  }\n}\nMatTabNavPanel.ɵfac = function MatTabNavPanel_Factory(t) {\n  return new (t || MatTabNavPanel)();\n};\nMatTabNavPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabNavPanel,\n  selectors: [[\"mat-tab-nav-panel\"]],\n  hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n  hostVars: 2,\n  hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  exportAs: [\"matTabNavPanel\"],\n  ngContentSelectors: _c2,\n  decls: 1,\n  vars: 0,\n  template: function MatTabNavPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTabsModule {}\nMatTabsModule.ɵfac = function MatTabsModule_Factory(t) {\n  return new (t || MatTabsModule)();\n};\nMatTabsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTabsModule\n});\nMatTabsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      declarations: [MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink,\n      // Private directives, should not be exported.\n      MatTabBody, MatTabBodyPortal, MatTabLabelWrapper, MatTabHeader]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, _MatTabBase, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLabelWrapperBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };", "map": {"version": 3, "names": ["i1$2", "DOCUMENT", "CommonModule", "i0", "forwardRef", "Directive", "Inject", "EventEmitter", "Optional", "Output", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "ContentChild", "ContentChildren", "QueryList", "Attribute", "NgModule", "i5", "mixinDisabled", "mixinColor", "mixinDisableRipple", "mixinTabIndex", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "MatRippleModule", "i2", "CdkPortalOutlet", "CdkPortal", "TemplatePortal", "PortalModule", "i5$1", "ObserversModule", "i4", "FocusKeyManager", "A11yModule", "i1", "Subscription", "Subject", "fromEvent", "of", "merge", "EMPTY", "Observable", "timer", "BehaviorSubject", "startWith", "distinctUntilChanged", "takeUntil", "take", "switchMap", "skip", "filter", "trigger", "state", "style", "transition", "animate", "coerceBooleanProperty", "coerceNumberProperty", "i1$1", "i3", "normalizePassiveListenerOptions", "ANIMATION_MODULE_TYPE", "hasModifierKey", "SPACE", "ENTER", "matTabsAnimations", "translateTab", "transform", "minHeight", "visibility", "MatTabBodyPortal", "constructor", "componentFactoryResolver", "viewContainerRef", "_host", "_document", "_centeringSub", "_leavingSub", "ngOnInit", "_beforeCentering", "pipe", "_isCenterPosition", "_position", "subscribe", "isCentering", "has<PERSON>tta<PERSON>", "attach", "_content", "_afterLeavingCenter", "preserve<PERSON><PERSON>nt", "detach", "ngOnDestroy", "unsubscribe", "ɵfac", "ComponentFactoryResolver", "ViewContainerRef", "MatTabBody", "ɵdir", "type", "args", "selector", "decorators", "undefined", "_MatTabBodyBase", "position", "_positionIndex", "_computePositionAnimationState", "_elementRef", "_dir", "changeDetectorRef", "_dirChangeSubscription", "_translateTabComplete", "_onCentering", "_onCentered", "animationDuration", "change", "dir", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "fromState", "toState", "event", "emit", "origin", "_computePositionFromO<PERSON>in", "complete", "_onTranslateTabStarted", "nativeElement", "clientHeight", "_getLayoutDirection", "value", "ElementRef", "Directionality", "ChangeDetectorRef", "elementRef", "ɵcmp", "encapsulation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "animations", "host", "template", "styles", "_portalHost", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provide", "useExisting", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "templateRef", "_closestTab", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "alignToElement", "element", "correspondingItem", "find", "currentItem", "_currentItem", "clientRect", "getBoundingClientRect", "activateInkBar", "mixinInkBarItem", "base", "_fitTo<PERSON>ontent", "fitInkBarToContent", "v", "newValue", "_inkBarElement", "_appendInkBarElement", "previousIndicatorClientRect", "_inkBarContentElement", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "setProperty", "remove", "_createInkBarElement", "documentNode", "ownerDocument", "document", "createElement", "className", "append<PERSON><PERSON><PERSON>", "ngDevMode", "Error", "parentElement", "querySelector", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "_MatTabLabelWrapperMixinBase", "_MatTabLabelWrapperBase", "focus", "getOffsetLeft", "getOffsetWidth", "_MatTabLabelWrapperBaseWithInkBarItem", "MatTabLabelWrapper", "inputs", "_MatTabMixinBase", "MAT_TAB_GROUP", "_MatTabBase", "content", "_contentPortal", "_viewContainerRef", "_closestTabGroup", "textLabel", "_stateChanges", "isActive", "ngOnChanges", "changes", "hasOwnProperty", "next", "_explicitContent", "_implicitContent", "_setTemplateLabelInput", "_templateLabel", "static", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelClass", "bodyClass", "Mat<PERSON><PERSON>", "templateLabel", "exportAs", "read", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "disablePagination", "_disablePagination", "selectedIndex", "_selectedIndex", "_selectedIndexChanged", "_keyManager", "updateActiveItem", "_changeDetectorRef", "_viewportRuler", "_ngZone", "_platform", "_animationMode", "_scrollDistance", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_stopScrolling", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "_stopInterval", "ngAfterViewInit", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "resize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "withHomeAndEnd", "withWrap", "skipPredicate", "onStable", "_itemsResized", "run", "Promise", "resolve", "then", "Math", "max", "min", "_getMaxScrollDistance", "newFocusIndex", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "observe", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "length", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "destroy", "_handleKeydown", "keyCode", "focusIndex", "get", "disabled", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "toArray", "tabIndex", "containerEl", "_tabListContainer", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "ViewportRuler", "NgZone", "Platform", "_MatTabHeaderBase", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "viewportRuler", "ngZone", "platform", "animationMode", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CdkObserveContent", "outputs", "descendants", "MAT_TABS_CONFIG", "nextId", "_MatTabGroupMixinBase", "_MatTabGroupBase", "dynamicHeight", "_dynamicHeight", "_indexToSelect", "_animationDuration", "test", "contentTabIndex", "_contentTabIndex", "_preserveContent", "backgroundColor", "_backgroundColor", "defaultConfig", "_tabs", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "_tabsSubscription", "_tabLabelSubscription", "headerPosition", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_groupId", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "tab", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "selectedTab", "i", "_allTabs", "reset", "notifyOn<PERSON><PERSON>es", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "MatTabChangeEvent", "map", "_getTabLabelId", "_getTabContentId", "_setTabBodyWrapperHeight", "tabHeight", "offsetHeight", "_removeTabBodyWrapperHeight", "_handleClick", "tabHeader", "_getTabIndex", "targetIndex", "_tabFocusChanged", "<PERSON><PERSON><PERSON><PERSON>", "MatTabGroup", "_fitInkBarToContent", "stretchTabs", "_stretchTabs", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "CdkMonitorFocus", "nextUniqueId", "_MatTabNavBase", "color", "updateActiveLink", "items", "active", "tabPanel", "_activeTabId", "id", "_getRole", "getAttribute", "_MatTabLinkMixinBase", "_MatTabLinkBase", "_isActive", "_tabNavBar", "rippleDisabled", "rippleConfig", "globalRippleOptions", "_focusMonitor", "parseInt", "animation", "enterDuration", "exitDuration", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "FocusMonitor", "_MatTabLinkBaseWithInkBarItem", "MatTabNav", "MatTabLink", "tabNavBar", "focusMonitor", "OnPush", "MatTabNavPanel", "MatTabsModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/tabs.mjs"], "sourcesContent": ["import * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Directive, Inject, EventEmitter, Optional, Output, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, ContentChild, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i5 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortalOutlet, CdkPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i5$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { Subscription, Subject, fromEvent, of, merge, EMPTY, Observable, timer, BehaviorSubject } from 'rxjs';\nimport { startWith, distinctUntilChanged, takeUntil, take, switchMap, skip, filter } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n    /** Animation translates a tab along the X axis. */\n    translateTab: trigger('translateTab', [\n        // Transitions to `none` instead of 0, because some browsers might blur the content.\n        state('center, void, left-origin-center, right-origin-center', style({ transform: 'none' })),\n        // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n        // in order to ensure that the element has a height before its state changes. This is\n        // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n        // not have a static height and is not rendered. See related issue: #9465\n        state('left', style({\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            // Normally this is redundant since we detach the content from the DOM, but if the user\n            // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n            visibility: 'hidden',\n        })),\n        state('right', style({\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n        })),\n        transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')),\n        transition('void => left-origin-center', [\n            style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n        transition('void => right-origin-center', [\n            style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n        super(componentFactoryResolver, viewContainerRef, _document);\n        this._host = _host;\n        /** Subscription to events for when the tab body begins centering. */\n        this._centeringSub = Subscription.EMPTY;\n        /** Subscription to events for when the tab body finishes leaving from center position. */\n        this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n            .subscribe((isCentering) => {\n            if (isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n}\nMatTabBodyPortal.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabBodyPortal, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: forwardRef(() => MatTabBody) }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabBodyPortal.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabBodyHost]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: MatTabBody, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatTabBody)]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\nclass _MatTabBodyBase {\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor(_elementRef, _dir, changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        /** Subscription to the directionality change observable. */\n        this._dirChangeSubscription = Subscription.EMPTY;\n        /** Emits when an animation on the tab is complete. */\n        this._translateTabComplete = new Subject();\n        /** Event emitted when the tab begins to animate towards the center as the active tab. */\n        this._onCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._beforeCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._afterLeavingCenter = new EventEmitter();\n        /** Event emitted when the tab completes its animation towards the center. */\n        this._onCentered = new EventEmitter(true);\n        // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n        // anyway to prevent the animations module from throwing an error if the body is used on its own.\n        /** Duration for the tab's animation. */\n        this.animationDuration = '500ms';\n        /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n        this.preserveContent = false;\n        if (_dir) {\n            this._dirChangeSubscription = _dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n        // Ensure that we get unique animation events, because the `.done` callback can get\n        // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n        this._translateTabComplete\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            // If the transition to the center is complete, emit an event.\n            if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n                this._onCentered.emit();\n            }\n            if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n                this._afterLeavingCenter.emit();\n            }\n        });\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n        if (this._position == 'center' && this.origin != null) {\n            this._position = this._computePositionFromOrigin(this.origin);\n        }\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n        const isCentering = this._isCenterPosition(event.toState);\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n        return (position == 'center' || position == 'left-origin-center' || position == 'right-origin-center');\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n        const dir = this._getLayoutDirection();\n        if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n            return 'left-origin-center';\n        }\n        return 'right-origin-center';\n    }\n}\n_MatTabBodyBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabBodyBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabBodyBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabBodyBase, inputs: { _content: [\"content\", \"_content\"], origin: \"origin\", animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _afterLeavingCenter: \"_afterLeavingCenter\", _onCentered: \"_onCentered\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabBodyBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _afterLeavingCenter: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], origin: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody extends _MatTabBodyBase {\n    constructor(elementRef, dir, changeDetectorRef) {\n        super(elementRef, dir, changeDetectorRef);\n    }\n}\nMatTabBody.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabBody, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatTabBody.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabBody, selector: \"mat-tab-body\", host: { classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: CdkPortalOutlet, descendants: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }], animations: [matTabsAnimations.translateTab], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, animations: [matTabsAnimations.translateTab], host: {\n                        'class': 'mat-mdc-tab-body',\n                    }, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _portalHost: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    constructor(/** Content for the tab. */ template) {\n        this.template = template;\n    }\n}\nMatTabContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabContent, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n        super(templateRef, viewContainerRef);\n        this._closestTab = _closestTab;\n    }\n}\nMatTabLabel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLabel, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: MAT_TAB, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabLabel.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabLabel, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const clientRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `ClientRect` of the previous item.\n            correspondingItem.activateInkBar(clientRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\n/**\n * Mixin that can be used to apply the `MatInkBarItem` behavior to a class.\n * Base on MDC's `MDCSlidingTabIndicatorFoundation`:\n * https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-tab-indicator/sliding-foundation.ts\n * @docs-private\n */\nfunction mixinInkBarItem(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this._fitToContent = false;\n        }\n        /** Whether the ink bar should fit to the entire tab or just its content. */\n        get fitInkBarToContent() {\n            return this._fitToContent;\n        }\n        set fitInkBarToContent(v) {\n            const newValue = coerceBooleanProperty(v);\n            if (this._fitToContent !== newValue) {\n                this._fitToContent = newValue;\n                if (this._inkBarElement) {\n                    this._appendInkBarElement();\n                }\n            }\n        }\n        /** Aligns the ink bar to the current item. */\n        activateInkBar(previousIndicatorClientRect) {\n            const element = this.elementRef.nativeElement;\n            // Early exit if no indicator is present to handle cases where an indicator\n            // may be activated without a prior indicator state\n            if (!previousIndicatorClientRect ||\n                !element.getBoundingClientRect ||\n                !this._inkBarContentElement) {\n                element.classList.add(ACTIVE_CLASS);\n                return;\n            }\n            // This animation uses the FLIP approach. You can read more about it at the link below:\n            // https://aerotwist.com/blog/flip-your-animations/\n            // Calculate the dimensions based on the dimensions of the previous indicator\n            const currentClientRect = element.getBoundingClientRect();\n            const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n            const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n            element.classList.add(NO_TRANSITION_CLASS);\n            this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n            // Force repaint before updating classes and transform to ensure the transform properly takes effect\n            element.getBoundingClientRect();\n            element.classList.remove(NO_TRANSITION_CLASS);\n            element.classList.add(ACTIVE_CLASS);\n            this._inkBarContentElement.style.setProperty('transform', '');\n        }\n        /** Removes the ink bar from the current item. */\n        deactivateInkBar() {\n            this.elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n        }\n        /** Initializes the foundation. */\n        ngOnInit() {\n            this._createInkBarElement();\n        }\n        /** Destroys the foundation. */\n        ngOnDestroy() {\n            this._inkBarElement?.remove();\n            this._inkBarElement = this._inkBarContentElement = null;\n        }\n        /** Creates and appends the ink bar element. */\n        _createInkBarElement() {\n            const documentNode = this.elementRef.nativeElement.ownerDocument || document;\n            this._inkBarElement = documentNode.createElement('span');\n            this._inkBarContentElement = documentNode.createElement('span');\n            this._inkBarElement.className = 'mdc-tab-indicator';\n            this._inkBarContentElement.className =\n                'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n            this._inkBarElement.appendChild(this._inkBarContentElement);\n            this._appendInkBarElement();\n        }\n        /**\n         * Appends the ink bar to the tab host element or content, depending on whether\n         * the ink bar should fit to content.\n         */\n        _appendInkBarElement() {\n            if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Ink bar element has not been created and cannot be appended');\n            }\n            const parentElement = this._fitToContent\n                ? this.elementRef.nativeElement.querySelector('.mdc-tab__content')\n                : this.elementRef.nativeElement;\n            if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Missing element to host the ink bar');\n            }\n            parentElement.appendChild(this._inkBarElement);\n        }\n    };\n}\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n/** @docs-private */\nconst _MatTabLabelWrapperMixinBase = mixinDisabled(class {\n});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass _MatTabLabelWrapperBase extends _MatTabLabelWrapperMixinBase {\n    constructor(elementRef) {\n        super();\n        this.elementRef = elementRef;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n}\n_MatTabLabelWrapperBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabLabelWrapperBase, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabLabelWrapperBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabLabelWrapperBase, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabLabelWrapperBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\nconst _MatTabLabelWrapperBaseWithInkBarItem = mixinInkBarItem(_MatTabLabelWrapperBase);\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBaseWithInkBarItem {\n}\nMatTabLabelWrapper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLabelWrapper, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatTabLabelWrapper.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: { disabled: \"disabled\", fitInkBarToContent: \"fitInkBarToContent\" }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    inputs: ['disabled', 'fitInkBarToContent'],\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTab.\n/** @docs-private */\nconst _MatTabMixinBase = mixinDisabled(class {\n});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\n/** @docs-private */\nclass _MatTabBase extends _MatTabMixinBase {\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    constructor(_viewContainerRef, _closestTabGroup) {\n        super();\n        this._viewContainerRef = _viewContainerRef;\n        this._closestTabGroup = _closestTabGroup;\n        /** Plain text label for the tab, used when there is no template label. */\n        this.textLabel = '';\n        /** Portal that will be the hosted content of the tab */\n        this._contentPortal = null;\n        /** Emits whenever the internal state of the tab changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The relatively indexed position where 0 represents the center, negative is left, and positive\n         * represents the right.\n         */\n        this.position = null;\n        /**\n         * The initial relatively index origin of the tab if it was created and selected after there\n         * was already a selected tab. Provides context of what position the tab should originate from.\n         */\n        this.origin = null;\n        /**\n         * Whether the tab is currently active.\n         */\n        this.isActive = false;\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n}\n_MatTabBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabBase, deps: [{ token: i0.ViewContainerRef }, { token: MAT_TAB_GROUP, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabBase, inputs: { textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\" }, viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB_GROUP]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }] } });\nclass MatTab extends _MatTabBase {\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n}\nMatTab.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTab, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatTab.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTab, selector: \"mat-tab\", inputs: { disabled: \"disabled\" }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }, { propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }], exportAs: [\"matTab\"], usesInheritance: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', inputs: ['disabled'], changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], propDecorators: { _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        value = coerceNumberProperty(value);\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._viewportRuler = _viewportRuler;\n        this._dir = _dir;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._animationMode = _animationMode;\n        /** The distance in pixels that the tab labels should be translated to the left. */\n        this._scrollDistance = 0;\n        /** Whether the header should scroll to the selected index after the view has been checked. */\n        this._selectedIndexChanged = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Whether the controls for pagination should be displayed */\n        this._showPaginationControls = false;\n        /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n        this._disableScrollAfter = true;\n        /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n        this._disableScrollBefore = true;\n        /** Stream that will stop the automated scrolling. */\n        this._stopScrolling = new Subject();\n        this._disablePagination = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the option is selected. */\n        this.selectFocusedIndex = new EventEmitter();\n        /** Event emitted when a label is focused. */\n        this.indexFocused = new EventEmitter();\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        _ngZone.runOutsideAngular(() => {\n            fromEvent(_elementRef.nativeElement, 'mouseleave')\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => {\n                this._stopInterval();\n            });\n        });\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('before');\n        });\n        fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('after');\n        });\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        const resize = this._viewportRuler.change(150);\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // Defer the first call in order to allow for slower browsers to lay out the elements.\n        // This helps in cases where the user lands directly on a page with paginated tabs.\n        // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n        // can hold up tests that are in a background tab.\n        this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n        // On dir change or window resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._changeDetectorRef.markForCheck();\n            }\n            this._showPaginationControls = isEnabled;\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n}\nMatPaginatedTabHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatedTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatPaginatedTabHeader.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatPaginatedTabHeader, inputs: { disablePagination: \"disablePagination\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disablePagination: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n}\n_MatTabHeaderBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabHeaderBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabHeaderBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabHeaderBase, inputs: { disableRipple: \"disableRipple\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabHeaderBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disableRipple: [{\n                type: Input\n            }] } });\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends _MatTabHeaderBase {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n}\nMatTabHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: { selectedIndex: \"selectedIndex\" }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5$1.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', inputs: ['selectedIndex'], outputs: ['selectFocusedIndex', 'indexFocused'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n// Boilerplate for applying mixins to MatTabGroup.\n/** @docs-private */\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n    /** Whether the tab group should grow to the size of the active tab. */\n    get dynamicHeight() {\n        return this._dynamicHeight;\n    }\n    set dynamicHeight(value) {\n        this._dynamicHeight = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = coerceNumberProperty(value, null);\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = coerceNumberProperty(value, null);\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    get preserveContent() {\n        return this._preserveContent;\n    }\n    set preserveContent(value) {\n        this._preserveContent = coerceBooleanProperty(value);\n    }\n    /** Background color of the tab group. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n        super(elementRef);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** All of the tabs that belong to the group. */\n        this._tabs = new QueryList();\n        /** The tab index that should be selected after the content has been checked. */\n        this._indexToSelect = 0;\n        /** Index of the tab that was focused last. */\n        this._lastFocusedTabIndex = null;\n        /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n        this._tabBodyWrapperHeight = 0;\n        /** Subscription to tabs being added/removed. */\n        this._tabsSubscription = Subscription.EMPTY;\n        /** Subscription to changes in the tab labels. */\n        this._tabLabelSubscription = Subscription.EMPTY;\n        this._dynamicHeight = false;\n        this._selectedIndex = null;\n        /** Position of the tab header. */\n        this.headerPosition = 'above';\n        this._disablePagination = false;\n        this._preserveContent = false;\n        /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        /** Event emitted when focus has changed within a tab group. */\n        this.focusChange = new EventEmitter();\n        /** Event emitted when the body animation has completed */\n        this.animationDone = new EventEmitter();\n        /** Event emitted when the tab selection has changed. */\n        this.selectedTabChange = new EventEmitter(true);\n        this._groupId = nextId++;\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n        this.preserveContent = !!defaultConfig?.preserveContent;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n        return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n        return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n}\n_MatTabGroupBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabGroupBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabGroupBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabGroupBase, inputs: { dynamicHeight: \"dynamicHeight\", selectedIndex: \"selectedIndex\", headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: \"contentTabIndex\", disablePagination: \"disablePagination\", preserveContent: \"preserveContent\", backgroundColor: \"backgroundColor\" }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabGroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { dynamicHeight: [{\n                type: Input\n            }], selectedIndex: [{\n                type: Input\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input\n            }], disablePagination: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], backgroundColor: [{\n                type: Input\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup extends _MatTabGroupBase {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(v) {\n        this._fitInkBarToContent = coerceBooleanProperty(v);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Whether tabs should be stretched to fill the header. */\n    get stretchTabs() {\n        return this._stretchTabs;\n    }\n    set stretchTabs(v) {\n        this._stretchTabs = coerceBooleanProperty(v);\n    }\n    constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n        super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n        this._fitInkBarToContent = false;\n        this._stretchTabs = true;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n    }\n}\nMatTabGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabGroup, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabGroup, selector: \"mat-tab-group\", inputs: { color: \"color\", disableRipple: \"disableRipple\", fitInkBarToContent: \"fitInkBarToContent\", stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"] }, host: { properties: { \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n        {\n            provide: MAT_TAB_GROUP,\n            useExisting: MatTabGroup,\n        },\n    ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }], exportAs: [\"matTabGroup\"], usesInheritance: true, ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-disabled{opacity:.4}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab-indicator__content--underline,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"], dependencies: [{ kind: \"directive\", type: i1$2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1$2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1$2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i4.CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\" }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\", \"fitInkBarToContent\"] }, { kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"selectedIndex\"], outputs: [\"selectFocusedIndex\", \"indexFocused\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, inputs: ['color', 'disableRipple'], providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-mdc-tab-group',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                    }, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-disabled{opacity:.4}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab-indicator__content--underline,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], fitInkBarToContent: [{\n                type: Input\n            }], stretchTabs: [{\n                type: Input,\n                args: ['mat-stretch-tabs']\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n        /** Theme color of the nav bar. */\n        this.color = 'primary';\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this.updateActiveLink();\n        });\n        super.ngAfterContentInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                this._changeDetectorRef.markForCheck();\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                return;\n            }\n        }\n        // The ink bar should hide itself if no items are active.\n        this.selectedIndex = -1;\n        this._inkBar.hide();\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n}\n_MatTabNavBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabNavBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabNavBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabNavBase, inputs: { backgroundColor: \"backgroundColor\", disableRipple: \"disableRipple\", color: \"color\", tabPanel: \"tabPanel\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabNavBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }] } });\n// Boilerplate for applying mixins to MatTabLink.\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {\n})));\n/** Base class with all of the `MatTabLink` functionality. */\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._isActive) {\n            this._isActive = newValue;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    constructor(_tabNavBar, \n    /** @docs-private */ elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n        super();\n        this._tabNavBar = _tabNavBar;\n        this.elementRef = elementRef;\n        this._focusMonitor = _focusMonitor;\n        /** Whether the tab link is active or not. */\n        this._isActive = false;\n        /** Unique id for the tab. */\n        this.id = `mat-tab-link-${nextUniqueId++}`;\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (this._tabNavBar.tabPanel && event.keyCode === SPACE) {\n            this.elementRef.nativeElement.click();\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n        if (this._tabNavBar.tabPanel) {\n            return this._isActive && !this.disabled ? 0 : -1;\n        }\n        else {\n            return this.tabIndex;\n        }\n    }\n}\n_MatTabLinkBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabLinkBase, deps: [{ token: _MatTabNavBase }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabLinkBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatTabLinkBase, inputs: { active: \"active\", id: \"id\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatTabLinkBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: _MatTabNavBase }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { active: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\nconst _MatTabLinkBaseWithInkBarItem = mixinInkBarItem(_MatTabLinkBase);\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends _MatTabNavBase {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(v) {\n        this._fitInkBarToContent.next(coerceBooleanProperty(v));\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Whether tabs should be stretched to fill the header. */\n    get stretchTabs() {\n        return this._stretchTabs;\n    }\n    set stretchTabs(v) {\n        this._stretchTabs = coerceBooleanProperty(v);\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n        super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n        this._fitInkBarToContent = new BehaviorSubject(false);\n        this._stretchTabs = true;\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n}\nMatTabNav.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabNav, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_TABS_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabNav.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabNav, selector: \"[mat-tab-nav-bar]\", inputs: { color: \"color\", fitInkBarToContent: \"fitInkBarToContent\", stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"] }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(function () { return MatTabLink; }), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5$1.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', inputs: ['color'], host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator .mdc-tab-indicator__content--underline{border-top-width:2px}.mdc-tab-indicator .mdc-tab-indicator__content--icon{height:34px;font-size:34px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-mdc-tab-header-with-background-background-color, transparent)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab .mdc-tab__text-label,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{border-color:var(--mat-mdc-tab-header-with-background-foreground-color, inherit)}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }] }]; }, propDecorators: { fitInkBarToContent: [{\n                type: Input\n            }], stretchTabs: [{\n                type: Input,\n                args: ['mat-stretch-tabs']\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends _MatTabLinkBaseWithInkBarItem {\n    constructor(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n        super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n        this._destroyed = new Subject();\n        tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n    }\n}\nMatTabLink.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLink, deps: [{ token: MatTabNav }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabLink.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabLink, selector: \"[mat-tab-link], [matTabLink]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", active: \"active\", id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_getTabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab-link.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab-link .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12}.mat-mdc-tab-link.mat-mdc-tab-disabled{pointer-events:none;opacity:.4}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', inputs: ['disabled', 'disableRipple', 'tabIndex', 'active', 'id'], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_getTabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-tab-link.mdc-tab{height:48px;flex-grow:0}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none}.mat-mdc-tab-link .mdc-tab__text-label{display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12}.mat-mdc-tab-link.mat-mdc-tab-disabled{pointer-events:none;opacity:.4}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"] }]\n        }], ctorParameters: function () { return [{ type: MatTabNav }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    constructor() {\n        /** Unique id for the tab panel. */\n        this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n}\nMatTabNavPanel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatTabNavPanel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatTabNavPanel, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTabsModule {\n}\nMatTabsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTabsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabsModule, declarations: [MatTabContent,\n        MatTabLabel,\n        MatTab,\n        MatTabGroup,\n        MatTabNav,\n        MatTabNavPanel,\n        MatTabLink,\n        // Private directives, should not be exported.\n        MatTabBody,\n        MatTabBodyPortal,\n        MatTabLabelWrapper,\n        MatTabHeader], imports: [CommonModule,\n        MatCommonModule,\n        PortalModule,\n        MatRippleModule,\n        ObserversModule,\n        A11yModule], exports: [MatCommonModule,\n        MatTabContent,\n        MatTabLabel,\n        MatTab,\n        MatTabGroup,\n        MatTabNav,\n        MatTabNavPanel,\n        MatTabLink] });\nMatTabsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabsModule, imports: [CommonModule,\n        MatCommonModule,\n        PortalModule,\n        MatRippleModule,\n        ObserversModule,\n        A11yModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        MatCommonModule,\n                        PortalModule,\n                        MatRippleModule,\n                        ObserversModule,\n                        A11yModule,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    declarations: [\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                        // Private directives, should not be exported.\n                        MatTabBody,\n                        MatTabBodyPortal,\n                        MatTabLabelWrapper,\n                        MatTabHeader,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, _MatTabBase, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLabelWrapperBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,iBAAiB;AACvC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAClQ,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAClK,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,eAAe,EAAEC,SAAS,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AAC9F,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,UAAU,QAAQ,mBAAmB;AAC/D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,YAAY,EAAEC,OAAO,EAAEC,SAAS,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,eAAe,QAAQ,MAAM;AAC7G,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC1G,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAgFwGnE,EAAE,gBAqiBmpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IAriBrpBA,EAAE,+FAkjD6lE;EAAA;EAAA;IAAA,eAljD/lEA,EAAE;IAAFA,EAAE,oDAkjD8kE;EAAA;AAAA;AAAA;EAAA;IAljDhlEA,EAAE,UAkjD8vE;EAAA;EAAA;IAAA,eAljDhwEA,EAAE;IAAFA,EAAE,oCAkjD8vE;EAAA;AAAA;AAAA;EAAA;IAAA,aAljDhwEA,EAAE;IAAFA,EAAE,+BAkjD69C;IAljD/9CA,EAAE;MAAA,oBAAFA,EAAE;MAAA;MAAA;MAAA,gBAAFA,EAAE;MAAA,YAAFA,EAAE;MAAA,OAAFA,EAAE,aAkjDk4C,uCAA+B;IAAA,EAAE;MAAA,oBAljDr6CA,EAAE;MAAA;MAAA,gBAAFA,EAAE;MAAA,OAAFA,EAAE,aAkjD+7C,sCAA2B;IAAA,EAAvD;IAljDr6CA,EAAE,wBAkjD0gD;IAljD5gDA,EAAE,8BAkjD01D;IAljD51DA,EAAE,iFAkjDqnE;IAljDvnEA,EAAE,wFAAFA,EAAE,wBAkjD4wE;IAljD9wEA,EAAE,eAkjD2xE;EAAA;EAAA;IAAA;IAAA;IAAA,YAljD7xEA,EAAE;IAAA,YAAFA,EAAE;IAAA,eAAFA,EAAE;IAAFA,EAAE,8DAkjDkvC;IAljDpvCA,EAAE,8CAkjDszB;IAljDxzBA,EAAE,mDAkjDk2B;IAljDp2BA,EAAE,aAkjD6uD;IAljD/uDA,EAAE,oCAkjD6uD;IAljD/uDA,EAAE,aAkjDi/D;IAljDn/DA,EAAE,yCAkjDi/D;EAAA;AAAA;AAAA;EAAA;IAAA,aAljDn/DA,EAAE;IAAFA,EAAE,sCAkjDivG;IAljDnvGA,EAAE;MAAFA,EAAE;MAAA,gBAAFA,EAAE;MAAA,OAAFA,EAAE,aAkjD6oG,qCAA6B;IAAA,EAAE;MAljD9qGA,EAAE;MAAA,gBAAFA,EAAE;MAAA,OAAFA,EAAE,aAkjD8sG,wCAAgC;IAAA,EAAlE;IAljD9qGA,EAAE,eAkjDowG;EAAA;EAAA;IAAA;IAAA;IAAA,eAljDtwGA,EAAE;IAAFA,EAAE,uEAkjDg1F;IAljDl1FA,EAAE,iDAkjD6lF;IAljD/lFA,EAAE,0HAkjD2sF;EAAA;AAAA;AAAA;AAAA;AA9nDrzF,MAAMoE,iBAAiB,GAAG;EACtB;EACAC,YAAY,EAAEf,OAAO,CAAC,cAAc,EAAE;EAClC;EACAC,KAAK,CAAC,uDAAuD,EAAEC,KAAK,CAAC;IAAEc,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC;EAC5F;EACA;EACA;EACA;EACAf,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChBc,SAAS,EAAE,0BAA0B;IACrCC,SAAS,EAAE,KAAK;IAChB;IACA;IACAC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHjB,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;IACjBc,SAAS,EAAE,yBAAyB;IACpCC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHf,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,sDAAsD,CAAC,CAAC,EACrID,UAAU,CAAC,4BAA4B,EAAE,CACrCD,KAAK,CAAC;IAAEc,SAAS,EAAE,0BAA0B;IAAEE,UAAU,EAAE;EAAS,CAAC,CAAC,EACtEd,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,EACFD,UAAU,CAAC,6BAA6B,EAAE,CACtCD,KAAK,CAAC;IAAEc,SAAS,EAAE,yBAAyB;IAAEE,UAAU,EAAE;EAAS,CAAC,CAAC,EACrEd,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,CACL;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,gBAAgB,SAAS7C,eAAe,CAAC;EAC3C8C,WAAW,CAACC,wBAAwB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAAE;IACtE,KAAK,CAACH,wBAAwB,EAAEC,gBAAgB,EAAEE,SAAS,CAAC;IAC5D,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,aAAa,GAAGzC,YAAY,CAACK,KAAK;IACvC;IACA,IAAI,CAACqC,WAAW,GAAG1C,YAAY,CAACK,KAAK;EACzC;EACA;EACAsC,QAAQ,GAAG;IACP,KAAK,CAACA,QAAQ,EAAE;IAChB,IAAI,CAACF,aAAa,GAAG,IAAI,CAACF,KAAK,CAACK,gBAAgB,CAC3CC,IAAI,CAACpC,SAAS,CAAC,IAAI,CAAC8B,KAAK,CAACO,iBAAiB,CAAC,IAAI,CAACP,KAAK,CAACQ,SAAS,CAAC,CAAC,CAAC,CACnEC,SAAS,CAAEC,WAAW,IAAK;MAC5B,IAAIA,WAAW,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,EAAE;QACpC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,WAAW,GAAG,IAAI,CAACH,KAAK,CAACc,mBAAmB,CAACL,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAACT,KAAK,CAACe,eAAe,EAAE;QAC7B,IAAI,CAACC,MAAM,EAAE;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAC,WAAW,GAAG;IACV,KAAK,CAACA,WAAW,EAAE;IACnB,IAAI,CAACf,aAAa,CAACgB,WAAW,EAAE;IAChC,IAAI,CAACf,WAAW,CAACe,WAAW,EAAE;EAClC;AACJ;AACAtB,gBAAgB,CAACuB,IAAI;EAAA,iBAA6FvB,gBAAgB,EAA1BzE,EAAE,mBAA0CA,EAAE,CAACiG,wBAAwB,GAAvEjG,EAAE,mBAAkFA,EAAE,CAACkG,gBAAgB,GAAvGlG,EAAE,mBAAkHC,UAAU,CAAC,MAAMkG,UAAU,CAAC,GAAhJnG,EAAE,mBAA2JF,QAAQ;AAAA,CAA4C;AACzT2E,gBAAgB,CAAC2B,IAAI,kBADmFpG,EAAE;EAAA,MACJyE,gBAAgB;EAAA;EAAA,WADdzE,EAAE;AAAA,EACkF;AAC5L;EAAA,mDAFwGA,EAAE,mBAEVyE,gBAAgB,EAAc,CAAC;IACnH4B,IAAI,EAAEnG,SAAS;IACfoG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAErG,EAAE,CAACiG;IAAyB,CAAC,EAAE;MAAEI,IAAI,EAAErG,EAAE,CAACkG;IAAiB,CAAC,EAAE;MAAEG,IAAI,EAAEF,UAAU;MAAEK,UAAU,EAAE,CAAC;QACrIH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACrG,UAAU,CAAC,MAAMkG,UAAU,CAAC;MACvC,CAAC;IAAE,CAAC,EAAE;MAAEE,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACxG,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,MAAM4G,eAAe,CAAC;EAClB;EACA,IAAIC,QAAQ,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,cAAc,GAAGD,QAAQ;IAC9B,IAAI,CAACE,8BAA8B,EAAE;EACzC;EACAnC,WAAW,CAACoC,WAAW,EAAEC,IAAI,EAAEC,iBAAiB,EAAE;IAC9C,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACE,sBAAsB,GAAG3E,YAAY,CAACK,KAAK;IAChD;IACA,IAAI,CAACuE,qBAAqB,GAAG,IAAI3E,OAAO,EAAE;IAC1C;IACA,IAAI,CAAC4E,YAAY,GAAG,IAAI/G,YAAY,EAAE;IACtC;IACA,IAAI,CAAC8E,gBAAgB,GAAG,IAAI9E,YAAY,EAAE;IAC1C;IACA,IAAI,CAACuF,mBAAmB,GAAG,IAAIvF,YAAY,EAAE;IAC7C;IACA,IAAI,CAACgH,WAAW,GAAG,IAAIhH,YAAY,CAAC,IAAI,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAACiH,iBAAiB,GAAG,OAAO;IAChC;IACA,IAAI,CAACzB,eAAe,GAAG,KAAK;IAC5B,IAAImB,IAAI,EAAE;MACN,IAAI,CAACE,sBAAsB,GAAGF,IAAI,CAACO,MAAM,CAAChC,SAAS,CAAEiC,GAAG,IAAK;QACzD,IAAI,CAACV,8BAA8B,CAACU,GAAG,CAAC;QACxCP,iBAAiB,CAACQ,YAAY,EAAE;MACpC,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACN,qBAAqB,CACrB/B,IAAI,CAACnC,oBAAoB,CAAC,CAACyE,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACEtC,SAAS,CAACuC,KAAK,IAAI;MACpB;MACA,IAAI,IAAI,CAACzC,iBAAiB,CAACyC,KAAK,CAACD,OAAO,CAAC,IAAI,IAAI,CAACxC,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACjF,IAAI,CAAC+B,WAAW,CAACU,IAAI,EAAE;MAC3B;MACA,IAAI,IAAI,CAAC1C,iBAAiB,CAACyC,KAAK,CAACF,SAAS,CAAC,IAAI,CAAC,IAAI,CAACvC,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACpF,IAAI,CAACM,mBAAmB,CAACmC,IAAI,EAAE;MACnC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI7C,QAAQ,GAAG;IACP,IAAI,IAAI,CAACI,SAAS,IAAI,QAAQ,IAAI,IAAI,CAAC0C,MAAM,IAAI,IAAI,EAAE;MACnD,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAAC2C,0BAA0B,CAAC,IAAI,CAACD,MAAM,CAAC;IACjE;EACJ;EACAjC,WAAW,GAAG;IACV,IAAI,CAACmB,sBAAsB,CAAClB,WAAW,EAAE;IACzC,IAAI,CAACmB,qBAAqB,CAACe,QAAQ,EAAE;EACzC;EACAC,sBAAsB,CAACL,KAAK,EAAE;IAC1B,MAAMtC,WAAW,GAAG,IAAI,CAACH,iBAAiB,CAACyC,KAAK,CAACD,OAAO,CAAC;IACzD,IAAI,CAAC1C,gBAAgB,CAAC4C,IAAI,CAACvC,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAAC4B,YAAY,CAACW,IAAI,CAAC,IAAI,CAAChB,WAAW,CAACqB,aAAa,CAACC,YAAY,CAAC;IACvE;EACJ;EACA;EACAC,mBAAmB,GAAG;IAClB,OAAO,IAAI,CAACtB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuB,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAlD,iBAAiB,CAACuB,QAAQ,EAAE;IACxB,OAAQA,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,IAAI,oBAAoB,IAAIA,QAAQ,IAAI,qBAAqB;EACzG;EACA;EACAE,8BAA8B,CAACU,GAAG,GAAG,IAAI,CAACc,mBAAmB,EAAE,EAAE;IAC7D,IAAI,IAAI,CAACzB,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACvB,SAAS,GAAGkC,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAACX,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACvB,SAAS,GAAGkC,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAAClC,SAAS,GAAG,QAAQ;IAC7B;EACJ;EACA;AACJ;AACA;AACA;EACI2C,0BAA0B,CAACD,MAAM,EAAE;IAC/B,MAAMR,GAAG,GAAG,IAAI,CAACc,mBAAmB,EAAE;IACtC,IAAKd,GAAG,IAAI,KAAK,IAAIQ,MAAM,IAAI,CAAC,IAAMR,GAAG,IAAI,KAAK,IAAIQ,MAAM,GAAG,CAAE,EAAE;MAC/D,OAAO,oBAAoB;IAC/B;IACA,OAAO,qBAAqB;EAChC;AACJ;AACArB,eAAe,CAACV,IAAI;EAAA,iBAA6FU,eAAe,EAvHxB1G,EAAE,mBAuHwCA,EAAE,CAACuI,UAAU,GAvHvDvI,EAAE,mBAuHkEqC,EAAE,CAACmG,cAAc,MAvHrFxI,EAAE,mBAuHgHA,EAAE,CAACyI,iBAAiB;AAAA,CAA4C;AAC1R/B,eAAe,CAACN,IAAI,kBAxHoFpG,EAAE;EAAA,MAwHL0G,eAAe;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA,EAA6U;AACjc;EAAA,mDAzHwG1G,EAAE,mBAyHV0G,eAAe,EAAc,CAAC;IAClHL,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC/FH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEtB,YAAY,EAAE,CAAC;MAC3Ed,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE4E,gBAAgB,EAAE,CAAC;MACnBmB,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEqF,mBAAmB,EAAE,CAAC;MACtBU,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE8G,WAAW,EAAE,CAAC;MACdf,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEoF,QAAQ,EAAE,CAAC;MACXW,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEyB,MAAM,EAAE,CAAC;MACT1B,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE8G,iBAAiB,EAAE,CAAC;MACpBhB,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEqF,eAAe,EAAE,CAAC;MAClBS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEoG,QAAQ,EAAE,CAAC;MACXN,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM4F,UAAU,SAASO,eAAe,CAAC;EACrChC,WAAW,CAACgE,UAAU,EAAEnB,GAAG,EAAEP,iBAAiB,EAAE;IAC5C,KAAK,CAAC0B,UAAU,EAAEnB,GAAG,EAAEP,iBAAiB,CAAC;EAC7C;AACJ;AACAb,UAAU,CAACH,IAAI;EAAA,iBAA6FG,UAAU,EA1JdnG,EAAE,mBA0J8BA,EAAE,CAACuI,UAAU,GA1J7CvI,EAAE,mBA0JwDqC,EAAE,CAACmG,cAAc,MA1J3ExI,EAAE,mBA0JsGA,EAAE,CAACyI,iBAAiB;AAAA,CAA4C;AAChRtC,UAAU,CAACwC,IAAI,kBA3JyF3I,EAAE;EAAA,MA2JVmG,UAAU;EAAA;EAAA;IAAA;MA3JFnG,EAAE,aA2J+I4B,eAAe;IAAA;IAAA;MAAA;MA3JhK5B,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,+BA2JmiB;MA3JriBA,EAAE;QAAA,OA2J4a,kCAA8B;MAAA,EAAE;QAAA,OAA8B,sCAAkC;MAAA,EAAhE;MA3J9cA,EAAE,yEA2JilB;MA3JnlBA,EAAE,eA2JylB;IAAA;IAAA;MA3J3lBA,EAAE,6BAAFA,EAAE,wCAAFA,EAAE,iDA2J6Y;IAAA;EAAA;EAAA,eAA6yByE,gBAAgB;EAAA;EAAA;EAAA;IAAA,WAA+C,CAACL,iBAAiB,CAACC,YAAY;EAAC;AAAA,EAAkG;AACr+C;EAAA,mDA5JwGrE,EAAE,mBA4JVmG,UAAU,EAAc,CAAC;IAC7GE,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEqC,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEC,eAAe,EAAEpI,uBAAuB,CAACqI,OAAO;MAAEC,UAAU,EAAE,CAAC5E,iBAAiB,CAACC,YAAY,CAAC;MAAE4E,IAAI,EAAE;QACpK,OAAO,EAAE;MACb,CAAC;MAAEC,QAAQ,EAAE,uXAAuX;MAAEC,MAAM,EAAE,CAAC,siBAAsiB;IAAE,CAAC;EACp8B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9C,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC/FH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEW,WAAW,EAAE,CAAC;MAC1E/C,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC1E,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyH,eAAe,GAAG,IAAIzI,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAM0I,aAAa,CAAC;EAChB5E,WAAW,EAAC,2BAA4BwE,QAAQ,EAAE;IAC9C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACAI,aAAa,CAACtD,IAAI;EAAA,iBAA6FsD,aAAa,EA3LpBtJ,EAAE,mBA2LoCA,EAAE,CAACa,WAAW;AAAA,CAA4C;AACxMyI,aAAa,CAAClD,IAAI,kBA5LsFpG,EAAE;EAAA,MA4LPsJ,aAAa;EAAA;EAAA,WA5LRtJ,EAAE,oBA4LgD,CAAC;IAAEuJ,OAAO,EAAEF,eAAe;IAAEG,WAAW,EAAEF;EAAc,CAAC,CAAC;AAAA,EAAiB;AACrO;EAAA,mDA7LwGtJ,EAAE,mBA6LVsJ,aAAa,EAAc,CAAC;IAChHjD,IAAI,EAAEnG,SAAS;IACfoG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BkD,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEF,eAAe;QAAEG,WAAW,EAAEF;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjD,IAAI,EAAErG,EAAE,CAACa;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6I,aAAa,GAAG,IAAI9I,cAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAM+I,OAAO,GAAG,IAAI/I,cAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAMgJ,WAAW,SAAS/H,SAAS,CAAC;EAChC6C,WAAW,CAACmF,WAAW,EAAEjF,gBAAgB,EAAEkF,WAAW,EAAE;IACpD,KAAK,CAACD,WAAW,EAAEjF,gBAAgB,CAAC;IACpC,IAAI,CAACkF,WAAW,GAAGA,WAAW;EAClC;AACJ;AACAF,WAAW,CAAC5D,IAAI;EAAA,iBAA6F4D,WAAW,EA9NhB5J,EAAE,mBA8NgCA,EAAE,CAACa,WAAW,GA9NhDb,EAAE,mBA8N2DA,EAAE,CAACkG,gBAAgB,GA9NhFlG,EAAE,mBA8N2F2J,OAAO;AAAA,CAA4D;AACxQC,WAAW,CAACxD,IAAI,kBA/NwFpG,EAAE;EAAA,MA+NT4J,WAAW;EAAA;EAAA,WA/NJ5J,EAAE,oBA+N2D,CAAC;IAAEuJ,OAAO,EAAEG,aAAa;IAAEF,WAAW,EAAEI;EAAY,CAAC,CAAC,GA/NnH5J,EAAE;AAAA,EA+NyJ;AACnQ;EAAA,mDAhOwGA,EAAE,mBAgOV4J,WAAW,EAAc,CAAC;IAC9GvD,IAAI,EAAEnG,SAAS;IACfoG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CkD,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEG,aAAa;QAAEF,WAAW,EAAEI;MAAY,CAAC;IACpE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAErG,EAAE,CAACa;IAAY,CAAC,EAAE;MAAEwF,IAAI,EAAErG,EAAE,CAACkG;IAAiB,CAAC,EAAE;MAAEG,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACqD,OAAO;MAClB,CAAC,EAAE;QACCtD,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0J,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZvF,WAAW,CAACwF,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;EACAC,IAAI,GAAG;IACH,IAAI,CAACD,MAAM,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,EAAE,CAAC;EACxD;EACA;EACAC,cAAc,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACP,MAAM,CAACQ,IAAI,CAACL,IAAI,IAAIA,IAAI,CAAC3B,UAAU,CAACP,aAAa,KAAKqC,OAAO,CAAC;IAC7F,MAAMG,WAAW,GAAG,IAAI,CAACC,YAAY;IACrCD,WAAW,EAAEL,gBAAgB,EAAE;IAC/B,IAAIG,iBAAiB,EAAE;MACnB,MAAMI,UAAU,GAAGF,WAAW,EAAEjC,UAAU,CAACP,aAAa,CAAC2C,qBAAqB,IAAI;MAClF;MACAL,iBAAiB,CAACM,cAAc,CAACF,UAAU,CAAC;MAC5C,IAAI,CAACD,YAAY,GAAGH,iBAAiB;IACzC;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,eAAe,CAACC,IAAI,EAAE;EAC3B,OAAO,cAAcA,IAAI,CAAC;IACtBvG,WAAW,CAAC,GAAG4B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAAC4E,aAAa,GAAG,KAAK;IAC9B;IACA;IACA,IAAIC,kBAAkB,GAAG;MACrB,OAAO,IAAI,CAACD,aAAa;IAC7B;IACA,IAAIC,kBAAkB,CAACC,CAAC,EAAE;MACtB,MAAMC,QAAQ,GAAG1H,qBAAqB,CAACyH,CAAC,CAAC;MACzC,IAAI,IAAI,CAACF,aAAa,KAAKG,QAAQ,EAAE;QACjC,IAAI,CAACH,aAAa,GAAGG,QAAQ;QAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;UACrB,IAAI,CAACC,oBAAoB,EAAE;QAC/B;MACJ;IACJ;IACA;IACAR,cAAc,CAACS,2BAA2B,EAAE;MACxC,MAAMhB,OAAO,GAAG,IAAI,CAAC9B,UAAU,CAACP,aAAa;MAC7C;MACA;MACA,IAAI,CAACqD,2BAA2B,IAC5B,CAAChB,OAAO,CAACM,qBAAqB,IAC9B,CAAC,IAAI,CAACW,qBAAqB,EAAE;QAC7BjB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;QACnC;MACJ;MACA;MACA;MACA;MACA,MAAM6B,iBAAiB,GAAGpB,OAAO,CAACM,qBAAqB,EAAE;MACzD,MAAMe,UAAU,GAAGL,2BAA2B,CAACM,KAAK,GAAGF,iBAAiB,CAACE,KAAK;MAC9E,MAAMC,SAAS,GAAGP,2BAA2B,CAACQ,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;MAC3ExB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC3B,mBAAmB,CAAC;MAC1C,IAAI,CAACyB,qBAAqB,CAACjI,KAAK,CAACyI,WAAW,CAAC,WAAW,EAAG,cAAaF,SAAU,cAAaF,UAAW,GAAE,CAAC;MAC7G;MACArB,OAAO,CAACM,qBAAqB,EAAE;MAC/BN,OAAO,CAACkB,SAAS,CAACQ,MAAM,CAAClC,mBAAmB,CAAC;MAC7CQ,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;MACnC,IAAI,CAAC0B,qBAAqB,CAACjI,KAAK,CAACyI,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;IACjE;IACA;IACA3B,gBAAgB,GAAG;MACf,IAAI,CAAC5B,UAAU,CAACP,aAAa,CAACuD,SAAS,CAACQ,MAAM,CAACnC,YAAY,CAAC;IAChE;IACA;IACA9E,QAAQ,GAAG;MACP,IAAI,CAACkH,oBAAoB,EAAE;IAC/B;IACA;IACArG,WAAW,GAAG;MACV,IAAI,CAACwF,cAAc,EAAEY,MAAM,EAAE;MAC7B,IAAI,CAACZ,cAAc,GAAG,IAAI,CAACG,qBAAqB,GAAG,IAAI;IAC3D;IACA;IACAU,oBAAoB,GAAG;MACnB,MAAMC,YAAY,GAAG,IAAI,CAAC1D,UAAU,CAACP,aAAa,CAACkE,aAAa,IAAIC,QAAQ;MAC5E,IAAI,CAAChB,cAAc,GAAGc,YAAY,CAACG,aAAa,CAAC,MAAM,CAAC;MACxD,IAAI,CAACd,qBAAqB,GAAGW,YAAY,CAACG,aAAa,CAAC,MAAM,CAAC;MAC/D,IAAI,CAACjB,cAAc,CAACkB,SAAS,GAAG,mBAAmB;MACnD,IAAI,CAACf,qBAAqB,CAACe,SAAS,GAChC,kEAAkE;MACtE,IAAI,CAAClB,cAAc,CAACmB,WAAW,CAAC,IAAI,CAAChB,qBAAqB,CAAC;MAC3D,IAAI,CAACF,oBAAoB,EAAE;IAC/B;IACA;AACR;AACA;AACA;IACQA,oBAAoB,GAAG;MACnB,IAAI,CAAC,IAAI,CAACD,cAAc,KAAK,OAAOoB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACzE,MAAMC,KAAK,CAAC,6DAA6D,CAAC;MAC9E;MACA,MAAMC,aAAa,GAAG,IAAI,CAAC1B,aAAa,GAClC,IAAI,CAACxC,UAAU,CAACP,aAAa,CAAC0E,aAAa,CAAC,mBAAmB,CAAC,GAChE,IAAI,CAACnE,UAAU,CAACP,aAAa;MACnC,IAAI,CAACyE,aAAa,KAAK,OAAOF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACnE,MAAMC,KAAK,CAAC,qCAAqC,CAAC;MACtD;MACAC,aAAa,CAACH,WAAW,CAAC,IAAI,CAACnB,cAAc,CAAC;IAClD;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASwB,+BAA+B,GAAG;EACvC,MAAMC,MAAM,GAAIvC,OAAO,KAAM;IACzBwB,IAAI,EAAExB,OAAO,GAAG,CAACA,OAAO,CAACwC,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDlB,KAAK,EAAEtB,OAAO,GAAG,CAACA,OAAO,CAACyC,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAItM,cAAc,CAAC,qBAAqB,EAAE;EACtEuM,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,4BAA4B,GAAGjM,aAAa,CAAC,MAAM,EACxD,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMkM,uBAAuB,SAASD,4BAA4B,CAAC;EAC/D3I,WAAW,CAACgE,UAAU,EAAE;IACpB,KAAK,EAAE;IACP,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;EACA6E,KAAK,GAAG;IACJ,IAAI,CAAC7E,UAAU,CAACP,aAAa,CAACoF,KAAK,EAAE;EACzC;EACAC,aAAa,GAAG;IACZ,OAAO,IAAI,CAAC9E,UAAU,CAACP,aAAa,CAAC6E,UAAU;EACnD;EACAS,cAAc,GAAG;IACb,OAAO,IAAI,CAAC/E,UAAU,CAACP,aAAa,CAAC8E,WAAW;EACpD;AACJ;AACAK,uBAAuB,CAACtH,IAAI;EAAA,iBAA6FsH,uBAAuB,EA7ZxCtN,EAAE,mBA6ZwDA,EAAE,CAACuI,UAAU;AAAA,CAA4C;AAC3N+E,uBAAuB,CAAClH,IAAI,kBA9Z4EpG,EAAE;EAAA,MA8ZGsN,uBAAuB;EAAA,WA9Z5BtN,EAAE;AAAA,EA8ZkE;AAC5K;EAAA,mDA/ZwGA,EAAE,mBA+ZVsN,uBAAuB,EAAc,CAAC;IAC1HjH,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7E,MAAMmF,qCAAqC,GAAG1C,eAAe,CAACsC,uBAAuB,CAAC;AACtF;AACA;AACA;AACA;AACA,MAAMK,kBAAkB,SAASD,qCAAqC,CAAC;AAEvEC,kBAAkB,CAAC3H,IAAI;EAAA;EAAA;IAAA,8EAzaiFhG,EAAE,uBAyaU2N,kBAAkB,SAAlBA,kBAAkB;EAAA;AAAA,GAAqD;AAC3LA,kBAAkB,CAACvH,IAAI,kBA1aiFpG,EAAE;EAAA,MA0aF2N,kBAAkB;EAAA;EAAA;EAAA;IAAA;MA1alB3N,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAAFA,EAAE;AAAA,EA0a8Q;AACxX;EAAA,mDA3awGA,EAAE,mBA2aV2N,kBAAkB,EAAc,CAAC;IACrHtH,IAAI,EAAEnG,SAAS;IACfoG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCqH,MAAM,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC;MAC1C3E,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4E,gBAAgB,GAAGzM,aAAa,CAAC,MAAM,EAC5C,CAAC;AACF;AACA;AACA;AACA;AACA,MAAM0M,aAAa,GAAG,IAAIlN,cAAc,CAAC,eAAe,CAAC;AACzD;AACA,MAAMmN,WAAW,SAASF,gBAAgB,CAAC;EACvC;EACA,IAAIG,OAAO,GAAG;IACV,OAAO,IAAI,CAACC,cAAc;EAC9B;EACAvJ,WAAW,CAACwJ,iBAAiB,EAAEC,gBAAgB,EAAE;IAC7C,KAAK,EAAE;IACP,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACH,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACI,aAAa,GAAG,IAAI9L,OAAO,EAAE;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACoE,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACoB,MAAM,GAAG,IAAI;IAClB;AACR;AACA;IACQ,IAAI,CAACuG,QAAQ,GAAG,KAAK;EACzB;EACAC,WAAW,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACJ,aAAa,CAACK,IAAI,EAAE;IAC7B;EACJ;EACA5I,WAAW,GAAG;IACV,IAAI,CAACuI,aAAa,CAACpG,QAAQ,EAAE;EACjC;EACAhD,QAAQ,GAAG;IACP,IAAI,CAACgJ,cAAc,GAAG,IAAInM,cAAc,CAAC,IAAI,CAAC6M,gBAAgB,IAAI,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACV,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,sBAAsB,CAACvG,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACwB,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACgF,cAAc,GAAGxG,KAAK;IAC/B;EACJ;AACJ;AACAyF,WAAW,CAAC/H,IAAI;EAAA,iBAA6F+H,WAAW,EAjgBhB/N,EAAE,mBAigBgCA,EAAE,CAACkG,gBAAgB,GAjgBrDlG,EAAE,mBAigBgE8N,aAAa;AAAA,CAA4D;AACnPC,WAAW,CAAC3H,IAAI,kBAlgBwFpG,EAAE;EAAA,MAkgBT+N,WAAW;EAAA;IAAA;MAlgBJ/N,EAAE,aAkgB8Qa,WAAW;IAAA;IAAA;MAAA;MAlgB3Rb,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAAFA,EAAE,6BAAFA,EAAE;AAAA,EAkgB0X;AACpe;EAAA,mDAngBwGA,EAAE,mBAmgBV+N,WAAW,EAAc,CAAC;IAC9G1H,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACkG;IAAiB,CAAC,EAAE;MAAEG,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7FH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACwH,aAAa;MACxB,CAAC,EAAE;QACCzH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuO,gBAAgB,EAAE,CAAC;MAC/CvI,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAACzF,WAAW,EAAE;QAAEkO,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEX,SAAS,EAAE,CAAC;MACZ/H,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE0I,SAAS,EAAE,CAAC;MACZ3I,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE2I,cAAc,EAAE,CAAC;MACjB5I,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE4I,UAAU,EAAE,CAAC;MACb7I,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE4O,SAAS,EAAE,CAAC;MACZ9I,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM6O,MAAM,SAASrB,WAAW,CAAC;EAC7B;EACA,IAAIsB,aAAa,GAAG;IAChB,OAAO,IAAI,CAACP,cAAc;EAC9B;EACA,IAAIO,aAAa,CAAC/G,KAAK,EAAE;IACrB,IAAI,CAACuG,sBAAsB,CAACvG,KAAK,CAAC;EACtC;AACJ;AACA8G,MAAM,CAACpJ,IAAI;EAAA;EAAA;IAAA,sDApiB6FhG,EAAE,uBAoiBFoP,MAAM,SAANA,MAAM;EAAA;AAAA,GAAqD;AACnKA,MAAM,CAACzG,IAAI,kBAriB6F3I,EAAE;EAAA,MAqiBdoP,MAAM;EAAA;EAAA;IAAA;MAriBMpP,EAAE,0BAqiB+KsJ,aAAa,KAA2BzI,WAAW;MAriBpOb,EAAE,0BAqiB6S4J,WAAW;IAAA;IAAA;MAAA;MAriB1T5J,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAqiB4D,CAAC;IAAEuJ,OAAO,EAAEI,OAAO;IAAEH,WAAW,EAAE4F;EAAO,CAAC,CAAC,GAriBzGpP,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,kEAqiBiqB;IAAA;EAAA;EAAA;AAAA,EAAqG;AACh3B;EAAA,mDAtiBwGA,EAAE,mBAsiBVoP,MAAM,EAAc,CAAC;IACzG/I,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEqH,MAAM,EAAE,CAAC,UAAU,CAAC;MAAE9E,eAAe,EAAEpI,uBAAuB,CAACqI,OAAO;MAAEH,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEyG,QAAQ,EAAE,QAAQ;MAAE7F,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEI,OAAO;QAAEH,WAAW,EAAE4F;MAAO,CAAC,CAAC;MAAElG,QAAQ,EAAE;IAAgR,CAAC;EACxf,CAAC,CAAC,QAAkB;IAAEyF,gBAAgB,EAAE,CAAC;MACjCtI,IAAI,EAAEvF,YAAY;MAClBwF,IAAI,EAAE,CAACgD,aAAa,EAAE;QAAEiG,IAAI,EAAE1O,WAAW;QAAEkO,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAEM,aAAa,EAAE,CAAC;MAChBhJ,IAAI,EAAEvF,YAAY;MAClBwF,IAAI,EAAE,CAACsD,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4F,2BAA2B,GAAGzL,+BAA+B,CAAC;EAChE0L,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;AACJ;AACA;AACA;EACI,IAAIC,iBAAiB,GAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiB,CAACvH,KAAK,EAAE;IACzB,IAAI,CAACwH,kBAAkB,GAAGnM,qBAAqB,CAAC2E,KAAK,CAAC;EAC1D;EACA;EACA,IAAIyH,aAAa,GAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAa,CAACzH,KAAK,EAAE;IACrBA,KAAK,GAAG1E,oBAAoB,CAAC0E,KAAK,CAAC;IACnC,IAAI,IAAI,CAAC0H,cAAc,IAAI1H,KAAK,EAAE;MAC9B,IAAI,CAAC2H,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACD,cAAc,GAAG1H,KAAK;MAC3B,IAAI,IAAI,CAAC4H,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACC,gBAAgB,CAAC7H,KAAK,CAAC;MAC5C;IACJ;EACJ;EACA5D,WAAW,CAACoC,WAAW,EAAEsJ,kBAAkB,EAAEC,cAAc,EAAEtJ,IAAI,EAAEuJ,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACnG,IAAI,CAAC1J,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACsJ,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACtJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACuJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACR,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACS,UAAU,GAAG,IAAInO,OAAO,EAAE;IAC/B;IACA,IAAI,CAACoO,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,IAAIvO,OAAO,EAAE;IACnC,IAAI,CAACuN,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACE,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACe,kBAAkB,GAAG,IAAI3Q,YAAY,EAAE;IAC5C;IACA,IAAI,CAAC4Q,YAAY,GAAG,IAAI5Q,YAAY,EAAE;IACtC;IACAkQ,OAAO,CAACW,iBAAiB,CAAC,MAAM;MAC5BzO,SAAS,CAACsE,WAAW,CAACqB,aAAa,EAAE,YAAY,CAAC,CAC7ChD,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAChCpL,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC4L,aAAa,EAAE;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,eAAe,GAAG;IACd;IACA3O,SAAS,CAAC,IAAI,CAAC4O,kBAAkB,CAACjJ,aAAa,EAAE,YAAY,EAAEqH,2BAA2B,CAAC,CACtFrK,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAChCpL,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC+L,qBAAqB,CAAC,QAAQ,CAAC;IACxC,CAAC,CAAC;IACF7O,SAAS,CAAC,IAAI,CAAC8O,cAAc,CAACnJ,aAAa,EAAE,YAAY,EAAEqH,2BAA2B,CAAC,CAClFrK,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAChCpL,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC+L,qBAAqB,CAAC,OAAO,CAAC;IACvC,CAAC,CAAC;EACN;EACAE,kBAAkB,GAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACzK,IAAI,GAAG,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG7E,EAAE,CAAC,KAAK,CAAC;IAC1D,MAAMgP,MAAM,GAAG,IAAI,CAACpB,cAAc,CAAC/I,MAAM,CAAC,GAAG,CAAC;IAC9C,MAAMoK,OAAO,GAAG,MAAM;MAClB,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,yBAAyB,EAAE;IACpC,CAAC;IACD,IAAI,CAAC1B,WAAW,GAAG,IAAI/N,eAAe,CAAC,IAAI,CAAC+H,MAAM,CAAC,CAC9C2H,yBAAyB,CAAC,IAAI,CAACxJ,mBAAmB,EAAE,CAAC,CACrDyJ,cAAc,EAAE,CAChBC,QAAQ;IACT;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B,IAAI,CAAC9B,WAAW,CAACC,gBAAgB,CAAC,IAAI,CAACH,cAAc,CAAC;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACM,OAAO,CAAC2B,QAAQ,CAAC9M,IAAI,CAACjC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoC,SAAS,CAACoM,OAAO,CAAC;IACtD;IACA;IACAhP,KAAK,CAAC8O,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACvH,MAAM,CAACsE,OAAO,EAAE,IAAI,CAAC0D,aAAa,EAAE,CAAC,CAC9D/M,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAChCpL,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACgL,OAAO,CAAC6B,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAAC7B,eAAe,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACC,qBAAqB,EAAE,EAAE,IAAI,CAACjC,eAAe,CAAC,CAAC;UAChGiB,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACxB,WAAW,CAAC2B,yBAAyB,CAAC,IAAI,CAACxJ,mBAAmB,EAAE,CAAC;IAC1E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAAC6H,WAAW,CAAC5I,MAAM,CAAChC,SAAS,CAACqN,aAAa,IAAI;MAC/C,IAAI,CAAC3B,YAAY,CAAClJ,IAAI,CAAC6K,aAAa,CAAC;MACrC,IAAI,CAACC,YAAY,CAACD,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAT,aAAa,GAAG;IACZ,IAAI,OAAOW,cAAc,KAAK,UAAU,EAAE;MACtC,OAAOlQ,KAAK;IAChB;IACA,OAAO,IAAI,CAACuH,MAAM,CAACsE,OAAO,CAACrJ,IAAI,CAACpC,SAAS,CAAC,IAAI,CAACmH,MAAM,CAAC,EAAE/G,SAAS,CAAE2P,QAAQ,IAAK,IAAIlQ,UAAU,CAAEmQ,QAAQ,IAAK,IAAI,CAACzC,OAAO,CAACW,iBAAiB,CAAC,MAAM;MAC9I,MAAM+B,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAACrE,IAAI,CAACuE,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAAC1I,OAAO,CAACC,IAAI,IAAI2I,cAAc,CAACE,OAAO,CAAC7I,IAAI,CAAC3B,UAAU,CAACP,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACT6K,cAAc,CAACG,UAAU,EAAE;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACA/P,IAAI,CAAC,CAAC,CAAC;IACP;IACA;IACAC,MAAM,CAAC4P,OAAO,IAAIA,OAAO,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAACxH,KAAK,GAAG,CAAC,IAAIuH,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqB,GAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAACvJ,MAAM,CAACwJ,MAAM,EAAE;MAC3C,IAAI,CAAC/B,gBAAgB,EAAE;MACvB,IAAI,CAAC8B,cAAc,GAAG,IAAI,CAACvJ,MAAM,CAACwJ,MAAM;MACxC,IAAI,CAACtD,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACyI,qBAAqB,EAAE;MAC5B,IAAI,CAAC0D,cAAc,CAAC,IAAI,CAAC3D,cAAc,CAAC;MACxC,IAAI,CAAC4D,uBAAuB,EAAE;MAC9B,IAAI,CAAChC,yBAAyB,EAAE;MAChC,IAAI,CAAC3B,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACG,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACqM,sBAAsB,EAAE;MAC7B,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACzD,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C;EACJ;EACA1B,WAAW,GAAG;IACV,IAAI,CAACoK,WAAW,EAAE6D,OAAO,EAAE;IAC3B,IAAI,CAACrD,UAAU,CAAChC,IAAI,EAAE;IACtB,IAAI,CAACgC,UAAU,CAACzI,QAAQ,EAAE;IAC1B,IAAI,CAAC6I,cAAc,CAAC7I,QAAQ,EAAE;EAClC;EACA;EACA+L,cAAc,CAACnM,KAAK,EAAE;IAClB;IACA,IAAI5D,cAAc,CAAC4D,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACoM,OAAO;MACjB,KAAK9P,KAAK;MACV,KAAKD,KAAK;QACN,IAAI,IAAI,CAACgQ,UAAU,KAAK,IAAI,CAACnE,aAAa,EAAE;UACxC,MAAM1F,IAAI,GAAG,IAAI,CAACH,MAAM,CAACiK,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAI7J,IAAI,IAAI,CAACA,IAAI,CAAC+J,QAAQ,EAAE;YACxB,IAAI,CAACrD,kBAAkB,CAACjJ,IAAI,CAAC,IAAI,CAACoM,UAAU,CAAC;YAC7C,IAAI,CAACG,aAAa,CAACxM,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,IAAI,CAACqI,WAAW,CAACoE,SAAS,CAACzM,KAAK,CAAC;IAAC;EAE9C;EACA;AACJ;AACA;EACI0M,iBAAiB,GAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAAC1N,WAAW,CAACqB,aAAa,CAACqM,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGD,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAAClE,OAAO,CAAC6B,GAAG,CAAC,MAAM;QACnB,IAAI,CAACR,gBAAgB,EAAE;QACvB,IAAI,CAACC,yBAAyB,EAAE;QAChC,IAAI,CAACxB,kBAAkB,CAAC5I,YAAY,EAAE;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImK,gBAAgB,GAAG;IACf,IAAI,CAAC+C,uBAAuB,EAAE;IAC9B,IAAI,CAACd,uBAAuB,EAAE;IAC9B,IAAI,CAACE,wBAAwB,EAAE;EACnC;EACA;EACA,IAAII,UAAU,GAAG;IACb,OAAO,IAAI,CAAChE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACyE,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIT,UAAU,CAAC5L,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACsM,aAAa,CAACtM,KAAK,CAAC,IAAI,IAAI,CAAC4L,UAAU,KAAK5L,KAAK,IAAI,CAAC,IAAI,CAAC4H,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAAC2E,aAAa,CAACvM,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIsM,aAAa,CAACE,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC5K,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAAC6K,OAAO,EAAE,CAACD,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIlC,YAAY,CAACoC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACrE,uBAAuB,EAAE;MAC9B,IAAI,CAACgD,cAAc,CAACqB,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAAC9K,MAAM,IAAI,IAAI,CAACA,MAAM,CAACwJ,MAAM,EAAE;MACnC,IAAI,CAACxJ,MAAM,CAAC6K,OAAO,EAAE,CAACC,QAAQ,CAAC,CAACzH,KAAK,EAAE;MACvC;MACA;MACA;MACA,MAAM0H,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC/M,aAAa;MACxD,MAAMZ,GAAG,GAAG,IAAI,CAACc,mBAAmB,EAAE;MACtC,IAAId,GAAG,IAAI,KAAK,EAAE;QACd0N,WAAW,CAACE,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDF,WAAW,CAACE,UAAU,GAAGF,WAAW,CAACG,WAAW,GAAGH,WAAW,CAAChI,WAAW;MAC9E;IACJ;EACJ;EACA;EACA5E,mBAAmB,GAAG;IAClB,OAAO,IAAI,CAACtB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuB,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAwL,wBAAwB,GAAG;IACvB,IAAI,IAAI,CAACjE,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMwF,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAACjN,mBAAmB,EAAE,KAAK,KAAK,GAAG,CAACgN,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAACpN,aAAa,CAAC3E,KAAK,CAACc,SAAS,GAAI,cAAaiO,IAAI,CAACiD,KAAK,CAACF,UAAU,CAAE,KAAI;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC/E,SAAS,CAACkF,OAAO,IAAI,IAAI,CAAClF,SAAS,CAACmF,IAAI,EAAE;MAC/C,IAAI,CAACR,iBAAiB,CAAC/M,aAAa,CAACgN,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIE,cAAc,GAAG;IACjB,OAAO,IAAI,CAAC5E,eAAe;EAC/B;EACA,IAAI4E,cAAc,CAAC/M,KAAK,EAAE;IACtB,IAAI,CAACqN,SAAS,CAACrN,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIsN,aAAa,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/M,aAAa,CAAC8E,WAAW;IACnE;IACA,MAAM8I,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAAClF,eAAe,GAAGsF,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqB,CAACH,SAAS,EAAE;IAC7B,IAAI,CAAC3E,aAAa,EAAE;IACpB,IAAI,CAAC0E,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlC,cAAc,CAACsC,UAAU,EAAE;IACvB,IAAI,IAAI,CAACpG,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMqG,aAAa,GAAG,IAAI,CAAChM,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC6K,OAAO,EAAE,CAACkB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/M,aAAa,CAAC8E,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAGiJ,aAAa,CAACxN,UAAU,CAACP,aAAa;IAC1E,IAAIgO,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAAC/N,mBAAmB,EAAE,IAAI,KAAK,EAAE;MACrC8N,cAAc,GAAGnJ,UAAU;MAC3BoJ,aAAa,GAAGD,cAAc,GAAGlJ,WAAW;IAChD,CAAC,MACI;MACDmJ,aAAa,GAAG,IAAI,CAACC,aAAa,CAAClO,aAAa,CAAC8E,WAAW,GAAGD,UAAU;MACzEmJ,cAAc,GAAGC,aAAa,GAAGnJ,WAAW;IAChD;IACA,MAAMqJ,gBAAgB,GAAG,IAAI,CAACjB,cAAc;IAC5C,MAAMkB,eAAe,GAAG,IAAI,CAAClB,cAAc,GAAGS,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAACjB,cAAc,IAAIiB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAAClB,cAAc,IAAI9C,IAAI,CAACE,GAAG,CAAC2D,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI5B,uBAAuB,GAAG;IACtB,IAAI,IAAI,CAAC7E,iBAAiB,EAAE;MACxB,IAAI,CAACc,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAM6F,SAAS,GAAG,IAAI,CAACH,aAAa,CAAClO,aAAa,CAACiN,WAAW,GAAG,IAAI,CAACtO,WAAW,CAACqB,aAAa,CAAC8E,WAAW;MAC3G,IAAI,CAACuJ,SAAS,EAAE;QACZ,IAAI,CAACnB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAImB,SAAS,KAAK,IAAI,CAAC7F,uBAAuB,EAAE;QAC5C,IAAI,CAACP,kBAAkB,CAAC5I,YAAY,EAAE;MAC1C;MACA,IAAI,CAACmJ,uBAAuB,GAAG6F,SAAS;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5C,uBAAuB,GAAG;IACtB,IAAI,IAAI,CAAC/D,iBAAiB,EAAE;MACxB,IAAI,CAACe,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACwE,cAAc,IAAI,CAAC;MACpD,IAAI,CAACzE,mBAAmB,GAAG,IAAI,CAACyE,cAAc,IAAI,IAAI,CAAC3C,qBAAqB,EAAE;MAC9E,IAAI,CAACtC,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIkL,qBAAqB,GAAG;IACpB,MAAM+D,eAAe,GAAG,IAAI,CAACJ,aAAa,CAAClO,aAAa,CAACiN,WAAW;IACpE,MAAMU,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/M,aAAa,CAAC8E,WAAW;IACnE,OAAOwJ,eAAe,GAAGX,UAAU,IAAI,CAAC;EAC5C;EACA;EACAlE,yBAAyB,GAAG;IACxB,MAAM8E,YAAY,GAAG,IAAI,CAACxM,MAAM,IAAI,IAAI,CAACA,MAAM,CAACwJ,MAAM,GAAG,IAAI,CAACxJ,MAAM,CAAC6K,OAAO,EAAE,CAAC,IAAI,CAAChF,aAAa,CAAC,GAAG,IAAI;IACzG,MAAM4G,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAChO,UAAU,CAACP,aAAa,GAAG,IAAI;IACxF,IAAIwO,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAACrM,cAAc,CAACoM,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAACzM,IAAI,EAAE;IACvB;EACJ;EACA;EACA+G,aAAa,GAAG;IACZ,IAAI,CAACJ,cAAc,CAACpC,IAAI,EAAE;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACI2C,qBAAqB,CAACwE,SAAS,EAAEgB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAAC5F,aAAa,EAAE;IACpB;IACArO,KAAK,CAAC6M,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACCxK,IAAI,CAAClC,SAAS,CAACP,KAAK,CAAC,IAAI,CAACoO,cAAc,EAAE,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAC5DpL,SAAS,CAAC,MAAM;MACjB,MAAM;QAAEyR,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACpB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAImB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAAC7F,aAAa,EAAE;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIyE,SAAS,CAAChP,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACkJ,iBAAiB,EAAE;MACxB,OAAO;QAAEkH,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAACrE,qBAAqB,EAAE;IACtD,IAAI,CAACjC,eAAe,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACsE,iBAAiB,EAAEpQ,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACkN,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACD,uBAAuB,EAAE;IAC9B,OAAO;MAAEmD,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAACvG;IAAgB,CAAC;EAChE;AACJ;AACAb,qBAAqB,CAAC5J,IAAI;EAAA,iBAA6F4J,qBAAqB,EAzhCpC5P,EAAE,mBAyhCoDA,EAAE,CAACuI,UAAU,GAzhCnEvI,EAAE,mBAyhC8EA,EAAE,CAACyI,iBAAiB,GAzhCpGzI,EAAE,mBAyhC+G6D,IAAI,CAACoT,aAAa,GAzhCnIjX,EAAE,mBAyhC8IqC,EAAE,CAACmG,cAAc,MAzhCjKxI,EAAE,mBAyhC4LA,EAAE,CAACkX,MAAM,GAzhCvMlX,EAAE,mBAyhCkN8D,EAAE,CAACqT,QAAQ,GAzhC/NnX,EAAE,mBAyhC0OgE,qBAAqB;AAAA,CAA4D;AACra4L,qBAAqB,CAACxJ,IAAI,kBA1hC8EpG,EAAE;EAAA,MA0hCC4P,qBAAqB;EAAA;IAAA;EAAA;AAAA,EAAqE;AACrM;EAAA,mDA3hCwG5P,EAAE,mBA2hCV4P,qBAAqB,EAAc,CAAC;IACxHvJ,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAExC,IAAI,CAACoT;IAAc,CAAC,EAAE;MAAE5Q,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC7JH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACkX;IAAO,CAAC,EAAE;MAAE7Q,IAAI,EAAEvC,EAAE,CAACqT;IAAS,CAAC,EAAE;MAAE9Q,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9EH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6L,iBAAiB,EAAE,CAAC;MAChDxJ,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6W,iBAAiB,SAASxH,qBAAqB,CAAC;EAClD;EACA,IAAIyH,aAAa,GAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAa,CAAC/O,KAAK,EAAE;IACrB,IAAI,CAACgP,cAAc,GAAG3T,qBAAqB,CAAC2E,KAAK,CAAC;EACtD;EACA5D,WAAW,CAACgE,UAAU,EAAE1B,iBAAiB,EAAEuQ,aAAa,EAAEhQ,GAAG,EAAEiQ,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAChP,UAAU,EAAE1B,iBAAiB,EAAEuQ,aAAa,EAAEhQ,GAAG,EAAEiQ,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACJ,cAAc,GAAG,KAAK;EAC/B;EACAjD,aAAa,CAACxM,KAAK,EAAE;IACjBA,KAAK,CAAC8P,cAAc,EAAE;EAC1B;AACJ;AACAP,iBAAiB,CAACpR,IAAI;EAAA,iBAA6FoR,iBAAiB,EAnkC5BpX,EAAE,mBAmkC4CA,EAAE,CAACuI,UAAU,GAnkC3DvI,EAAE,mBAmkCsEA,EAAE,CAACyI,iBAAiB,GAnkC5FzI,EAAE,mBAmkCuG6D,IAAI,CAACoT,aAAa,GAnkC3HjX,EAAE,mBAmkCsIqC,EAAE,CAACmG,cAAc,MAnkCzJxI,EAAE,mBAmkCoLA,EAAE,CAACkX,MAAM,GAnkC/LlX,EAAE,mBAmkC0M8D,EAAE,CAACqT,QAAQ,GAnkCvNnX,EAAE,mBAmkCkOgE,qBAAqB;AAAA,CAA4D;AAC7ZoT,iBAAiB,CAAChR,IAAI,kBApkCkFpG,EAAE;EAAA,MAokCHoX,iBAAiB;EAAA;IAAA;EAAA;EAAA,WApkChBpX,EAAE;AAAA,EAokCkG;AAC5M;EAAA,mDArkCwGA,EAAE,mBAqkCVoX,iBAAiB,EAAc,CAAC;IACpH/Q,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAExC,IAAI,CAACoT;IAAc,CAAC,EAAE;MAAE5Q,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC7JH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACkX;IAAO,CAAC,EAAE;MAAE7Q,IAAI,EAAEvC,EAAE,CAACqT;IAAS,CAAC,EAAE;MAAE9Q,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9EH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqT,aAAa,EAAE,CAAC;MAC5ChR,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqX,YAAY,SAASR,iBAAiB,CAAC;EACzC1S,WAAW,CAACgE,UAAU,EAAE1B,iBAAiB,EAAEuQ,aAAa,EAAEhQ,GAAG,EAAEiQ,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAChP,UAAU,EAAE1B,iBAAiB,EAAEuQ,aAAa,EAAEhQ,GAAG,EAAEiQ,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EAC7F;EACAnG,kBAAkB,GAAG;IACjB,IAAI,CAACqF,OAAO,GAAG,IAAI3M,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAACqH,kBAAkB,EAAE;EAC9B;AACJ;AACAqG,YAAY,CAAC5R,IAAI;EAAA,iBAA6F4R,YAAY,EAjmClB5X,EAAE,mBAimCkCA,EAAE,CAACuI,UAAU,GAjmCjDvI,EAAE,mBAimC4DA,EAAE,CAACyI,iBAAiB,GAjmClFzI,EAAE,mBAimC6F6D,IAAI,CAACoT,aAAa,GAjmCjHjX,EAAE,mBAimC4HqC,EAAE,CAACmG,cAAc,MAjmC/IxI,EAAE,mBAimC0KA,EAAE,CAACkX,MAAM,GAjmCrLlX,EAAE,mBAimCgM8D,EAAE,CAACqT,QAAQ,GAjmC7MnX,EAAE,mBAimCwNgE,qBAAqB;AAAA,CAA4D;AACnZ4T,YAAY,CAACjP,IAAI,kBAlmCuF3I,EAAE;EAAA,MAkmCR4X,YAAY;EAAA;EAAA;IAAA;MAlmCN5X,EAAE,0BAkmCua2N,kBAAkB;IAAA;IAAA;MAAA;MAlmC3b3N,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,kCAkmCwoD;MAlmC1oDA,EAAE;QAAA,OAkmCmgD,0BAAsB,QAAQ,CAAC;MAAA,EAAE;QAAA,OAAqB,0BAAsB,QAAQ,SAAS;MAAA,EAA5D;QAAA,OAAkF,mBAAe;MAAA,EAAjG;MAlmCtiDA,EAAE,uBAkmCusD;MAlmCzsDA,EAAE,eAkmCktD;MAlmCptDA,EAAE,+BAkmC44D;MAlmC94DA,EAAE;QAAA,OAkmCwyD,0BAAsB;MAAA,EAAE;MAlmCl0DA,EAAE,+BAkmC0gE;MAlmC5gEA,EAAE;QAAA,OAkmCo/D,uBAAmB;MAAA,EAAE;MAlmC3gEA,EAAE,+BAkmCgkE;MAlmClkEA,EAAE,gBAkmCimE;MAlmCnmEA,EAAE,eAkmC6mE;MAlmC/mEA,EAAE,oCAkmCgvF;MAlmClvFA,EAAE;QAAA,OAkmCinF,0BAAsB,OAAO,SAAS;MAAA,EAAE;QAAA,OAAiB,0BAAsB,OAAO,CAAC;MAAA,EAA/C;QAAA,OAAqE,mBAAe;MAAA,EAApF;MAlmC3pFA,EAAE,wBAkmC+yF;MAlmCjzFA,EAAE,eAkmC0zF;IAAA;IAAA;MAlmC5zFA,EAAE,gFAkmCg8C;MAlmCl8CA,EAAE,+EAkmCk3C;MAlmCp3CA,EAAE,aAkmC24D;MAlmC74DA,EAAE,gFAkmC24D;MAlmC74DA,EAAE,aAkmCqhF;MAlmCvhFA,EAAE,+EAkmCqhF;MAlmCvhFA,EAAE,8EAkmCw8E;IAAA;EAAA;EAAA,eAAkmFmB,EAAE,CAAC0W,SAAS,EAAwP7V,IAAI,CAAC8V,iBAAiB;EAAA;EAAA;AAAA,EAA0P;AACxqL;EAAA,mDAnmCwG9X,EAAE,mBAmmCV4X,YAAY,EAAc,CAAC;IAC/GvR,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAEqH,MAAM,EAAE,CAAC,eAAe,CAAC;MAAEmK,OAAO,EAAE,CAAC,oBAAoB,EAAE,cAAc,CAAC;MAAEnP,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEC,eAAe,EAAEpI,uBAAuB,CAACqI,OAAO;MAAEE,IAAI,EAAE;QACpM,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAEC,QAAQ,EAAE,6yDAA6yD;MAAEC,MAAM,EAAE,CAAC,qrEAAqrE;IAAE,CAAC;EACzgI,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9C,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAExC,IAAI,CAACoT;IAAc,CAAC,EAAE;MAAE5Q,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC7JH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACkX;IAAO,CAAC,EAAE;MAAE7Q,IAAI,EAAEvC,EAAE,CAACqT;IAAS,CAAC,EAAE;MAAE9Q,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9EH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkG,MAAM,EAAE,CAAC;MACrC7D,IAAI,EAAEtF,eAAe;MACrBuF,IAAI,EAAE,CAACqH,kBAAkB,EAAE;QAAEqK,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAE9C,iBAAiB,EAAE,CAAC;MACpB7O,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEwG,QAAQ,EAAE,CAAC;MACXlP,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEsH,aAAa,EAAE,CAAC;MAChBhQ,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEuC,cAAc,EAAE,CAAC;MACjBjL,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8K,kBAAkB,EAAE,CAAC;MACrB/K,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2R,eAAe,GAAG,IAAIrX,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsX,MAAM,GAAG,CAAC;AACd;AACA;AACA,MAAMC,qBAAqB,GAAG9W,UAAU,CAACC,kBAAkB,CAAC,MAAM;EAC9DoD,WAAW,CAACoC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC,EAAE,SAAS,CAAC;AACd;AACA;AACA;AACA;AACA,MAAMsR,gBAAgB,SAASD,qBAAqB,CAAC;EACjD;EACA,IAAIE,aAAa,GAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAa,CAAC/P,KAAK,EAAE;IACrB,IAAI,CAACgQ,cAAc,GAAG3U,qBAAqB,CAAC2E,KAAK,CAAC;EACtD;EACA;EACA,IAAIyH,aAAa,GAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAa,CAACzH,KAAK,EAAE;IACrB,IAAI,CAACiQ,cAAc,GAAG3U,oBAAoB,CAAC0E,KAAK,EAAE,IAAI,CAAC;EAC3D;EACA;EACA,IAAIjB,iBAAiB,GAAG;IACpB,OAAO,IAAI,CAACmR,kBAAkB;EAClC;EACA,IAAInR,iBAAiB,CAACiB,KAAK,EAAE;IACzB,IAAI,CAACkQ,kBAAkB,GAAG,OAAO,CAACC,IAAI,CAACnQ,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,GAAG,IAAI,GAAGA,KAAK;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIoQ,eAAe,GAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAe,CAACpQ,KAAK,EAAE;IACvB,IAAI,CAACqQ,gBAAgB,GAAG/U,oBAAoB,CAAC0E,KAAK,EAAE,IAAI,CAAC;EAC7D;EACA;AACJ;AACA;AACA;EACI,IAAIuH,iBAAiB,GAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiB,CAACvH,KAAK,EAAE;IACzB,IAAI,CAACwH,kBAAkB,GAAGnM,qBAAqB,CAAC2E,KAAK,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI1C,eAAe,GAAG;IAClB,OAAO,IAAI,CAACgT,gBAAgB;EAChC;EACA,IAAIhT,eAAe,CAAC0C,KAAK,EAAE;IACvB,IAAI,CAACsQ,gBAAgB,GAAGjV,qBAAqB,CAAC2E,KAAK,CAAC;EACxD;EACA;EACA,IAAIuQ,eAAe,GAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAe,CAACvQ,KAAK,EAAE;IACvB,MAAMoD,SAAS,GAAG,IAAI,CAAC5E,WAAW,CAACqB,aAAa,CAACuD,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAG,kBAAiB,IAAI,CAAC2M,eAAgB,EAAC,CAAC;IACtF,IAAIvQ,KAAK,EAAE;MACPoD,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAG,kBAAiBrD,KAAM,EAAC,CAAC;IACxE;IACA,IAAI,CAACwQ,gBAAgB,GAAGxQ,KAAK;EACjC;EACA5D,WAAW,CAACgE,UAAU,EAAE0H,kBAAkB,EAAE2I,aAAa,EAAEvI,cAAc,EAAE;IACvE,KAAK,CAAC9H,UAAU,CAAC;IACjB,IAAI,CAAC0H,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACwI,KAAK,GAAG,IAAIhY,SAAS,EAAE;IAC5B;IACA,IAAI,CAACuX,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACU,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,GAAG7W,YAAY,CAACK,KAAK;IAC3C;IACA,IAAI,CAACyW,qBAAqB,GAAG9W,YAAY,CAACK,KAAK;IAC/C,IAAI,CAAC2V,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACtI,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACqJ,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACvJ,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC8I,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACU,mBAAmB,GAAG,IAAIlZ,YAAY,EAAE;IAC7C;IACA,IAAI,CAACmZ,WAAW,GAAG,IAAInZ,YAAY,EAAE;IACrC;IACA,IAAI,CAACoZ,aAAa,GAAG,IAAIpZ,YAAY,EAAE;IACvC;IACA,IAAI,CAACqZ,iBAAiB,GAAG,IAAIrZ,YAAY,CAAC,IAAI,CAAC;IAC/C,IAAI,CAACsZ,QAAQ,GAAGxB,MAAM,EAAE;IACxB,IAAI,CAAC7Q,iBAAiB,GAClB0R,aAAa,IAAIA,aAAa,CAAC1R,iBAAiB,GAAG0R,aAAa,CAAC1R,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAACwI,iBAAiB,GAClBkJ,aAAa,IAAIA,aAAa,CAAClJ,iBAAiB,IAAI,IAAI,GAClDkJ,aAAa,CAAClJ,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACwI,aAAa,GACdU,aAAa,IAAIA,aAAa,CAACV,aAAa,IAAI,IAAI,GAAGU,aAAa,CAACV,aAAa,GAAG,KAAK;IAC9F,IAAI,CAACK,eAAe,GAAGK,aAAa,EAAEL,eAAe,IAAI,IAAI;IAC7D,IAAI,CAAC9S,eAAe,GAAG,CAAC,CAACmT,aAAa,EAAEnT,eAAe;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4N,qBAAqB,GAAG;IACpB;IACA;IACA,MAAMmG,aAAa,GAAI,IAAI,CAACpB,cAAc,GAAG,IAAI,CAACqB,cAAc,CAAC,IAAI,CAACrB,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAACvI,cAAc,IAAI2J,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAAC7J,cAAc,IAAI,IAAI;MAC9C,IAAI,CAAC6J,UAAU,EAAE;QACb,IAAI,CAACJ,iBAAiB,CAAC3R,IAAI,CAAC,IAAI,CAACgS,kBAAkB,CAACH,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMI,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC7R,aAAa;QAClD4R,OAAO,CAACvW,KAAK,CAACe,SAAS,GAAGwV,OAAO,CAAC3R,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACAgK,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC0G,KAAK,CAAC5O,OAAO,CAAC,CAAC6P,GAAG,EAAEnF,KAAK,KAAMmF,GAAG,CAAC3L,QAAQ,GAAGwG,KAAK,KAAK6E,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACP,mBAAmB,CAACxR,IAAI,CAAC6R,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAACK,eAAe,CAAC7R,aAAa,CAAC3E,KAAK,CAACe,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACyU,KAAK,CAAC5O,OAAO,CAAC,CAAC6P,GAAG,EAAEnF,KAAK,KAAK;MAC/BmF,GAAG,CAACtT,QAAQ,GAAGmO,KAAK,GAAG6E,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAAC3J,cAAc,IAAI,IAAI,IAAIiK,GAAG,CAACtT,QAAQ,IAAI,CAAC,IAAI,CAACsT,GAAG,CAAClS,MAAM,EAAE;QACjEkS,GAAG,CAAClS,MAAM,GAAG4R,aAAa,GAAG,IAAI,CAAC3J,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAK2J,aAAa,EAAE;MACvC,IAAI,CAAC3J,cAAc,GAAG2J,aAAa;MACnC,IAAI,CAACV,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAAC7I,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C;EACJ;EACA+J,kBAAkB,GAAG;IACjB,IAAI,CAAC2I,yBAAyB,EAAE;IAChC,IAAI,CAACC,qBAAqB,EAAE;IAC5B;IACA;IACA,IAAI,CAAChB,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACxK,OAAO,CAAClJ,SAAS,CAAC,MAAM;MACxD,MAAMqU,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACrB,cAAc,CAAC;MAC9D;MACA;MACA,IAAIoB,aAAa,KAAK,IAAI,CAAC3J,cAAc,EAAE;QACvC,MAAMoK,IAAI,GAAG,IAAI,CAACpB,KAAK,CAACjE,OAAO,EAAE;QACjC,IAAIsF,WAAW;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAAC1G,MAAM,EAAE4G,CAAC,EAAE,EAAE;UAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAAChM,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACiK,cAAc,GAAG,IAAI,CAACvI,cAAc,GAAGsK,CAAC;YAC7C,IAAI,CAACrB,oBAAoB,GAAG,IAAI;YAChCoB,WAAW,GAAGD,IAAI,CAACE,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACD,WAAW,IAAID,IAAI,CAACT,aAAa,CAAC,EAAE;UACrCvH,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;YACzB8H,IAAI,CAACT,aAAa,CAAC,CAACrL,QAAQ,GAAG,IAAI;YACnC,IAAI,CAACmL,iBAAiB,CAAC3R,IAAI,CAAC,IAAI,CAACgS,kBAAkB,CAACH,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACvJ,kBAAkB,CAAC5I,YAAY,EAAE;IAC1C,CAAC,CAAC;EACN;EACA;EACA0S,yBAAyB,GAAG;IACxB;IACA;IACA;IACA,IAAI,CAACK,QAAQ,CAAC/L,OAAO,CAACrJ,IAAI,CAACpC,SAAS,CAAC,IAAI,CAACwX,QAAQ,CAAC,CAAC,CAACjV,SAAS,CAAE8U,IAAI,IAAK;MACrE,IAAI,CAACpB,KAAK,CAACwB,KAAK,CAACJ,IAAI,CAAC/W,MAAM,CAAC4W,GAAG,IAAI;QAChC,OAAOA,GAAG,CAAC9L,gBAAgB,KAAK,IAAI,IAAI,CAAC8L,GAAG,CAAC9L,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAAC6K,KAAK,CAACyB,eAAe,EAAE;IAChC,CAAC,CAAC;EACN;EACA3U,WAAW,GAAG;IACV,IAAI,CAACkT,KAAK,CAACjF,OAAO,EAAE;IACpB,IAAI,CAACoF,iBAAiB,CAACpT,WAAW,EAAE;IACpC,IAAI,CAACqT,qBAAqB,CAACrT,WAAW,EAAE;EAC5C;EACA;EACA2U,aAAa,GAAG;IACZ,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC/I,yBAAyB,EAAE;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgB,GAAG;IACf,IAAI,IAAI,CAACgJ,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAChJ,gBAAgB,EAAE;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIiJ,QAAQ,CAAC9F,KAAK,EAAE;IACZ,MAAM+F,MAAM,GAAG,IAAI,CAACF,UAAU;IAC9B,IAAIE,MAAM,EAAE;MACRA,MAAM,CAAC3G,UAAU,GAAGY,KAAK;IAC7B;EACJ;EACAgG,aAAa,CAAChG,KAAK,EAAE;IACjB,IAAI,CAACmE,oBAAoB,GAAGnE,KAAK;IACjC,IAAI,CAACyE,WAAW,CAACzR,IAAI,CAAC,IAAI,CAACgS,kBAAkB,CAAChF,KAAK,CAAC,CAAC;EACzD;EACAgF,kBAAkB,CAAChF,KAAK,EAAE;IACtB,MAAMjN,KAAK,GAAG,IAAIkT,iBAAiB,EAAE;IACrClT,KAAK,CAACiN,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAACkE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACtF,MAAM,EAAE;MACjC7L,KAAK,CAACoS,GAAG,GAAG,IAAI,CAACjB,KAAK,CAACjE,OAAO,EAAE,CAACD,KAAK,CAAC;IAC3C;IACA,OAAOjN,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsS,qBAAqB,GAAG;IACpB,IAAI,IAAI,CAACf,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAACrT,WAAW,EAAE;IAC5C;IACA,IAAI,CAACqT,qBAAqB,GAAG1W,KAAK,CAAC,GAAG,IAAI,CAACsW,KAAK,CAACgC,GAAG,CAACf,GAAG,IAAIA,GAAG,CAAC5L,aAAa,CAAC,CAAC,CAAC/I,SAAS,CAAC,MAAM,IAAI,CAAC8K,kBAAkB,CAAC5I,YAAY,EAAE,CAAC;EAC3I;EACA;EACAoS,cAAc,CAAC9E,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAOvC,IAAI,CAACE,GAAG,CAAC,IAAI,CAACuG,KAAK,CAACtF,MAAM,GAAG,CAAC,EAAEnB,IAAI,CAACC,GAAG,CAACsC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAmG,cAAc,CAACX,CAAC,EAAE;IACd,OAAQ,iBAAgB,IAAI,CAACZ,QAAS,IAAGY,CAAE,EAAC;EAChD;EACA;EACAY,gBAAgB,CAACZ,CAAC,EAAE;IAChB,OAAQ,mBAAkB,IAAI,CAACZ,QAAS,IAAGY,CAAE,EAAC;EAClD;EACA;AACJ;AACA;AACA;EACIa,wBAAwB,CAACC,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAAC9C,cAAc,IAAI,CAAC,IAAI,CAACY,qBAAqB,EAAE;MACrD;IACJ;IACA,MAAMa,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC7R,aAAa;IAClD4R,OAAO,CAACvW,KAAK,CAAC+P,MAAM,GAAG,IAAI,CAAC2F,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAACc,eAAe,CAAC7R,aAAa,CAACkT,YAAY,EAAE;MACjDtB,OAAO,CAACvW,KAAK,CAAC+P,MAAM,GAAG6H,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACAE,2BAA2B,GAAG;IAC1B,MAAMvB,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC7R,aAAa;IAClD,IAAI,CAAC+Q,qBAAqB,GAAGa,OAAO,CAAC3R,YAAY;IACjD2R,OAAO,CAACvW,KAAK,CAAC+P,MAAM,GAAG,EAAE;IACzB,IAAI,CAACiG,aAAa,CAAC1R,IAAI,EAAE;EAC7B;EACA;EACAyT,YAAY,CAACtB,GAAG,EAAEuB,SAAS,EAAE1G,KAAK,EAAE;IAChC0G,SAAS,CAACtH,UAAU,GAAGY,KAAK;IAC5B,IAAI,CAACmF,GAAG,CAAC7F,QAAQ,EAAE;MACf,IAAI,CAACrE,aAAa,GAAG+E,KAAK;IAC9B;EACJ;EACA;EACA2G,YAAY,CAAC3G,KAAK,EAAE;IAChB,MAAM4G,WAAW,GAAG,IAAI,CAACzC,oBAAoB,IAAI,IAAI,CAAClJ,aAAa;IACnE,OAAO+E,KAAK,KAAK4G,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACAC,gBAAgB,CAACC,WAAW,EAAE9G,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAI8G,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACjB,UAAU,CAACzG,UAAU,GAAGY,KAAK;IACtC;EACJ;AACJ;AACAsD,gBAAgB,CAACpS,IAAI;EAAA,iBAA6FoS,gBAAgB,EAt+C1BpY,EAAE,mBAs+C0CA,EAAE,CAACuI,UAAU,GAt+CzDvI,EAAE,mBAs+CoEA,EAAE,CAACyI,iBAAiB,GAt+C1FzI,EAAE,mBAs+CqGiY,eAAe,MAt+CtHjY,EAAE,mBAs+CiJgE,qBAAqB;AAAA,CAA4D;AAC5UoU,gBAAgB,CAAChS,IAAI,kBAv+CmFpG,EAAE;EAAA,MAu+CJoY,gBAAgB;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAv+CdpY,EAAE;AAAA,EAu+C2f;AACrmB;EAAA,mDAx+CwGA,EAAE,mBAw+CVoY,gBAAgB,EAAc,CAAC;IACnH/R,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAAC2R,eAAe;MAC1B,CAAC,EAAE;QACC5R,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqU,aAAa,EAAE,CAAC;MAC5ChS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEwP,aAAa,EAAE,CAAC;MAChB1J,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE8Y,cAAc,EAAE,CAAC;MACjBhT,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE8G,iBAAiB,EAAE,CAAC;MACpBhB,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEmY,eAAe,EAAE,CAAC;MAClBrS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEsP,iBAAiB,EAAE,CAAC;MACpBxJ,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEqF,eAAe,EAAE,CAAC;MAClBS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEsY,eAAe,EAAE,CAAC;MAClBxS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE+Y,mBAAmB,EAAE,CAAC;MACtBjT,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEiZ,WAAW,EAAE,CAAC;MACdlT,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEkZ,aAAa,EAAE,CAAC;MAChBnT,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEmZ,iBAAiB,EAAE,CAAC;MACpBpT,IAAI,EAAE/F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMub,WAAW,SAASzD,gBAAgB,CAAC;EACvC;EACA,IAAIjN,kBAAkB,GAAG;IACrB,OAAO,IAAI,CAAC2Q,mBAAmB;EACnC;EACA,IAAI3Q,kBAAkB,CAACC,CAAC,EAAE;IACtB,IAAI,CAAC0Q,mBAAmB,GAAGnY,qBAAqB,CAACyH,CAAC,CAAC;IACnD,IAAI,CAACgF,kBAAkB,CAAC5I,YAAY,EAAE;EAC1C;EACA;EACA,IAAIuU,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAW,CAAC3Q,CAAC,EAAE;IACf,IAAI,CAAC4Q,YAAY,GAAGrY,qBAAqB,CAACyH,CAAC,CAAC;EAChD;EACA1G,WAAW,CAACgE,UAAU,EAAE1B,iBAAiB,EAAE+R,aAAa,EAAErB,aAAa,EAAE;IACrE,KAAK,CAAChP,UAAU,EAAE1B,iBAAiB,EAAE+R,aAAa,EAAErB,aAAa,CAAC;IAClE,IAAI,CAACoE,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACE,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC7Q,kBAAkB,GACnB4N,aAAa,IAAIA,aAAa,CAAC5N,kBAAkB,IAAI,IAAI,GACnD4N,aAAa,CAAC5N,kBAAkB,GAChC,KAAK;EACnB;AACJ;AACA0Q,WAAW,CAAC7V,IAAI;EAAA,iBAA6F6V,WAAW,EA5iDhB7b,EAAE,mBA4iDgCA,EAAE,CAACuI,UAAU,GA5iD/CvI,EAAE,mBA4iD0DA,EAAE,CAACyI,iBAAiB,GA5iDhFzI,EAAE,mBA4iD2FiY,eAAe,MA5iD5GjY,EAAE,mBA4iDuIgE,qBAAqB;AAAA,CAA4D;AAClU6X,WAAW,CAAClT,IAAI,kBA7iDwF3I,EAAE;EAAA,MA6iDT6b,WAAW;EAAA;EAAA;IAAA;MA7iDJ7b,EAAE,0BAkjDlDoP,MAAM;IAAA;IAAA;MAAA;MAljD0CpP,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA6iD8b,CAChiB;IACIuJ,OAAO,EAAEuE,aAAa;IACtBtE,WAAW,EAAEqS;EACjB,CAAC,CACJ,GAljDmG7b,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,0CAkjDgkB;MAljDlkBA,EAAE;QAAA,OAkjDue,yBAAqB;MAAA,EAAE;QAAA;MAAA;MAljDhgBA,EAAE,2DAkjDkzE;MAljDpzEA,EAAE,eAkjDq0E;MAljDv0EA,EAAE,+BAkjDk9E;MAljDp9EA,EAAE,6EAkjDowG;MAljDtwGA,EAAE,eAkjD4wG;IAAA;IAAA;MAljD9wGA,EAAE,oDAkjDsV;MAljDxVA,EAAE,aAkjDowB;MAljDtwBA,EAAE,iCAkjDowB;MAljDtwBA,EAAE,aAkjD87E;MAljDh8EA,EAAE,gFAkjD87E;MAljDh8EA,EAAE,aAkjDiiF;MAljDniFA,EAAE,iCAkjDiiF;IAAA;EAAA;EAAA,eAAm0LH,IAAI,CAACoc,OAAO,EAAoFpc,IAAI,CAACqc,OAAO,EAAmHrc,IAAI,CAACsc,IAAI,EAA6Fxa,EAAE,CAACC,eAAe,EAAiJT,EAAE,CAAC0W,SAAS,EAAwP3V,EAAE,CAACka,eAAe,EAA2JjW,UAAU,EAAyDwH,kBAAkB,EAA6GiK,YAAY;EAAA;EAAA;AAAA,EAA6M;AAClwT;EAAA,mDAnjDwG5X,EAAE,mBAmjDV6b,WAAW,EAAc,CAAC;IAC9GxV,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAE+I,QAAQ,EAAE,aAAa;MAAE1G,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEC,eAAe,EAAEpI,uBAAuB,CAACqI,OAAO;MAAE6E,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;MAAEnE,SAAS,EAAE,CACzL;QACIF,OAAO,EAAEuE,aAAa;QACtBtE,WAAW,EAAEqS;MACjB,CAAC,CACJ;MAAE5S,IAAI,EAAE;QACL,OAAO,EAAE,mBAAmB;QAC5B,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE;MAC9C,CAAC;MAAEC,QAAQ,EAAE,4gGAA4gG;MAAEC,MAAM,EAAE,CAAC,6hKAA6hK;IAAE,CAAC;EAChlQ,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9C,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHH,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAAC2R,eAAe;MAC1B,CAAC,EAAE;QACC5R,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuW,QAAQ,EAAE,CAAC;MACvClU,IAAI,EAAEtF,eAAe;MACrBuF,IAAI,EAAE,CAAC8I,MAAM,EAAE;QAAE4I,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEgC,eAAe,EAAE,CAAC;MAClB3T,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEqU,UAAU,EAAE,CAAC;MACbtU,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE6E,kBAAkB,EAAE,CAAC;MACrB9E,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEwb,WAAW,EAAE,CAAC;MACd1V,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMyU,iBAAiB,CAAC;;AAGxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsB,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAAS1M,qBAAqB,CAAC;EAC/C;EACA,IAAIiJ,eAAe,GAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAe,CAACvQ,KAAK,EAAE;IACvB,MAAMoD,SAAS,GAAG,IAAI,CAAC5E,WAAW,CAACqB,aAAa,CAACuD,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAG,kBAAiB,IAAI,CAAC2M,eAAgB,EAAC,CAAC;IACtF,IAAIvQ,KAAK,EAAE;MACPoD,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAG,kBAAiBrD,KAAM,EAAC,CAAC;IACxE;IACA,IAAI,CAACwQ,gBAAgB,GAAGxQ,KAAK;EACjC;EACA;EACA,IAAI+O,aAAa,GAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAa,CAAC/O,KAAK,EAAE;IACrB,IAAI,CAACgP,cAAc,GAAG3T,qBAAqB,CAAC2E,KAAK,CAAC;EACtD;EACA5D,WAAW,CAACgE,UAAU,EAAEnB,GAAG,EAAEiQ,MAAM,EAAExQ,iBAAiB,EAAEuQ,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAChP,UAAU,EAAE1B,iBAAiB,EAAEuQ,aAAa,EAAEhQ,GAAG,EAAEiQ,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACJ,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACiF,KAAK,GAAG,SAAS;EAC1B;EACAlI,aAAa,GAAG;IACZ;EAAA;EAEJ9C,kBAAkB,GAAG;IACjB;IACA;IACA,IAAI,CAACrH,MAAM,CAACsE,OAAO,CAACrJ,IAAI,CAACpC,SAAS,CAAC,IAAI,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAACpL,SAAS,CAAC,MAAM;MAClF,IAAI,CAACkX,gBAAgB,EAAE;IAC3B,CAAC,CAAC;IACF,KAAK,CAACjL,kBAAkB,EAAE;EAC9B;EACA;EACAiL,gBAAgB,GAAG;IACf,IAAI,CAAC,IAAI,CAACtS,MAAM,EAAE;MACd;IACJ;IACA,MAAMuS,KAAK,GAAG,IAAI,CAACvS,MAAM,CAAC6K,OAAO,EAAE;IACnC,KAAK,IAAIuF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,KAAK,CAAC/I,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACnC,IAAImC,KAAK,CAACnC,CAAC,CAAC,CAACoC,MAAM,EAAE;QACjB,IAAI,CAAC3M,aAAa,GAAGuK,CAAC;QACtB,IAAI,CAAClK,kBAAkB,CAAC5I,YAAY,EAAE;QACtC,IAAI,IAAI,CAACmV,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACC,YAAY,GAAGH,KAAK,CAACnC,CAAC,CAAC,CAACuC,EAAE;QAC5C;QACA;MACJ;IACJ;IACA;IACA,IAAI,CAAC9M,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC6G,OAAO,CAACzM,IAAI,EAAE;EACvB;EACA2S,QAAQ,GAAG;IACP,OAAO,IAAI,CAACH,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC7V,WAAW,CAACqB,aAAa,CAAC4U,YAAY,CAAC,MAAM,CAAC;EAC1F;AACJ;AACAT,cAAc,CAACtW,IAAI;EAAA,iBAA6FsW,cAAc,EAvqDtBtc,EAAE,mBAuqDsCA,EAAE,CAACuI,UAAU,GAvqDrDvI,EAAE,mBAuqDgEqC,EAAE,CAACmG,cAAc,MAvqDnFxI,EAAE,mBAuqD8GA,EAAE,CAACkX,MAAM,GAvqDzHlX,EAAE,mBAuqDoIA,EAAE,CAACyI,iBAAiB,GAvqD1JzI,EAAE,mBAuqDqK6D,IAAI,CAACoT,aAAa,GAvqDzLjX,EAAE,mBAuqDoM8D,EAAE,CAACqT,QAAQ,GAvqDjNnX,EAAE,mBAuqD4NgE,qBAAqB;AAAA,CAA4D;AACvZsY,cAAc,CAAClW,IAAI,kBAxqDqFpG,EAAE;EAAA,MAwqDNsc,cAAc;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAxqDVtc,EAAE;AAAA,EAwqDsK;AAChR;EAAA,mDAzqDwGA,EAAE,mBAyqDVsc,cAAc,EAAc,CAAC;IACjHjW,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC/FH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACkX;IAAO,CAAC,EAAE;MAAE7Q,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAExC,IAAI,CAACoT;IAAc,CAAC,EAAE;MAAE5Q,IAAI,EAAEvC,EAAE,CAACqT;IAAS,CAAC,EAAE;MAAE9Q,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5IH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6U,eAAe,EAAE,CAAC;MAC9CxS,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE8W,aAAa,EAAE,CAAC;MAChBhR,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEgc,KAAK,EAAE,CAAC;MACRlW,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEoc,QAAQ,EAAE,CAAC;MACXtW,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMyc,oBAAoB,GAAGzb,aAAa,CAACD,kBAAkB,CAACF,aAAa,CAAC,MAAM,EACjF,CAAC,CAAC,CAAC;AACJ;AACA,MAAM6b,eAAe,SAASD,oBAAoB,CAAC;EAC/C;EACA,IAAIN,MAAM,GAAG;IACT,OAAO,IAAI,CAACQ,SAAS;EACzB;EACA,IAAIR,MAAM,CAACpU,KAAK,EAAE;IACd,MAAM+C,QAAQ,GAAG1H,qBAAqB,CAAC2E,KAAK,CAAC;IAC7C,IAAI+C,QAAQ,KAAK,IAAI,CAAC6R,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAG7R,QAAQ;MACzB,IAAI,CAAC8R,UAAU,CAACX,gBAAgB,EAAE;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIY,cAAc,GAAG;IACjB,OAAQ,IAAI,CAAChJ,QAAQ,IACjB,IAAI,CAACiD,aAAa,IAClB,IAAI,CAAC8F,UAAU,CAAC9F,aAAa,IAC7B,CAAC,CAAC,IAAI,CAACgG,YAAY,CAACjJ,QAAQ;EACpC;EACA1P,WAAW,CAACyY,UAAU,EACtB,oBAAqBzU,UAAU,EAAE4U,mBAAmB,EAAEtI,QAAQ,EAAEuI,aAAa,EAAE7F,aAAa,EAAE;IAC1F,KAAK,EAAE;IACP,IAAI,CAACyF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACzU,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC6U,aAAa,GAAGA,aAAa;IAClC;IACA,IAAI,CAACL,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACL,EAAE,GAAI,gBAAeR,YAAY,EAAG,EAAC;IAC1C,IAAI,CAACgB,YAAY,GAAGC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACtI,QAAQ,GAAGwI,QAAQ,CAACxI,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI0C,aAAa,KAAK,gBAAgB,EAAE;MACpC,IAAI,CAAC2F,YAAY,CAACI,SAAS,GAAG;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;EACJ;EACA;EACApQ,KAAK,GAAG;IACJ,IAAI,CAAC7E,UAAU,CAACP,aAAa,CAACoF,KAAK,EAAE;EACzC;EACA4D,eAAe,GAAG;IACd,IAAI,CAACoM,aAAa,CAACK,OAAO,CAAC,IAAI,CAAClV,UAAU,CAAC;EAC/C;EACA5C,WAAW,GAAG;IACV,IAAI,CAACyX,aAAa,CAACM,cAAc,CAAC,IAAI,CAACnV,UAAU,CAAC;EACtD;EACAoV,YAAY,GAAG;IACX;IACA;IACA,IAAI,CAACX,UAAU,CAACjJ,UAAU,GAAG,IAAI,CAACiJ,UAAU,CAACjT,MAAM,CAAC6K,OAAO,EAAE,CAACgJ,OAAO,CAAC,IAAI,CAAC;EAC/E;EACA/J,cAAc,CAACnM,KAAK,EAAE;IAClB,IAAI,IAAI,CAACsV,UAAU,CAACR,QAAQ,IAAI9U,KAAK,CAACoM,OAAO,KAAK/P,KAAK,EAAE;MACrD,IAAI,CAACwE,UAAU,CAACP,aAAa,CAAC6V,KAAK,EAAE;IACzC;EACJ;EACAC,gBAAgB,GAAG;IACf,OAAO,IAAI,CAACd,UAAU,CAACR,QAAQ,GACzB,IAAI,CAACQ,UAAU,CAACR,QAAQ,EAAEE,EAAE,GAC5B,IAAI,CAACnU,UAAU,CAACP,aAAa,CAAC4U,YAAY,CAAC,eAAe,CAAC;EACrE;EACAmB,gBAAgB,GAAG;IACf,IAAI,IAAI,CAACf,UAAU,CAACR,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACD,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAAChU,UAAU,CAACP,aAAa,CAAC4U,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAoB,eAAe,GAAG;IACd,OAAO,IAAI,CAACzB,MAAM,IAAI,CAAC,IAAI,CAACS,UAAU,CAACR,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAG,QAAQ,GAAG;IACP,OAAO,IAAI,CAACK,UAAU,CAACR,QAAQ,GAAG,KAAK,GAAG,IAAI,CAACjU,UAAU,CAACP,aAAa,CAAC4U,YAAY,CAAC,MAAM,CAAC;EAChG;EACAtB,YAAY,GAAG;IACX,IAAI,IAAI,CAAC0B,UAAU,CAACR,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACO,SAAS,IAAI,CAAC,IAAI,CAAC9I,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,MACI;MACD,OAAO,IAAI,CAACY,QAAQ;IACxB;EACJ;AACJ;AACAiI,eAAe,CAACjX,IAAI;EAAA,iBAA6FiX,eAAe,EArxDxBjd,EAAE,mBAqxDwCsc,cAAc,GArxDxDtc,EAAE,mBAqxDmEA,EAAE,CAACuI,UAAU,GArxDlFvI,EAAE,mBAqxD6FwB,yBAAyB,MArxDxHxB,EAAE,mBAqxDmJ,UAAU,GArxD/JA,EAAE,mBAqxD2LkC,EAAE,CAACkc,YAAY,GArxD5Mpe,EAAE,mBAqxDuNgE,qBAAqB;AAAA,CAA4D;AAClZiZ,eAAe,CAAC7W,IAAI,kBAtxDoFpG,EAAE;EAAA,MAsxDLid,eAAe;EAAA;IAAA;IAAA;EAAA;EAAA,WAtxDZjd,EAAE;AAAA,EAsxD0F;AACpM;EAAA,mDAvxDwGA,EAAE,mBAuxDVid,eAAe,EAAc,CAAC;IAClH5W,IAAI,EAAEnG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmG,IAAI,EAAEiW;IAAe,CAAC,EAAE;MAAEjW,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QACjHH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAAC9E,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE6E,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEpF,SAAS;QACfqF,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAEnE,EAAE,CAACkc;IAAa,CAAC,EAAE;MAAE/X,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0Y,MAAM,EAAE,CAAC;MACrCrW,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEsc,EAAE,EAAE,CAAC;MACLxW,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8d,6BAA6B,GAAGrT,eAAe,CAACiS,eAAe,CAAC;AACtE;AACA;AACA;AACA;AACA,MAAMqB,SAAS,SAAShC,cAAc,CAAC;EACnC;EACA,IAAInR,kBAAkB,GAAG;IACrB,OAAO,IAAI,CAAC2Q,mBAAmB,CAACxT,KAAK;EACzC;EACA,IAAI6C,kBAAkB,CAACC,CAAC,EAAE;IACtB,IAAI,CAAC0Q,mBAAmB,CAACpN,IAAI,CAAC/K,qBAAqB,CAACyH,CAAC,CAAC,CAAC;IACvD,IAAI,CAACgF,kBAAkB,CAAC5I,YAAY,EAAE;EAC1C;EACA;EACA,IAAIuU,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAW,CAAC3Q,CAAC,EAAE;IACf,IAAI,CAAC4Q,YAAY,GAAGrY,qBAAqB,CAACyH,CAAC,CAAC;EAChD;EACA1G,WAAW,CAACgE,UAAU,EAAEnB,GAAG,EAAEiQ,MAAM,EAAExQ,iBAAiB,EAAEuQ,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAEqB,aAAa,EAAE;IAC3G,KAAK,CAACrQ,UAAU,EAAEnB,GAAG,EAAEiQ,MAAM,EAAExQ,iBAAiB,EAAEuQ,aAAa,EAAEE,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACoE,mBAAmB,GAAG,IAAIhZ,eAAe,CAAC,KAAK,CAAC;IACrD,IAAI,CAACkZ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnM,iBAAiB,GAClBkJ,aAAa,IAAIA,aAAa,CAAClJ,iBAAiB,IAAI,IAAI,GAClDkJ,aAAa,CAAClJ,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC1E,kBAAkB,GACnB4N,aAAa,IAAIA,aAAa,CAAC5N,kBAAkB,IAAI,IAAI,GACnD4N,aAAa,CAAC5N,kBAAkB,GAChC,KAAK;EACnB;EACAoG,kBAAkB,GAAG;IACjB,IAAI,CAACqF,OAAO,GAAG,IAAI3M,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAACqH,kBAAkB,EAAE;EAC9B;EACAJ,eAAe,GAAG;IACd,IAAI,CAAC,IAAI,CAACwL,QAAQ,KAAK,OAAOjQ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACwE,eAAe,EAAE;EAC3B;AACJ;AACAmN,SAAS,CAACtY,IAAI;EAAA,iBAA6FsY,SAAS,EAx1DZte,EAAE,mBAw1D4BA,EAAE,CAACuI,UAAU,GAx1D3CvI,EAAE,mBAw1DsDqC,EAAE,CAACmG,cAAc,MAx1DzExI,EAAE,mBAw1DoGA,EAAE,CAACkX,MAAM,GAx1D/GlX,EAAE,mBAw1D0HA,EAAE,CAACyI,iBAAiB,GAx1DhJzI,EAAE,mBAw1D2J6D,IAAI,CAACoT,aAAa,GAx1D/KjX,EAAE,mBAw1D0L8D,EAAE,CAACqT,QAAQ,GAx1DvMnX,EAAE,mBAw1DkNgE,qBAAqB,MAx1DzOhE,EAAE,mBAw1DoQiY,eAAe;AAAA,CAA4D;AACzbqG,SAAS,CAAC3V,IAAI,kBAz1D0F3I,EAAE;EAAA,MAy1DXse,SAAS;EAAA;EAAA;IAAA;MAz1DAte,EAAE,0BAy1DuwBue,UAAU;IAAA;IAAA;MAAA;MAz1DnxBve,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,kCAy1DgiE;MAz1DliEA,EAAE;QAAA,OAy1D25D,0BAAsB,QAAQ,CAAC;MAAA,EAAE;QAAA,OAAqB,0BAAsB,QAAQ,SAAS;MAAA,EAA5D;QAAA,OAAkF,mBAAe;MAAA,EAAjG;MAz1D97DA,EAAE,uBAy1D+lE;MAz1DjmEA,EAAE,eAy1D0mE;MAz1D5mEA,EAAE,+BAy1D+sE;MAz1DjtEA,EAAE;QAAA,OAy1DsrE,0BAAsB;MAAA,EAAE;MAz1DhtEA,EAAE,+BAy1DwyE;MAz1D1yEA,EAAE;QAAA,OAy1DkxE,uBAAmB;MAAA,EAAE;MAz1DzyEA,EAAE,+BAy1D61E;MAz1D/1EA,EAAE,gBAy1D83E;MAz1Dh4EA,EAAE,eAy1D04E;MAz1D54EA,EAAE,oCAy1D6gG;MAz1D/gGA,EAAE;QAAA,OAy1D84F,0BAAsB,OAAO,SAAS;MAAA,EAAE;QAAA,OAAiB,0BAAsB,OAAO,CAAC;MAAA,EAA/C;QAAA,OAAqE,mBAAe;MAAA,EAApF;MAz1Dx7FA,EAAE,wBAy1D4kG;MAz1D9kGA,EAAE,eAy1DulG;IAAA;IAAA;MAz1DzlGA,EAAE,gFAy1Dw1D;MAz1D11DA,EAAE,+EAy1D0wD;MAz1D5wDA,EAAE,cAy1DkzF;MAz1DpzFA,EAAE,+EAy1DkzF;MAz1DpzFA,EAAE,8EAy1DquF;IAAA;EAAA;EAAA,eAAy6MmB,EAAE,CAAC0W,SAAS,EAAwP7V,IAAI,CAAC8V,iBAAiB;EAAA;EAAA;AAAA,EAA0P;AAC5wT;EAAA,mDA11DwG9X,EAAE,mBA01DVse,SAAS,EAAc,CAAC;IAC5GjY,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAE+I,QAAQ,EAAE,yBAAyB;MAAE1B,MAAM,EAAE,CAAC,OAAO,CAAC;MAAE3E,IAAI,EAAE;QAC1F,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE;MACvC,CAAC;MAAEL,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEC,eAAe,EAAEpI,uBAAuB,CAACqI,OAAO;MAAEG,QAAQ,EAAE,krDAAkrD;MAAEC,MAAM,EAAE,CAAC,4/LAA4/L;IAAE,CAAC;EAC9yP,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9C,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEhE,EAAE,CAACmG,cAAc;MAAEhC,UAAU,EAAE,CAAC;QAC/FH,IAAI,EAAEhG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEgG,IAAI,EAAErG,EAAE,CAACkX;IAAO,CAAC,EAAE;MAAE7Q,IAAI,EAAErG,EAAE,CAACyI;IAAkB,CAAC,EAAE;MAAEpC,IAAI,EAAExC,IAAI,CAACoT;IAAc,CAAC,EAAE;MAAE5Q,IAAI,EAAEvC,EAAE,CAACqT;IAAS,CAAC,EAAE;MAAE9Q,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5IH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEqC,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAAC2R,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE9M,kBAAkB,EAAE,CAAC;MACjD9E,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEwb,WAAW,EAAE,CAAC;MACd1V,IAAI,EAAE9F,KAAK;MACX+F,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE4D,MAAM,EAAE,CAAC;MACT7D,IAAI,EAAEtF,eAAe;MACrBuF,IAAI,EAAE,CAACrG,UAAU,CAAC,MAAMse,UAAU,CAAC,EAAE;QAAEvG,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAE9C,iBAAiB,EAAE,CAAC;MACpB7O,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEwG,QAAQ,EAAE,CAAC;MACXlP,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEsH,aAAa,EAAE,CAAC;MAChBhQ,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEyI,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEuC,cAAc,EAAE,CAAC;MACjBjL,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8K,kBAAkB,EAAE,CAAC;MACrB/K,IAAI,EAAE1F,SAAS;MACf2F,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMiY,UAAU,SAASF,6BAA6B,CAAC;EACnD3Z,WAAW,CAAC8Z,SAAS,EAAE9V,UAAU,EAAE4U,mBAAmB,EAAEtI,QAAQ,EAAEyJ,YAAY,EAAE/G,aAAa,EAAE;IAC3F,KAAK,CAAC8G,SAAS,EAAE9V,UAAU,EAAE4U,mBAAmB,EAAEtI,QAAQ,EAAEyJ,YAAY,EAAE/G,aAAa,CAAC;IACxF,IAAI,CAAChH,UAAU,GAAG,IAAInO,OAAO,EAAE;IAC/Bic,SAAS,CAAC1C,mBAAmB,CAAC3W,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACyN,UAAU,CAAC,CAAC,CAACpL,SAAS,CAAC6F,kBAAkB,IAAI;MAC3F,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACArF,WAAW,GAAG;IACV,IAAI,CAAC4K,UAAU,CAAChC,IAAI,EAAE;IACtB,IAAI,CAACgC,UAAU,CAACzI,QAAQ,EAAE;IAC1B,KAAK,CAACnC,WAAW,EAAE;EACvB;AACJ;AACAyY,UAAU,CAACvY,IAAI;EAAA,iBAA6FuY,UAAU,EA55Ddve,EAAE,mBA45D8Bse,SAAS,GA55DzCte,EAAE,mBA45DoDA,EAAE,CAACuI,UAAU,GA55DnEvI,EAAE,mBA45D8EwB,yBAAyB,MA55DzGxB,EAAE,mBA45DoI,UAAU,GA55DhJA,EAAE,mBA45D4KkC,EAAE,CAACkc,YAAY,GA55D7Lpe,EAAE,mBA45DwMgE,qBAAqB;AAAA,CAA4D;AACnYua,UAAU,CAAC5V,IAAI,kBA75DyF3I,EAAE;EAAA,MA65DVue,UAAU;EAAA;EAAA;EAAA;EAAA;IAAA;MA75DFve,EAAE;QAAA,OA65DV,kBAAc;MAAA;QAAA,OAAd,0BAAsB;MAAA;IAAA;IAAA;MA75DdA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,wBA65D8vB;MA75DhwBA,EAAE,6BA65D67B;MA75D/7BA,EAAE,gBA65DogC;MA75DtgCA,EAAE,eA65D+gC;IAAA;IAAA;MA75DjhCA,EAAE,aA65Du2B;MA75Dz2BA,EAAE,6DA65Du2B;IAAA;EAAA;EAAA,eAA6nCmB,EAAE,CAAC0W,SAAS;EAAA;EAAA;EAAA;AAAA,EAA6T;AACv5E;EAAA,mDA95DwG7X,EAAE,mBA85DVue,UAAU,EAAc,CAAC;IAC7GlY,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAE+I,QAAQ,EAAE,YAAY;MAAE1B,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC;MAAE9E,eAAe,EAAEpI,uBAAuB,CAACge,MAAM;MAAE9V,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MAAEI,IAAI,EAAE;QAChO,OAAO,EAAE,kDAAkD;QAC3D,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAEC,QAAQ,EAAE,uUAAuU;MAAEC,MAAM,EAAE,CAAC,+4BAA+4B;IAAE,CAAC;EAC7vC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9C,IAAI,EAAEiY;IAAU,CAAC,EAAE;MAAEjY,IAAI,EAAErG,EAAE,CAACuI;IAAW,CAAC,EAAE;MAAElC,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5GH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAAC9E,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE6E,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEpF,SAAS;QACfqF,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAEnE,EAAE,CAACkc;IAAa,CAAC,EAAE;MAAE/X,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEhG;MACV,CAAC,EAAE;QACCgG,IAAI,EAAElG,MAAM;QACZmG,IAAI,EAAE,CAACtC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAM2a,cAAc,CAAC;EACjBja,WAAW,GAAG;IACV;IACA,IAAI,CAACmY,EAAE,GAAI,qBAAoBR,YAAY,EAAG,EAAC;EACnD;AACJ;AACAsC,cAAc,CAAC3Y,IAAI;EAAA,iBAA6F2Y,cAAc;AAAA,CAAmD;AACjLA,cAAc,CAAChW,IAAI,kBAt8DqF3I,EAAE;EAAA,MAs8DN2e,cAAc;EAAA;EAAA,oBAAqF,UAAU;EAAA;EAAA;IAAA;MAt8DzG3e,EAAE;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAs8D+S;IAAA;EAAA;EAAA;EAAA;AAAA,EAAkH;AAC3gB;EAAA,mDAv8DwGA,EAAE,mBAu8DV2e,cAAc,EAAc,CAAC;IACjHtY,IAAI,EAAE7F,SAAS;IACf8F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7B+I,QAAQ,EAAE,gBAAgB;MAC1BpG,QAAQ,EAAE,2BAA2B;MACrCD,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDL,aAAa,EAAEnI,iBAAiB,CAACoI,IAAI;MACrCC,eAAe,EAAEpI,uBAAuB,CAACge;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE7B,EAAE,EAAE,CAAC;MACnBxW,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqe,aAAa,CAAC;AAEpBA,aAAa,CAAC5Y,IAAI;EAAA,iBAA6F4Y,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBAp+DsF7e,EAAE;EAAA,MAo+DM4e;AAAa,EAuBvG;AACtBA,aAAa,CAACE,IAAI,kBA5/DsF9e,EAAE;EAAA,UA4/D+BD,YAAY,EAC7I0B,eAAe,EACfM,YAAY,EACZL,eAAe,EACfO,eAAe,EACfG,UAAU,EAAEX,eAAe;AAAA,EAAI;AACvC;EAAA,mDAlgEwGzB,EAAE,mBAkgEV4e,aAAa,EAAc,CAAC;IAChHvY,IAAI,EAAEnF,QAAQ;IACdoF,IAAI,EAAE,CAAC;MACCyY,OAAO,EAAE,CACLhf,YAAY,EACZ0B,eAAe,EACfM,YAAY,EACZL,eAAe,EACfO,eAAe,EACfG,UAAU,CACb;MACD4c,OAAO,EAAE,CACLvd,eAAe,EACf6H,aAAa,EACbM,WAAW,EACXwF,MAAM,EACNyM,WAAW,EACXyC,SAAS,EACTK,cAAc,EACdJ,UAAU,CACb;MACDU,YAAY,EAAE,CACV3V,aAAa,EACbM,WAAW,EACXwF,MAAM,EACNyM,WAAW,EACXyC,SAAS,EACTK,cAAc,EACdJ,UAAU;MACV;MACApY,UAAU,EACV1B,gBAAgB,EAChBkJ,kBAAkB,EAClBiK,YAAY;IAEpB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASjO,OAAO,EAAEsO,eAAe,EAAE5O,eAAe,EAAEyE,aAAa,EAAEpE,aAAa,EAAEO,SAAS,EAAE2F,qBAAqB,EAAER,MAAM,EAAEjJ,UAAU,EAAE1B,gBAAgB,EAAEsW,iBAAiB,EAAEzR,aAAa,EAAEuS,WAAW,EAAEjE,YAAY,EAAEhO,WAAW,EAAE+D,kBAAkB,EAAE4Q,UAAU,EAAED,SAAS,EAAEK,cAAc,EAAEC,aAAa,EAAE1R,uBAAuB,EAAEJ,+BAA+B,EAAEiB,WAAW,EAAErH,eAAe,EAAE0R,gBAAgB,EAAEhB,iBAAiB,EAAE9J,uBAAuB,EAAE2P,eAAe,EAAEX,cAAc,EAAElY,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}