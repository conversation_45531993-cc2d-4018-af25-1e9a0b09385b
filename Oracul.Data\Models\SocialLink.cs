using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Social link entity for profile social media connections
    /// </summary>
    public class SocialLink : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Platform { get; set; } = string.Empty; // linkedin, twitter, github, behance, dribbble, instagram, facebook, youtube, other

        [Required]
        [MaxLength(500)]
        public string Url { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? DisplayName { get; set; }

        public bool IsPublic { get; set; } = true;

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
    }
}
