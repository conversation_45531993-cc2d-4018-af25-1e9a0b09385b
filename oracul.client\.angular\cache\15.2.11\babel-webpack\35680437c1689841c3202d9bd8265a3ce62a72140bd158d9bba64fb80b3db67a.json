{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class OAuthService {\n  constructor() {\n    this.googleInitialized = false;\n    this.facebookInitialized = false;\n    // Configuration from environment\n    this.GOOGLE_CLIENT_ID = environment.oauth.google.clientId;\n    this.FACEBOOK_APP_ID = environment.oauth.facebook.appId;\n    this.initializeGoogle();\n    this.initializeFacebook();\n  }\n  initializeGoogle() {\n    if (typeof window !== 'undefined') {\n      // Load Google Sign-In script\n      const script = document.createElement('script');\n      script.src = 'https://accounts.google.com/gsi/client';\n      script.async = true;\n      script.defer = true;\n      script.onload = () => {\n        this.googleInitialized = true;\n      };\n      document.head.appendChild(script);\n    }\n  }\n  initializeFacebook() {\n    if (typeof window !== 'undefined') {\n      // Load Facebook SDK\n      window.fbAsyncInit = () => {\n        window.FB.init({\n          appId: this.FACEBOOK_APP_ID,\n          cookie: true,\n          xfbml: true,\n          version: 'v18.0'\n        });\n        this.facebookInitialized = true;\n      };\n      // Load Facebook SDK script\n      const script = document.createElement('script');\n      script.src = 'https://connect.facebook.net/en_US/sdk.js';\n      script.async = true;\n      script.defer = true;\n      document.head.appendChild(script);\n    }\n  }\n  signInWithGoogle() {\n    return new Observable(observer => {\n      if (!this.googleInitialized || !window.google) {\n        observer.error('Google Sign-In not initialized');\n        return;\n      }\n      window.google.accounts.id.initialize({\n        client_id: this.GOOGLE_CLIENT_ID,\n        callback: response => {\n          try {\n            // Decode JWT token to get user info\n            const payload = JSON.parse(atob(response.credential.split('.')[1]));\n            const user = {\n              provider: 'google',\n              id: payload.sub,\n              email: payload.email,\n              firstName: payload.given_name,\n              lastName: payload.family_name,\n              profilePictureUrl: payload.picture,\n              accessToken: response.credential\n            };\n            observer.next(user);\n            observer.complete();\n          } catch (error) {\n            observer.error('Failed to parse Google response');\n          }\n        }\n      });\n      window.google.accounts.id.prompt();\n    });\n  }\n  signInWithFacebook() {\n    return new Observable(observer => {\n      if (!this.facebookInitialized || !window.FB) {\n        observer.error('Facebook SDK not initialized');\n        return;\n      }\n      window.FB.login(response => {\n        if (response.authResponse) {\n          // Get user profile information\n          window.FB.api('/me', {\n            fields: 'name,email,first_name,last_name,picture'\n          }, userInfo => {\n            const user = {\n              provider: 'facebook',\n              id: userInfo.id,\n              email: userInfo.email,\n              firstName: userInfo.first_name,\n              lastName: userInfo.last_name,\n              profilePictureUrl: userInfo.picture?.data?.url,\n              accessToken: response.authResponse.accessToken\n            };\n            observer.next(user);\n            observer.complete();\n          });\n        } else {\n          observer.error('Facebook login cancelled or failed');\n        }\n      }, {\n        scope: 'email,public_profile'\n      });\n    });\n  }\n  // Alternative method using popup for Google\n  signInWithGooglePopup() {\n    return new Observable(observer => {\n      if (!this.googleInitialized || !window.google) {\n        observer.error('Google Sign-In not initialized');\n        return;\n      }\n      const client = window.google.accounts.oauth2.initTokenClient({\n        client_id: this.GOOGLE_CLIENT_ID,\n        scope: 'email profile',\n        callback: response => {\n          if (response.access_token) {\n            // Use the access token to get user profile\n            fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${response.access_token}`).then(res => res.json()).then(userInfo => {\n              const user = {\n                provider: 'google',\n                id: userInfo.id,\n                email: userInfo.email,\n                firstName: userInfo.given_name,\n                lastName: userInfo.family_name,\n                profilePictureUrl: userInfo.picture,\n                accessToken: response.access_token\n              };\n              observer.next(user);\n              observer.complete();\n            }).catch(error => observer.error('Failed to get user profile'));\n          } else {\n            observer.error('Google login failed');\n          }\n        }\n      });\n      client.requestAccessToken();\n    });\n  }\n  isGoogleReady() {\n    return this.googleInitialized && !!window.google;\n  }\n  isFacebookReady() {\n    return this.facebookInitialized && !!window.FB;\n  }\n  static {\n    this.ɵfac = function OAuthService_Factory(t) {\n      return new (t || OAuthService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OAuthService,\n      factory: OAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAASA,UAAU,QAAiB,MAAM;AAC1C,SAASC,WAAW,QAAQ,mCAAmC;;AAuB/D,OAAM,MAAOC,YAAY;EAQvBC;IAPQ,sBAAiB,GAAG,KAAK;IACzB,wBAAmB,GAAG,KAAK;IAEnC;IACiB,qBAAgB,GAAGF,WAAW,CAACG,KAAK,CAACC,MAAM,CAACC,QAAQ;IACpD,oBAAe,GAAGL,WAAW,CAACG,KAAK,CAACG,QAAQ,CAACC,KAAK;IAGjE,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQD,gBAAgB;IACtB,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;MACjC;MACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,wCAAwC;MACrDH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBJ,MAAM,CAACK,KAAK,GAAG,IAAI;MACnBL,MAAM,CAACM,MAAM,GAAG,MAAK;QACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC/B,CAAC;MACDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;;EAErC;EAEQF,kBAAkB;IACxB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MACjC;MACAA,MAAM,CAACW,WAAW,GAAG,MAAK;QACxBX,MAAM,CAACY,EAAE,CAACC,IAAI,CAAC;UACbhB,KAAK,EAAE,IAAI,CAACiB,eAAe;UAC3BC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,IAAI;UACXC,OAAO,EAAE;SACV,CAAC;QACF,IAAI,CAACC,mBAAmB,GAAG,IAAI;MACjC,CAAC;MAED;MACA,MAAMjB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,2CAA2C;MACxDH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBJ,MAAM,CAACK,KAAK,GAAG,IAAI;MACnBJ,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;;EAErC;EAEAkB,gBAAgB;IACd,OAAO,IAAI9B,UAAU,CAAC+B,QAAQ,IAAG;MAC/B,IAAI,CAAC,IAAI,CAACZ,iBAAiB,IAAI,CAACR,MAAM,CAACN,MAAM,EAAE;QAC7C0B,QAAQ,CAACC,KAAK,CAAC,gCAAgC,CAAC;QAChD;;MAGFrB,MAAM,CAACN,MAAM,CAAC4B,QAAQ,CAACC,EAAE,CAACC,UAAU,CAAC;QACnCC,SAAS,EAAE,IAAI,CAACC,gBAAgB;QAChCC,QAAQ,EAAGC,QAAa,IAAI;UAC1B,IAAI;YACF;YACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAACK,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnE,MAAMC,IAAI,GAAc;cACtBC,QAAQ,EAAE,QAAQ;cAClBb,EAAE,EAAEM,OAAO,CAACQ,GAAG;cACfC,KAAK,EAAET,OAAO,CAACS,KAAK;cACpBC,SAAS,EAAEV,OAAO,CAACW,UAAU;cAC7BC,QAAQ,EAAEZ,OAAO,CAACa,WAAW;cAC7BC,iBAAiB,EAAEd,OAAO,CAACe,OAAO;cAClCC,WAAW,EAAEjB,QAAQ,CAACK;aACvB;YAEDb,QAAQ,CAAC0B,IAAI,CAACX,IAAI,CAAC;YACnBf,QAAQ,CAAC2B,QAAQ,EAAE;WACpB,CAAC,OAAO1B,KAAK,EAAE;YACdD,QAAQ,CAACC,KAAK,CAAC,iCAAiC,CAAC;;QAErD;OACD,CAAC;MAEFrB,MAAM,CAACN,MAAM,CAAC4B,QAAQ,CAACC,EAAE,CAACyB,MAAM,EAAE;IACpC,CAAC,CAAC;EACJ;EAEAC,kBAAkB;IAChB,OAAO,IAAI5D,UAAU,CAAC+B,QAAQ,IAAG;MAC/B,IAAI,CAAC,IAAI,CAACF,mBAAmB,IAAI,CAAClB,MAAM,CAACY,EAAE,EAAE;QAC3CQ,QAAQ,CAACC,KAAK,CAAC,8BAA8B,CAAC;QAC9C;;MAGFrB,MAAM,CAACY,EAAE,CAACsC,KAAK,CAAEtB,QAAa,IAAI;QAChC,IAAIA,QAAQ,CAACuB,YAAY,EAAE;UACzB;UACAnD,MAAM,CAACY,EAAE,CAACwC,GAAG,CAAC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAyC,CAAE,EAAGC,QAAa,IAAI;YAC5F,MAAMnB,IAAI,GAAc;cACtBC,QAAQ,EAAE,UAAU;cACpBb,EAAE,EAAE+B,QAAQ,CAAC/B,EAAE;cACfe,KAAK,EAAEgB,QAAQ,CAAChB,KAAK;cACrBC,SAAS,EAAEe,QAAQ,CAACC,UAAU;cAC9Bd,QAAQ,EAAEa,QAAQ,CAACE,SAAS;cAC5Bb,iBAAiB,EAAEW,QAAQ,CAACV,OAAO,EAAEa,IAAI,EAAEC,GAAG;cAC9Cb,WAAW,EAAEjB,QAAQ,CAACuB,YAAY,CAACN;aACpC;YAEDzB,QAAQ,CAAC0B,IAAI,CAACX,IAAI,CAAC;YACnBf,QAAQ,CAAC2B,QAAQ,EAAE;UACrB,CAAC,CAAC;SACH,MAAM;UACL3B,QAAQ,CAACC,KAAK,CAAC,oCAAoC,CAAC;;MAExD,CAAC,EAAE;QAAEsC,KAAK,EAAE;MAAsB,CAAE,CAAC;IACvC,CAAC,CAAC;EACJ;EAEA;EACAC,qBAAqB;IACnB,OAAO,IAAIvE,UAAU,CAAC+B,QAAQ,IAAG;MAC/B,IAAI,CAAC,IAAI,CAACZ,iBAAiB,IAAI,CAACR,MAAM,CAACN,MAAM,EAAE;QAC7C0B,QAAQ,CAACC,KAAK,CAAC,gCAAgC,CAAC;QAChD;;MAGF,MAAMwC,MAAM,GAAG7D,MAAM,CAACN,MAAM,CAAC4B,QAAQ,CAACwC,MAAM,CAACC,eAAe,CAAC;QAC3DtC,SAAS,EAAE,IAAI,CAACC,gBAAgB;QAChCiC,KAAK,EAAE,eAAe;QACtBhC,QAAQ,EAAGC,QAAa,IAAI;UAC1B,IAAIA,QAAQ,CAACoC,YAAY,EAAE;YACzB;YACAC,KAAK,CAAC,8DAA8DrC,QAAQ,CAACoC,YAAY,EAAE,CAAC,CACzFE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,CAAC,CACvBF,IAAI,CAACZ,QAAQ,IAAG;cACf,MAAMnB,IAAI,GAAc;gBACtBC,QAAQ,EAAE,QAAQ;gBAClBb,EAAE,EAAE+B,QAAQ,CAAC/B,EAAE;gBACfe,KAAK,EAAEgB,QAAQ,CAAChB,KAAK;gBACrBC,SAAS,EAAEe,QAAQ,CAACd,UAAU;gBAC9BC,QAAQ,EAAEa,QAAQ,CAACZ,WAAW;gBAC9BC,iBAAiB,EAAEW,QAAQ,CAACV,OAAO;gBACnCC,WAAW,EAAEjB,QAAQ,CAACoC;eACvB;cAED5C,QAAQ,CAAC0B,IAAI,CAACX,IAAI,CAAC;cACnBf,QAAQ,CAAC2B,QAAQ,EAAE;YACrB,CAAC,CAAC,CACDsB,KAAK,CAAChD,KAAK,IAAID,QAAQ,CAACC,KAAK,CAAC,4BAA4B,CAAC,CAAC;WAChE,MAAM;YACLD,QAAQ,CAACC,KAAK,CAAC,qBAAqB,CAAC;;QAEzC;OACD,CAAC;MAEFwC,MAAM,CAACS,kBAAkB,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAC,aAAa;IACX,OAAO,IAAI,CAAC/D,iBAAiB,IAAI,CAAC,CAACR,MAAM,CAACN,MAAM;EAClD;EAEA8E,eAAe;IACb,OAAO,IAAI,CAACtD,mBAAmB,IAAI,CAAC,CAAClB,MAAM,CAACY,EAAE;EAChD;;;uBAnKWrB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAkF,SAAZlF,YAAY;MAAAmF,YAFX;IAAM;EAAA", "names": ["Observable", "environment", "OAuthService", "constructor", "o<PERSON>h", "google", "clientId", "facebook", "appId", "initializeGoogle", "initializeFacebook", "window", "script", "document", "createElement", "src", "async", "defer", "onload", "googleInitialized", "head", "append<PERSON><PERSON><PERSON>", "fbAsyncInit", "FB", "init", "FACEBOOK_APP_ID", "cookie", "xfbml", "version", "facebookInitialized", "signInWithGoogle", "observer", "error", "accounts", "id", "initialize", "client_id", "GOOGLE_CLIENT_ID", "callback", "response", "payload", "JSON", "parse", "atob", "credential", "split", "user", "provider", "sub", "email", "firstName", "given_name", "lastName", "family_name", "profilePictureUrl", "picture", "accessToken", "next", "complete", "prompt", "signInWithFacebook", "login", "authResponse", "api", "fields", "userInfo", "first_name", "last_name", "data", "url", "scope", "signInWithGooglePopup", "client", "oauth2", "initTokenClient", "access_token", "fetch", "then", "res", "json", "catch", "requestAccessToken", "isGoogleReady", "isFacebookReady", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\oauth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, Subject } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\n\r\ndeclare global {\r\n  interface Window {\r\n    google: any;\r\n    FB: any;\r\n    fbAsyncInit: () => void;\r\n  }\r\n}\r\n\r\nexport interface OAuthUser {\r\n  provider: 'google' | 'facebook';\r\n  id: string;\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  profilePictureUrl?: string;\r\n  accessToken: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OAuthService {\r\n  private googleInitialized = false;\r\n  private facebookInitialized = false;\r\n\r\n  // Configuration from environment\r\n  private readonly GOOGLE_CLIENT_ID = environment.oauth.google.clientId;\r\n  private readonly FACEBOOK_APP_ID = environment.oauth.facebook.appId;\r\n\r\n  constructor() {\r\n    this.initializeGoogle();\r\n    this.initializeFacebook();\r\n  }\r\n\r\n  private initializeGoogle(): void {\r\n    if (typeof window !== 'undefined') {\r\n      // Load Google Sign-In script\r\n      const script = document.createElement('script');\r\n      script.src = 'https://accounts.google.com/gsi/client';\r\n      script.async = true;\r\n      script.defer = true;\r\n      script.onload = () => {\r\n        this.googleInitialized = true;\r\n      };\r\n      document.head.appendChild(script);\r\n    }\r\n  }\r\n\r\n  private initializeFacebook(): void {\r\n    if (typeof window !== 'undefined') {\r\n      // Load Facebook SDK\r\n      window.fbAsyncInit = () => {\r\n        window.FB.init({\r\n          appId: this.FACEBOOK_APP_ID,\r\n          cookie: true,\r\n          xfbml: true,\r\n          version: 'v18.0'\r\n        });\r\n        this.facebookInitialized = true;\r\n      };\r\n\r\n      // Load Facebook SDK script\r\n      const script = document.createElement('script');\r\n      script.src = 'https://connect.facebook.net/en_US/sdk.js';\r\n      script.async = true;\r\n      script.defer = true;\r\n      document.head.appendChild(script);\r\n    }\r\n  }\r\n\r\n  signInWithGoogle(): Observable<OAuthUser> {\r\n    return new Observable(observer => {\r\n      if (!this.googleInitialized || !window.google) {\r\n        observer.error('Google Sign-In not initialized');\r\n        return;\r\n      }\r\n\r\n      window.google.accounts.id.initialize({\r\n        client_id: this.GOOGLE_CLIENT_ID,\r\n        callback: (response: any) => {\r\n          try {\r\n            // Decode JWT token to get user info\r\n            const payload = JSON.parse(atob(response.credential.split('.')[1]));\r\n\r\n            const user: OAuthUser = {\r\n              provider: 'google',\r\n              id: payload.sub,\r\n              email: payload.email,\r\n              firstName: payload.given_name,\r\n              lastName: payload.family_name,\r\n              profilePictureUrl: payload.picture,\r\n              accessToken: response.credential\r\n            };\r\n\r\n            observer.next(user);\r\n            observer.complete();\r\n          } catch (error) {\r\n            observer.error('Failed to parse Google response');\r\n          }\r\n        }\r\n      });\r\n\r\n      window.google.accounts.id.prompt();\r\n    });\r\n  }\r\n\r\n  signInWithFacebook(): Observable<OAuthUser> {\r\n    return new Observable(observer => {\r\n      if (!this.facebookInitialized || !window.FB) {\r\n        observer.error('Facebook SDK not initialized');\r\n        return;\r\n      }\r\n\r\n      window.FB.login((response: any) => {\r\n        if (response.authResponse) {\r\n          // Get user profile information\r\n          window.FB.api('/me', { fields: 'name,email,first_name,last_name,picture' }, (userInfo: any) => {\r\n            const user: OAuthUser = {\r\n              provider: 'facebook',\r\n              id: userInfo.id,\r\n              email: userInfo.email,\r\n              firstName: userInfo.first_name,\r\n              lastName: userInfo.last_name,\r\n              profilePictureUrl: userInfo.picture?.data?.url,\r\n              accessToken: response.authResponse.accessToken\r\n            };\r\n\r\n            observer.next(user);\r\n            observer.complete();\r\n          });\r\n        } else {\r\n          observer.error('Facebook login cancelled or failed');\r\n        }\r\n      }, { scope: 'email,public_profile' });\r\n    });\r\n  }\r\n\r\n  // Alternative method using popup for Google\r\n  signInWithGooglePopup(): Observable<OAuthUser> {\r\n    return new Observable(observer => {\r\n      if (!this.googleInitialized || !window.google) {\r\n        observer.error('Google Sign-In not initialized');\r\n        return;\r\n      }\r\n\r\n      const client = window.google.accounts.oauth2.initTokenClient({\r\n        client_id: this.GOOGLE_CLIENT_ID,\r\n        scope: 'email profile',\r\n        callback: (response: any) => {\r\n          if (response.access_token) {\r\n            // Use the access token to get user profile\r\n            fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${response.access_token}`)\r\n              .then(res => res.json())\r\n              .then(userInfo => {\r\n                const user: OAuthUser = {\r\n                  provider: 'google',\r\n                  id: userInfo.id,\r\n                  email: userInfo.email,\r\n                  firstName: userInfo.given_name,\r\n                  lastName: userInfo.family_name,\r\n                  profilePictureUrl: userInfo.picture,\r\n                  accessToken: response.access_token\r\n                };\r\n\r\n                observer.next(user);\r\n                observer.complete();\r\n              })\r\n              .catch(error => observer.error('Failed to get user profile'));\r\n          } else {\r\n            observer.error('Google login failed');\r\n          }\r\n        }\r\n      });\r\n\r\n      client.requestAccessToken();\r\n    });\r\n  }\r\n\r\n  isGoogleReady(): boolean {\r\n    return this.googleInitialized && !!window.google;\r\n  }\r\n\r\n  isFacebookReady(): boolean {\r\n    return this.facebookInitialized && !!window.FB;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}