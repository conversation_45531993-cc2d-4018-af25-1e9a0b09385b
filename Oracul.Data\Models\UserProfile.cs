using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// User profile entity for astrology-focused professional profiles
    /// </summary>
    public class UserProfile : BaseEntity
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(150)]
        public string Slug { get; set; } = string.Empty;

        public bool IsPublic { get; set; } = true;

        public int ProfileCompletionPercentage { get; set; } = 0;

        // Header Section
        [MaxLength(500)]
        public string? ProfilePhotoUrl { get; set; }

        [MaxLength(500)]
        public string? CoverPhotoUrl { get; set; }

        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? ProfessionalTitle { get; set; }

        [MaxLength(500)]
        public string? Headline { get; set; }

        // Professional Summary
        public string? Summary { get; set; }

        // Analytics
        public int ProfileViews { get; set; } = 0;

        public DateTime? LastViewedAt { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual ProfileLocation? Location { get; set; }
        public virtual ContactInformation? ContactInfo { get; set; }
        public virtual ICollection<ProfileSkill> Skills { get; set; } = new List<ProfileSkill>();
        public virtual ICollection<BlogPost> BlogPosts { get; set; } = new List<BlogPost>();
        public virtual ICollection<Achievement> Achievements { get; set; } = new List<Achievement>();
        public virtual ICollection<Certification> Certifications { get; set; } = new List<Certification>();
        public virtual ICollection<WorkExperience> Experiences { get; set; } = new List<WorkExperience>();
        public virtual ICollection<PortfolioItem> PortfolioItems { get; set; } = new List<PortfolioItem>();
        public virtual ICollection<SocialLink> SocialLinks { get; set; } = new List<SocialLink>();
    }
}
