using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Blog post tag entity for categorizing articles
    /// </summary>
    public class BlogPostTag : BaseEntity
    {
        [Required]
        public int BlogPostId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Tag { get; set; } = string.Empty;

        // Navigation properties
        public virtual BlogPost BlogPost { get; set; } = null!;
    }
}
