{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/oracle.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/chips\";\nimport * as i14 from \"@angular/material/tooltip\";\nfunction OracleListComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"mat-card\", 14)(2, \"mat-card-content\")(3, \"div\", 15);\n    i0.ɵɵelement(4, \"mat-spinner\", 16);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Loading oracles...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction OracleListComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"mat-card\", 18)(2, \"mat-card-content\")(3, \"div\", 19)(4, \"mat-icon\", 20);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function OracleListComponent_div_24_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.refresh());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Try Again \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction OracleListComponent_div_25_div_1_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" Try adjusting your search criteria or filters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_25_div_1_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" No oracles are currently registered in the system. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_25_div_1_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function OracleListComponent_div_25_div_1_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(3);\n      ctx_r11.searchTerm = \"\";\n      ctx_r11.showAvailableOnly = false;\n      return i0.ɵɵresetView(ctx_r11.applyFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 26)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"No oracles found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, OracleListComponent_div_25_div_1_p_8_Template, 2, 0, \"p\", 27);\n    i0.ɵɵtemplate(9, OracleListComponent_div_25_div_1_p_9_Template, 2, 0, \"p\", 27);\n    i0.ɵɵtemplate(10, OracleListComponent_div_25_div_1_button_10_Template, 2, 0, \"button\", 28);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.searchTerm || ctx_r6.showAvailableOnly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.searchTerm && !ctx_r6.showAvailableOnly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.searchTerm || ctx_r6.showAvailableOnly);\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 42);\n    i0.ɵɵlistener(\"error\", function OracleListComponent_div_25_mat_card_2_img_3_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const oracle_r13 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(oracle_r13.profilePictureUrl = null);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", oracle_r13.profilePictureUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r14.getFullName(oracle_r13));\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 43);\n    i0.ɵɵtext(1, \"person\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getExperienceText(oracle_r13), \" \");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", oracle_r13.about.length > 120 ? i0.ɵɵpipeBind3(2, 1, oracle_r13.about, 0, 120) + \"...\" : oracle_r13.about, \" \");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.formatPrice(oracle_r13.hourlyRate), \"/hour\");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_div_17_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const spec_r31 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", spec_r31, \" \");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_div_17_mat_chip_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", oracle_r13.specializations.length - 3, \" more \");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, OracleListComponent_div_25_mat_card_2_div_17_mat_chip_2_Template, 2, 1, \"mat-chip\", 48);\n    i0.ɵɵtemplate(3, OracleListComponent_div_25_mat_card_2_div_17_mat_chip_3_Template, 2, 1, \"mat-chip\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", oracle_r13.specializations.slice(0, 3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.specializations.length > 3);\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"room_service\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const oracle_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", oracle_r13.services.length, \" service\", oracle_r13.services.length !== 1 ? \"s\" : \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r20.getServiceCategories(oracle_r13).join(\", \"), \") \");\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 54)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_25_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 29);\n    i0.ɵɵlistener(\"click\", function OracleListComponent_div_25_mat_card_2_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const oracle_r13 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.viewProfile(oracle_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 30);\n    i0.ɵɵtemplate(3, OracleListComponent_div_25_mat_card_2_img_3_Template, 1, 2, \"img\", 31);\n    i0.ɵɵtemplate(4, OracleListComponent_div_25_mat_card_2_mat_icon_4_Template, 2, 0, \"mat-icon\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\")(8, \"div\", 33);\n    i0.ɵɵtemplate(9, OracleListComponent_div_25_mat_card_2_span_9_Template, 4, 1, \"span\", 34);\n    i0.ɵɵelementStart(10, \"span\", 35)(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\");\n    i0.ɵɵtemplate(15, OracleListComponent_div_25_mat_card_2_p_15_Template, 3, 5, \"p\", 36);\n    i0.ɵɵtemplate(16, OracleListComponent_div_25_mat_card_2_div_16_Template, 5, 1, \"div\", 37);\n    i0.ɵɵtemplate(17, OracleListComponent_div_25_mat_card_2_div_17_Template, 4, 2, \"div\", 38);\n    i0.ɵɵtemplate(18, OracleListComponent_div_25_mat_card_2_div_18_Template, 8, 3, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 40)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" View Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, OracleListComponent_div_25_mat_card_2_button_24_Template, 4, 0, \"button\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const oracle_r13 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.profilePictureUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !oracle_r13.profilePictureUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getFullName(oracle_r13));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.yearsOfExperience);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"available\", oracle_r13.isAvailable);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(oracle_r13.isAvailable ? \"check_circle\" : \"schedule\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", oracle_r13.isAvailable ? \"Available\" : \"Busy\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.about);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.hourlyRate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.specializations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.services.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", oracle_r13.email);\n  }\n}\nfunction OracleListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, OracleListComponent_div_25_div_1_Template, 11, 3, \"div\", 23);\n    i0.ɵɵtemplate(2, OracleListComponent_div_25_mat_card_2_Template, 25, 13, \"mat-card\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredOracles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredOracles);\n  }\n}\nfunction OracleListComponent_div_26_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" (filtered)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵtemplate(5, OracleListComponent_div_26_span_5_Template, 2, 0, \"span\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" Showing \", ctx_r3.filteredOracles.length, \" of \", ctx_r3.oracles.length, \" oracle\", ctx_r3.oracles.length !== 1 ? \"s\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.searchTerm || ctx_r3.showAvailableOnly);\n  }\n}\nexport class OracleListComponent {\n  constructor(router, oracleService, snackBar) {\n    this.router = router;\n    this.oracleService = oracleService;\n    this.snackBar = snackBar;\n    this.oracles = [];\n    this.filteredOracles = [];\n    this.loading = true;\n    this.error = null;\n    this.searchTerm = '';\n    this.showAvailableOnly = false;\n  }\n  ngOnInit() {\n    this.loadOracles();\n  }\n  loadOracles() {\n    this.loading = true;\n    this.error = null;\n    this.oracleService.getOracles().subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.oracles = response.data;\n          this.applyFilters();\n        } else {\n          this.error = response.message || 'Failed to load oracles';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading oracles:', error);\n        this.error = 'Failed to load oracles';\n        this.loading = false;\n        this.snackBar.open('Failed to load oracles', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  onAvailabilityFilterChange() {\n    this.applyFilters();\n  }\n  applyFilters() {\n    let filtered = [...this.oracles];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(oracle => oracle.firstName.toLowerCase().includes(searchLower) || oracle.lastName.toLowerCase().includes(searchLower) || oracle.specializations.some(spec => spec.toLowerCase().includes(searchLower)) || oracle.services.some(service => service.name.toLowerCase().includes(searchLower) || service.category.toLowerCase().includes(searchLower)));\n    }\n    // Apply availability filter\n    if (this.showAvailableOnly) {\n      filtered = filtered.filter(oracle => oracle.isAvailable);\n    }\n    this.filteredOracles = filtered;\n  }\n  viewProfile(oracle) {\n    this.router.navigate(['/oracle', oracle.id]);\n  }\n  getFullName(oracle) {\n    return `${oracle.firstName} ${oracle.lastName}`;\n  }\n  getExperienceText(oracle) {\n    if (!oracle.yearsOfExperience) return 'Experience not specified';\n    const years = oracle.yearsOfExperience;\n    return years === 1 ? '1 year' : `${years} years`;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  getServiceCategories(oracle) {\n    const categories = new Set(oracle.services.map(service => service.category));\n    return Array.from(categories);\n  }\n  refresh() {\n    this.loadOracles();\n  }\n  static {\n    this.ɵfac = function OracleListComponent_Factory(t) {\n      return new (t || OracleListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.OracleService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OracleListComponent,\n      selectors: [[\"app-oracle-list\"]],\n      decls: 27,\n      vars: 6,\n      consts: [[1, \"oracle-list-container\"], [1, \"header-card\"], [1, \"search-filters\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Name, specialization, or service\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [1, \"filter-controls\"], [3, \"ngModel\", \"ngModelChange\", \"change\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"oracle-grid\", 4, \"ngIf\"], [\"class\", \"results-summary\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-card\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-content\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"oracle-grid\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"oracle-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"no-results\"], [1, \"no-results-content\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [1, \"oracle-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"oracle-avatar\"], [3, \"src\", \"alt\", \"error\", 4, \"ngIf\"], [\"class\", \"default-avatar-icon\", 4, \"ngIf\"], [1, \"oracle-subtitle\"], [\"class\", \"experience\", 4, \"ngIf\"], [1, \"availability\"], [\"class\", \"about-preview\", 4, \"ngIf\"], [\"class\", \"hourly-rate\", 4, \"ngIf\"], [\"class\", \"specializations\", 4, \"ngIf\"], [\"class\", \"service-categories\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", 4, \"ngIf\"], [3, \"src\", \"alt\", \"error\"], [1, \"default-avatar-icon\"], [1, \"experience\"], [1, \"about-preview\"], [1, \"hourly-rate\"], [1, \"specializations\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-chip\", 4, \"ngIf\"], [1, \"more-chip\"], [1, \"service-categories\"], [1, \"service-info\"], [1, \"categories\"], [\"mat-button\", \"\"], [1, \"results-summary\"]],\n      template: function OracleListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Oracle Directory \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Find and connect with spiritual advisors and service providers \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 2)(11, \"mat-form-field\", 3)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Search oracles...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function OracleListComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function OracleListComponent_Template_input_input_14_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"mat-checkbox\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function OracleListComponent_Template_mat_checkbox_ngModelChange_18_listener($event) {\n            return ctx.showAvailableOnly = $event;\n          })(\"change\", function OracleListComponent_Template_mat_checkbox_change_18_listener() {\n            return ctx.onAvailabilityFilterChange();\n          });\n          i0.ɵɵtext(19, \" Available only \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function OracleListComponent_Template_button_click_20_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"refresh\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(23, OracleListComponent_div_23_Template, 7, 0, \"div\", 9);\n          i0.ɵɵtemplate(24, OracleListComponent_div_24_Template, 14, 1, \"div\", 10);\n          i0.ɵɵtemplate(25, OracleListComponent_div_25_Template, 3, 2, \"div\", 11);\n          i0.ɵɵtemplate(26, OracleListComponent_div_26_Template, 6, 4, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.showAvailableOnly);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredOracles.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardAvatar, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatProgressSpinner, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatInput, i12.MatCheckbox, i13.MatChip, i13.MatChipSet, i14.MatTooltip, i4.SlicePipe],\n      styles: [\".oracle-list-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n\\n.header-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.search-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: flex-end;\\n  flex-wrap: wrap;\\n}\\n\\n.search-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n}\\n\\n.filter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 40px 0;\\n}\\n\\n.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n  text-align: center;\\n  padding: 20px;\\n}\\n\\n.error-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n\\n.oracle-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 24px;\\n}\\n\\n.oracle-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.oracle-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-2px);\\n}\\n\\n\\n.oracle-avatar[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  background-color: #f5f5f5;\\n}\\n\\n.oracle-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.default-avatar-icon[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  font-size: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #999;\\n  background-color: #f0f0f0;\\n}\\n\\n\\n.oracle-subtitle[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.experience[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.experience[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6f00;\\n}\\n\\n.availability[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.85rem;\\n  color: #999;\\n}\\n\\n.availability.available[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n}\\n\\n.availability[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n\\n.about-preview[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.4;\\n  margin: 12px 0;\\n  font-size: 0.95rem;\\n}\\n\\n.hourly-rate[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #2e7d32;\\n  font-weight: 500;\\n  margin: 8px 0;\\n}\\n\\n.hourly-rate[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.specializations[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n}\\n\\n.specializations[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 6px;\\n}\\n\\n.specializations[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  height: 28px;\\n  background-color: #f0f0f0;\\n}\\n\\n.more-chip[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  color: #1976d2 !important;\\n}\\n\\n.service-categories[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n}\\n\\n.service-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.service-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #888;\\n}\\n\\n\\n.no-results[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.no-results-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n\\n.no-results-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #bbb;\\n}\\n\\n\\n.results-summary[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.results-summary[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n}\\n\\n.results-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .oracle-list-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .search-filters[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n\\n  .search-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n\\n  .filter-controls[_ngcontent-%COMP%] {\\n    justify-content: space-between;\\n  }\\n\\n  .oracle-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .oracle-list-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  .oracle-card[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n\\n  .oracle-subtitle[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n\\n  .about-preview[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;IAyCEA,+BAA+C;IAIvCA,kCAAyC;IACzCA,yBAAG;IAAAA,kCAAkB;IAAAA,iBAAI;;;;;;IAOjCA,+BAAuD;IAIxBA,qBAAK;IAAAA,iBAAW;IACvCA,0BAAI;IAAAA,qBAAK;IAAAA,iBAAK;IACdA,yBAAG;IAAAA,YAAW;IAAAA,iBAAI;IAClBA,mCAA8D;IAApBA;MAAAA;MAAA;MAAA,OAASA,+BAAS;IAAA,EAAC;IAC3DA,iCAAU;IAAAA,wBAAO;IAAAA,iBAAW;IAC5BA,4BACF;IAAAA,iBAAS;;;;IAJNA,eAAW;IAAXA,kCAAW;;;;;IAmBZA,yBAA2C;IACzCA,gEACF;IAAAA,iBAAI;;;;;IACJA,yBAA6C;IAC3CA,oEACF;IAAAA,iBAAI;;;;;;IACJA,kCACgD;IADNA;MAAAA;MAAA;MAAAC,qBAAsB,EAAE;MAAAA,4BAAsB,KAAK;MAAA,OAAED,qCAAc;IAAA,EAAC;IAE5GA,+BACF;IAAAA,iBAAS;;;;;IAfjBA,+BAA6D;IAI3CA,0BAAU;IAAAA,iBAAW;IAC/BA,0BAAI;IAAAA,gCAAgB;IAAAA,iBAAK;IACzBA,8EAEI;IACJA,8EAEI;IACJA,0FAGS;IACXA,iBAAM;;;;IAVAA,eAAqC;IAArCA,oEAAqC;IAGrCA,eAAuC;IAAvCA,sEAAuC;IAIlCA,eAAqC;IAArCA,oEAAqC;;;;;;IAYhDA,+BAG+C;IAA1CA;MAAAA;MAAA;MAAA,OAASA,8CAA2B,IAAI;IAAA,EAAC;IAH9CA,iBAG+C;;;;;IAF1CA,oEAAgC;;;;;IAGrCA,oCAAwE;IAAAA,sBAAM;IAAAA,iBAAW;;;;;IAKvFA,gCAA0D;IAC9CA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAO;;;;;IADLA,eACF;IADEA,sEACF;;;;;IAWJA,6BAA8C;IAC5CA,YACF;;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,0IACF;;;;;IAGAA,+BAAmD;IACvCA,4BAAY;IAAAA,iBAAW;IACjCA,4BAAM;IAAAA,YAAyC;IAAAA,iBAAO;;;;;IAAhDA,eAAyC;IAAzCA,8EAAyC;;;;;IAM7CA,gCAAkE;IAChEA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,yCACF;;;;;IACAA,oCAAsE;IACpEA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,4EACF;;;;;IAPJA,+BAAuE;IAEnEA,wGAEW;IACXA,wGAEW;IACbA,iBAAe;;;;IANcA,eAAqC;IAArCA,gEAAqC;IAGrDA,eAAuC;IAAvCA,4DAAuC;;;;;IAOtDA,+BAAmE;IAErDA,4BAAY;IAAAA,iBAAW;IACjCA,4BAAM;IAAAA,YAAiF;IAAAA,iBAAO;IAC9FA,gCAAyB;IACvBA,YACF;IAAAA,iBAAO;;;;;IAHDA,eAAiF;IAAjFA,kHAAiF;IAErFA,eACF;IADEA,sFACF;;;;;IAUJA,kCAAwC;IAC5BA,qBAAK;IAAAA,iBAAW;IAC1BA,yBACF;IAAAA,iBAAS;;;;;;IApEbA,oCAAmG;IAA9BA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChGA,uCAAiB;IAEbA,uFAG+C;IAC/CA,iGAAyF;IAC3FA,iBAAM;IACNA,sCAAgB;IAAAA,YAAyB;IAAAA,iBAAiB;IAC1DA,yCAAmB;IAEfA,yFAGO;IACPA,iCAAkE;IACtDA,aAAsD;IAAAA,iBAAW;IAC3EA,aACF;IAAAA,iBAAO;IAKbA,yCAAkB;IAEhBA,qFAEI;IAGJA,yFAGM;IAGNA,yFASM;IAGNA,yFAQM;IACRA,iBAAmB;IAEnBA,yCAAkB;IAEJA,2BAAU;IAAAA,iBAAW;IAC/BA,+BACF;IAAAA,iBAAS;IACTA,+FAGS;IACXA,iBAAmB;;;;;IAlETA,eAA8B;IAA9BA,mDAA8B;IAIzBA,eAA+B;IAA/BA,oDAA+B;IAE5BA,eAAyB;IAAzBA,oDAAyB;IAG9BA,eAA8B;IAA9BA,mDAA8B;IAIVA,eAAsC;IAAtCA,mDAAsC;IACrDA,eAAsD;IAAtDA,0EAAsD;IAChEA,eACF;IADEA,8EACF;IAOAA,eAAkB;IAAlBA,uCAAkB;IAKhBA,eAAuB;IAAvBA,4CAAuB;IAMvBA,eAAuC;IAAvCA,4DAAuC;IAYvCA,eAAgC;IAAhCA,qDAAgC;IAgBlBA,eAAkB;IAAlBA,uCAAkB;;;;;IAzF5CA,+BAAoD;IAElDA,6EAmBM;IAGNA,wFAsEW;IACbA,iBAAM;;;;IA7FEA,eAAkC;IAAlCA,0DAAkC;IAsBXA,eAAkB;IAAlBA,gDAAkB;;;;;IA+EzCA,4BAA8C;IAACA,2BAAU;IAAAA,iBAAO;;;;;IALxEA,+BAAsF;IAI9EA,YACA;IAAAA,8EAAgE;IAClEA,iBAAI;;;;IAFFA,eACA;IADAA,wJACA;IAAOA,eAAqC;IAArCA,oEAAqC;;;ACnKtD,OAAM,MAAOE,mBAAmB;EAQ9BC,YACUC,MAAc,EACdC,aAA4B,EAC5BC,QAAqB;IAFrB,WAAM,GAANF,MAAM;IACN,kBAAa,GAAbC,aAAa;IACb,aAAQ,GAARC,QAAQ;IAVlB,YAAO,GAAoB,EAAE;IAC7B,oBAAe,GAAoB,EAAE;IACrC,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;IAC3B,eAAU,GAAG,EAAE;IACf,sBAAiB,GAAG,KAAK;EAMrB;EAEJC,QAAQ;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQA,WAAW;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACL,aAAa,CAACM,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACC,OAAO,GAAGH,QAAQ,CAACE,IAAI;UAC5B,IAAI,CAACE,YAAY,EAAE;SACpB,MAAM;UACL,IAAI,CAACR,KAAK,GAAGI,QAAQ,CAACK,OAAO,IAAI,wBAAwB;;QAE3D,IAAI,CAACV,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfU,OAAO,CAACV,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,wBAAwB;QACrC,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,QAAQ,CAACe,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEAC,cAAc;IACZ,IAAI,CAACN,YAAY,EAAE;EACrB;EAEAO,0BAA0B;IACxB,IAAI,CAACP,YAAY,EAAE;EACrB;EAEAA,YAAY;IACV,IAAIQ,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACT,OAAO,CAAC;IAEhC;IACA,IAAI,IAAI,CAACU,UAAU,CAACC,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,WAAW,EAAE;MACjDJ,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAACC,MAAM,IAC/BA,MAAM,CAACC,SAAS,CAACH,WAAW,EAAE,CAACI,QAAQ,CAACL,WAAW,CAAC,IACpDG,MAAM,CAACG,QAAQ,CAACL,WAAW,EAAE,CAACI,QAAQ,CAACL,WAAW,CAAC,IACnDG,MAAM,CAACI,eAAe,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACR,WAAW,EAAE,CAACI,QAAQ,CAACL,WAAW,CAAC,CAAC,IAC7EG,MAAM,CAACO,QAAQ,CAACF,IAAI,CAACG,OAAO,IAC1BA,OAAO,CAACC,IAAI,CAACX,WAAW,EAAE,CAACI,QAAQ,CAACL,WAAW,CAAC,IAChDW,OAAO,CAACE,QAAQ,CAACZ,WAAW,EAAE,CAACI,QAAQ,CAACL,WAAW,CAAC,CACrD,CACF;;IAGH;IACA,IAAI,IAAI,CAACc,iBAAiB,EAAE;MAC1BjB,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACY,WAAW,CAAC;;IAG1D,IAAI,CAACC,eAAe,GAAGnB,QAAQ;EACjC;EAEAoB,WAAW,CAACd,MAAqB;IAC/B,IAAI,CAAC5B,MAAM,CAAC2C,QAAQ,CAAC,CAAC,SAAS,EAAEf,MAAM,CAACgB,EAAE,CAAC,CAAC;EAC9C;EAEAC,WAAW,CAACjB,MAAqB;IAC/B,OAAO,GAAGA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACG,QAAQ,EAAE;EACjD;EAEAe,iBAAiB,CAAClB,MAAqB;IACrC,IAAI,CAACA,MAAM,CAACmB,iBAAiB,EAAE,OAAO,0BAA0B;IAChE,MAAMC,KAAK,GAAGpB,MAAM,CAACmB,iBAAiB;IACtC,OAAOC,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAGA,KAAK,QAAQ;EAClD;EAEAC,WAAW,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EAEAM,oBAAoB,CAAC5B,MAAqB;IACxC,MAAM6B,UAAU,GAAG,IAAIC,GAAG,CAAC9B,MAAM,CAACO,QAAQ,CAACwB,GAAG,CAACvB,OAAO,IAAIA,OAAO,CAACE,QAAQ,CAAC,CAAC;IAC5E,OAAOsB,KAAK,CAACC,IAAI,CAACJ,UAAU,CAAC;EAC/B;EAEAK,OAAO;IACL,IAAI,CAAC1D,WAAW,EAAE;EACpB;;;uBAzGWN,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAiE;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDThCvE,8BAAmC;UAKjBA,0BAAU;UAAAA,iBAAW;UAC/BA,kCACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,gFACF;UAAAA,iBAAoB;UAEtBA,wCAAkB;UAIDA,kCAAiB;UAAAA,iBAAY;UACxCA,iCAGsD;UAF/CA;YAAA;UAAA,EAAwB;YAAA,OACfwE,oBAAgB;UAAA,EADD;UAD/BxE,iBAGsD;UACtDA,oCAAoB;UAAAA,uBAAM;UAAAA,iBAAW;UAGvCA,+BAA6B;UAEzBA;YAAA;UAAA,EAA+B;YAAA,OACrBwE,gCAA4B;UAAA,EADP;UAE/BxE,iCACF;UAAAA,iBAAe;UAEfA,kCAAiE;UAAzCA;YAAA,OAASwE,aAAS;UAAA,EAAC;UACzCxE,iCAAU;UAAAA,wBAAO;UAAAA,iBAAW;UAQtCA,sEASM;UAGNA,wEAcM;UAGNA,uEA+FM;UAGNA,uEASM;UACRA,iBAAM;;;UA/JWA,gBAAwB;UAAxBA,wCAAwB;UAQ7BA,eAA+B;UAA/BA,+CAA+B;UAcnCA,eAAa;UAAbA,kCAAa;UAYbA,eAAuB;UAAvBA,gDAAuB;UAiBvBA,eAAwB;UAAxBA,iDAAwB;UAkGxBA,eAAsD;UAAtDA,mFAAsD", "names": ["i0", "ctx_r11", "OracleListComponent", "constructor", "router", "oracleService", "snackBar", "ngOnInit", "loadOracles", "loading", "error", "<PERSON><PERSON><PERSON><PERSON>", "subscribe", "next", "response", "success", "data", "oracles", "applyFilters", "message", "console", "open", "duration", "panelClass", "onSearchChange", "onAvailabilityFilterChange", "filtered", "searchTerm", "trim", "searchLower", "toLowerCase", "filter", "oracle", "firstName", "includes", "lastName", "specializations", "some", "spec", "services", "service", "name", "category", "showAvailableOnly", "isAvailable", "filteredOracles", "viewProfile", "navigate", "id", "getFullName", "getExperienceText", "yearsOfExperience", "years", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "getServiceCategories", "categories", "Set", "map", "Array", "from", "refresh", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\oracle-list\\oracle-list.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\oracle-list\\oracle-list.component.ts"], "sourcesContent": ["<!-- Oracle List Page -->\n<div class=\"oracle-list-container\">\n  <!-- Header -->\n  <mat-card class=\"header-card\">\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>psychology</mat-icon>\n        Oracle Directory\n      </mat-card-title>\n      <mat-card-subtitle>\n        Find and connect with spiritual advisors and service providers\n      </mat-card-subtitle>\n    </mat-card-header>\n    <mat-card-content>\n      <!-- Search and Filters -->\n      <div class=\"search-filters\">\n        <mat-form-field appearance=\"outline\" class=\"search-field\">\n          <mat-label>Search oracles...</mat-label>\n          <input matInput \n                 [(ngModel)]=\"searchTerm\" \n                 (input)=\"onSearchChange()\"\n                 placeholder=\"Name, specialization, or service\">\n          <mat-icon matSuffix>search</mat-icon>\n        </mat-form-field>\n        \n        <div class=\"filter-controls\">\n          <mat-checkbox \n            [(ngModel)]=\"showAvailableOnly\" \n            (change)=\"onAvailabilityFilterChange()\">\n            Available only\n          </mat-checkbox>\n          \n          <button mat-icon-button (click)=\"refresh()\" matTooltip=\"Refresh\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-card class=\"loading-card\">\n      <mat-card-content>\n        <div class=\"loading-content\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading oracles...</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\n    <mat-card class=\"error-card\">\n      <mat-card-content>\n        <div class=\"error-content\">\n          <mat-icon color=\"warn\">error</mat-icon>\n          <h3>Error</h3>\n          <p>{{ error }}</p>\n          <button mat-raised-button color=\"primary\" (click)=\"refresh()\">\n            <mat-icon>refresh</mat-icon>\n            Try Again\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Oracle Grid -->\n  <div *ngIf=\"!loading && !error\" class=\"oracle-grid\">\n    <!-- No Results -->\n    <div *ngIf=\"filteredOracles.length === 0\" class=\"no-results\">\n      <mat-card>\n        <mat-card-content>\n          <div class=\"no-results-content\">\n            <mat-icon>search_off</mat-icon>\n            <h3>No oracles found</h3>\n            <p *ngIf=\"searchTerm || showAvailableOnly\">\n              Try adjusting your search criteria or filters.\n            </p>\n            <p *ngIf=\"!searchTerm && !showAvailableOnly\">\n              No oracles are currently registered in the system.\n            </p>\n            <button mat-raised-button color=\"primary\" (click)=\"searchTerm = ''; showAvailableOnly = false; applyFilters()\" \n                    *ngIf=\"searchTerm || showAvailableOnly\">\n              Clear Filters\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Oracle Cards -->\n    <mat-card *ngFor=\"let oracle of filteredOracles\" class=\"oracle-card\" (click)=\"viewProfile(oracle)\">\n      <mat-card-header>\n        <div mat-card-avatar class=\"oracle-avatar\">\n          <img *ngIf=\"oracle.profilePictureUrl\"\n               [src]=\"oracle.profilePictureUrl\"\n               [alt]=\"getFullName(oracle)\"\n               (error)=\"oracle.profilePictureUrl = null\">\n          <mat-icon *ngIf=\"!oracle.profilePictureUrl\" class=\"default-avatar-icon\">person</mat-icon>\n        </div>\n        <mat-card-title>{{ getFullName(oracle) }}</mat-card-title>\n        <mat-card-subtitle>\n          <div class=\"oracle-subtitle\">\n            <span *ngIf=\"oracle.yearsOfExperience\" class=\"experience\">\n              <mat-icon>star</mat-icon>\n              {{ getExperienceText(oracle) }}\n            </span>\n            <span class=\"availability\" [class.available]=\"oracle.isAvailable\">\n              <mat-icon>{{ oracle.isAvailable ? 'check_circle' : 'schedule' }}</mat-icon>\n              {{ oracle.isAvailable ? 'Available' : 'Busy' }}\n            </span>\n          </div>\n        </mat-card-subtitle>\n      </mat-card-header>\n      \n      <mat-card-content>\n        <!-- About (truncated) -->\n        <p *ngIf=\"oracle.about\" class=\"about-preview\">\n          {{ oracle.about.length > 120 ? (oracle.about | slice:0:120) + '...' : oracle.about }}\n        </p>\n        \n        <!-- Hourly Rate -->\n        <div *ngIf=\"oracle.hourlyRate\" class=\"hourly-rate\">\n          <mat-icon>attach_money</mat-icon>\n          <span>{{ formatPrice(oracle.hourlyRate) }}/hour</span>\n        </div>\n        \n        <!-- Specializations -->\n        <div *ngIf=\"oracle.specializations.length > 0\" class=\"specializations\">\n          <mat-chip-set>\n            <mat-chip *ngFor=\"let spec of oracle.specializations.slice(0, 3)\">\n              {{ spec }}\n            </mat-chip>\n            <mat-chip *ngIf=\"oracle.specializations.length > 3\" class=\"more-chip\">\n              +{{ oracle.specializations.length - 3 }} more\n            </mat-chip>\n          </mat-chip-set>\n        </div>\n        \n        <!-- Service Categories -->\n        <div *ngIf=\"oracle.services.length > 0\" class=\"service-categories\">\n          <div class=\"service-info\">\n            <mat-icon>room_service</mat-icon>\n            <span>{{ oracle.services.length }} service{{ oracle.services.length !== 1 ? 's' : '' }}</span>\n            <span class=\"categories\">\n              ({{ getServiceCategories(oracle).join(', ') }})\n            </span>\n          </div>\n        </div>\n      </mat-card-content>\n      \n      <mat-card-actions>\n        <button mat-button color=\"primary\">\n          <mat-icon>visibility</mat-icon>\n          View Profile\n        </button>\n        <button mat-button *ngIf=\"oracle.email\">\n          <mat-icon>email</mat-icon>\n          Contact\n        </button>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n\n  <!-- Results Summary -->\n  <div *ngIf=\"!loading && !error && filteredOracles.length > 0\" class=\"results-summary\">\n    <mat-card>\n      <mat-card-content>\n        <p>\n          Showing {{ filteredOracles.length }} of {{ oracles.length }} oracle{{ oracles.length !== 1 ? 's' : '' }}\n          <span *ngIf=\"searchTerm || showAvailableOnly\"> (filtered)</span>\n        </p>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { OracleService, OracleProfile } from '../services/oracle.service';\n\n@Component({\n  selector: 'app-oracle-list',\n  templateUrl: './oracle-list.component.html',\n  styleUrls: ['./oracle-list.component.css']\n})\nexport class OracleListComponent implements OnInit {\n  oracles: OracleProfile[] = [];\n  filteredOracles: OracleProfile[] = [];\n  loading = true;\n  error: string | null = null;\n  searchTerm = '';\n  showAvailableOnly = false;\n\n  constructor(\n    private router: Router,\n    private oracleService: OracleService,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    this.loadOracles();\n  }\n\n  private loadOracles(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.oracleService.getOracles().subscribe({\n      next: (response) => {\n        if (response.success && response.data) {\n          this.oracles = response.data;\n          this.applyFilters();\n        } else {\n          this.error = response.message || 'Failed to load oracles';\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading oracles:', error);\n        this.error = 'Failed to load oracles';\n        this.loading = false;\n        this.snackBar.open('Failed to load oracles', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  onAvailabilityFilterChange(): void {\n    this.applyFilters();\n  }\n\n  applyFilters(): void {\n    let filtered = [...this.oracles];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(oracle =>\n        oracle.firstName.toLowerCase().includes(searchLower) ||\n        oracle.lastName.toLowerCase().includes(searchLower) ||\n        oracle.specializations.some(spec => spec.toLowerCase().includes(searchLower)) ||\n        oracle.services.some(service => \n          service.name.toLowerCase().includes(searchLower) ||\n          service.category.toLowerCase().includes(searchLower)\n        )\n      );\n    }\n\n    // Apply availability filter\n    if (this.showAvailableOnly) {\n      filtered = filtered.filter(oracle => oracle.isAvailable);\n    }\n\n    this.filteredOracles = filtered;\n  }\n\n  viewProfile(oracle: OracleProfile): void {\n    this.router.navigate(['/oracle', oracle.id]);\n  }\n\n  getFullName(oracle: OracleProfile): string {\n    return `${oracle.firstName} ${oracle.lastName}`;\n  }\n\n  getExperienceText(oracle: OracleProfile): string {\n    if (!oracle.yearsOfExperience) return 'Experience not specified';\n    const years = oracle.yearsOfExperience;\n    return years === 1 ? '1 year' : `${years} years`;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  getServiceCategories(oracle: OracleProfile): string[] {\n    const categories = new Set(oracle.services.map(service => service.category));\n    return Array.from(categories);\n  }\n\n  refresh(): void {\n    this.loadOracles();\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}