{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ServiceService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/api`;\n  }\n  /**\r\n   * Get all active services\r\n   */\n  getActiveServices() {\n    return this.http.get(`${this.API_URL}/oracle/services`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get services by category\r\n   */\n  getServicesByCategory(category) {\n    return this.http.get(`${this.API_URL}/services/category/${category}`).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    console.error('Service service error:', error);\n    return throwError(() => error);\n  }\n  static {\n    this.ɵfac = function ServiceService_Factory(t) {\n      return new (t || ServiceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceService,\n      factory: ServiceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;;;AAsB5D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFP,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,MAAM;EAEd;EAExC;;;EAGAC,iBAAiB;IACf,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAyB,GAAG,IAAI,CAACC,OAAO,kBAAkB,CAAC,CAC5EC,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAC,qBAAqB,CAACC,QAAgB;IACpC,OAAO,IAAI,CAACR,IAAI,CAACG,GAAG,CAAY,GAAG,IAAI,CAACC,OAAO,sBAAsBI,QAAQ,EAAE,CAAC,CAC7EH,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEQA,WAAW,CAACG,KAAU;IAC5BC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAOd,UAAU,CAAC,MAAMc,KAAK,CAAC;EAChC;;;uBAxBWX,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAAa,SAAdb,cAAc;MAAAc,YAFb;IAAM;EAAA", "names": ["throwError", "catchError", "environment", "ServiceService", "constructor", "http", "apiUrl", "getActiveServices", "get", "API_URL", "pipe", "handleError", "getServicesByCategory", "category", "error", "console", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\services\\service.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface Service {\n  id: number;\n  name: string;\n  description?: string;\n  price: number;\n  durationMinutes: number;\n  category: string;\n  isActive: boolean;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  message: string;\n  data?: T;\n  errors: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ServiceService {\n  private readonly API_URL = `${environment.apiUrl}/api`;\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Get all active services\n   */\n  getActiveServices(): Observable<ApiResponse<Service[]>> {\n    return this.http.get<ApiResponse<Service[]>>(`${this.API_URL}/oracle/services`)\n      .pipe(catchError(this.handleError));\n  }\n\n  /**\n   * Get services by category\n   */\n  getServicesByCategory(category: string): Observable<Service[]> {\n    return this.http.get<Service[]>(`${this.API_URL}/services/category/${category}`)\n      .pipe(catchError(this.handleError));\n  }\n\n  private handleError(error: any): Observable<never> {\n    console.error('Service service error:', error);\n    return throwError(() => error);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}