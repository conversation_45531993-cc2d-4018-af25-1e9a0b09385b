using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Service for seeding astrology-focused profile data
    /// </summary>
    public class ProfileSeedService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProfileSeedService> _logger;

        public ProfileSeedService(IUnitOfWork unitOfWork, ILogger<ProfileSeedService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Seed the database with 5 diverse oracle/astrology profiles
        /// </summary>
        public async Task SeedProfileDataAsync()
        {
            try
            {
                // Check if profiles already exist
                var existingProfiles = await _unitOfWork.Profiles.GetAllAsync();
                if (existingProfiles.Count() >= 5)
                {
                    _logger.LogInformation("Профилните данни вече са заредени в базата данни");
                    return;
                }

                // Clear existing profiles if less than 5
                if (existingProfiles.Any())
                {
                    foreach (var existingProfile in existingProfiles)
                    {
                        _unitOfWork.Profiles.SoftDelete(existingProfile);
                    }
                    await _unitOfWork.SaveChangesAsync();
                }

                // Create 5 diverse oracle profiles
                await CreateOracleProfilesAsync();

                _logger.LogInformation("5 различни оракулски профила са заредени успешно в базата данни");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профилните данни");
                throw;
            }
        }

        /// <summary>
        /// Create 5 diverse oracle profiles with different specialties
        /// </summary>
        private async Task CreateOracleProfilesAsync()
        {
            // Skip Luna Starweaver since she already exists, create only the new oracle profiles
            var oracleProfiles = new[]
            {
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Sage",
                    LastName = "Moonchild",
                    Username = "sage-moonchild",
                    Slug = "sage-moonchild",
                    Title = "Crystal Healer & Energy Worker",
                    Headline = "Healing through the power of crystals and energy work. Transforming lives with ancient crystal wisdom and modern energy techniques.",
                    Summary = "Master crystal healer and energy worker with 15 years of experience in vibrational healing. Specializing in chakra balancing, crystal therapy, Reiki healing, and energy clearing. I help clients release blockages and align with their highest potential through the sacred power of crystals.",
                    Location = new { City = "Santa Fe", State = "NM", Country = "USA", Display = "Santa Fe, NM" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=300&fit=crop",
                    Website = "https://crystalmoonhealing.com",
                    Phone = "+1 (505) 555-MOON",
                    Address = "888 Sacred Mountain Way",
                    PostalCode = "87501",
                    Skills = new[] { "Crystal Healing", "Energy Work", "Chakra Balancing", "Reiki Healing", "Meditation", "Aura Cleansing", "Sacred Geometry", "Sound Healing" },
                    Specialty = "Crystal Healing"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "River",
                    LastName = "Palmistry",
                    Username = "river-palmistry",
                    Slug = "river-palmistry",
                    Title = "Third-Generation Palm Reader & Life Path Guide",
                    Headline = "Reading the stories written in your hands. Revealing your life's journey through the ancient art of palmistry passed down through generations.",
                    Summary = "Third-generation palm reader with an inherited gift for seeing life's path through the lines of your hands. With 18 years of practice, I provide insights into personality, relationships, career potential, and future possibilities through detailed palm analysis.",
                    Location = new { City = "New Orleans", State = "LA", Country = "USA", Display = "New Orleans, LA" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=300&fit=crop",
                    Website = "https://riverpalmistry.com",
                    Phone = "+1 (504) 555-PALM",
                    Address = "123 French Quarter Lane",
                    PostalCode = "70116",
                    Skills = new[] { "Palm Reading", "Life Path Analysis", "Personality Reading", "Relationship Compatibility", "Career Guidance", "Hand Analysis", "Chiromancy", "Fortune Telling" },
                    Specialty = "Palmistry"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Aurora",
                    LastName = "Wisdom",
                    Username = "aurora-wisdom",
                    Slug = "aurora-wisdom",
                    Title = "Spiritual Counselor & Intuitive Life Coach",
                    Headline = "Guiding souls toward their highest potential. Combining spiritual wisdom with practical life coaching to help you manifest your dreams.",
                    Summary = "Intuitive spiritual counselor and life coach with 14 years of experience helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life's journeys, combining spiritual insights with practical coaching techniques.",
                    Location = new { City = "Portland", State = "OR", Country = "USA", Display = "Portland, OR" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=800&h=300&fit=crop",
                    Website = "https://aurorawisdom.com",
                    Phone = "+1 (503) 555-WISE",
                    Address = "456 Spiritual Heights Blvd",
                    PostalCode = "97205",
                    Skills = new[] { "Spiritual Counseling", "Life Coaching", "Purpose Discovery", "Intuitive Reading", "Manifestation", "Mindfulness", "Meditation Guidance", "Soul Healing" },
                    Specialty = "Spiritual Counseling"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Cosmic",
                    LastName = "Dawn",
                    Username = "cosmic-dawn",
                    Slug = "cosmic-dawn",
                    Title = "Numerologist & Sacred Geometry Expert",
                    Headline = "Discovering your life's blueprint through numbers. Revealing the hidden patterns and meanings in your life through numerology and sacred geometry.",
                    Summary = "Master numerologist and sacred geometry expert with 16 years of experience revealing the hidden patterns and meanings in your life through the power of numbers. I help clients understand their life path, soul purpose, and divine timing through detailed numerological analysis.",
                    Location = new { City = "Boulder", State = "CO", Country = "USA", Display = "Boulder, CO" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=300&fit=crop",
                    Website = "https://cosmicdawnnumerology.com",
                    Phone = "+1 (303) 555-NUMS",
                    Address = "789 Sacred Numbers Ave",
                    PostalCode = "80302",
                    Skills = new[] { "Numerology", "Sacred Geometry", "Life Path Numbers", "Name Analysis", "Birth Date Reading", "Compatibility Numbers", "Business Numerology", "Pythagorean System" },
                    Specialty = "Numerology"
                }
            };

            foreach (var oracleData in oracleProfiles)
            {
                await CreateSingleOracleProfileAsync(oracleData);
            }
        }

        /// <summary>
        /// Create a single oracle profile with all related data
        /// </summary>
        private async Task CreateSingleOracleProfileAsync(dynamic oracleData)
        {
            // Check if user already exists
            var existingUser = await _unitOfWork.Users.GetByEmailAsync(oracleData.Email);
            User user;

            if (existingUser == null)
            {
                // Create new user
                user = new User
                {
                    FirstName = oracleData.FirstName,
                    LastName = oracleData.LastName,
                    Email = oracleData.Email,
                    PasswordHash = "$2a$11$example.hash.for.testing.purposes.only",
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow
                };
                _unitOfWork.Users.Add(user);
                await _unitOfWork.SaveChangesAsync();
            }
            else
            {
                user = existingUser;
            }

            // Check if profile already exists for this user
            var existingProfile = await _unitOfWork.Profiles.GetByUserIdAsync(user.Id);
            if (existingProfile != null)
            {
                _logger.LogInformation($"Профилът за {oracleData.FirstName} {oracleData.LastName} вече съществува");
                return;
            }

            // Create profile
            var profile = new UserProfile
            {
                UserId = user.Id,
                Username = oracleData.Username,
                Slug = oracleData.Slug,
                IsPublic = true,
                ProfileCompletionPercentage = 90,
                ProfilePhotoUrl = oracleData.ProfilePhoto,
                CoverPhotoUrl = oracleData.CoverPhoto,
                FirstName = oracleData.FirstName,
                LastName = oracleData.LastName,
                ProfessionalTitle = oracleData.Title,
                Headline = oracleData.Headline,
                Summary = oracleData.Summary,
                ProfileViews = Random.Shared.Next(500, 4000),
                LastViewedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 30)),
                CreatedAt = DateTime.UtcNow.AddYears(-Random.Shared.Next(1, 3))
            };
            _unitOfWork.Profiles.Add(profile);
            await _unitOfWork.SaveChangesAsync();

            // Add location
            var location = new ProfileLocation
            {
                UserProfileId = profile.Id,
                City = oracleData.Location.City,
                State = oracleData.Location.State,
                Country = oracleData.Location.Country,
                DisplayLocation = oracleData.Location.Display
            };
            _unitOfWork.Profiles.GetDbContext().Set<ProfileLocation>().Add(location);

            // Add contact information
            var contactInfo = new ContactInformation
            {
                UserProfileId = profile.Id,
                Email = oracleData.Email,
                IsEmailPublic = true,
                Website = oracleData.Website,
                PortfolioUrl = oracleData.Website + "/portfolio"
            };
            _unitOfWork.Profiles.GetDbContext().Set<ContactInformation>().Add(contactInfo);
            await _unitOfWork.SaveChangesAsync();

            // Add business address
            var businessAddress = new BusinessAddress
            {
                ContactInformationId = contactInfo.Id,
                Street = oracleData.Address,
                City = oracleData.Location.City,
                State = oracleData.Location.State,
                PostalCode = oracleData.PostalCode,
                Country = oracleData.Location.Country,
                IsPublic = true
            };
            _unitOfWork.Profiles.GetDbContext().Set<BusinessAddress>().Add(businessAddress);

            // Add phone number
            var phoneNumber = new PhoneNumber
            {
                ContactInformationId = contactInfo.Id,
                Number = oracleData.Phone,
                Type = "business",
                IsPublic = true,
                IsPrimary = true
            };
            _unitOfWork.Profiles.GetDbContext().Set<PhoneNumber>().Add(phoneNumber);

            // Add skills
            foreach (var skillName in oracleData.Skills)
            {
                var skill = new ProfileSkill
                {
                    UserProfileId = profile.Id,
                    Name = skillName,
                    Category = oracleData.Specialty,
                    Endorsements = Random.Shared.Next(20, 100),
                    ProficiencyLevel = Random.Shared.Next(1, 4) switch
                    {
                        1 => "beginner",
                        2 => "intermediate",
                        3 => "advanced",
                        _ => "expert"
                    }
                };
                _unitOfWork.Profiles.GetDbContext().Set<ProfileSkill>().Add(skill);
            }

            // Add a blog post
            var blogPost = new BlogPost
            {
                UserProfileId = profile.Id,
                Title = GetBlogTitleForSpecialty(oracleData.Specialty),
                Excerpt = GetBlogExcerptForSpecialty(oracleData.Specialty),
                Content = GetBlogContentForSpecialty(oracleData.Specialty),
                PublishedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 90)),
                ReadCount = Random.Shared.Next(100, 3000),
                Slug = GetBlogSlugForSpecialty(oracleData.Specialty),
                FeaturedImageUrl = GetBlogImageForSpecialty(oracleData.Specialty)
            };
            _unitOfWork.Profiles.GetDbContext().Set<BlogPost>().Add(blogPost);
            await _unitOfWork.SaveChangesAsync();

            // Add certifications
            var certifications = GetCertificationsForSpecialty(oracleData.Specialty, profile.Id);
            foreach (var certification in certifications)
            {
                _unitOfWork.Profiles.GetDbContext().Set<Certification>().Add(certification);
            }

            // Add work experience
            var experience = new WorkExperience
            {
                UserProfileId = profile.Id,
                Company = GetCompanyNameForSpecialty(oracleData.Specialty, oracleData.FirstName),
                Position = "Founder & Lead " + oracleData.Specialty + " Practitioner",
                StartDate = DateTime.UtcNow.AddYears(-Random.Shared.Next(5, 15)),
                IsCurrent = true,
                Description = GetExperienceDescriptionForSpecialty(oracleData.Specialty),
                Location = oracleData.Location.Display,
                CompanyLogoUrl = "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=48&h=48&fit=crop"
            };
            _unitOfWork.Profiles.GetDbContext().Set<WorkExperience>().Add(experience);
            await _unitOfWork.SaveChangesAsync();

            // Add social links
            var socialLinks = new[]
            {
                new SocialLink { UserProfileId = profile.Id, Platform = "instagram", Url = $"https://instagram.com/{oracleData.Username.Replace("-", "_")}", DisplayName = $"@{oracleData.Username.Replace("-", "_")}", IsPublic = true },
                new SocialLink { UserProfileId = profile.Id, Platform = "youtube", Url = $"https://youtube.com/c/{oracleData.FirstName}{oracleData.Specialty}", DisplayName = $"{oracleData.FirstName} {oracleData.Specialty}", IsPublic = true },
                new SocialLink { UserProfileId = profile.Id, Platform = "facebook", Url = $"https://facebook.com/{oracleData.FirstName}{oracleData.Specialty}Studio", DisplayName = $"{oracleData.FirstName} {oracleData.Specialty} Studio", IsPublic = true }
            };

            foreach (var socialLink in socialLinks)
            {
                _unitOfWork.Profiles.GetDbContext().Set<SocialLink>().Add(socialLink);
            }

            await _unitOfWork.SaveChangesAsync();

            // Update profile completion percentage
            await _unitOfWork.Profiles.UpdateProfileCompletionPercentageAsync(profile.Id);
        }

        #region Helper Methods for Specialty-Specific Content

        private string GetBlogTitleForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Mercury Retrograde: Navigating Communication Challenges",
                "Crystal Healing" => "The Power of Amethyst: Healing Your Third Eye Chakra",
                "Palmistry" => "Reading Life Lines: Understanding Your Destiny Through Your Hands",
                "Spiritual Counseling" => "Finding Your Soul Purpose: A Guide to Spiritual Awakening",
                "Numerology" => "Your Life Path Number: Unlocking Your Divine Blueprint",
                _ => "Spiritual Wisdom for Modern Times"
            };
        }

        private string GetBlogExcerptForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Discover how to harness the transformative energy of Mercury retrograde periods and turn apparent setbacks into opportunities for growth.",
                "Crystal Healing" => "Learn how amethyst can enhance your intuition and open your third eye chakra for deeper spiritual insights.",
                "Palmistry" => "Explore the ancient art of palm reading and discover what your life lines reveal about your destiny and life path.",
                "Spiritual Counseling" => "A comprehensive guide to discovering your soul's purpose and aligning with your highest spiritual potential.",
                "Numerology" => "Understand the significance of your life path number and how it influences your personality, relationships, and life journey.",
                _ => "Ancient wisdom for navigating modern spiritual challenges."
            };
        }

        private string GetBlogContentForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Mercury retrograde periods are often misunderstood and feared, but they offer powerful opportunities for growth and introspection. During these times, we're invited to slow down, review, and realign with our true path...",
                "Crystal Healing" => "Amethyst, known as the stone of spiritual wisdom, has been revered for centuries for its ability to enhance intuition and promote spiritual growth. This beautiful purple crystal works directly with the third eye chakra...",
                "Palmistry" => "The art of palmistry has been practiced for thousands of years, offering insights into personality, relationships, and life potential. Your hands tell the story of your soul's journey...",
                "Spiritual Counseling" => "Finding your soul purpose is one of the most important journeys you can undertake. It requires deep introspection, spiritual practice, and often guidance from experienced counselors...",
                "Numerology" => "Your life path number is calculated from your birth date and reveals the core lessons and experiences your soul has chosen for this lifetime. Understanding this number can provide profound insights...",
                _ => "Spiritual wisdom has been passed down through generations, offering guidance for those seeking deeper meaning and purpose in their lives..."
            };
        }

        private string GetBlogSlugForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "mercury-retrograde-navigation-guide",
                "Crystal Healing" => "amethyst-third-eye-chakra-healing",
                "Palmistry" => "reading-life-lines-destiny-guide",
                "Spiritual Counseling" => "finding-soul-purpose-spiritual-awakening",
                "Numerology" => "life-path-number-divine-blueprint",
                _ => "spiritual-wisdom-modern-times"
            };
        }

        private string GetBlogImageForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=200&fit=crop",
                "Crystal Healing" => "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop",
                "Palmistry" => "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop",
                "Spiritual Counseling" => "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=200&fit=crop",
                "Numerology" => "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=200&fit=crop",
                _ => "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop"
            };
        }

        private Certification[] GetCertificationsForSpecialty(string specialty, int profileId)
        {
            return specialty switch
            {
                "Astrology" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Professional Astrologer (CPA)", IssuingOrganization = "International Society for Astrological Research", IssueDate = DateTime.UtcNow.AddYears(-5), ExpirationDate = DateTime.UtcNow.AddYears(1), CredentialId = "ISAR-CPA-2019-7734" },
                    new Certification { UserProfileId = profileId, Name = "Advanced Tarot Certification", IssuingOrganization = "American Tarot Association", IssueDate = DateTime.UtcNow.AddYears(-4), CredentialId = "ATA-ADV-2020-9821" }
                },
                "Crystal Healing" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Master Crystal Healer", IssuingOrganization = "Crystal Healing Institute", IssueDate = DateTime.UtcNow.AddYears(-6), ExpirationDate = DateTime.UtcNow.AddYears(2), CredentialId = "CHI-MASTER-2018-3344" },
                    new Certification { UserProfileId = profileId, Name = "Reiki Master Teacher", IssuingOrganization = "International Reiki Association", IssueDate = DateTime.UtcNow.AddYears(-5), CredentialId = "IRA-MASTER-2019-5566" }
                },
                "Palmistry" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Palmistry Expert", IssuingOrganization = "International Palmistry Association", IssueDate = DateTime.UtcNow.AddYears(-7), CredentialId = "IPA-EXPERT-2017-8899" },
                    new Certification { UserProfileId = profileId, Name = "Hand Analysis Specialist", IssuingOrganization = "American Hand Analysis Society", IssueDate = DateTime.UtcNow.AddYears(-6), CredentialId = "AHAS-SPEC-2018-1122" }
                },
                "Spiritual Counseling" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Spiritual Counselor", IssuingOrganization = "International Spiritual Counseling Association", IssueDate = DateTime.UtcNow.AddYears(-8), ExpirationDate = DateTime.UtcNow.AddYears(1), CredentialId = "ISCA-CERT-2016-4455" },
                    new Certification { UserProfileId = profileId, Name = "Life Coach Certification", IssuingOrganization = "International Coach Federation", IssueDate = DateTime.UtcNow.AddYears(-5), CredentialId = "ICF-COACH-2019-7788" }
                },
                "Numerology" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Master Numerologist", IssuingOrganization = "International Numerology Association", IssueDate = DateTime.UtcNow.AddYears(-9), CredentialId = "INA-MASTER-2015-9900" },
                    new Certification { UserProfileId = profileId, Name = "Sacred Geometry Specialist", IssuingOrganization = "Sacred Geometry Institute", IssueDate = DateTime.UtcNow.AddYears(-4), CredentialId = "SGI-SPEC-2020-2233" }
                },
                _ => new Certification[0]
            };
        }

        private string GetCompanyNameForSpecialty(string specialty, string firstName)
        {
            return specialty switch
            {
                "Astrology" => $"{firstName} Astrology Studio",
                "Crystal Healing" => $"{firstName} Crystal Healing Center",
                "Palmistry" => $"{firstName} Palm Reading Studio",
                "Spiritual Counseling" => $"{firstName} Spiritual Wisdom Center",
                "Numerology" => $"{firstName} Numerology Institute",
                _ => $"{firstName} Spiritual Services"
            };
        }

        private string GetExperienceDescriptionForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Founded and operate a thriving astrology practice offering personalized readings, workshops, and spiritual guidance. Specialize in natal chart interpretation, relationship compatibility, and life transition guidance.",
                "Crystal Healing" => "Established a comprehensive crystal healing practice providing energy work, chakra balancing, and vibrational healing services. Help clients release blockages and align with their highest potential.",
                "Palmistry" => "Built a successful palmistry practice offering detailed hand analysis, life path readings, and personality insights. Provide guidance on relationships, career potential, and future possibilities.",
                "Spiritual Counseling" => "Created a holistic spiritual counseling practice combining traditional counseling with spiritual guidance. Help clients discover their purpose and navigate life's challenges with wisdom and compassion.",
                "Numerology" => "Developed a specialized numerology practice offering life path analysis, name readings, and timing guidance. Help clients understand their divine blueprint and make aligned life decisions.",
                _ => "Established a comprehensive spiritual practice offering various forms of guidance and healing services."
            };
        }

        #endregion
    }
}
