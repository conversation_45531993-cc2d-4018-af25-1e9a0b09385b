using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Phone number entity for contact information
    /// </summary>
    public class PhoneNumber : BaseEntity
    {
        [Required]
        public int ContactInformationId { get; set; }

        [Required]
        [MaxLength(20)]
        public string Number { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Type { get; set; } = string.Empty; // mobile, business, home

        public bool IsPublic { get; set; } = false;

        public bool IsPrimary { get; set; } = false;

        // Navigation properties
        public virtual ContactInformation ContactInformation { get; set; } = null!;
    }
}
