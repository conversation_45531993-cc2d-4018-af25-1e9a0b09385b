{"ast": null, "code": "import { Subject, takeUntil, fork<PERSON>oin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"../../../auth/services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/platform-browser\";\nfunction ProfileViewComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 43);\n  }\n}\nfunction ProfileViewComponent_div_0_h2_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.profile.professionalTitle);\n  }\n}\nfunction ProfileViewComponent_div_0_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.profile.headline);\n  }\n}\nfunction ProfileViewComponent_div_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.profile.location == null ? null : ctx_r5.profile.location.displayLocation);\n  }\n}\nfunction ProfileViewComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r6.profile.profileViews, \" profile views\");\n  }\n}\nfunction ProfileViewComponent_div_0_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ProfileViewComponent_div_0_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onContactClick());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileViewComponent_div_0_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ProfileViewComponent_div_0_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onEditProfile());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileViewComponent_div_0_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileViewComponent_div_0_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onFollowUser());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Follow \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileViewComponent_div_0_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Report\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileViewComponent_div_0_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"span\");\n    i0.ɵɵtext(3, \"Profile Completion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-progress-bar\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.profile.profileCompletionPercentage, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r12.profile.profileCompletionPercentage)(\"color\", ctx_r12.getProfileCompletionColor());\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 56)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" About \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵelement(7, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r13.profile.summary, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_div_8_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const exp_r32 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", exp_r32.location, \"\");\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const exp_r32 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(exp_r32.description);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_div_8_div_12_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const achievement_r39 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(achievement_r39);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_div_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"ul\");\n    i0.ɵɵtemplate(2, ProfileViewComponent_div_0_mat_card_42_div_8_div_12_li_2_Template, 2, 1, \"li\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const exp_r32 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", exp_r32.achievements);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"img\", 63);\n    i0.ɵɵelementStart(3, \"div\", 64)(4, \"h3\", 65);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, ProfileViewComponent_div_0_mat_card_42_div_8_span_10_Template, 2, 1, \"span\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, ProfileViewComponent_div_0_mat_card_42_div_8_div_11_Template, 3, 1, \"div\", 68);\n    i0.ɵɵtemplate(12, ProfileViewComponent_div_0_mat_card_42_div_8_div_12_Template, 3, 1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const exp_r32 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", exp_r32.companyLogoUrl || \"/assets/images/default-company.png\", i0.ɵɵsanitizeUrl)(\"alt\", exp_r32.company);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(exp_r32.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(exp_r32.company);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r31.formatDate(exp_r32.startDate), \" - \", exp_r32.isCurrent ? \"Present\" : ctx_r31.formatDate(exp_r32.endDate), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", exp_r32.location);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", exp_r32.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", exp_r32.achievements == null ? null : exp_r32.achievements.length);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 58)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Experience \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 59);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_mat_card_42_div_8_Template, 13, 9, \"div\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.profile.experiences);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 84);\n  }\n  if (rf & 2) {\n    const item_r42 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", item_r42.imageUrls[0], i0.ɵɵsanitizeUrl)(\"alt\", item_r42.title);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r47 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tech_r47);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_div_11_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 88)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"launch\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Project \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r42 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", item_r42.projectUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_div_11_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 88)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Code \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r42 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", item_r42.githubUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, ProfileViewComponent_div_0_mat_card_43_div_8_div_11_a_1_Template, 4, 1, \"a\", 87);\n    i0.ɵɵtemplate(2, ProfileViewComponent_div_0_mat_card_43_div_8_div_11_a_2_Template, 4, 1, \"a\", 87);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r42 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r42.projectUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r42.githubUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77);\n    i0.ɵɵtemplate(2, ProfileViewComponent_div_0_mat_card_43_div_8_img_2_Template, 1, 2, \"img\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 80)(9, \"div\", 81);\n    i0.ɵɵtemplate(10, ProfileViewComponent_div_0_mat_card_43_div_8_span_10_Template, 2, 1, \"span\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, ProfileViewComponent_div_0_mat_card_43_div_8_div_11_Template, 3, 2, \"div\", 83);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r42 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r42.imageUrls.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r42.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r42.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", item_r42.technologies);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r42.projectUrl || item_r42.githubUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 73)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"work_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Portfolio \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 74);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_mat_card_43_div_8_Template, 12, 5, \"div\", 75);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.profile.portfolioItems);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_44_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"img\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r54 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", post_r54.featuredImageUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r54.title);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_44_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtemplate(1, ProfileViewComponent_div_0_mat_card_44_div_8_div_1_Template, 2, 2, \"div\", 93);\n    i0.ɵɵelementStart(2, \"div\", 94)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 95)(8, \"span\", 96);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 97);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const post_r54 = ctx.$implicit;\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", post_r54.featuredImageUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r54.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r54.excerpt);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r53.formatDate(post_r54.publishedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", post_r54.readCount, \" reads\");\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 89)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"article\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Recent Articles \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 90);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_mat_card_44_div_8_Template, 12, 5, \"div\", 91);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.blogPosts.slice(0, 3));\n  }\n}\nfunction ProfileViewComponent_div_0_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"mailto:\" + ctx_r17.profile.contactInfo.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.profile.contactInfo.email);\n  }\n}\nfunction ProfileViewComponent_div_0_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const phone_r57 = ctx.$implicit;\n    i0.ɵɵproperty(\"hidden\", !phone_r57.isPublic);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"tel:\" + phone_r57.number, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", phone_r57.number, \" (\", phone_r57.type, \")\");\n  }\n}\nfunction ProfileViewComponent_div_0_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 102);\n    i0.ɵɵtext(4, \"Website\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r19.profile.contactInfo.website, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 102);\n    i0.ɵɵtext(4, \"Portfolio\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r20.profile.contactInfo.portfolioUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_57_div_7_div_4_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function ProfileViewComponent_div_0_mat_card_57_div_7_div_4_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const skill_r61 = i0.ɵɵnextContext().$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r63.onEndorseSkill(skill_r61.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thumb_up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_57_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 110)(2, \"span\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 112);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ProfileViewComponent_div_0_mat_card_57_div_7_div_4_button_6_Template, 3, 0, \"button\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r61 = ctx.$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r61.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r61.endorsements, \" endorsements\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r60.isOwnProfile && ctx_r60.isAuthenticated && !skill_r61.isEndorsedByCurrentUser);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_57_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"h4\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 107);\n    i0.ɵɵtemplate(4, ProfileViewComponent_div_0_mat_card_57_div_7_div_4_Template, 7, 3, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r59 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r59.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r59.value);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 103)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Skills & Expertise \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, ProfileViewComponent_div_0_mat_card_57_div_7_Template, 5, 2, \"div\", 104);\n    i0.ɵɵpipe(8, \"keyvalue\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(8, 1, ctx_r21.getSkillsByCategory()));\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_58_div_8_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const achievement_r67 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", achievement_r67.organization, \"\");\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_58_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119);\n    i0.ɵɵelement(2, \"img\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 120)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 121);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ProfileViewComponent_div_0_mat_card_58_div_8_span_10_Template, 2, 1, \"span\", 122);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const achievement_r67 = ctx.$implicit;\n    const ctx_r66 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", achievement_r67.imageUrl || \"/assets/images/default-achievement.png\", i0.ɵɵsanitizeUrl)(\"alt\", achievement_r67.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(achievement_r67.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(achievement_r67.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r66.formatDate(achievement_r67.achievedAt));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", achievement_r67.organization);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 115)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Achievements \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 116);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_mat_card_58_div_8_Template, 11, 6, \"div\", 117);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.profile.achievements);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_59_div_8_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cert_r71 = i0.ɵɵnextContext().$implicit;\n    const ctx_r72 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 Expires \", ctx_r72.formatDate(cert_r71.expirationDate), \"\");\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_59_div_8_a_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 133)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"launch\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" View Credential \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cert_r71 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", cert_r71.credentialUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_59_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128);\n    i0.ɵɵelement(2, \"img\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 129)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 130);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, ProfileViewComponent_div_0_mat_card_59_div_8_span_10_Template, 2, 1, \"span\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ProfileViewComponent_div_0_mat_card_59_div_8_a_11_Template, 4, 1, \"a\", 132);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const cert_r71 = ctx.$implicit;\n    const ctx_r70 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", cert_r71.imageUrl || \"/assets/images/default-certification.png\", i0.ɵɵsanitizeUrl)(\"alt\", cert_r71.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(cert_r71.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(cert_r71.issuingOrganization);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Issued \", ctx_r70.formatDate(cert_r71.issueDate), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", cert_r71.expirationDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", cert_r71.credentialUrl);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 124)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"verified\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Certifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 125);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_mat_card_59_div_8_Template, 12, 7, \"div\", 126);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.profile.certifications);\n  }\n}\nfunction ProfileViewComponent_div_0_mat_card_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 134)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Profile Analytics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 135)(8, \"div\", 136)(9, \"span\", 137);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 138);\n    i0.ɵɵtext(12, \"Total Views\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 136)(14, \"span\", 137);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 138);\n    i0.ɵɵtext(17, \"Unique Visitors\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 136)(19, \"span\", 137);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 138);\n    i0.ɵɵtext(22, \"Skill Endorsements\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 136)(24, \"span\", 137);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 138);\n    i0.ɵɵtext(27, \"Contact Clicks\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r24.analytics.profileViews);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r24.analytics.uniqueVisitors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r24.analytics.skillEndorsements);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r24.analytics.contactButtonClicks);\n  }\n}\nfunction ProfileViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"div\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"div\", 8);\n    i0.ɵɵelement(7, \"img\", 9);\n    i0.ɵɵtemplate(8, ProfileViewComponent_div_0_div_8_Template, 1, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"h1\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ProfileViewComponent_div_0_h2_12_Template, 2, 1, \"h2\", 13);\n    i0.ɵɵtemplate(13, ProfileViewComponent_div_0_p_13_Template, 2, 1, \"p\", 14);\n    i0.ɵɵelementStart(14, \"div\", 15);\n    i0.ɵɵtemplate(15, ProfileViewComponent_div_0_div_15_Template, 5, 1, \"div\", 16);\n    i0.ɵɵelementStart(16, \"div\", 17)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, ProfileViewComponent_div_0_div_21_Template, 5, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 19);\n    i0.ɵɵtemplate(23, ProfileViewComponent_div_0_button_23_Template, 4, 0, \"button\", 20);\n    i0.ɵɵtemplate(24, ProfileViewComponent_div_0_button_24_Template, 4, 0, \"button\", 21);\n    i0.ɵɵtemplate(25, ProfileViewComponent_div_0_button_25_Template, 4, 0, \"button\", 22);\n    i0.ɵɵelementStart(26, \"button\", 23)(27, \"mat-icon\");\n    i0.ɵɵtext(28, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-menu\", null, 24)(31, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProfileViewComponent_div_0_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.onShareProfile());\n    });\n    i0.ɵɵelementStart(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"share\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Share Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, ProfileViewComponent_div_0_button_36_Template, 5, 0, \"button\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, ProfileViewComponent_div_0_div_37_Template, 7, 3, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 28)(39, \"div\", 29)(40, \"div\", 30);\n    i0.ɵɵtemplate(41, ProfileViewComponent_div_0_mat_card_41_Template, 8, 1, \"mat-card\", 31);\n    i0.ɵɵtemplate(42, ProfileViewComponent_div_0_mat_card_42_Template, 9, 1, \"mat-card\", 32);\n    i0.ɵɵtemplate(43, ProfileViewComponent_div_0_mat_card_43_Template, 9, 1, \"mat-card\", 33);\n    i0.ɵɵtemplate(44, ProfileViewComponent_div_0_mat_card_44_Template, 9, 1, \"mat-card\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 35)(46, \"mat-card\", 36)(47, \"mat-card-header\")(48, \"mat-card-title\")(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \" Contact Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"mat-card-content\");\n    i0.ɵɵtemplate(53, ProfileViewComponent_div_0_div_53_Template, 5, 2, \"div\", 37);\n    i0.ɵɵtemplate(54, ProfileViewComponent_div_0_div_54_Template, 5, 4, \"div\", 38);\n    i0.ɵɵtemplate(55, ProfileViewComponent_div_0_div_55_Template, 5, 1, \"div\", 37);\n    i0.ɵɵtemplate(56, ProfileViewComponent_div_0_div_56_Template, 5, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(57, ProfileViewComponent_div_0_mat_card_57_Template, 9, 3, \"mat-card\", 39);\n    i0.ɵɵtemplate(58, ProfileViewComponent_div_0_mat_card_58_Template, 9, 1, \"mat-card\", 40);\n    i0.ɵɵtemplate(59, ProfileViewComponent_div_0_mat_card_59_Template, 9, 1, \"mat-card\", 41);\n    i0.ɵɵtemplate(60, ProfileViewComponent_div_0_mat_card_60_Template, 28, 4, \"mat-card\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r10 = i0.ɵɵreference(30);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", ctx_r0.profile.coverPhotoUrl ? \"url(\" + ctx_r0.profile.coverPhotoUrl + \")\" : \"var(--theme-gradient-primary)\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r0.profile.profilePhotoUrl || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.profile.firstName + \" \" + ctx_r0.profile.lastName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.profile.firstName, \" \", ctx_r0.profile.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.professionalTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.headline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.location == null ? null : ctx_r0.profile.location.displayLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getExperienceYears(), \" years experience\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOwnProfile && ctx_r0.isAuthenticated);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r10);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOwnProfile);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.summary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.experiences.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.portfolioItems.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.blogPosts.length);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.contactInfo.email && ctx_r0.profile.contactInfo.isEmailPublic);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.profile.contactInfo.phoneNumbers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.contactInfo.website);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.contactInfo.portfolioUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.skills.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.achievements.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile.certifications.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOwnProfile && ctx_r0.analytics);\n  }\n}\nfunction ProfileViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 139);\n    i0.ɵɵelement(1, \"mat-spinner\", 140);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n// import { ContactDialogComponent } from '../contact-dialog/contact-dialog.component';\nexport class ProfileViewComponent {\n  constructor(route, router, profileService, authService, snackBar, dialog, titleService, metaService) {\n    this.route = route;\n    this.router = router;\n    this.profileService = profileService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.titleService = titleService;\n    this.metaService = metaService;\n    this.profile = null;\n    this.analytics = null;\n    this.blogPosts = [];\n    this.isOwnProfile = false;\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    this.currentUserId = null;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeComponent() {\n    // Check authentication status\n    this.authService.isAuthenticated$.pipe(takeUntil(this.destroy$)).subscribe(isAuth => {\n      this.isAuthenticated = isAuth;\n    });\n    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUserId = user?.id || null;\n    });\n    // Get profile identifier from route\n    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      const identifier = params['identifier']; // username or user-id\n      if (identifier) {\n        this.loadProfile(identifier);\n      }\n    });\n  }\n  loadProfile(identifier) {\n    this.isLoading = true;\n    const profileRequest = this.profileService.getProfile(identifier);\n    const blogPostsRequest = this.profileService.getBlogPosts(parseInt(identifier) || 0);\n    forkJoin({\n      profile: profileRequest,\n      blogPosts: blogPostsRequest\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: ({\n        profile,\n        blogPosts\n      }) => {\n        this.profile = profile;\n        this.blogPosts = blogPosts;\n        this.isOwnProfile = this.currentUserId === profile.userId;\n        // Load analytics if it's own profile\n        if (this.isOwnProfile) {\n          this.loadAnalytics();\n        }\n        // Record profile view if not own profile\n        if (!this.isOwnProfile) {\n          this.recordProfileView();\n        }\n        // Set SEO metadata\n        this.setSEOMetadata();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading profile:', error);\n        this.snackBar.open('Profile not found', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/']);\n        this.isLoading = false;\n      }\n    });\n  }\n  loadAnalytics() {\n    if (!this.isOwnProfile) return;\n    this.profileService.getProfileAnalytics().pipe(takeUntil(this.destroy$)).subscribe({\n      next: analytics => {\n        this.analytics = analytics;\n      },\n      error: error => {\n        console.error('Error loading analytics:', error);\n      }\n    });\n  }\n  recordProfileView() {\n    if (!this.profile) return;\n    this.profileService.recordProfileView({\n      profileId: this.profile.id,\n      viewerUserId: this.currentUserId || undefined,\n      referrer: document.referrer,\n      userAgent: navigator.userAgent\n    }).subscribe({\n      error: error => {\n        console.error('Error recording profile view:', error);\n      }\n    });\n  }\n  setSEOMetadata() {\n    if (!this.profile) return;\n    const title = `${this.profile.firstName} ${this.profile.lastName} - ${this.profile.professionalTitle || 'Professional Profile'}`;\n    const description = this.profile.summary || `View ${this.profile.firstName} ${this.profile.lastName}'s professional profile, experience, and portfolio.`;\n    const imageUrl = this.profile.profilePhotoUrl || '/assets/images/default-profile.jpg';\n    const profileUrl = `${window.location.origin}/profile/${this.profile.slug}`;\n    // Set page title\n    this.titleService.setTitle(title);\n    // Set meta tags\n    this.metaService.updateTag({\n      name: 'description',\n      content: description\n    });\n    this.metaService.updateTag({\n      property: 'og:title',\n      content: title\n    });\n    this.metaService.updateTag({\n      property: 'og:description',\n      content: description\n    });\n    this.metaService.updateTag({\n      property: 'og:image',\n      content: imageUrl\n    });\n    this.metaService.updateTag({\n      property: 'og:url',\n      content: profileUrl\n    });\n    this.metaService.updateTag({\n      property: 'og:type',\n      content: 'profile'\n    });\n    this.metaService.updateTag({\n      name: 'twitter:card',\n      content: 'summary_large_image'\n    });\n    this.metaService.updateTag({\n      name: 'twitter:title',\n      content: title\n    });\n    this.metaService.updateTag({\n      name: 'twitter:description',\n      content: description\n    });\n    this.metaService.updateTag({\n      name: 'twitter:image',\n      content: imageUrl\n    });\n  }\n  // Action Methods\n  onContactClick() {\n    if (!this.profile) return;\n    // TODO: Implement contact dialog\n    // const dialogRef = this.dialog.open(ContactDialogComponent, {\n    //   width: '500px',\n    //   data: {\n    //     profile: this.profile,\n    //     isAuthenticated: this.isAuthenticated\n    //   }\n    // });\n    // dialogRef.afterClosed().subscribe(result => {\n    //   if (result) {\n    //     this.snackBar.open('Message sent successfully!', 'Close', { duration: 3000 });\n    //   }\n    // });\n    // Temporary implementation\n    this.snackBar.open('Contact feature coming soon!', 'Close', {\n      duration: 2000\n    });\n  }\n  onEditProfile() {\n    this.router.navigate(['/profile/edit']);\n  }\n  onShareProfile() {\n    if (!this.profile) return;\n    if (navigator.share) {\n      navigator.share({\n        title: `${this.profile.firstName} ${this.profile.lastName} - Professional Profile`,\n        text: this.profile.summary || 'Check out this professional profile',\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        this.snackBar.open('Profile URL copied to clipboard!', 'Close', {\n          duration: 3000\n        });\n      });\n    }\n  }\n  onEndorseSkill(skillId) {\n    if (!this.isAuthenticated || !this.profile || this.isOwnProfile) return;\n    this.profileService.endorseSkill({\n      skillId: skillId,\n      endorsedUserId: this.profile.userId\n    }).subscribe({\n      next: () => {\n        this.snackBar.open('Skill endorsed!', 'Close', {\n          duration: 2000\n        });\n        // Refresh profile to update endorsement count\n        this.loadProfile(this.profile.slug);\n      },\n      error: error => {\n        console.error('Error endorsing skill:', error);\n        this.snackBar.open('Failed to endorse skill', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  onFollowUser() {\n    // TODO: Implement follow functionality\n    this.snackBar.open('Follow feature coming soon!', 'Close', {\n      duration: 2000\n    });\n  }\n  // Utility Methods\n  getProfileCompletionColor() {\n    if (!this.profile) return 'warn';\n    const percentage = this.profile.profileCompletionPercentage;\n    if (percentage >= 80) return 'primary';\n    if (percentage >= 50) return 'accent';\n    return 'warn';\n  }\n  getExperienceYears() {\n    if (!this.profile?.experiences.length) return 0;\n    const totalMonths = this.profile.experiences.reduce((total, exp) => {\n      const start = new Date(exp.startDate);\n      const end = exp.endDate ? new Date(exp.endDate) : new Date();\n      const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());\n      return total + months;\n    }, 0);\n    return Math.floor(totalMonths / 12);\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long'\n    });\n  }\n  getSkillsByCategory() {\n    if (!this.profile?.skills.length) return {};\n    return this.profile.skills.reduce((acc, skill) => {\n      const category = skill.category || 'Other';\n      if (!acc[category]) acc[category] = [];\n      acc[category].push(skill);\n      return acc;\n    }, {});\n  }\n  static {\n    this.ɵfac = function ProfileViewComponent_Factory(t) {\n      return new (t || ProfileViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.Title), i0.ɵɵdirectiveInject(i6.Meta));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileViewComponent,\n      selectors: [[\"app-profile-view\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"profile-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"profile-container\"], [1, \"cover-section\"], [1, \"cover-photo\"], [1, \"cover-overlay\"], [1, \"profile-header\"], [1, \"header-content\"], [1, \"profile-photo-container\"], [1, \"profile-photo\", 3, \"src\", \"alt\"], [\"class\", \"online-indicator\", 4, \"ngIf\"], [1, \"profile-info\"], [1, \"profile-name\"], [\"class\", \"profile-title\", 4, \"ngIf\"], [\"class\", \"profile-headline\", 4, \"ngIf\"], [1, \"profile-meta\"], [\"class\", \"location\", 4, \"ngIf\"], [1, \"experience-years\"], [\"class\", \"profile-views\", 4, \"ngIf\"], [1, \"profile-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"contact-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"more-actions\", 3, \"matMenuTriggerFor\"], [\"profileMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 4, \"ngIf\"], [\"class\", \"profile-completion\", 4, \"ngIf\"], [1, \"profile-content\"], [1, \"content-grid\"], [1, \"left-column\"], [\"class\", \"profile-section about-section\", 4, \"ngIf\"], [\"class\", \"profile-section experience-section\", 4, \"ngIf\"], [\"class\", \"profile-section portfolio-section\", 4, \"ngIf\"], [\"class\", \"profile-section blog-section\", 4, \"ngIf\"], [1, \"right-column\"], [1, \"profile-section\", \"contact-section\"], [\"class\", \"contact-item\", 4, \"ngIf\"], [\"class\", \"contact-item\", 3, \"hidden\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"profile-section skills-section\", 4, \"ngIf\"], [\"class\", \"profile-section achievements-section\", 4, \"ngIf\"], [\"class\", \"profile-section certifications-section\", 4, \"ngIf\"], [\"class\", \"profile-section analytics-section\", 4, \"ngIf\"], [1, \"online-indicator\"], [1, \"profile-title\"], [1, \"profile-headline\"], [1, \"location\"], [1, \"profile-views\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"contact-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\"], [1, \"profile-completion\"], [1, \"completion-header\"], [1, \"completion-percentage\"], [3, \"value\", \"color\"], [1, \"profile-section\", \"about-section\"], [1, \"summary-text\", 3, \"innerHTML\"], [1, \"profile-section\", \"experience-section\"], [1, \"experience-timeline\"], [\"class\", \"experience-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"experience-item\"], [1, \"experience-header\"], [1, \"company-logo\", 3, \"src\", \"alt\"], [1, \"experience-info\"], [1, \"position\"], [1, \"company\"], [1, \"duration\"], [\"class\", \"experience-description\", 4, \"ngIf\"], [\"class\", \"experience-achievements\", 4, \"ngIf\"], [1, \"experience-description\"], [1, \"experience-achievements\"], [4, \"ngFor\", \"ngForOf\"], [1, \"profile-section\", \"portfolio-section\"], [1, \"portfolio-grid\"], [\"class\", \"portfolio-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"portfolio-item\"], [1, \"portfolio-image\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"portfolio-info\"], [1, \"portfolio-tech\"], [1, \"tech-chips\"], [\"class\", \"tech-chip\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"portfolio-links\", 4, \"ngIf\"], [3, \"src\", \"alt\"], [1, \"tech-chip\"], [1, \"portfolio-links\"], [\"mat-button\", \"\", \"target\", \"_blank\", 3, \"href\", 4, \"ngIf\"], [\"mat-button\", \"\", \"target\", \"_blank\", 3, \"href\"], [1, \"profile-section\", \"blog-section\"], [1, \"blog-posts\"], [\"class\", \"blog-post\", 4, \"ngFor\", \"ngForOf\"], [1, \"blog-post\"], [\"class\", \"blog-image\", 4, \"ngIf\"], [1, \"blog-content\"], [1, \"blog-meta\"], [1, \"publish-date\"], [1, \"read-count\"], [1, \"blog-image\"], [1, \"contact-item\"], [3, \"href\"], [1, \"contact-item\", 3, \"hidden\"], [\"target\", \"_blank\", 3, \"href\"], [1, \"profile-section\", \"skills-section\"], [\"class\", \"skills-by-category\", 4, \"ngFor\", \"ngForOf\"], [1, \"skills-by-category\"], [1, \"skill-category\"], [1, \"skills-list\"], [\"class\", \"skill-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"skill-item\"], [1, \"skill-info\"], [1, \"skill-name\"], [1, \"endorsement-count\"], [\"mat-icon-button\", \"\", \"class\", \"endorse-btn\", \"matTooltip\", \"Endorse this skill\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Endorse this skill\", 1, \"endorse-btn\", 3, \"click\"], [1, \"profile-section\", \"achievements-section\"], [1, \"achievements-list\"], [\"class\", \"achievement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"achievement-item\"], [1, \"achievement-icon\"], [1, \"achievement-info\"], [1, \"achievement-date\"], [\"class\", \"achievement-org\", 4, \"ngIf\"], [1, \"achievement-org\"], [1, \"profile-section\", \"certifications-section\"], [1, \"certifications-list\"], [\"class\", \"certification-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"certification-item\"], [1, \"certification-icon\"], [1, \"certification-info\"], [1, \"certification-date\"], [4, \"ngIf\"], [\"target\", \"_blank\", \"class\", \"credential-link\", 3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"credential-link\", 3, \"href\"], [1, \"profile-section\", \"analytics-section\"], [1, \"analytics-grid\"], [1, \"analytics-item\"], [1, \"analytics-value\"], [1, \"analytics-label\"], [1, \"loading-container\"], [\"diameter\", \"50\"]],\n      template: function ProfileViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProfileViewComponent_div_0_Template, 61, 30, \"div\", 0);\n          i0.ɵɵtemplate(1, ProfileViewComponent_div_1_Template, 4, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.profile);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\r\\n  min-height: 100vh;\\r\\n  background-color: var(--theme-background);\\r\\n}\\r\\n\\r\\n\\r\\n.cover-section[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  height: 300px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.cover-photo[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-size: cover;\\r\\n  background-position: center;\\r\\n  background-repeat: no-repeat;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.cover-overlay[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));\\r\\n}\\r\\n\\r\\n\\r\\n.profile-header[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-surface);\\r\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\\r\\n  position: relative;\\r\\n  margin-top: -100px;\\r\\n  z-index: 10;\\r\\n  border-radius: 16px 16px 0 0;\\r\\n  margin-left: 20px;\\r\\n  margin-right: 20px;\\r\\n}\\r\\n\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: flex-start;\\r\\n  padding: 24px;\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.profile-photo-container[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n\\r\\n.profile-photo[_ngcontent-%COMP%] {\\r\\n  width: 150px;\\r\\n  height: 150px;\\r\\n  border-radius: 50%;\\r\\n  border: 4px solid var(--theme-surface);\\r\\n  box-shadow: 0 4px 16px rgba(0,0,0,0.2);\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.online-indicator[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  right: 10px;\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n  background-color: #4caf50;\\r\\n  border: 3px solid var(--theme-surface);\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.profile-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n\\r\\n.profile-name[_ngcontent-%COMP%] {\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 600;\\r\\n  margin: 0 0 8px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.profile-title[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: 400;\\r\\n  margin: 0 0 8px 0;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.profile-headline[_ngcontent-%COMP%] {\\r\\n  font-size: 1.1rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin: 0 0 16px 0;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\n.profile-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 16px;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.profile-meta[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n.profile-meta[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n}\\r\\n\\r\\n.profile-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n  align-items: flex-end;\\r\\n}\\r\\n\\r\\n.contact-btn[_ngcontent-%COMP%] {\\r\\n  min-width: 140px;\\r\\n}\\r\\n\\r\\n.profile-completion[_ngcontent-%COMP%] {\\r\\n  padding: 0 24px 24px;\\r\\n}\\r\\n\\r\\n.completion-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.completion-percentage[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.profile-content[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n}\\r\\n\\r\\n.content-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: 2fr 1fr;\\r\\n  gap: 24px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.profile-section[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n}\\r\\n\\r\\n.profile-section[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\r\\n  padding-bottom: 16px;\\r\\n}\\r\\n\\r\\n.profile-section[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 1.3rem;\\r\\n}\\r\\n\\r\\n\\r\\n.summary-text[_ngcontent-%COMP%] {\\r\\n  line-height: 1.6;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.experience-timeline[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.experience-item[_ngcontent-%COMP%] {\\r\\n  border-left: 3px solid var(--theme-accent);\\r\\n  padding-left: 20px;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.experience-item[_ngcontent-%COMP%]::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  left: -8px;\\r\\n  top: 8px;\\r\\n  width: 12px;\\r\\n  height: 12px;\\r\\n  background-color: var(--theme-accent);\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.experience-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.company-logo[_ngcontent-%COMP%] {\\r\\n  width: 48px;\\r\\n  height: 48px;\\r\\n  border-radius: 8px;\\r\\n  object-fit: cover;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n\\r\\n.experience-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 4px 0;\\r\\n  font-size: 1.2rem;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.experience-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 4px 0;\\r\\n  font-size: 1rem;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.duration[_ngcontent-%COMP%] {\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.experience-description[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 12px;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\n.experience-achievements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  padding-left: 20px;\\r\\n}\\r\\n\\r\\n.experience-achievements[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 4px;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n.portfolio-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\r\\n  gap: 20px;\\r\\n}\\r\\n\\r\\n.portfolio-item[_ngcontent-%COMP%] {\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 12px;\\r\\n  overflow: hidden;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.portfolio-item[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 24px rgba(0,0,0,0.15);\\r\\n}\\r\\n\\r\\n.portfolio-image[_ngcontent-%COMP%] {\\r\\n  height: 200px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.portfolio-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.portfolio-info[_ngcontent-%COMP%] {\\r\\n  padding: 16px;\\r\\n}\\r\\n\\r\\n.portfolio-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.portfolio-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 12px 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  line-height: 1.4;\\r\\n}\\r\\n\\r\\n.portfolio-tech[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.portfolio-links[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.tech-chips[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.tech-chip[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n\\r\\n.blog-posts[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.blog-post[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  padding: 16px;\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n  transition: background-color 0.3s ease;\\r\\n}\\r\\n\\r\\n.blog-post[_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.blog-image[_ngcontent-%COMP%] {\\r\\n  width: 80px;\\r\\n  height: 80px;\\r\\n  flex-shrink: 0;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.blog-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.blog-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  font-size: 1.1rem;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.blog-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  line-height: 1.4;\\r\\n}\\r\\n\\r\\n.blog-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  font-size: 0.85rem;\\r\\n  color: var(--theme-text-hint);\\r\\n}\\r\\n\\r\\n\\r\\n.contact-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  margin-bottom: 12px;\\r\\n  padding: 8px;\\r\\n  border-radius: 8px;\\r\\n  transition: background-color 0.3s ease;\\r\\n}\\r\\n\\r\\n.contact-item[_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n}\\r\\n\\r\\n.contact-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n.contact-item[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-primary);\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.contact-item[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.skill-category[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 12px 0;\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 1.1rem;\\r\\n}\\r\\n\\r\\n.skills-list[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.skill-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 8px 12px;\\r\\n  margin-bottom: 8px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n  border-radius: 8px;\\r\\n  transition: background-color 0.3s ease;\\r\\n}\\r\\n\\r\\n.skill-item[_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(0,0,0,0.04);\\r\\n}\\r\\n\\r\\n.skill-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 2px;\\r\\n}\\r\\n\\r\\n.skill-name[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.endorsement-count[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.endorse-btn[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n\\r\\n.achievements-list[_ngcontent-%COMP%], .certifications-list[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.achievement-item[_ngcontent-%COMP%], .certification-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  padding: 16px;\\r\\n  border: 1px solid rgba(0,0,0,0.1);\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.achievement-icon[_ngcontent-%COMP%], .certification-icon[_ngcontent-%COMP%] {\\r\\n  width: 48px;\\r\\n  height: 48px;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n\\r\\n.achievement-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .certification-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  object-fit: cover;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.achievement-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .certification-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 4px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.achievement-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .certification-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n.achievement-date[_ngcontent-%COMP%], .certification-date[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: var(--theme-text-hint);\\r\\n}\\r\\n\\r\\n.credential-link[_ngcontent-%COMP%] {\\r\\n  display: inline-flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  margin-top: 8px;\\r\\n  color: var(--theme-primary);\\r\\n  text-decoration: none;\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n\\r\\n.analytics-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(2, 1fr);\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.analytics-item[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  padding: 16px;\\r\\n  background-color: rgba(0,0,0,0.02);\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.analytics-value[_ngcontent-%COMP%] {\\r\\n  display: block;\\r\\n  font-size: 1.8rem;\\r\\n  font-weight: 600;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.analytics-label[_ngcontent-%COMP%] {\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 50vh;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 1024px) {\\r\\n  .content-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .right-column[_ngcontent-%COMP%] {\\r\\n    order: -1;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .profile-header[_ngcontent-%COMP%] {\\r\\n    margin-left: 10px;\\r\\n    margin-right: 10px;\\r\\n  }\\r\\n\\r\\n  .header-content[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n    text-align: center;\\r\\n    gap: 16px;\\r\\n  }\\r\\n\\r\\n  .profile-photo[_ngcontent-%COMP%] {\\r\\n    width: 120px;\\r\\n    height: 120px;\\r\\n  }\\r\\n\\r\\n  .profile-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: row;\\r\\n    align-items: center;\\r\\n  }\\r\\n\\r\\n  .profile-content[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .portfolio-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .analytics-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .profile-name[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .profile-title[_ngcontent-%COMP%] {\\r\\n    font-size: 1.2rem;\\r\\n  }\\r\\n\\r\\n  .profile-meta[_ngcontent-%COMP%] {\\r\\n    justify-content: center;\\r\\n  }\\r\\n\\r\\n  .experience-header[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  .blog-post[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n\\r\\n  .blog-image[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    height: 120px;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;;;;;;;;;;ICc3CC,0BAA0D;;;;;IAK1DA,8BAA4D;IAAAA,YAA+B;IAAAA,iBAAK;;;;IAApCA,eAA+B;IAA/BA,sDAA+B;;;;;IAC3FA,6BAAqD;IAAAA,YAAsB;IAAAA,iBAAI;;;;IAA1BA,eAAsB;IAAtBA,6CAAsB;;;;;IAGzEA,+BAAgE;IACpDA,2BAAW;IAAAA,iBAAW;IAChCA,4BAAM;IAAAA,YAAuC;IAAAA,iBAAO;;;;IAA9CA,eAAuC;IAAvCA,sGAAuC;;;;;IAM/CA,+BAAiD;IACrCA,0BAAU;IAAAA,iBAAW;IAC/BA,4BAAM;IAAAA,YAAwC;IAAAA,iBAAO;;;;IAA/CA,eAAwC;IAAxCA,wEAAwC;;;;;;IAMlDA,kCAKwB;IADtBA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IAE1BA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAC1BA,yBACF;IAAAA,iBAAS;;;;;;IAETA,kCAIuB;IADrBA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IAEzBA,gCAAU;IAAAA,oBAAI;IAAAA,iBAAW;IACzBA,8BACF;IAAAA,iBAAS;;;;;;IAETA,kCAG2C;IADzCA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAExBA,gCAAU;IAAAA,0BAAU;IAAAA,iBAAW;IAC/BA,wBACF;IAAAA,iBAAS;;;;;IAcPA,kCAA4C;IAChCA,oBAAI;IAAAA,iBAAW;IACzBA,4BAAM;IAAAA,sBAAM;IAAAA,iBAAO;;;;;IAO3BA,+BAAqD;IAE3CA,kCAAkB;IAAAA,iBAAO;IAC/BA,gCAAoC;IAAAA,YAA0C;IAAAA,iBAAO;IAEvFA,uCAGmB;IACrBA,iBAAM;;;;IANkCA,eAA0C;IAA1CA,2EAA0C;IAG9EA,eAA6C;IAA7CA,mEAA6C;;;;;IAY7CA,oCAAwE;IAGxDA,sBAAM;IAAAA,iBAAW;IAC3BA,uBACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAChBA,0BAA8D;IAChEA,iBAAmB;;;;IADSA,eAA6B;IAA7BA,sEAA6B;;;;;IA0B7CA,gCAA4C;IAACA,YAAoB;IAAAA,iBAAO;;;;IAA3BA,eAAoB;IAApBA,uDAAoB;;;;;IAIvEA,+BAA4D;IACvDA,YAAqB;IAAAA,iBAAI;;;;IAAzBA,eAAqB;IAArBA,yCAAqB;;;;;IAItBA,0BAAiD;IAAAA,YAAiB;IAAAA,iBAAK;;;;IAAtBA,eAAiB;IAAjBA,qCAAiB;;;;;IAFtEA,+BAAsE;IAElEA,mGAAuE;IACzEA,iBAAK;;;;IADyBA,eAAmB;IAAnBA,8CAAmB;;;;;IArBrDA,+BAAqE;IAEjEA,0BAGuB;IACvBA,+BAA6B;IACNA,YAAkB;IAAAA,iBAAK;IAC5CA,8BAAoB;IAAAA,YAAiB;IAAAA,iBAAK;IAC1CA,6BAAoB;IAClBA,YAEA;IAAAA,kGAAwE;IAC1EA,iBAAI;IAGRA,gGAEM;IACNA,gGAIM;IACRA,iBAAM;;;;;IArBAA,eAAkE;IAAlEA,sGAAkE;IAI7CA,eAAkB;IAAlBA,sCAAkB;IACnBA,eAAiB;IAAjBA,qCAAiB;IAEnCA,eAEA;IAFAA,kJAEA;IAAwBA,eAAkB;IAAlBA,uCAAkB;IAIXA,eAAqB;IAArBA,0CAAqB;IAGpBA,eAA8B;IAA9BA,wFAA8B;;;;;IA5B5EA,oCAAwF;IAGxEA,oBAAI;IAAAA,iBAAW;IACzBA,4BACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAEdA,yFAwBM;IACRA,iBAAM;;;;IAzByCA,eAAsB;IAAtBA,qDAAsB;;;;;IAyC/DA,0BAAgF;;;;IAA3EA,6DAAyB;;;;;IAO1BA,gCAA+D;IAAAA,YAAU;IAAAA,iBAAO;;;;IAAjBA,eAAU;IAAVA,8BAAU;;;;;IAI3EA,6BAA+E;IACnEA,sBAAM;IAAAA,iBAAW;IAC3BA,8BACF;IAAAA,iBAAI;;;;IAHUA,4DAAwB;;;;;IAItCA,6BAA6E;IACjEA,oBAAI;IAAAA,iBAAW;IACzBA,2BACF;IAAAA,iBAAI;;;;IAHUA,2DAAuB;;;;;IALvCA,+BAAuE;IACrEA,iGAGI;IACJA,iGAGI;IACNA,iBAAM;;;;IARoDA,eAAqB;IAArBA,0CAAqB;IAItBA,eAAoB;IAApBA,yCAAoB;;;;;IAjBjFA,+BAAwE;IAEpEA,8FAAgF;IAClFA,iBAAM;IACNA,+BAA4B;IACtBA,YAAgB;IAAAA,iBAAK;IACzBA,yBAAG;IAAAA,YAAsB;IAAAA,iBAAI;IAC7BA,+BAA4B;IAExBA,kGAAgF;IAClFA,iBAAM;IAERA,gGASM;IACRA,iBAAM;;;;IApB+CA,eAA2B;IAA3BA,gDAA2B;IAG1EA,eAAgB;IAAhBA,oCAAgB;IACjBA,eAAsB;IAAtBA,0CAAsB;IAGoBA,eAAoB;IAApBA,+CAAoB;IAGnCA,eAAuC;IAAvCA,gEAAuC;;;;;IArB/EA,oCAA0F;IAG1EA,4BAAY;IAAAA,iBAAW;IACjCA,2BACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAEdA,yFAuBM;IACRA,iBAAM;;;;IAxByCA,eAAyB;IAAzBA,wDAAyB;;;;;IAuCpEA,+BAAsD;IACpDA,0BAAsD;IACxDA,iBAAM;;;;IADCA,eAA6B;IAA7BA,iEAA6B;;;;;IAFtCA,+BAAkE;IAChEA,8FAEM;IACNA,+BAA0B;IACpBA,YAAgB;IAAAA,iBAAK;IACzBA,yBAAG;IAAAA,YAAkB;IAAAA,iBAAI;IACzBA,+BAAuB;IACMA,YAAkC;IAAAA,iBAAO;IACpEA,iCAAyB;IAAAA,aAA0B;IAAAA,iBAAO;;;;;IARrCA,eAA2B;IAA3BA,gDAA2B;IAI9CA,eAAgB;IAAhBA,oCAAgB;IACjBA,eAAkB;IAAlBA,sCAAkB;IAEQA,eAAkC;IAAlCA,8DAAkC;IACpCA,eAA0B;IAA1BA,uDAA0B;;;;;IAlB/DA,oCAAwE;IAGxDA,uBAAO;IAAAA,iBAAW;IAC5BA,iCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAEdA,yFAYM;IACRA,iBAAM;;;;IAboCA,eAAwB;IAAxBA,uDAAwB;;;;;IA6BlEA,+BAAiG;IACrFA,qBAAK;IAAAA,iBAAW;IAC1BA,8BAAkD;IAAAA,YAA+B;IAAAA,iBAAI;;;;IAAlFA,eAA8C;IAA9CA,sFAA8C;IAACA,eAA+B;IAA/BA,uDAA+B;;;;;IAEnFA,gCAA4G;IAChGA,qBAAK;IAAAA,iBAAW;IAC1BA,8BAAkC;IAAAA,YAAqC;IAAAA,iBAAI;;;;IAFIA,4CAA0B;IAEtGA,eAA8B;IAA9BA,kEAA8B;IAACA,eAAqC;IAArCA,sEAAqC;;;;;IAEzEA,+BAA8D;IAClDA,wBAAQ;IAAAA,iBAAW;IAC7BA,8BAAwD;IAAAA,uBAAO;IAAAA,iBAAI;;;;IAAhEA,eAAoC;IAApCA,4EAAoC;;;;;IAEzCA,+BAAmE;IACvDA,oBAAI;IAAAA,iBAAW;IACzBA,8BAA6D;IAAAA,yBAAS;IAAAA,iBAAI;;;;IAAvEA,eAAyC;IAAzCA,iFAAyC;;;;;;IAsBxCA,mCAKkC;IAFhCA;MAAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAGlCA,gCAAU;IAAAA,wBAAQ;IAAAA,iBAAW;;;;;IAXjCA,gCAA6D;IAEhCA,YAAgB;IAAAA,iBAAO;IAChDA,iCAAgC;IAAAA,YAAqC;IAAAA,iBAAO;IAE9EA,2GAOS;IACXA,iBAAM;;;;;IAXuBA,eAAgB;IAAhBA,oCAAgB;IACTA,eAAqC;IAArCA,kEAAqC;IAMpEA,eAAwE;IAAxEA,6GAAwE;;;;;IAZjFA,gCAA0F;IAC7DA,YAAkB;IAAAA,iBAAK;IAClDA,gCAAyB;IACvBA,+FAaM;IACRA,iBAAM;;;;IAhBqBA,eAAkB;IAAlBA,sCAAkB;IAEDA,eAAiB;IAAjBA,4CAAiB;;;;;IAXnEA,qCAA+E;IAG/DA,0BAAU;IAAAA,iBAAW;IAC/BA,oCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAChBA,yFAkBM;;IACRA,iBAAmB;;;;IAnBoCA,eAAmC;IAAnCA,6EAAmC;;;;;IAwClFA,iCAA+D;IAACA,YAAgC;IAAAA,iBAAO;;;;IAAvCA,eAAgC;IAAhCA,mEAAgC;;;;;IARpGA,gCAA+E;IAE3EA,0BAAwG;IAC1GA,iBAAM;IACNA,gCAA8B;IACxBA,YAAuB;IAAAA,iBAAK;IAChCA,yBAAG;IAAAA,YAA6B;IAAAA,iBAAI;IACpCA,iCAA+B;IAAAA,YAAwC;IAAAA,iBAAO;IAC9EA,mGAAuG;IACzGA,iBAAM;;;;;IAPCA,eAAwE;IAAxEA,4GAAwE;IAGzEA,eAAuB;IAAvBA,2CAAuB;IACxBA,eAA6B;IAA7BA,iDAA6B;IACDA,eAAwC;IAAxCA,oEAAwC;IACxCA,eAA8B;IAA9BA,mDAA8B;;;;;IAjBvEA,qCAA2F;IAG3EA,4BAAY;IAAAA,iBAAW;IACjCA,8BACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAEdA,0FAUM;IACRA,iBAAM;;;;IAXkDA,eAAuB;IAAvBA,sDAAuB;;;;;IAkCvEA,4BAAkC;IAACA,YAA+C;IAAAA,iBAAO;;;;;IAAtDA,eAA+C;IAA/CA,0FAA+C;;;;;IAEpFA,8BAAkG;IACtFA,sBAAM;IAAAA,iBAAW;IAC3BA,iCACF;IAAAA,iBAAI;;;;IAHDA,+DAA2B;;;;;IAXlCA,gCAA4E;IAExEA,0BAA2F;IAC7FA,iBAAM;IACNA,gCAAgC;IAC1BA,YAAe;IAAAA,iBAAK;IACxBA,yBAAG;IAAAA,YAA8B;IAAAA,iBAAI;IACrCA,iCAAiC;IAC/BA,YACA;IAAAA,mGAAyF;IAC3FA,iBAAO;IACPA,6FAGI;IACNA,iBAAM;;;;;IAbCA,eAAmE;IAAnEA,uGAAmE;IAGpEA,eAAe;IAAfA,mCAAe;IAChBA,eAA8B;IAA9BA,kDAA8B;IAE/BA,eACA;IADAA,8EACA;IAAOA,eAAyB;IAAzBA,8CAAyB;IAEcA,eAAwB;IAAxBA,6CAAwB;;;;;IApBlFA,qCAA+F;IAG/EA,wBAAQ;IAAAA,iBAAW;IAC7BA,gCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAEdA,0FAgBM;IACRA,iBAAM;;;;IAjB6CA,eAAyB;IAAzBA,wDAAyB;;;;;IAsBhFA,qCAAsF;IAGtEA,yBAAS;IAAAA,iBAAW;IAC9BA,mCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAGkBA,aAA4B;IAAAA,iBAAO;IACjEA,kCAA8B;IAAAA,4BAAW;IAAAA,iBAAO;IAElDA,iCAA4B;IACIA,aAA8B;IAAAA,iBAAO;IACnEA,kCAA8B;IAAAA,gCAAe;IAAAA,iBAAO;IAEtDA,iCAA4B;IACIA,aAAiC;IAAAA,iBAAO;IACtEA,kCAA8B;IAAAA,mCAAkB;IAAAA,iBAAO;IAEzDA,iCAA4B;IACIA,aAAmC;IAAAA,iBAAO;IACxEA,kCAA8B;IAAAA,+BAAc;IAAAA,iBAAO;;;;IAbrBA,gBAA4B;IAA5BA,oDAA4B;IAI5BA,eAA8B;IAA9BA,sDAA8B;IAI9BA,eAAiC;IAAjCA,yDAAiC;IAIjCA,eAAmC;IAAnCA,2DAAmC;;;;;;IA5WjFA,8BAA6D;IAIvDA,yBAAiC;IACnCA,iBAAM;IAIRA,8BAA4B;IAGtBA,yBAGwB;IACxBA,4EAA0D;IAC5DA,iBAAM;IAENA,+BAA0B;IACCA,aAA8C;IAAAA,iBAAK;IAC5EA,4EAAgG;IAChGA,0EAA+E;IAE/EA,gCAA0B;IACxBA,8EAGM;IACNA,gCAA8B;IAClBA,qBAAI;IAAAA,iBAAW;IACzBA,6BAAM;IAAAA,aAA2C;IAAAA,iBAAO;IAE1DA,8EAGM;IACRA,iBAAM;IAGRA,gCAA6B;IAC3BA,oFAQS;IAETA,oFAOS;IAETA,oFAMS;IAETA,mCAGuB;IACXA,0BAAS;IAAAA,iBAAW;IAGhCA,2CAAiC;IACTA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IAC9CA,iCAAU;IAAAA,sBAAK;IAAAA,iBAAW;IAC1BA,6BAAM;IAAAA,8BAAa;IAAAA,iBAAO;IAE5BA,oFAGS;IACXA,iBAAW;IAKfA,8EASM;IACRA,iBAAM;IAGNA,gCAA6B;IAKvBA,wFAUW;IAGXA,wFAoCW;IAGXA,wFAmCW;IAGXA,wFAwBW;IACbA,iBAAM;IAGNA,gCAA0B;IAKRA,6BAAY;IAAAA,iBAAW;IACjCA,sCACF;IAAAA,iBAAiB;IAEnBA,yCAAkB;IAChBA,8EAGM;IACNA,8EAGM;IACNA,8EAGM;IACNA,8EAGM;IACRA,iBAAmB;IAIrBA,wFA4BW;IAGXA,wFAsBW;IAGXA,wFA4BW;IAGXA,yFA2BW;IACbA,iBAAM;;;;;IA/WiBA,eAAyH;IAAzHA,gJAAyH;IAU5IA,eAAsE;IAAtEA,6GAAsE;IAGzCA,eAAmB;IAAnBA,2CAAmB;IAIzBA,eAA8C;IAA9CA,qFAA8C;IAC5CA,eAA+B;IAA/BA,uDAA+B;IAC7BA,eAAsB;IAAtBA,8CAAsB;IAG1BA,eAAuC;IAAvCA,uGAAuC;IAMtDA,eAA2C;IAA3CA,2EAA2C;IAEvBA,eAAmB;IAAnBA,2CAAmB;IAa9CA,eAAmB;IAAnBA,2CAAmB;IASnBA,eAAkB;IAAlBA,0CAAkB;IAQlBA,eAAsC;IAAtCA,qEAAsC;IAOvCA,eAAiC;IAAjCA,wCAAiC;IAUVA,gBAAmB;IAAnBA,2CAAmB;IASfA,eAAkB;IAAlBA,0CAAkB;IAkBEA,eAAqB;IAArBA,6CAAqB;IAahBA,eAAgC;IAAhCA,wDAAgC;IAuCjCA,eAAmC;IAAnCA,2DAAmC;IAsCxCA,eAAsB;IAAtBA,8CAAsB;IAsCvCA,eAAoE;IAApEA,mGAAoE;IAInDA,eAAmC;IAAnCA,iEAAmC;IAIpDA,eAAiC;IAAjCA,yDAAiC;IAIjCA,eAAsC;IAAtCA,8DAAsC;IAQnBA,eAA2B;IAA3BA,mDAA2B;IA+BrBA,eAAiC;IAAjCA,yDAAiC;IAyB/BA,eAAmC;IAAnCA,2DAAmC;IA+BxCA,eAA+B;IAA/BA,8DAA+B;;;;;IAkC5FA,gCAAiD;IAC/CA,mCAAyC;IACzCA,yBAAG;IAAAA,kCAAkB;IAAAA,iBAAI;;;ADhX3B;AAOA,OAAM,MAAOC,oBAAoB;EAY/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB,EACrBC,MAAiB,EACjBC,YAAmB,EACnBC,WAAiB;IAPjB,UAAK,GAALP,KAAK;IACL,WAAM,GAANC,MAAM;IACN,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,aAAQ,GAARC,QAAQ;IACR,WAAM,GAANC,MAAM;IACN,iBAAY,GAAZC,YAAY;IACZ,gBAAW,GAAXC,WAAW;IAnBrB,YAAO,GAAuB,IAAI;IAClC,cAAS,GAA4B,IAAI;IACzC,cAAS,GAAe,EAAE;IAE1B,iBAAY,GAAG,KAAK;IACpB,cAAS,GAAG,IAAI;IAChB,oBAAe,GAAG,KAAK;IACvB,kBAAa,GAAkB,IAAI;IAE3B,aAAQ,GAAG,IAAIb,OAAO,EAAQ;EAWnC;EAEHc,QAAQ;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAW;IACT,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;IACpB,IAAI,CAACD,QAAQ,CAACE,QAAQ,EAAE;EAC1B;EAEQJ,mBAAmB;IACzB;IACA,IAAI,CAACN,WAAW,CAACW,gBAAgB,CAC9BC,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACC,eAAe,GAAGD,MAAM;IAC/B,CAAC,CAAC;IAEJ,IAAI,CAACd,WAAW,CAACgB,YAAY,CAC1BJ,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACI,IAAI,IAAG;MAChB,IAAI,CAACC,aAAa,GAAGD,IAAI,EAAEE,EAAE,IAAI,IAAI;IACvC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACtB,KAAK,CAACuB,MAAM,CACdR,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACO,MAAM,IAAG;MAClB,MAAMC,UAAU,GAAGD,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;MACzC,IAAIC,UAAU,EAAE;QACd,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;;IAEhC,CAAC,CAAC;EACN;EAEQC,WAAW,CAACD,UAAkB;IACpC,IAAI,CAACE,SAAS,GAAG,IAAI;IAErB,MAAMC,cAAc,GAAG,IAAI,CAACzB,cAAc,CAAC0B,UAAU,CAACJ,UAAU,CAAC;IACjE,MAAMK,gBAAgB,GAAG,IAAI,CAAC3B,cAAc,CAAC4B,YAAY,CAACC,QAAQ,CAACP,UAAU,CAAC,IAAI,CAAC,CAAC;IAEpF5B,QAAQ,CAAC;MACPoC,OAAO,EAAEL,cAAc;MACvBM,SAAS,EAAEJ;KACZ,CAAC,CAACd,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAChCK,SAAS,CAAC;MACTJ,IAAI,EAAE,CAAC;QAAEoB,OAAO;QAAEC;MAAS,CAAE,KAAI;QAC/B,IAAI,CAACD,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,YAAY,GAAG,IAAI,CAACb,aAAa,KAAKW,OAAO,CAACG,MAAM;QAEzD;QACA,IAAI,IAAI,CAACD,YAAY,EAAE;UACrB,IAAI,CAACE,aAAa,EAAE;;QAGtB;QACA,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;UACtB,IAAI,CAACG,iBAAiB,EAAE;;QAG1B;QACA,IAAI,CAACC,cAAc,EAAE;QAErB,IAAI,CAACZ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDa,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACnC,QAAQ,CAACqC,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAACjB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQU,aAAa;IACnB,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;IAExB,IAAI,CAAChC,cAAc,CAAC0C,mBAAmB,EAAE,CACtC7B,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAAC;MACTJ,IAAI,EAAGiC,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;MAC5B,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEQF,iBAAiB;IACvB,IAAI,CAAC,IAAI,CAACL,OAAO,EAAE;IAEnB,IAAI,CAAC9B,cAAc,CAACmC,iBAAiB,CAAC;MACpCS,SAAS,EAAE,IAAI,CAACd,OAAO,CAACV,EAAE;MAC1ByB,YAAY,EAAE,IAAI,CAAC1B,aAAa,IAAI2B,SAAS;MAC7CC,QAAQ,EAAEC,QAAQ,CAACD,QAAQ;MAC3BE,SAAS,EAAEC,SAAS,CAACD;KACtB,CAAC,CAACnC,SAAS,CAAC;MACXuB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEQD,cAAc;IACpB,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE;IAEnB,MAAMqB,KAAK,GAAG,GAAG,IAAI,CAACrB,OAAO,CAACsB,SAAS,IAAI,IAAI,CAACtB,OAAO,CAACuB,QAAQ,MAAM,IAAI,CAACvB,OAAO,CAACwB,iBAAiB,IAAI,sBAAsB,EAAE;IAChI,MAAMC,WAAW,GAAG,IAAI,CAACzB,OAAO,CAAC0B,OAAO,IAAI,QAAQ,IAAI,CAAC1B,OAAO,CAACsB,SAAS,IAAI,IAAI,CAACtB,OAAO,CAACuB,QAAQ,qDAAqD;IACxJ,MAAMI,QAAQ,GAAG,IAAI,CAAC3B,OAAO,CAAC4B,eAAe,IAAI,oCAAoC;IACrF,MAAMC,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAY,IAAI,CAAChC,OAAO,CAACiC,IAAI,EAAE;IAE3E;IACA,IAAI,CAAC3D,YAAY,CAAC4D,QAAQ,CAACb,KAAK,CAAC;IAEjC;IACA,IAAI,CAAC9C,WAAW,CAAC4D,SAAS,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEZ;IAAW,CAAE,CAAC;IACzE,IAAI,CAAClD,WAAW,CAAC4D,SAAS,CAAC;MAAEG,QAAQ,EAAE,UAAU;MAAED,OAAO,EAAEhB;IAAK,CAAE,CAAC;IACpE,IAAI,CAAC9C,WAAW,CAAC4D,SAAS,CAAC;MAAEG,QAAQ,EAAE,gBAAgB;MAAED,OAAO,EAAEZ;IAAW,CAAE,CAAC;IAChF,IAAI,CAAClD,WAAW,CAAC4D,SAAS,CAAC;MAAEG,QAAQ,EAAE,UAAU;MAAED,OAAO,EAAEV;IAAQ,CAAE,CAAC;IACvE,IAAI,CAACpD,WAAW,CAAC4D,SAAS,CAAC;MAAEG,QAAQ,EAAE,QAAQ;MAAED,OAAO,EAAER;IAAU,CAAE,CAAC;IACvE,IAAI,CAACtD,WAAW,CAAC4D,SAAS,CAAC;MAAEG,QAAQ,EAAE,SAAS;MAAED,OAAO,EAAE;IAAS,CAAE,CAAC;IACvE,IAAI,CAAC9D,WAAW,CAAC4D,SAAS,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAqB,CAAE,CAAC;IACpF,IAAI,CAAC9D,WAAW,CAAC4D,SAAS,CAAC;MAAEC,IAAI,EAAE,eAAe;MAAEC,OAAO,EAAEhB;IAAK,CAAE,CAAC;IACrE,IAAI,CAAC9C,WAAW,CAAC4D,SAAS,CAAC;MAAEC,IAAI,EAAE,qBAAqB;MAAEC,OAAO,EAAEZ;IAAW,CAAE,CAAC;IACjF,IAAI,CAAClD,WAAW,CAAC4D,SAAS,CAAC;MAAEC,IAAI,EAAE,eAAe;MAAEC,OAAO,EAAEV;IAAQ,CAAE,CAAC;EAC1E;EAEA;EACAY,cAAc;IACZ,IAAI,CAAC,IAAI,CAACvC,OAAO,EAAE;IAEnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;IACA,IAAI,CAAC5B,QAAQ,CAACqC,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACjF;EAEA8B,aAAa;IACX,IAAI,CAACvE,MAAM,CAAC0C,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEA8B,cAAc;IACZ,IAAI,CAAC,IAAI,CAACzC,OAAO,EAAE;IAEnB,IAAIoB,SAAS,CAACsB,KAAK,EAAE;MACnBtB,SAAS,CAACsB,KAAK,CAAC;QACdrB,KAAK,EAAE,GAAG,IAAI,CAACrB,OAAO,CAACsB,SAAS,IAAI,IAAI,CAACtB,OAAO,CAACuB,QAAQ,yBAAyB;QAClFoB,IAAI,EAAE,IAAI,CAAC3C,OAAO,CAAC0B,OAAO,IAAI,qCAAqC;QACnEkB,GAAG,EAAEd,MAAM,CAACC,QAAQ,CAACc;OACtB,CAAC,CAACC,KAAK,CAACtC,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACAa,SAAS,CAAC2B,SAAS,CAACC,SAAS,CAAClB,MAAM,CAACC,QAAQ,CAACc,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;QAC5D,IAAI,CAAC7E,QAAQ,CAACqC,IAAI,CAAC,kCAAkC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACrF,CAAC,CAAC;;EAEN;EAEAwC,cAAc,CAACC,OAAe;IAC5B,IAAI,CAAC,IAAI,CAACjE,eAAe,IAAI,CAAC,IAAI,CAACc,OAAO,IAAI,IAAI,CAACE,YAAY,EAAE;IAEjE,IAAI,CAAChC,cAAc,CAACkF,YAAY,CAAC;MAC/BD,OAAO,EAAEA,OAAO;MAChBE,cAAc,EAAE,IAAI,CAACrD,OAAO,CAACG;KAC9B,CAAC,CAACnB,SAAS,CAAC;MACXJ,IAAI,EAAE,MAAK;QACT,IAAI,CAACR,QAAQ,CAACqC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAClE;QACA,IAAI,CAACjB,WAAW,CAAC,IAAI,CAACO,OAAQ,CAACiC,IAAI,CAAC;MACtC,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACnC,QAAQ,CAACqC,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC5E;KACD,CAAC;EACJ;EAEA4C,YAAY;IACV;IACA,IAAI,CAAClF,QAAQ,CAACqC,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EAChF;EAEA;EACA6C,yBAAyB;IACvB,IAAI,CAAC,IAAI,CAACvD,OAAO,EAAE,OAAO,MAAM;IAEhC,MAAMwD,UAAU,GAAG,IAAI,CAACxD,OAAO,CAACyD,2BAA2B;IAC3D,IAAID,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS;IACtC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ;IACrC,OAAO,MAAM;EACf;EAEAE,kBAAkB;IAChB,IAAI,CAAC,IAAI,CAAC1D,OAAO,EAAE2D,WAAW,CAACC,MAAM,EAAE,OAAO,CAAC;IAE/C,MAAMC,WAAW,GAAG,IAAI,CAAC7D,OAAO,CAAC2D,WAAW,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;MACjE,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACF,GAAG,CAACG,SAAS,CAAC;MACrC,MAAMC,GAAG,GAAGJ,GAAG,CAACK,OAAO,GAAG,IAAIH,IAAI,CAACF,GAAG,CAACK,OAAO,CAAC,GAAG,IAAIH,IAAI,EAAE;MAC5D,MAAMI,MAAM,GAAG,CAACF,GAAG,CAACG,WAAW,EAAE,GAAGN,KAAK,CAACM,WAAW,EAAE,IAAI,EAAE,IAAIH,GAAG,CAACI,QAAQ,EAAE,GAAGP,KAAK,CAACO,QAAQ,EAAE,CAAC;MACnG,OAAOT,KAAK,GAAGO,MAAM;IACvB,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOG,IAAI,CAACC,KAAK,CAACb,WAAW,GAAG,EAAE,CAAC;EACrC;EAEAc,UAAU,CAACC,IAAmB;IAC5B,OAAO,IAAIV,IAAI,CAACU,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE;KACR,CAAC;EACJ;EAEAC,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAAChF,OAAO,EAAEiF,MAAM,CAACrB,MAAM,EAAE,OAAO,EAAE;IAE3C,OAAO,IAAI,CAAC5D,OAAO,CAACiF,MAAM,CAACnB,MAAM,CAAC,CAACoB,GAAG,EAAEC,KAAK,KAAI;MAC/C,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAI,OAAO;MAC1C,IAAI,CAACF,GAAG,CAACE,QAAQ,CAAC,EAAEF,GAAG,CAACE,QAAQ,CAAC,GAAG,EAAE;MACtCF,GAAG,CAACE,QAAQ,CAAC,CAACC,IAAI,CAACF,KAAK,CAAC;MACzB,OAAOD,GAAG;IACZ,CAAC,EAAE,EAAmC,CAAC;EACzC;;;uBAlQWpH,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAwH;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCjBjC7H,uEAqXM;UAGNA,qEAGM;;;UA3X0BA,oDAA2B;UAwX3BA,eAAe;UAAfA,oCAAe", "names": ["Subject", "takeUntil", "fork<PERSON><PERSON>n", "i0", "ProfileViewComponent", "constructor", "route", "router", "profileService", "authService", "snackBar", "dialog", "titleService", "metaService", "ngOnInit", "initializeComponent", "ngOnDestroy", "destroy$", "next", "complete", "isAuthenticated$", "pipe", "subscribe", "isAuth", "isAuthenticated", "currentUser$", "user", "currentUserId", "id", "params", "identifier", "loadProfile", "isLoading", "profileRequest", "getProfile", "blogPostsRequest", "getBlogPosts", "parseInt", "profile", "blogPosts", "isOwnProfile", "userId", "loadAnalytics", "recordProfileView", "setSEOMetadata", "error", "console", "open", "duration", "navigate", "getProfileAnalytics", "analytics", "profileId", "viewerUserId", "undefined", "referrer", "document", "userAgent", "navigator", "title", "firstName", "lastName", "professional<PERSON>itle", "description", "summary", "imageUrl", "profilePhotoUrl", "profileUrl", "window", "location", "origin", "slug", "setTitle", "updateTag", "name", "content", "property", "onContactClick", "onEditProfile", "onShareProfile", "share", "text", "url", "href", "catch", "clipboard", "writeText", "then", "onEndorseSkill", "skillId", "endorseSkill", "endorsedUserId", "onFollowUser", "getProfileCompletionColor", "percentage", "profileCompletionPercentage", "getExperienceYears", "experiences", "length", "totalMonths", "reduce", "total", "exp", "start", "Date", "startDate", "end", "endDate", "months", "getFullYear", "getMonth", "Math", "floor", "formatDate", "date", "toLocaleDateString", "year", "month", "getSkillsByCategory", "skills", "acc", "skill", "category", "push", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-view\\profile-view.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\profile-view\\profile-view.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { Title, Meta } from '@angular/platform-browser';\r\n\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport { AuthService } from '../../../auth/services/auth.service';\r\nimport { UserProfile, ProfileAnalytics, BlogPost } from '../../models/profile.models';\r\n// import { ContactDialogComponent } from '../contact-dialog/contact-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-profile-view',\r\n  templateUrl: './profile-view.component.html',\r\n  styleUrls: ['./profile-view.component.css']\r\n})\r\nexport class ProfileViewComponent implements OnInit, OnDestroy {\r\n  profile: UserProfile | null = null;\r\n  analytics: ProfileAnalytics | null = null;\r\n  blogPosts: BlogPost[] = [];\r\n\r\n  isOwnProfile = false;\r\n  isLoading = true;\r\n  isAuthenticated = false;\r\n  currentUserId: number | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private profileService: ProfileService,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar,\r\n    private dialog: MatDialog,\r\n    private titleService: Title,\r\n    private metaService: Meta\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    // Check authentication status\r\n    this.authService.isAuthenticated$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(isAuth => {\r\n        this.isAuthenticated = isAuth;\r\n      });\r\n\r\n    this.authService.currentUser$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(user => {\r\n        this.currentUserId = user?.id || null;\r\n      });\r\n\r\n    // Get profile identifier from route\r\n    this.route.params\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(params => {\r\n        const identifier = params['identifier']; // username or user-id\r\n        if (identifier) {\r\n          this.loadProfile(identifier);\r\n        }\r\n      });\r\n  }\r\n\r\n  private loadProfile(identifier: string): void {\r\n    this.isLoading = true;\r\n\r\n    const profileRequest = this.profileService.getProfile(identifier);\r\n    const blogPostsRequest = this.profileService.getBlogPosts(parseInt(identifier) || 0);\r\n\r\n    forkJoin({\r\n      profile: profileRequest,\r\n      blogPosts: blogPostsRequest\r\n    }).pipe(takeUntil(this.destroy$))\r\n    .subscribe({\r\n      next: ({ profile, blogPosts }) => {\r\n        this.profile = profile;\r\n        this.blogPosts = blogPosts;\r\n        this.isOwnProfile = this.currentUserId === profile.userId;\r\n\r\n        // Load analytics if it's own profile\r\n        if (this.isOwnProfile) {\r\n          this.loadAnalytics();\r\n        }\r\n\r\n        // Record profile view if not own profile\r\n        if (!this.isOwnProfile) {\r\n          this.recordProfileView();\r\n        }\r\n\r\n        // Set SEO metadata\r\n        this.setSEOMetadata();\r\n\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading profile:', error);\r\n        this.snackBar.open('Profile not found', 'Close', { duration: 5000 });\r\n        this.router.navigate(['/']);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadAnalytics(): void {\r\n    if (!this.isOwnProfile) return;\r\n\r\n    this.profileService.getProfileAnalytics()\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (analytics) => {\r\n          this.analytics = analytics;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading analytics:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  private recordProfileView(): void {\r\n    if (!this.profile) return;\r\n\r\n    this.profileService.recordProfileView({\r\n      profileId: this.profile.id,\r\n      viewerUserId: this.currentUserId || undefined,\r\n      referrer: document.referrer,\r\n      userAgent: navigator.userAgent\r\n    }).subscribe({\r\n      error: (error) => {\r\n        console.error('Error recording profile view:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setSEOMetadata(): void {\r\n    if (!this.profile) return;\r\n\r\n    const title = `${this.profile.firstName} ${this.profile.lastName} - ${this.profile.professionalTitle || 'Professional Profile'}`;\r\n    const description = this.profile.summary || `View ${this.profile.firstName} ${this.profile.lastName}'s professional profile, experience, and portfolio.`;\r\n    const imageUrl = this.profile.profilePhotoUrl || '/assets/images/default-profile.jpg';\r\n    const profileUrl = `${window.location.origin}/profile/${this.profile.slug}`;\r\n\r\n    // Set page title\r\n    this.titleService.setTitle(title);\r\n\r\n    // Set meta tags\r\n    this.metaService.updateTag({ name: 'description', content: description });\r\n    this.metaService.updateTag({ property: 'og:title', content: title });\r\n    this.metaService.updateTag({ property: 'og:description', content: description });\r\n    this.metaService.updateTag({ property: 'og:image', content: imageUrl });\r\n    this.metaService.updateTag({ property: 'og:url', content: profileUrl });\r\n    this.metaService.updateTag({ property: 'og:type', content: 'profile' });\r\n    this.metaService.updateTag({ name: 'twitter:card', content: 'summary_large_image' });\r\n    this.metaService.updateTag({ name: 'twitter:title', content: title });\r\n    this.metaService.updateTag({ name: 'twitter:description', content: description });\r\n    this.metaService.updateTag({ name: 'twitter:image', content: imageUrl });\r\n  }\r\n\r\n  // Action Methods\r\n  onContactClick(): void {\r\n    if (!this.profile) return;\r\n\r\n    // TODO: Implement contact dialog\r\n    // const dialogRef = this.dialog.open(ContactDialogComponent, {\r\n    //   width: '500px',\r\n    //   data: {\r\n    //     profile: this.profile,\r\n    //     isAuthenticated: this.isAuthenticated\r\n    //   }\r\n    // });\r\n\r\n    // dialogRef.afterClosed().subscribe(result => {\r\n    //   if (result) {\r\n    //     this.snackBar.open('Message sent successfully!', 'Close', { duration: 3000 });\r\n    //   }\r\n    // });\r\n\r\n    // Temporary implementation\r\n    this.snackBar.open('Contact feature coming soon!', 'Close', { duration: 2000 });\r\n  }\r\n\r\n  onEditProfile(): void {\r\n    this.router.navigate(['/profile/edit']);\r\n  }\r\n\r\n  onShareProfile(): void {\r\n    if (!this.profile) return;\r\n\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: `${this.profile.firstName} ${this.profile.lastName} - Professional Profile`,\r\n        text: this.profile.summary || 'Check out this professional profile',\r\n        url: window.location.href\r\n      }).catch(console.error);\r\n    } else {\r\n      // Fallback: copy to clipboard\r\n      navigator.clipboard.writeText(window.location.href).then(() => {\r\n        this.snackBar.open('Profile URL copied to clipboard!', 'Close', { duration: 3000 });\r\n      });\r\n    }\r\n  }\r\n\r\n  onEndorseSkill(skillId: number): void {\r\n    if (!this.isAuthenticated || !this.profile || this.isOwnProfile) return;\r\n\r\n    this.profileService.endorseSkill({\r\n      skillId: skillId,\r\n      endorsedUserId: this.profile.userId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.snackBar.open('Skill endorsed!', 'Close', { duration: 2000 });\r\n        // Refresh profile to update endorsement count\r\n        this.loadProfile(this.profile!.slug);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error endorsing skill:', error);\r\n        this.snackBar.open('Failed to endorse skill', 'Close', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  onFollowUser(): void {\r\n    // TODO: Implement follow functionality\r\n    this.snackBar.open('Follow feature coming soon!', 'Close', { duration: 2000 });\r\n  }\r\n\r\n  // Utility Methods\r\n  getProfileCompletionColor(): string {\r\n    if (!this.profile) return 'warn';\r\n\r\n    const percentage = this.profile.profileCompletionPercentage;\r\n    if (percentage >= 80) return 'primary';\r\n    if (percentage >= 50) return 'accent';\r\n    return 'warn';\r\n  }\r\n\r\n  getExperienceYears(): number {\r\n    if (!this.profile?.experiences.length) return 0;\r\n\r\n    const totalMonths = this.profile.experiences.reduce((total, exp) => {\r\n      const start = new Date(exp.startDate);\r\n      const end = exp.endDate ? new Date(exp.endDate) : new Date();\r\n      const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());\r\n      return total + months;\r\n    }, 0);\r\n\r\n    return Math.floor(totalMonths / 12);\r\n  }\r\n\r\n  formatDate(date: Date | string): string {\r\n    return new Date(date).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long'\r\n    });\r\n  }\r\n\r\n  getSkillsByCategory(): { [category: string]: any[] } {\r\n    if (!this.profile?.skills.length) return {};\r\n\r\n    return this.profile.skills.reduce((acc, skill) => {\r\n      const category = skill.category || 'Other';\r\n      if (!acc[category]) acc[category] = [];\r\n      acc[category].push(skill);\r\n      return acc;\r\n    }, {} as { [category: string]: any[] });\r\n  }\r\n}\r\n", "<div class=\"profile-container\" *ngIf=\"!isLoading && profile\">\r\n  <!-- Cover Photo Section -->\r\n  <div class=\"cover-section\">\r\n    <div class=\"cover-photo\" [style.background-image]=\"profile.coverPhotoUrl ? 'url(' + profile.coverPhotoUrl + ')' : 'var(--theme-gradient-primary)'\">\r\n      <div class=\"cover-overlay\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Profile Header -->\r\n  <div class=\"profile-header\">\r\n    <div class=\"header-content\">\r\n      <div class=\"profile-photo-container\">\r\n        <img\r\n          [src]=\"profile.profilePhotoUrl || '/assets/images/default-avatar.png'\"\r\n          [alt]=\"profile.firstName + ' ' + profile.lastName\"\r\n          class=\"profile-photo\">\r\n        <div class=\"online-indicator\" *ngIf=\"!isOwnProfile\"></div>\r\n      </div>\r\n\r\n      <div class=\"profile-info\">\r\n        <h1 class=\"profile-name\">{{ profile.firstName }} {{ profile.lastName }}</h1>\r\n        <h2 class=\"profile-title\" *ngIf=\"profile.professionalTitle\">{{ profile.professionalTitle }}</h2>\r\n        <p class=\"profile-headline\" *ngIf=\"profile.headline\">{{ profile.headline }}</p>\r\n\r\n        <div class=\"profile-meta\">\r\n          <div class=\"location\" *ngIf=\"profile.location?.displayLocation\">\r\n            <mat-icon>location_on</mat-icon>\r\n            <span>{{ profile.location?.displayLocation }}</span>\r\n          </div>\r\n          <div class=\"experience-years\">\r\n            <mat-icon>work</mat-icon>\r\n            <span>{{ getExperienceYears() }} years experience</span>\r\n          </div>\r\n          <div class=\"profile-views\" *ngIf=\"!isOwnProfile\">\r\n            <mat-icon>visibility</mat-icon>\r\n            <span>{{ profile.profileViews }} profile views</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"profile-actions\">\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          class=\"contact-btn\"\r\n          (click)=\"onContactClick()\"\r\n          *ngIf=\"!isOwnProfile\">\r\n          <mat-icon>email</mat-icon>\r\n          Contact\r\n        </button>\r\n\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          (click)=\"onEditProfile()\"\r\n          *ngIf=\"isOwnProfile\">\r\n          <mat-icon>edit</mat-icon>\r\n          Edit Profile\r\n        </button>\r\n\r\n        <button\r\n          mat-stroked-button\r\n          (click)=\"onFollowUser()\"\r\n          *ngIf=\"!isOwnProfile && isAuthenticated\">\r\n          <mat-icon>person_add</mat-icon>\r\n          Follow\r\n        </button>\r\n\r\n        <button\r\n          mat-icon-button\r\n          [matMenuTriggerFor]=\"profileMenu\"\r\n          class=\"more-actions\">\r\n          <mat-icon>more_vert</mat-icon>\r\n        </button>\r\n\r\n        <mat-menu #profileMenu=\"matMenu\">\r\n          <button mat-menu-item (click)=\"onShareProfile()\">\r\n            <mat-icon>share</mat-icon>\r\n            <span>Share Profile</span>\r\n          </button>\r\n          <button mat-menu-item *ngIf=\"!isOwnProfile\">\r\n            <mat-icon>flag</mat-icon>\r\n            <span>Report</span>\r\n          </button>\r\n        </mat-menu>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Completion Progress (Own Profile Only) -->\r\n    <div class=\"profile-completion\" *ngIf=\"isOwnProfile\">\r\n      <div class=\"completion-header\">\r\n        <span>Profile Completion</span>\r\n        <span class=\"completion-percentage\">{{ profile.profileCompletionPercentage }}%</span>\r\n      </div>\r\n      <mat-progress-bar\r\n        [value]=\"profile.profileCompletionPercentage\"\r\n        [color]=\"getProfileCompletionColor()\">\r\n      </mat-progress-bar>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"profile-content\">\r\n    <div class=\"content-grid\">\r\n      <!-- Left Column -->\r\n      <div class=\"left-column\">\r\n        <!-- About Section -->\r\n        <mat-card class=\"profile-section about-section\" *ngIf=\"profile.summary\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>person</mat-icon>\r\n              About\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"summary-text\" [innerHTML]=\"profile.summary\"></div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Experience Section -->\r\n        <mat-card class=\"profile-section experience-section\" *ngIf=\"profile.experiences.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>work</mat-icon>\r\n              Experience\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"experience-timeline\">\r\n              <div class=\"experience-item\" *ngFor=\"let exp of profile.experiences\">\r\n                <div class=\"experience-header\">\r\n                  <img\r\n                    [src]=\"exp.companyLogoUrl || '/assets/images/default-company.png'\"\r\n                    [alt]=\"exp.company\"\r\n                    class=\"company-logo\">\r\n                  <div class=\"experience-info\">\r\n                    <h3 class=\"position\">{{ exp.position }}</h3>\r\n                    <h4 class=\"company\">{{ exp.company }}</h4>\r\n                    <p class=\"duration\">\r\n                      {{ formatDate(exp.startDate) }} -\r\n                      {{ exp.isCurrent ? 'Present' : formatDate(exp.endDate!) }}\r\n                      <span class=\"location\" *ngIf=\"exp.location\"> • {{ exp.location }}</span>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"experience-description\" *ngIf=\"exp.description\">\r\n                  <p>{{ exp.description }}</p>\r\n                </div>\r\n                <div class=\"experience-achievements\" *ngIf=\"exp.achievements?.length\">\r\n                  <ul>\r\n                    <li *ngFor=\"let achievement of exp.achievements\">{{ achievement }}</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Portfolio Section -->\r\n        <mat-card class=\"profile-section portfolio-section\" *ngIf=\"profile.portfolioItems.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>work_outline</mat-icon>\r\n              Portfolio\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"portfolio-grid\">\r\n              <div class=\"portfolio-item\" *ngFor=\"let item of profile.portfolioItems\">\r\n                <div class=\"portfolio-image\">\r\n                  <img [src]=\"item.imageUrls[0]\" [alt]=\"item.title\" *ngIf=\"item.imageUrls.length\">\r\n                </div>\r\n                <div class=\"portfolio-info\">\r\n                  <h3>{{ item.title }}</h3>\r\n                  <p>{{ item.description }}</p>\r\n                  <div class=\"portfolio-tech\">\r\n                    <div class=\"tech-chips\">\r\n                      <span class=\"tech-chip\" *ngFor=\"let tech of item.technologies\">{{ tech }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"portfolio-links\" *ngIf=\"item.projectUrl || item.githubUrl\">\r\n                    <a mat-button [href]=\"item.projectUrl\" target=\"_blank\" *ngIf=\"item.projectUrl\">\r\n                      <mat-icon>launch</mat-icon>\r\n                      View Project\r\n                    </a>\r\n                    <a mat-button [href]=\"item.githubUrl\" target=\"_blank\" *ngIf=\"item.githubUrl\">\r\n                      <mat-icon>code</mat-icon>\r\n                      View Code\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Blog Posts Section -->\r\n        <mat-card class=\"profile-section blog-section\" *ngIf=\"blogPosts.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>article</mat-icon>\r\n              Recent Articles\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"blog-posts\">\r\n              <div class=\"blog-post\" *ngFor=\"let post of blogPosts.slice(0, 3)\">\r\n                <div class=\"blog-image\" *ngIf=\"post.featuredImageUrl\">\r\n                  <img [src]=\"post.featuredImageUrl\" [alt]=\"post.title\">\r\n                </div>\r\n                <div class=\"blog-content\">\r\n                  <h3>{{ post.title }}</h3>\r\n                  <p>{{ post.excerpt }}</p>\r\n                  <div class=\"blog-meta\">\r\n                    <span class=\"publish-date\">{{ formatDate(post.publishedAt) }}</span>\r\n                    <span class=\"read-count\">{{ post.readCount }} reads</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Right Column -->\r\n      <div class=\"right-column\">\r\n        <!-- Contact Info -->\r\n        <mat-card class=\"profile-section contact-section\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>contact_mail</mat-icon>\r\n              Contact Information\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"contact-item\" *ngIf=\"profile.contactInfo.email && profile.contactInfo.isEmailPublic\">\r\n              <mat-icon>email</mat-icon>\r\n              <a [href]=\"'mailto:' + profile.contactInfo.email\">{{ profile.contactInfo.email }}</a>\r\n            </div>\r\n            <div class=\"contact-item\" *ngFor=\"let phone of profile.contactInfo.phoneNumbers\" [hidden]=\"!phone.isPublic\">\r\n              <mat-icon>phone</mat-icon>\r\n              <a [href]=\"'tel:' + phone.number\">{{ phone.number }} ({{ phone.type }})</a>\r\n            </div>\r\n            <div class=\"contact-item\" *ngIf=\"profile.contactInfo.website\">\r\n              <mat-icon>language</mat-icon>\r\n              <a [href]=\"profile.contactInfo.website\" target=\"_blank\">Website</a>\r\n            </div>\r\n            <div class=\"contact-item\" *ngIf=\"profile.contactInfo.portfolioUrl\">\r\n              <mat-icon>work</mat-icon>\r\n              <a [href]=\"profile.contactInfo.portfolioUrl\" target=\"_blank\">Portfolio</a>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Skills Section -->\r\n        <mat-card class=\"profile-section skills-section\" *ngIf=\"profile.skills.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>psychology</mat-icon>\r\n              Skills & Expertise\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"skills-by-category\" *ngFor=\"let category of getSkillsByCategory() | keyvalue\">\r\n              <h4 class=\"skill-category\">{{ category.key }}</h4>\r\n              <div class=\"skills-list\">\r\n                <div class=\"skill-item\" *ngFor=\"let skill of category.value\">\r\n                  <div class=\"skill-info\">\r\n                    <span class=\"skill-name\">{{ skill.name }}</span>\r\n                    <span class=\"endorsement-count\">{{ skill.endorsements }} endorsements</span>\r\n                  </div>\r\n                  <button\r\n                    mat-icon-button\r\n                    class=\"endorse-btn\"\r\n                    (click)=\"onEndorseSkill(skill.id)\"\r\n                    *ngIf=\"!isOwnProfile && isAuthenticated && !skill.isEndorsedByCurrentUser\"\r\n                    matTooltip=\"Endorse this skill\">\r\n                    <mat-icon>thumb_up</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Achievements Section -->\r\n        <mat-card class=\"profile-section achievements-section\" *ngIf=\"profile.achievements.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>emoji_events</mat-icon>\r\n              Achievements\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"achievements-list\">\r\n              <div class=\"achievement-item\" *ngFor=\"let achievement of profile.achievements\">\r\n                <div class=\"achievement-icon\">\r\n                  <img [src]=\"achievement.imageUrl || '/assets/images/default-achievement.png'\" [alt]=\"achievement.title\">\r\n                </div>\r\n                <div class=\"achievement-info\">\r\n                  <h4>{{ achievement.title }}</h4>\r\n                  <p>{{ achievement.description }}</p>\r\n                  <span class=\"achievement-date\">{{ formatDate(achievement.achievedAt) }}</span>\r\n                  <span class=\"achievement-org\" *ngIf=\"achievement.organization\"> • {{ achievement.organization }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Certifications Section -->\r\n        <mat-card class=\"profile-section certifications-section\" *ngIf=\"profile.certifications.length\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>verified</mat-icon>\r\n              Certifications\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"certifications-list\">\r\n              <div class=\"certification-item\" *ngFor=\"let cert of profile.certifications\">\r\n                <div class=\"certification-icon\">\r\n                  <img [src]=\"cert.imageUrl || '/assets/images/default-certification.png'\" [alt]=\"cert.name\">\r\n                </div>\r\n                <div class=\"certification-info\">\r\n                  <h4>{{ cert.name }}</h4>\r\n                  <p>{{ cert.issuingOrganization }}</p>\r\n                  <span class=\"certification-date\">\r\n                    Issued {{ formatDate(cert.issueDate) }}\r\n                    <span *ngIf=\"cert.expirationDate\"> • Expires {{ formatDate(cert.expirationDate) }}</span>\r\n                  </span>\r\n                  <a [href]=\"cert.credentialUrl\" target=\"_blank\" *ngIf=\"cert.credentialUrl\" class=\"credential-link\">\r\n                    <mat-icon>launch</mat-icon>\r\n                    View Credential\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Analytics (Own Profile Only) -->\r\n        <mat-card class=\"profile-section analytics-section\" *ngIf=\"isOwnProfile && analytics\">\r\n          <mat-card-header>\r\n            <mat-card-title>\r\n              <mat-icon>analytics</mat-icon>\r\n              Profile Analytics\r\n            </mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"analytics-grid\">\r\n              <div class=\"analytics-item\">\r\n                <span class=\"analytics-value\">{{ analytics.profileViews }}</span>\r\n                <span class=\"analytics-label\">Total Views</span>\r\n              </div>\r\n              <div class=\"analytics-item\">\r\n                <span class=\"analytics-value\">{{ analytics.uniqueVisitors }}</span>\r\n                <span class=\"analytics-label\">Unique Visitors</span>\r\n              </div>\r\n              <div class=\"analytics-item\">\r\n                <span class=\"analytics-value\">{{ analytics.skillEndorsements }}</span>\r\n                <span class=\"analytics-label\">Skill Endorsements</span>\r\n              </div>\r\n              <div class=\"analytics-item\">\r\n                <span class=\"analytics-value\">{{ analytics.contactButtonClicks }}</span>\r\n                <span class=\"analytics-label\">Contact Clicks</span>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Loading State -->\r\n<div class=\"loading-container\" *ngIf=\"isLoading\">\r\n  <mat-spinner diameter=\"50\"></mat-spinner>\r\n  <p>Loading profile...</p>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}