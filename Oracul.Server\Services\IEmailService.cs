namespace Oracul.Server.Services
{
    public interface IEmailService
    {
        /// <summary>
        /// Sends a password reset email
        /// </summary>
        /// <param name="email">Recipient email address</param>
        /// <param name="resetToken">Password reset token</param>
        /// <param name="userName">User's name</param>
        /// <returns>True if email was sent successfully</returns>
        Task<bool> SendPasswordResetEmailAsync(string email, string resetToken, string userName);

        /// <summary>
        /// Sends an email confirmation email
        /// </summary>
        /// <param name="email">Recipient email address</param>
        /// <param name="confirmationToken">Email confirmation token</param>
        /// <param name="userName">User's name</param>
        /// <returns>True if email was sent successfully</returns>
        Task<bool> SendEmailConfirmationAsync(string email, string confirmationToken, string userName);

        /// <summary>
        /// Sends a welcome email to new users
        /// </summary>
        /// <param name="email">Recipient email address</param>
        /// <param name="userName">User's name</param>
        /// <returns>True if email was sent successfully</returns>
        Task<bool> SendWelcomeEmailAsync(string email, string userName);

        /// <summary>
        /// Sends a generic email
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body (HTML)</param>
        /// <returns>True if email was sent successfully</returns>
        Task<bool> SendEmailAsync(string to, string subject, string body);
    }
}
