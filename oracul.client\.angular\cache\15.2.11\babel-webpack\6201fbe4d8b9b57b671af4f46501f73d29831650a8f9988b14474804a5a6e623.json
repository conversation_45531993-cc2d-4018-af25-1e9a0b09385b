{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/oauth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"../../core/i18n/translation.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/divider\";\nfunction LoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getEmailErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_icon_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"login\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, oauthService, router, route, snackBar, t) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.t = t;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.returnUrl = '/';\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n    // Redirect if already logged in\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      if (isAuth) {\n        this.router.navigate([this.returnUrl]);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const loginRequest = {\n        email: this.loginForm.value.email,\n        password: this.loginForm.value.password\n      };\n      this.authService.login(loginRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open(this.t.auth.loginSuccess, this.t.common.close, {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate([this.returnUrl]);\n          } else {\n            this.snackBar.open(response.message || this.t.auth.loginError, this.t.common.close, {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error || this.t.auth.loginError, this.t.common.close, {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getEmailErrorMessage() {\n    const emailControl = this.loginForm.get('email');\n    if (emailControl?.hasError('required')) {\n      return this.t.auth.emailRequired;\n    }\n    if (emailControl?.hasError('email')) {\n      return this.t.auth.invalidEmail;\n    }\n    return '';\n  }\n  getPasswordErrorMessage() {\n    const passwordControl = this.loginForm.get('password');\n    if (passwordControl?.hasError('required')) {\n      return this.t.auth.passwordRequired;\n    }\n    if (passwordControl?.hasError('minlength')) {\n      return this.t.auth.passwordTooShort;\n    }\n    return '';\n  }\n  navigateToRegister() {\n    this.router.navigate(['/register'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  navigateToForgotPassword() {\n    this.router.navigate(['/forgot-password']);\n  }\n  signInWithGoogle() {\n    this.isLoading = true;\n    this.oauthService.signInWithGooglePopup().subscribe({\n      next: oauthUser => {\n        this.handleOAuthLogin(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Google sign-in failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  signInWithFacebook() {\n    this.isLoading = true;\n    this.oauthService.signInWithFacebook().subscribe({\n      next: oauthUser => {\n        this.handleOAuthLogin(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Facebook sign-in failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  handleOAuthLogin(oauthUser) {\n    const oauthRequest = {\n      provider: oauthUser.provider,\n      accessToken: oauthUser.accessToken,\n      email: oauthUser.email,\n      firstName: oauthUser.firstName,\n      lastName: oauthUser.lastName,\n      profilePictureUrl: oauthUser.profilePictureUrl\n    };\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          this.snackBar.open(`Welcome back! Signed in with ${oauthUser.provider}`, 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        } else {\n          this.snackBar.open(response.message || 'OAuth login failed', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'OAuth login failed. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 55,\n      vars: 22,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"login-icon\"], [1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\", 3, \"placeholder\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\", \"placeholder\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [1, \"divider-container\"], [1, \"divider-text\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"google-button\", 3, \"disabled\", \"click\"], [\"src\", \"https://developers.google.com/identity/images/g-logo.png\", \"alt\", \"Google\", 1, \"oauth-icon\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"facebook-button\", 3, \"disabled\", \"click\"], [1, \"oauth-icon\", \"facebook-icon\"], [1, \"login-actions\"], [1, \"action-links\"], [\"mat-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 3, \"click\"], [1, \"register-section\"], [1, \"register-text\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"full-width\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\")(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" \\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0434\\u0430\\u043D\\u043D\\u0438\\u0442\\u0435 \\u0441\\u0438 \\u0437\\u0430 \\u0434\\u043E\\u0441\\u0442\\u044A\\u043F \\u0434\\u043E \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\\u0430 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 5)(12, \"mat-label\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 6);\n          i0.ɵɵelementStart(15, \"mat-icon\", 7);\n          i0.ɵɵtext(16, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, LoginComponent_mat_error_17_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 5)(19, \"mat-label\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 9);\n          i0.ɵɵelementStart(22, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_22_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 11);\n          i0.ɵɵtemplate(27, LoginComponent_mat_icon_27_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtemplate(28, LoginComponent_mat_icon_28_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 12);\n          i0.ɵɵelement(31, \"mat-divider\");\n          i0.ɵɵelementStart(32, \"span\", 13);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"mat-divider\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 14)(36, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_36_listener() {\n            return ctx.signInWithGoogle();\n          });\n          i0.ɵɵelement(37, \"img\", 16);\n          i0.ɵɵtext(38, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0441 Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_39_listener() {\n            return ctx.signInWithFacebook();\n          });\n          i0.ɵɵelementStart(40, \"mat-icon\", 18);\n          i0.ɵɵtext(41, \"facebook\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0441 Facebook \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"mat-card-actions\", 19)(44, \"div\", 20)(45, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_45_listener() {\n            return ctx.navigateToForgotPassword();\n          });\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(47, \"mat-divider\");\n          i0.ɵɵelementStart(48, \"div\", 22)(49, \"p\", 23);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_51_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_11_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.loginTitle, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.t.auth.email);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u044F \\u0438\\u043C\\u0435\\u0439\\u043B\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.t.auth.password);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"\\u0412\\u043B\\u0438\\u0437\\u0430\\u043D\\u0435...\" : ctx.t.auth.signIn, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.auth.orContinueWith);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.forgotPassword, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.auth.dontHaveAccount);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.createAccount, \" \");\n        }\n      },\n      dependencies: [i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatProgressSpinner, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, i13.MatInput, i14.MatDivider],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  min-height: 100vh;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-gradient-auth);\\r\\n}\\r\\n\\r\\n.login-card[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  max-width: 400px;\\r\\n  padding: 0;\\r\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-auth);\\r\\n  color: white;\\r\\n  padding: 24px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 8px;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 24px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.login-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 28px;\\r\\n  width: 28px;\\r\\n  height: 28px;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 14px;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.login-form[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.login-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 16px;\\r\\n  font-weight: 500;\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.login-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n.login-actions[_ngcontent-%COMP%] {\\r\\n  padding: 0 24px 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.action-links[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.register-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.register-text[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.mat-form-field[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n\\r\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.divider-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  margin: 24px 0 16px;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.divider-text[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n  white-space: nowrap;\\r\\n}\\r\\n\\r\\n.oauth-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.oauth-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 14px;\\r\\n  font-weight: 500;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n.oauth-icon[_ngcontent-%COMP%] {\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%] {\\r\\n  border-color: #dadce0;\\r\\n  color: #3c4043;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f8f9fa;\\r\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%] {\\r\\n  border-color: #1877f2;\\r\\n  color: #1877f2;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f0f2f5;\\r\\n}\\r\\n\\r\\n.facebook-icon[_ngcontent-%COMP%] {\\r\\n  color: #1877f2;\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .login-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .login-card[_ngcontent-%COMP%] {\\r\\n    max-width: 100%;\\r\\n  }\\r\\n\\r\\n  .login-form[_ngcontent-%COMP%] {\\r\\n    padding: 20px;\\r\\n  }\\r\\n\\r\\n  .login-actions[_ngcontent-%COMP%] {\\r\\n    padding: 0 20px 20px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n  .success-snackbar {\\r\\n  background-color: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n  .error-snackbar {\\r\\n  background-color: #f44336;\\r\\n  color: white;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;ICuBzDC,iCAAsF;IACpFA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,8DACF;;;;;IAqBAA,iCAA4F;IAC1FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,iEACF;;;;;IAUAA,gCAA4B;IAC1BA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA6B;IAAAA,qBAAK;IAAAA,iBAAW;;;ADhDvD,OAAM,MAAOC,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB,EACtBC,CAAqB;IANpB,gBAAW,GAAXN,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IACN,UAAK,GAALC,KAAK;IACL,aAAQ,GAARC,QAAQ;IACT,MAAC,GAADC,CAAC;IAXV,cAAS,GAAG,KAAK;IACjB,iBAAY,GAAG,IAAI;IACnB,cAAS,GAAG,GAAG;IAWb,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACa,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACf,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACV,KAAK,CAACW,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG;IAEpE;IACA,IAAI,CAACf,WAAW,CAACgB,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAIA,MAAM,EAAE;QACV,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;;IAE1C,CAAC,CAAC;EACJ;EAEAO,QAAQ;IACN,IAAI,IAAI,CAACd,SAAS,CAACe,KAAK,EAAE;MACxB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMC,YAAY,GAAiB;QACjCf,KAAK,EAAE,IAAI,CAACF,SAAS,CAACkB,KAAK,CAAChB,KAAK;QACjCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACkB,KAAK,CAACd;OAChC;MAED,IAAI,CAACV,WAAW,CAACyB,KAAK,CAACF,YAAY,CAAC,CAACN,SAAS,CAAC;QAC7CS,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACL,SAAS,GAAG,KAAK;UACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,IAAI,CAACxB,CAAC,CAACyB,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC1B,CAAC,CAAC2B,MAAM,CAACC,KAAK,EAAE;cAChEC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,kBAAkB;aAChC,CAAC;YACF,IAAI,CAACjC,MAAM,CAACiB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;WACvC,MAAM;YACL,IAAI,CAACT,QAAQ,CAACyB,IAAI,CAACF,QAAQ,CAACS,OAAO,IAAI,IAAI,CAAC/B,CAAC,CAACyB,IAAI,CAACO,UAAU,EAAE,IAAI,CAAChC,CAAC,CAAC2B,MAAM,CAACC,KAAK,EAAE;cAClFC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,gBAAgB;aAC9B,CAAC;;QAEN,CAAC;QACDG,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChB,SAAS,GAAG,KAAK;UACtB,IAAI,CAAClB,QAAQ,CAACyB,IAAI,CAACS,KAAK,IAAI,IAAI,CAACjC,CAAC,CAACyB,IAAI,CAACO,UAAU,EAAE,IAAI,CAAChC,CAAC,CAAC2B,MAAM,CAACC,KAAK,EAAE;YACvEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACI,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoB;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACoC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACvC,SAAS,CAACwC,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,oBAAoB;IAClB,MAAMC,YAAY,GAAG,IAAI,CAAC3C,SAAS,CAACwC,GAAG,CAAC,OAAO,CAAC;IAChD,IAAIG,YAAY,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACtC,OAAO,IAAI,CAAC7C,CAAC,CAACyB,IAAI,CAACqB,aAAa;;IAElC,IAAIF,YAAY,EAAEC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACnC,OAAO,IAAI,CAAC7C,CAAC,CAACyB,IAAI,CAACsB,YAAY;;IAEjC,OAAO,EAAE;EACX;EAEAC,uBAAuB;IACrB,MAAMC,eAAe,GAAG,IAAI,CAAChD,SAAS,CAACwC,GAAG,CAAC,UAAU,CAAC;IACtD,IAAIQ,eAAe,EAAEJ,QAAQ,CAAC,UAAU,CAAC,EAAE;MACzC,OAAO,IAAI,CAAC7C,CAAC,CAACyB,IAAI,CAACyB,gBAAgB;;IAErC,IAAID,eAAe,EAAEJ,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC1C,OAAO,IAAI,CAAC7C,CAAC,CAACyB,IAAI,CAAC0B,gBAAgB;;IAErC,OAAO,EAAE;EACX;EAEAC,kBAAkB;IAChB,IAAI,CAACvD,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAAEJ,WAAW,EAAE;QAAEF,SAAS,EAAE,IAAI,CAACA;MAAS;IAAE,CAAE,CAAC;EACrF;EAEA6C,wBAAwB;IACtB,IAAI,CAACxD,MAAM,CAACiB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAwC,gBAAgB;IACd,IAAI,CAACrC,SAAS,GAAG,IAAI;IAErB,IAAI,CAACrB,YAAY,CAAC2D,qBAAqB,EAAE,CAAC3C,SAAS,CAAC;MAClDS,IAAI,EAAGmC,SAAoB,IAAI;QAC7B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;MAClC,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAClB,QAAQ,CAACyB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;UACtEK,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA4B,kBAAkB;IAChB,IAAI,CAACzC,SAAS,GAAG,IAAI;IAErB,IAAI,CAACrB,YAAY,CAAC8D,kBAAkB,EAAE,CAAC9C,SAAS,CAAC;MAC/CS,IAAI,EAAGmC,SAAoB,IAAI;QAC7B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;MAClC,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAClB,QAAQ,CAACyB,IAAI,CAAC,4CAA4C,EAAE,OAAO,EAAE;UACxEK,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQ2B,gBAAgB,CAACD,SAAoB;IAC3C,MAAMG,YAAY,GAAsB;MACtCC,QAAQ,EAAEJ,SAAS,CAACI,QAAQ;MAC5BC,WAAW,EAAEL,SAAS,CAACK,WAAW;MAClC1D,KAAK,EAAEqD,SAAS,CAACrD,KAAK;MACtB2D,SAAS,EAAEN,SAAS,CAACM,SAAS;MAC9BC,QAAQ,EAAEP,SAAS,CAACO,QAAQ;MAC5BC,iBAAiB,EAAER,SAAS,CAACQ;KAC9B;IAED,IAAI,CAACrE,WAAW,CAACsE,cAAc,CAACN,YAAY,CAAC,CAAC/C,SAAS,CAAC;MACtDS,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,gCAAgCgC,SAAS,CAACI,QAAQ,EAAE,EAAE,OAAO,EAAE;YAChF/B,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAACjC,MAAM,CAACiB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;SACvC,MAAM;UACL,IAAI,CAACT,QAAQ,CAACyB,IAAI,CAACF,QAAQ,CAACS,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;YACpEF,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAClB,QAAQ,CAACyB,IAAI,CAACS,KAAK,IAAI,uCAAuC,EAAE,OAAO,EAAE;UAC5EJ,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;;;uBAhLWtC,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAA0E;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCd3B/E,8BAA6B;UAIQA,oBAAI;UAAAA,iBAAW;UAC5CA,YACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,oOACF;UAAAA,iBAAoB;UAGtBA,wCAAkB;UACcA;YAAA,OAAYgF,cAAU;UAAA,EAAC;UAEnDhF,0CAAwD;UAC3CA,aAAkB;UAAAA,iBAAY;UACzCA,4BAKuB;UACvBA,oCAAoB;UAAAA,sBAAK;UAAAA,iBAAW;UACpCA,6EAEY;UACdA,iBAAiB;UAGjBA,0CAAwD;UAC3CA,aAAqB;UAAAA,iBAAY;UAC5CA,4BAKkC;UAClCA,mCAMqC;UAFnCA;YAAA;UAAA,EAAsC;UAGtCA,iCAAU;UAAAA,aAAoD;UAAAA,iBAAW;UAE3EA,6EAEY;UACdA,iBAAiB;UAGjBA,mCAKyB;UACvBA,2EAEW;UACXA,2EAA6C;UAC7CA,aACF;UAAAA,iBAAS;UAGTA,gCAA+B;UAC7BA,+BAA2B;UAC3BA,iCAA2B;UAAAA,aAA2B;UAAAA,iBAAO;UAC7DA,+BAA2B;UAC7BA,iBAAM;UAGNA,gCAA2B;UAOvBA;YAAA,OAASgF,sBAAkB;UAAA,EAAC;UAC5BhF,2BAAoG;UACpGA,iFACF;UAAAA,iBAAS;UAGTA,mCAKiC;UAA/BA;YAAA,OAASgF,wBAAoB;UAAA,EAAC;UAC9BhF,qCAA2C;UAAAA,yBAAQ;UAAAA,iBAAW;UAC9DA,mFACF;UAAAA,iBAAS;UAKfA,6CAAwC;UAMlCA;YAAA,OAASgF,8BAA0B;UAAA,EAAC;UACpChF,aACF;UAAAA,iBAAS;UAGXA,+BAA2B;UAE3BA,gCAA8B;UACHA,aAA4B;UAAAA,iBAAI;UACzDA,mCAKqB;UADnBA;YAAA,OAASgF,wBAAoB;UAAA,EAAC;UAE9BhF,iCAAU;UAAAA,2BAAU;UAAAA,iBAAW;UAC/BA,aACF;UAAAA,iBAAS;;;;;UAtHTA,eACF;UADEA,sDACF;UAOMA,eAAuB;UAAvBA,yCAAuB;UAGdA,eAAkB;UAAlBA,sCAAkB;UAK3BA,eAAsC;UAAtCA,8IAAsC;UAG5BA,eAAwE;UAAxEA,6KAAwE;UAOzEA,eAAqB;UAArBA,yCAAqB;UAG9BA,eAA2C;UAA3CA,6DAA2C;UAS3CA,eAAkC;UAAlCA,mGAAkC;UAExBA,eAAoD;UAApDA,wEAAoD;UAEpDA,eAA8E;UAA9EA,uLAA8E;UAW1FA,eAAsB;UAAtBA,wCAAsB;UACXA,eAAe;UAAfA,oCAAe;UAGfA,eAAgB;UAAhBA,qCAAgB;UAC3BA,eACF;UADEA,oHACF;UAK6BA,eAA2B;UAA3BA,+CAA2B;UAWpDA,eAAsB;UAAtBA,wCAAsB;UAWtBA,eAAsB;UAAtBA,wCAAsB;UAgBxBA,eACF;UADEA,0DACF;UAMyBA,eAA4B;UAA5BA,gDAA4B;UAQnDA,eACF;UADEA,yDACF", "names": ["Validators", "i0", "LoginComponent", "constructor", "formBuilder", "authService", "oauthService", "router", "route", "snackBar", "t", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "returnUrl", "snapshot", "queryParams", "isAuthenticated$", "subscribe", "isAuth", "navigate", "onSubmit", "valid", "isLoading", "loginRequest", "value", "login", "next", "response", "success", "open", "auth", "loginSuccess", "common", "close", "duration", "panelClass", "message", "loginError", "error", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getEmailErrorMessage", "emailControl", "<PERSON><PERSON><PERSON><PERSON>", "emailRequired", "invalidEmail", "getPasswordErrorMessage", "passwordControl", "passwordRequired", "passwordTooShort", "navigateToRegister", "navigateToForgotPassword", "signInWithGoogle", "signInWithGooglePopup", "<PERSON><PERSON><PERSON><PERSON>ser", "handleOAuthLogin", "signInWithFacebook", "oauthRequest", "provider", "accessToken", "firstName", "lastName", "profilePictureUrl", "loginWithOAuth", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\login\\login.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { OAuthService, OAuthUser } from '../services/oauth.service';\r\nimport { LoginRequest, OAuthLoginRequest } from '../models/auth.models';\r\nimport { TranslationService } from '../../core/i18n/translation.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.css']\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginForm: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  returnUrl = '/';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private oauthService: OAuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar,\r\n    public t: TranslationService\r\n  ) {\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\r\n\r\n    // Redirect if already logged in\r\n    this.authService.isAuthenticated$.subscribe(isAuth => {\r\n      if (isAuth) {\r\n        this.router.navigate([this.returnUrl]);\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading = true;\r\n\r\n      const loginRequest: LoginRequest = {\r\n        email: this.loginForm.value.email,\r\n        password: this.loginForm.value.password\r\n      };\r\n\r\n      this.authService.login(loginRequest).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          if (response.success) {\r\n            this.snackBar.open(this.t.auth.loginSuccess, this.t.common.close, {\r\n              duration: 3000,\r\n              panelClass: ['success-snackbar']\r\n            });\r\n            this.router.navigate([this.returnUrl]);\r\n          } else {\r\n            this.snackBar.open(response.message || this.t.auth.loginError, this.t.common.close, {\r\n              duration: 5000,\r\n              panelClass: ['error-snackbar']\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          this.snackBar.open(error || this.t.auth.loginError, this.t.common.close, {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.loginForm.controls).forEach(key => {\r\n      const control = this.loginForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getEmailErrorMessage(): string {\r\n    const emailControl = this.loginForm.get('email');\r\n    if (emailControl?.hasError('required')) {\r\n      return this.t.auth.emailRequired;\r\n    }\r\n    if (emailControl?.hasError('email')) {\r\n      return this.t.auth.invalidEmail;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getPasswordErrorMessage(): string {\r\n    const passwordControl = this.loginForm.get('password');\r\n    if (passwordControl?.hasError('required')) {\r\n      return this.t.auth.passwordRequired;\r\n    }\r\n    if (passwordControl?.hasError('minlength')) {\r\n      return this.t.auth.passwordTooShort;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    this.router.navigate(['/register'], { queryParams: { returnUrl: this.returnUrl } });\r\n  }\r\n\r\n  navigateToForgotPassword(): void {\r\n    this.router.navigate(['/forgot-password']);\r\n  }\r\n\r\n  signInWithGoogle(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithGooglePopup().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthLogin(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Google sign-in failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  signInWithFacebook(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithFacebook().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthLogin(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Facebook sign-in failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleOAuthLogin(oauthUser: OAuthUser): void {\r\n    const oauthRequest: OAuthLoginRequest = {\r\n      provider: oauthUser.provider,\r\n      accessToken: oauthUser.accessToken,\r\n      email: oauthUser.email,\r\n      firstName: oauthUser.firstName,\r\n      lastName: oauthUser.lastName,\r\n      profilePictureUrl: oauthUser.profilePictureUrl\r\n    };\r\n\r\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          this.snackBar.open(`Welcome back! Signed in with ${oauthUser.provider}`, 'Close', {\r\n            duration: 3000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n          this.router.navigate([this.returnUrl]);\r\n        } else {\r\n          this.snackBar.open(response.message || 'OAuth login failed', 'Close', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'OAuth login failed. Please try again.', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"login-container\">\r\n  <mat-card class=\"login-card\">\r\n    <mat-card-header class=\"login-header\">\r\n      <mat-card-title>\r\n        <mat-icon class=\"login-icon\">lock</mat-icon>\r\n        {{ t.auth.loginTitle }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Въведете данните си за достъп до акаунта\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\r\n        <!-- Email Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>{{ t.auth.email }}</mat-label>\r\n          <input\r\n            matInput\r\n            type=\"email\"\r\n            formControlName=\"email\"\r\n            [placeholder]=\"'Въведете вашия имейл'\"\r\n            autocomplete=\"email\">\r\n          <mat-icon matSuffix>email</mat-icon>\r\n          <mat-error *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\r\n            {{ getEmailErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Password Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>{{ t.auth.password }}</mat-label>\r\n          <input\r\n            matInput\r\n            [type]=\"hidePassword ? 'password' : 'text'\"\r\n            formControlName=\"password\"\r\n            [placeholder]=\"'Въведете вашата парола'\"\r\n            autocomplete=\"current-password\">\r\n          <button\r\n            mat-icon-button\r\n            matSuffix\r\n            type=\"button\"\r\n            (click)=\"hidePassword = !hidePassword\"\r\n            [attr.aria-label]=\"'Скрий парола'\"\r\n            [attr.aria-pressed]=\"hidePassword\">\r\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\r\n            {{ getPasswordErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Login Button -->\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          type=\"submit\"\r\n          class=\"full-width login-button\"\r\n          [disabled]=\"isLoading\">\r\n          <mat-icon *ngIf=\"isLoading\">\r\n            <mat-spinner diameter=\"20\"></mat-spinner>\r\n          </mat-icon>\r\n          <mat-icon *ngIf=\"!isLoading\">login</mat-icon>\r\n          {{ isLoading ? 'Влизане...' : t.auth.signIn }}\r\n        </button>\r\n\r\n        <!-- Divider -->\r\n        <div class=\"divider-container\">\r\n          <mat-divider></mat-divider>\r\n          <span class=\"divider-text\">{{ t.auth.orContinueWith }}</span>\r\n          <mat-divider></mat-divider>\r\n        </div>\r\n\r\n        <!-- OAuth Buttons -->\r\n        <div class=\"oauth-buttons\">\r\n          <!-- Google Sign-In Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button google-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signInWithGoogle()\">\r\n            <img src=\"https://developers.google.com/identity/images/g-logo.png\" alt=\"Google\" class=\"oauth-icon\">\r\n            Продължи с Google\r\n          </button>\r\n\r\n          <!-- Facebook Sign-In Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button facebook-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signInWithFacebook()\">\r\n            <mat-icon class=\"oauth-icon facebook-icon\">facebook</mat-icon>\r\n            Продължи с Facebook\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions class=\"login-actions\">\r\n      <div class=\"action-links\">\r\n        <button\r\n          mat-button\r\n          color=\"accent\"\r\n          type=\"button\"\r\n          (click)=\"navigateToForgotPassword()\">\r\n          {{ t.auth.forgotPassword }}\r\n        </button>\r\n      </div>\r\n\r\n      <mat-divider></mat-divider>\r\n\r\n      <div class=\"register-section\">\r\n        <p class=\"register-text\">{{ t.auth.dontHaveAccount }}</p>\r\n        <button\r\n          mat-stroked-button\r\n          color=\"primary\"\r\n          type=\"button\"\r\n          (click)=\"navigateToRegister()\"\r\n          class=\"full-width\">\r\n          <mat-icon>person_add</mat-icon>\r\n          {{ t.auth.createAccount }}\r\n        </button>\r\n      </div>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}