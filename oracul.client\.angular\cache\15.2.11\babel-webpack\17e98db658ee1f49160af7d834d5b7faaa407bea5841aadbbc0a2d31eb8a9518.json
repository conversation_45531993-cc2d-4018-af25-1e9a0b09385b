{"ast": null, "code": "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the virtual scrolling strategy. */\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n  constructor() {\n    this._itemSize = 20;\n    this._minBufferPx = 100;\n    this._maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  /** The size of the items in the list (in pixels). */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n}\nCdkFixedSizeVirtualScroll.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n  return new (t || CdkFixedSizeVirtualScroll)();\n};\nCdkFixedSizeVirtualScroll.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkFixedSizeVirtualScroll,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n  inputs: {\n    itemSize: \"itemSize\",\n    minBufferPx: \"minBufferPx\",\n    maxBufferPx: \"maxBufferPx\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLL_STRATEGY,\n    useFactory: _fixedSizeVirtualScrollStrategyFactory,\n    deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      standalone: true,\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n  constructor(_ngZone, _platform, document) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    this._scrolled = new Subject();\n    /** Keeps track of the global `scroll` and `resize` subscriptions. */\n    this._globalSubscription = null;\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    this._scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    this.scrollContainers = new Map();\n    this._document = document;\n  }\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n    return new Observable(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      }\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    });\n  }\n  ngOnDestroy() {\n    this._removeGlobalListener();\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n    return false;\n  }\n  /** Sets up the global scroll listeners. */\n  _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      const window = this._getWindow();\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n  /** Cleans up the global scroll listener. */\n  _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n      this._globalSubscription = null;\n    }\n  }\n}\nScrollDispatcher.ɵfac = function ScrollDispatcher_Factory(t) {\n  return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n};\nScrollDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollDispatcher,\n  factory: ScrollDispatcher.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    this.elementRef = elementRef;\n    this.scrollDispatcher = scrollDispatcher;\n    this.ngZone = ngZone;\n    this.dir = dir;\n    this._destroyed = new Subject();\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n  ngOnInit() {\n    this.scrollDispatcher.register(this);\n  }\n  ngOnDestroy() {\n    this.scrollDispatcher.deregister(this);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    // Rewrite start & end offsets as right or left offsets.\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    }\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n      if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n    this._applyScrollToOptions(options);\n  }\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    }\n    // Rewrite start & end as left or right offsets.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n    if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n}\nCdkScrollable.ɵfac = function CdkScrollable_Factory(t) {\n  return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\nCdkScrollable.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkScrollable,\n  selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]],\n  standalone: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n  constructor(_platform, ngZone, document) {\n    this._platform = _platform;\n    /** Stream of viewport change events. */\n    this._change = new Subject();\n    /** Event listener that will be used to handle the viewport change events. */\n    this._changeListener = event => {\n      this._change.next(event);\n    };\n    this._document = document;\n    ngZone.runOutsideAngular(() => {\n      if (_platform.isBrowser) {\n        const window = this._getWindow();\n        // Note that bind the events ourselves, rather than going through something like RxJS's\n        // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n        window.addEventListener('resize', this._changeListener);\n        window.addEventListener('orientationchange', this._changeListener);\n      }\n      // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n  ngOnDestroy() {\n    if (this._platform.isBrowser) {\n      const window = this._getWindow();\n      window.removeEventListener('resize', this._changeListener);\n      window.removeEventListener('orientationchange', this._changeListener);\n    }\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    };\n    // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n    return output;\n  }\n  /** Gets a ClientRect for the viewport's bounds. */\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const document = this._document;\n    const window = this._getWindow();\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n  _updateViewportSize() {\n    const window = this._getWindow();\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n}\nViewportRuler.ɵfac = function ViewportRuler_Factory(t) {\n  return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n};\nViewportRuler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ViewportRuler,\n  factory: ViewportRuler.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n  }\n  /**\n   * Measure the viewport size for the provided orientation.\n   *\n   * @param orientation The orientation to measure the size from.\n   */\n  measureViewportSize(orientation) {\n    const viewportEl = this.elementRef.nativeElement;\n    return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n}\nCdkVirtualScrollable.ɵfac = function CdkVirtualScrollable_Factory(t) {\n  return new (t || CdkVirtualScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\nCdkVirtualScrollable.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualScrollable,\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollable, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n  /** The direction the viewport scrolls. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n      this._calculateSpacerSize();\n    }\n  }\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(value) {\n    this._appendOnly = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n    this.elementRef = elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollStrategy = _scrollStrategy;\n    this.scrollable = scrollable;\n    this._platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    this._detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    this._renderedRangeSubject = new Subject();\n    this._orientation = 'vertical';\n    this._appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** A stream that emits whenever the rendered range changes. */\n    this.renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    this._totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    this._totalContentWidth = '';\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    this._totalContentHeight = '';\n    /** The currently rendered range of indices. */\n    this._renderedRange = {\n      start: 0,\n      end: 0\n    };\n    /** The length of the data bound to this viewport (in number of items). */\n    this._dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    this._viewportSize = 0;\n    /** The last rendered content offset that was set. */\n    this._renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    this._renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    this._isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    this._runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    this._viewportChanges = Subscription.EMPTY;\n    if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n    if (!this.scrollable) {\n      // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n      this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n      this.scrollable = this;\n    }\n  }\n  ngOnInit() {\n    // Scrolling depends on the element dimensions which we can't get during SSR.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    if (this.scrollable === this) {\n      super.ngOnInit();\n    }\n    // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n      this._scrollStrategy.attach(this);\n      this.scrollable.elementScrolled().pipe(\n      // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null),\n      // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER)).subscribe(() => this._scrollStrategy.onContentScrolled());\n      this._markChangeDetectionNeeded();\n    }));\n  }\n  ngOnDestroy() {\n    this.detach();\n    this._scrollStrategy.detach();\n    // Complete all subjects\n    this._renderedRangeSubject.complete();\n    this._detachedSubject.complete();\n    this._viewportChanges.unsubscribe();\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    }\n    // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n          this._scrollStrategy.onDataLengthChanged();\n        }\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n  detach() {\n    this._forOf = null;\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n  getViewportSize() {\n    return this._viewportSize;\n  }\n  // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n  /** Get the current rendered range of items. */\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n      this._calculateSpacerSize();\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n      this._renderedRangeSubject.next(this._renderedRange = range);\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`;\n      // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n    this.scrollable.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the scrollable (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n  measureScrollOffset(from) {\n    // This is to break the call cycle\n    let measureScrollOffset;\n    if (this.scrollable == this) {\n      measureScrollOffset = _from => super.measureScrollOffset(_from);\n    } else {\n      measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n    }\n    return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) - this.measureViewportOffset());\n  }\n  /**\n   * Measures the offset of the viewport from the scrolling container\n   * @param from The edge to measure from.\n   */\n  measureViewportOffset(from) {\n    let fromRect;\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const isRtl = this.dir?.value == 'rtl';\n    if (from == 'start') {\n      fromRect = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      fromRect = isRtl ? LEFT : RIGHT;\n    } else if (from) {\n      fromRect = from;\n    } else {\n      fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n    }\n    const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n    const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n    return viewportClientRect - scrollerClientRect;\n  }\n  /** Measure the combined size of all of the rendered items. */\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n  _measureViewportSize() {\n    this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n  }\n  /** Queue up change detection to run. */\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    }\n    // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n  _doChangeDetection() {\n    this._isChangeDetectionPending = false;\n    // Apply the content transform. The transform can't be set via an Angular binding because\n    // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n    // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n    // the `Number` function first to coerce it to a numeric value.\n    this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n    // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n    // from the root, since the repeated items are content projected in. Calling `detectChanges`\n    // instead does not properly check the projected content.\n    this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n    const runAfterChangeDetection = this._runAfterChangeDetection;\n    this._runAfterChangeDetection = [];\n    for (const fn of runAfterChangeDetection) {\n      fn();\n    }\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n  _calculateSpacerSize() {\n    this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n    this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n  }\n}\nCdkVirtualScrollViewport.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n  return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler), i0.ɵɵdirectiveInject(VIRTUAL_SCROLLABLE, 8));\n};\nCdkVirtualScrollViewport.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CdkVirtualScrollViewport,\n  selectors: [[\"cdk-virtual-scroll-viewport\"]],\n  viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n    }\n  },\n  hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n  hostVars: 4,\n  hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n    }\n  },\n  inputs: {\n    orientation: \"orientation\",\n    appendOnly: \"appendOnly\"\n  },\n  outputs: {\n    scrolledIndexChange: \"scrolledIndexChange\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n    deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 4,\n  consts: [[1, \"cdk-virtual-scroll-content-wrapper\"], [\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-spacer\"]],\n  template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 2);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n    }\n  },\n  styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      providers: [{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: ViewportRuler\n    }, {\n      type: CdkVirtualScrollable,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLLABLE]\n      }]\n    }];\n  }, {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n  /** The DataSource to display. */\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  /** The template used to stamp out new elements. */\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  constructor( /** The view container to add items to. */\n  _viewContainerRef, /** The template to use when stamping out new items. */\n  _template, /** The set of available differs. */\n  _differs, /** The strategy used to render items in the virtual scroll viewport. */\n  _viewRepeater, /** The virtual scrolling viewport that these items are being rendered in. */\n  _viewport, ngZone) {\n    this._viewContainerRef = _viewContainerRef;\n    this._template = _template;\n    this._differs = _differs;\n    this._viewRepeater = _viewRepeater;\n    this._viewport = _viewport;\n    /** Emits when the rendered view of the data changes. */\n    this.viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    this._dataSourceChanges = new Subject();\n    /** Emits whenever the data in the current DataSource changes. */\n    this.dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null),\n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(),\n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    this._differ = null;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    this._needsUpdate = false;\n    this._destroyed = new Subject();\n    this.dataStream.subscribe(data => {\n      this._data = data;\n      this._onRenderedDataChange();\n    });\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n      this._onRenderedDataChange();\n    });\n    this._viewport.attach(this);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    }\n    // The index into the list of rendered views for the first item in the range.\n    const renderedStartIndex = range.start - this._renderedRange.start;\n    // The length of the range we're measuring.\n    const rangeLen = range.end - range.start;\n    // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n    let firstNode;\n    let lastNode;\n    // Find the first node by starting from the beginning and going forwards.\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    }\n    // Find the last node by starting from the end and going backwards.\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n      this._needsUpdate = false;\n    }\n  }\n  ngOnDestroy() {\n    this._viewport.detach();\n    this._dataSourceChanges.next(undefined);\n    this._dataSourceChanges.complete();\n    this.viewChange.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n    // Update $implicit for any items that had an identity change.\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n      view.context.$implicit = record.item;\n    });\n    // Update the context variables on all items.\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n}\nCdkVirtualForOf.ɵfac = function CdkVirtualForOf_Factory(t) {\n  return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nCdkVirtualForOf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualForOf,\n  selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n  inputs: {\n    cdkVirtualForOf: \"cdkVirtualForOf\",\n    cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n    cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n    cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }],\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i2$1._RecycleViewRepeaterStrategy,\n      decorators: [{\n        type: Inject,\n        args: [_VIEW_REPEATER_STRATEGY]\n      }]\n    }, {\n      type: CdkVirtualScrollViewport,\n      decorators: [{\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n  }\n}\nCdkVirtualScrollableElement.ɵfac = function CdkVirtualScrollableElement_Factory(t) {\n  return new (t || CdkVirtualScrollableElement)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\nCdkVirtualScrollableElement.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualScrollableElement,\n  selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n  hostAttrs: [1, \"cdk-virtual-scrollable\"],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLLABLE,\n    useExisting: CdkVirtualScrollableElement\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableElement, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualScrollingElement]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }],\n      standalone: true,\n      host: {\n        'class': 'cdk-virtual-scrollable'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n  constructor(scrollDispatcher, ngZone, dir) {\n    super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n}\nCdkVirtualScrollableWindow.ɵfac = function CdkVirtualScrollableWindow_Factory(t) {\n  return new (t || CdkVirtualScrollableWindow)(i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\nCdkVirtualScrollableWindow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualScrollableWindow,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLLABLE,\n    useExisting: CdkVirtualScrollableWindow\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableWindow, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }],\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkScrollableModule {}\nCdkScrollableModule.ɵfac = function CdkScrollableModule_Factory(t) {\n  return new (t || CdkScrollableModule)();\n};\nCdkScrollableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CdkScrollableModule\n});\nCdkScrollableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      imports: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {}\nScrollingModule.ɵfac = function ScrollingModule_Factory(t) {\n  return new (t || ScrollingModule)();\n};\nScrollingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ScrollingModule\n});\nScrollingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, BidiModule, CdkScrollableModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollableWindow, CdkVirtualScrollableElement],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollableWindow, CdkVirtualScrollableElement]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["coerceNumberProperty", "coerceElement", "coerceBooleanProperty", "i0", "InjectionToken", "forwardRef", "Directive", "Input", "Injectable", "Optional", "Inject", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "SkipSelf", "ElementRef", "NgModule", "Subject", "of", "Observable", "fromEvent", "animationFrameScheduler", "asapScheduler", "Subscription", "isObservable", "distinctUntilChanged", "auditTime", "filter", "takeUntil", "startWith", "pairwise", "switchMap", "shareReplay", "i1", "getRtlScrollAxisType", "supportsScrollBehavior", "Platform", "DOCUMENT", "i2", "BidiModule", "i2$1", "isDataSource", "ArrayDataSource", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "ɵdir", "provide", "useFactory", "deps", "type", "args", "selector", "standalone", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "document", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "_document", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_addGlobalListener", "subscription", "_removeGlobalListener", "ngOnDestroy", "for<PERSON>ach", "_", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "_getWindow", "defaultView", "window", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "runOutsideAngular", "NgZone", "ɵprov", "providedIn", "undefined", "decorators", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "_destroyed", "_elementScrolled", "ngOnInit", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "Directionality", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "event", "addEventListener", "change", "_viewportSize", "removeEventListener", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_orientation", "_calculateSpacerSize", "appendOnly", "_appendOnly", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "Promise", "resolve", "then", "run", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "fn", "ChangeDetectorRef", "ɵcmp", "virtualScrollable", "host", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "cdkVirtualForOf", "_cdkVirtualForOf", "_dataSourceChanges", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "cdkVirtualForTemplate", "_template", "cdkVirtualForTemplateCacheSize", "_view<PERSON><PERSON><PERSON>er", "viewCacheSize", "_viewContainerRef", "_differs", "viewChange", "prev", "cur", "_changeDataSource", "_differ", "_data", "_onRenderedDataChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "i", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "first", "last", "even", "odd", "templateRef", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkVirtualScrollableElement", "useExisting", "CdkVirtualScrollableWindow", "CdkScrollableModule", "ɵmod", "ɵinj", "exports", "imports", "ScrollingModule"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/cdk/fesm2020/scrolling.mjs"], "sourcesContent": ["import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n}\nCdkFixedSizeVirtualScroll.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkFixedSizeVirtualScroll.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n        {\n            provide: VIRTUAL_SCROLL_STRATEGY,\n            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n        },\n    ], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    standalone: true,\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n}\nScrollDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n}\nCdkScrollable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkScrollable.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n}\nViewportRuler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nViewportRuler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n}\nCdkVirtualScrollable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualScrollable.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkVirtualScrollable, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(value) {\n        this._appendOnly = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        this.scrollable = scrollable;\n        this._platform = inject(Platform);\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        this._appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n}\nCdkVirtualScrollViewport.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }, { token: VIRTUAL_SCROLLABLE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nCdkVirtualScrollViewport.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: \"appendOnly\" }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n        },\n    ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLL_STRATEGY]\n                }] }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: ScrollDispatcher }, { type: ViewportRuler }, { type: CdkVirtualScrollable, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLLABLE]\n                }] }]; }, propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n}\nCdkVirtualForOf.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualForOf.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.NgZone }]; }, propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n}\nCdkVirtualScrollableElement.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualScrollableElement.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor(scrollDispatcher, ngZone, dir) {\n        super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n}\nCdkVirtualScrollableWindow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [{ token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualScrollableWindow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkScrollableModule {\n}\nCdkScrollableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCdkScrollableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] });\nCdkScrollableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollableModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n}\nScrollingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nScrollingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n        CdkFixedSizeVirtualScroll,\n        CdkVirtualForOf,\n        CdkVirtualScrollableWindow,\n        CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n        CdkVirtualForOf,\n        CdkVirtualScrollViewport,\n        CdkVirtualScrollableWindow,\n        CdkVirtualScrollableElement] });\nScrollingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n        CdkScrollableModule,\n        CdkVirtualScrollViewport, BidiModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,uBAAuB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5N,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,QAAQ,MAAM;AAC7H,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,oBAAoB,EAAEC,sBAAsB,EAAEC,QAAQ,QAAQ,uBAAuB;AAC9F,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,eAAe,EAAEC,uBAAuB,EAAEC,4BAA4B,QAAQ,0BAA0B;;AAE/H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA,MAAMC,uBAAuB,GAAG,IAAI5C,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6C,8BAA8B,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIC,WAAW,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,IAAIlC,OAAO,EAAE;IACzC;IACA,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAACE,IAAI,CAAC5B,oBAAoB,EAAE,CAAC;IACjF;IACA,IAAI,CAAC6B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIQ,MAAM,CAACC,QAAQ,EAAE;IACb,IAAI,CAACL,SAAS,GAAGK,QAAQ;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EACA;EACAC,MAAM,GAAG;IACL,IAAI,CAACX,oBAAoB,CAACY,QAAQ,EAAE;IACpC,IAAI,CAACT,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,uBAAuB,CAAChB,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACX,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACU,uBAAuB,EAAE;IAC9B,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EACA;EACAM,iBAAiB,GAAG;IAChB,IAAI,CAACN,oBAAoB,EAAE;EAC/B;EACA;EACAO,mBAAmB,GAAG;IAClB,IAAI,CAACR,uBAAuB,EAAE;IAC9B,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EACA;EACAQ,iBAAiB,GAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuB,GAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAa,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACnB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACoB,cAAc,CAACF,KAAK,GAAG,IAAI,CAACjB,SAAS,EAAEkB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuB,GAAG;IACtB,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACqB,mBAAmB,CAAC,IAAI,CAACrB,SAAS,CAACsB,aAAa,EAAE,GAAG,IAAI,CAACrB,SAAS,CAAC;EACvF;EACA;EACAM,oBAAoB,GAAG;IACnB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;MACjB;IACJ;IACA,MAAMuB,aAAa,GAAG,IAAI,CAACvB,SAAS,CAACwB,gBAAgB,EAAE;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAC5B,SAAS,CAAC6B,eAAe,EAAE;IACrD,MAAMC,UAAU,GAAG,IAAI,CAAC9B,SAAS,CAACsB,aAAa,EAAE;IACjD,IAAIS,YAAY,GAAG,IAAI,CAAC/B,SAAS,CAACgC,mBAAmB,EAAE;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAAChC,SAAS,GAAG,CAAC,GAAG8B,YAAY,GAAG,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAC9E;IACA,IAAIwB,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC3B,SAAS,CAAC;MAChE,MAAMoC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACpC,SAAS;QAC/CwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAACzB,SAAS;IAClE,IAAIwC,WAAW,GAAG,IAAI,CAACvC,YAAY,IAAIuB,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGsC,WAAW,IAAI,IAAI,CAACxC,SAAS,CAAC;MACjFwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM0C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC1B,SAAS,IAAI8B,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAACzC,YAAY,IAAIuB,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGwC,SAAS,IAAI,IAAI,CAAC1C,SAAS,CAAC;QAC7E,IAAI2C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAAC6C,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAACzB,SAAS,CAAC8C,wBAAwB,CAAC,IAAI,CAAC7C,SAAS,GAAGwB,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAAC7B,oBAAoB,CAACkD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsC,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5B1D,WAAW,GAAG;IACV,IAAI,CAACQ,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB;IACA,IAAI,CAAC+C,eAAe,GAAG,IAAI1D,8BAA8B,CAAC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EAChH;EACA;EACA,IAAIF,QAAQ,GAAG;IACX,OAAO,IAAI,CAACO,SAAS;EACzB;EACA,IAAIP,QAAQ,CAAC0D,KAAK,EAAE;IAChB,IAAI,CAACnD,SAAS,GAAG1D,oBAAoB,CAAC6G,KAAK,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACI,IAAIzD,WAAW,GAAG;IACd,OAAO,IAAI,CAACO,YAAY;EAC5B;EACA,IAAIP,WAAW,CAACyD,KAAK,EAAE;IACnB,IAAI,CAAClD,YAAY,GAAG3D,oBAAoB,CAAC6G,KAAK,CAAC;EACnD;EACA;AACJ;AACA;EACI,IAAIxD,WAAW,GAAG;IACd,OAAO,IAAI,CAACO,YAAY;EAC5B;EACA,IAAIP,WAAW,CAACwD,KAAK,EAAE;IACnB,IAAI,CAACjD,YAAY,GAAG5D,oBAAoB,CAAC6G,KAAK,CAAC;EACnD;EACAC,WAAW,GAAG;IACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACnG;AACJ;AACAuD,yBAAyB,CAACG,IAAI;EAAA,iBAA6FH,yBAAyB;AAAA,CAAmD;AACvMA,yBAAyB,CAACI,IAAI,kBADmF7G,EAAE;EAAA,MACJyG,yBAAyB;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WADvBzG,EAAE,oBACmM,CAC9S;IACI8G,OAAO,EAAEjE,uBAAuB;IAChCkE,UAAU,EAAET,sCAAsC;IAClDU,IAAI,EAAE,CAAC9G,UAAU,CAAC,MAAMuG,yBAAyB,CAAC;EACtD,CAAC,CACJ,GAP4GzG,EAAE;AAAA,EAOxE;AAC3C;EAAA,mDARiHA,EAAE,mBAQnByG,yBAAyB,EAAc,CAAC;IAC5HQ,IAAI,EAAE9G,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCAAuC;MACjDC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,CACP;QACIP,OAAO,EAAEjE,uBAAuB;QAChCkE,UAAU,EAAET,sCAAsC;QAClDU,IAAI,EAAE,CAAC9G,UAAU,CAAC,MAAMuG,yBAAyB,CAAC;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzD,QAAQ,EAAE,CAAC;MACzBiE,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE6C,WAAW,EAAE,CAAC;MACdgE,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE8C,WAAW,EAAE,CAAC;MACd+D,IAAI,EAAE7G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkH,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBxE,WAAW,CAACyE,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IACtC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACE,SAAS,GAAG,IAAI1G,OAAO,EAAE;IAC9B;IACA,IAAI,CAAC2G,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,EAAE;IACjC,IAAI,CAACC,SAAS,GAAGN,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIO,QAAQ,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAACK,GAAG,CAACD,UAAU,CAAC,EAAE;MACxC,IAAI,CAACJ,gBAAgB,CAACM,GAAG,CAACF,UAAU,EAAEA,UAAU,CAACG,eAAe,EAAE,CAACC,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAACtB,IAAI,CAAC6B,UAAU,CAAC,CAAC,CAAC;IACxH;EACJ;EACA;AACJ;AACA;AACA;EACIK,UAAU,CAACL,UAAU,EAAE;IACnB,MAAMM,mBAAmB,GAAG,IAAI,CAACV,gBAAgB,CAACW,GAAG,CAACP,UAAU,CAAC;IACjE,IAAIM,mBAAmB,EAAE;MACrBA,mBAAmB,CAACE,WAAW,EAAE;MACjC,IAAI,CAACZ,gBAAgB,CAACa,MAAM,CAACT,UAAU,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,QAAQ,CAACC,aAAa,GAAGvB,mBAAmB,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAO5H,EAAE,EAAE;IACf;IACA,OAAO,IAAIC,UAAU,CAAE4H,QAAQ,IAAK;MAChC,IAAI,CAAC,IAAI,CAACnB,mBAAmB,EAAE;QAC3B,IAAI,CAACoB,kBAAkB,EAAE;MAC7B;MACA;MACA;MACA,MAAMC,YAAY,GAAGJ,aAAa,GAAG,CAAC,GAChC,IAAI,CAAClB,SAAS,CAACtE,IAAI,CAAC3B,SAAS,CAACmH,aAAa,CAAC,CAAC,CAACP,SAAS,CAACS,QAAQ,CAAC,GACjE,IAAI,CAACpB,SAAS,CAACW,SAAS,CAACS,QAAQ,CAAC;MACxC,IAAI,CAAClB,cAAc,EAAE;MACrB,OAAO,MAAM;QACToB,YAAY,CAACP,WAAW,EAAE;QAC1B,IAAI,CAACb,cAAc,EAAE;QACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;UACtB,IAAI,CAACqB,qBAAqB,EAAE;QAChC;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAC,WAAW,GAAG;IACV,IAAI,CAACD,qBAAqB,EAAE;IAC5B,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,CAAC,CAACC,CAAC,EAAEC,SAAS,KAAK,IAAI,CAACf,UAAU,CAACe,SAAS,CAAC,CAAC;IAC3E,IAAI,CAAC3B,SAAS,CAAC5D,QAAQ,EAAE;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwF,gBAAgB,CAACC,mBAAmB,EAAEX,aAAa,EAAE;IACjD,MAAMY,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;IACvE,OAAO,IAAI,CAACZ,QAAQ,CAACC,aAAa,CAAC,CAACxF,IAAI,CAAC1B,MAAM,CAACgI,MAAM,IAAI;MACtD,OAAO,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;EACP;EACA;EACAD,2BAA2B,CAACF,mBAAmB,EAAE;IAC7C,MAAMK,mBAAmB,GAAG,EAAE;IAC9B,IAAI,CAAC/B,gBAAgB,CAACsB,OAAO,CAAC,CAACU,aAAa,EAAE5B,UAAU,KAAK;MACzD,IAAI,IAAI,CAAC6B,0BAA0B,CAAC7B,UAAU,EAAEsB,mBAAmB,CAAC,EAAE;QAClEK,mBAAmB,CAACG,IAAI,CAAC9B,UAAU,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAO2B,mBAAmB;EAC9B;EACA;EACAI,UAAU,GAAG;IACT,OAAO,IAAI,CAACjC,SAAS,CAACkC,WAAW,IAAIC,MAAM;EAC/C;EACA;EACAJ,0BAA0B,CAAC7B,UAAU,EAAEsB,mBAAmB,EAAE;IACxD,IAAIY,OAAO,GAAGtK,aAAa,CAAC0J,mBAAmB,CAAC;IAChD,IAAIa,iBAAiB,GAAGnC,UAAU,CAACoC,aAAa,EAAE,CAACC,aAAa;IAChE;IACA;IACA,GAAG;MACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;IACzC,OAAO,KAAK;EAChB;EACA;EACAxB,kBAAkB,GAAG;IACjB,IAAI,CAACpB,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACiD,iBAAiB,CAAC,MAAM;MAC5D,MAAMN,MAAM,GAAG,IAAI,CAACF,UAAU,EAAE;MAChC,OAAO7I,SAAS,CAAC+I,MAAM,CAACzC,QAAQ,EAAE,QAAQ,CAAC,CAACY,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAACtB,IAAI,EAAE,CAAC;IACtF,CAAC,CAAC;EACN;EACA;EACA6C,qBAAqB,GAAG;IACpB,IAAI,IAAI,CAACtB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACc,WAAW,EAAE;MACtC,IAAI,CAACd,mBAAmB,GAAG,IAAI;IACnC;EACJ;AACJ;AACAL,gBAAgB,CAACX,IAAI;EAAA,iBAA6FW,gBAAgB,EA5KjBvH,EAAE,UA4KiCA,EAAE,CAAC0K,MAAM,GA5K5C1K,EAAE,UA4KuDiC,EAAE,CAACG,QAAQ,GA5KpEpC,EAAE,UA4K+EqC,QAAQ;AAAA,CAA6D;AACvQkF,gBAAgB,CAACoD,KAAK,kBA7K2F3K,EAAE;EAAA,OA6KGuH,gBAAgB;EAAA,SAAhBA,gBAAgB;EAAA,YAAc;AAAM,EAAG;AAC7J;EAAA,mDA9KiHvH,EAAE,mBA8KnBuH,gBAAgB,EAAc,CAAC;IACnHN,IAAI,EAAE5G,UAAU;IAChB6G,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3D,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAEhF,EAAE,CAACG;IAAS,CAAC,EAAE;MAAE6E,IAAI,EAAE4D,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1G7D,IAAI,EAAE3G;MACV,CAAC,EAAE;QACC2G,IAAI,EAAE1G,MAAM;QACZ2G,IAAI,EAAE,CAAC7E,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0I,aAAa,CAAC;EAChBhI,WAAW,CAACiI,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAG,IAAInK,OAAO,EAAE;IAC/B,IAAI,CAACoK,gBAAgB,GAAG,IAAIlK,UAAU,CAAE4H,QAAQ,IAAK,IAAI,CAACmC,MAAM,CAACT,iBAAiB,CAAC,MAAMrJ,SAAS,CAAC,IAAI,CAAC4J,UAAU,CAACT,aAAa,EAAE,QAAQ,CAAC,CACtIlH,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACwJ,UAAU,CAAC,CAAC,CAChC9C,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;EAC9B;EACAuC,QAAQ,GAAG;IACP,IAAI,CAACL,gBAAgB,CAAChD,QAAQ,CAAC,IAAI,CAAC;EACxC;EACAkB,WAAW,GAAG;IACV,IAAI,CAAC8B,gBAAgB,CAAC1C,UAAU,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC6C,UAAU,CAAC/E,IAAI,EAAE;IACtB,IAAI,CAAC+E,UAAU,CAACrH,QAAQ,EAAE;EAC9B;EACA;EACAsE,eAAe,GAAG;IACd,OAAO,IAAI,CAACgD,gBAAgB;EAChC;EACA;EACAf,aAAa,GAAG;IACZ,OAAO,IAAI,CAACU,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,QAAQ,CAACC,OAAO,EAAE;IACd,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACT,aAAa;IACxC,MAAMmB,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACzE,KAAK,IAAI,KAAK;IACjD;IACA,IAAI8E,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;MACtBH,OAAO,CAACG,IAAI,GAAGD,KAAK,GAAGF,OAAO,CAACvG,GAAG,GAAGuG,OAAO,CAACxG,KAAK;IACtD;IACA,IAAIwG,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;MACvBJ,OAAO,CAACI,KAAK,GAAGF,KAAK,GAAGF,OAAO,CAACxG,KAAK,GAAGwG,OAAO,CAACvG,GAAG;IACvD;IACA;IACA,IAAIuG,OAAO,CAACK,MAAM,IAAI,IAAI,EAAE;MACxBL,OAAO,CAACM,GAAG,GACPL,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGR,OAAO,CAACK,MAAM;IAC1D;IACA;IACA,IAAIH,KAAK,IAAIxJ,oBAAoB,EAAE,IAAI,CAAC,CAAC,gCAAgC;MACrE,IAAIsJ,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBH,OAAO,CAACI,KAAK,GACTH,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGV,OAAO,CAACG,IAAI;MACtD;MACA,IAAIzJ,oBAAoB,EAAE,IAAI,CAAC,CAAC,kCAAkC;QAC9DsJ,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK;MAChC,CAAC,MACI,IAAI1J,oBAAoB,EAAE,IAAI,CAAC,CAAC,iCAAiC;QAClEsJ,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK,GAAG,CAACJ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;MACjE;IACJ,CAAC,MACI;MACD,IAAIJ,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;QACvBJ,OAAO,CAACG,IAAI,GACRF,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGV,OAAO,CAACI,KAAK;MACvD;IACJ;IACA,IAAI,CAACO,qBAAqB,CAACX,OAAO,CAAC;EACvC;EACAW,qBAAqB,CAACX,OAAO,EAAE;IAC3B,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACT,aAAa;IACxC,IAAIpI,sBAAsB,EAAE,EAAE;MAC1BsJ,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;IACxB,CAAC,MACI;MACD,IAAIA,OAAO,CAACM,GAAG,IAAI,IAAI,EAAE;QACrBL,EAAE,CAACW,SAAS,GAAGZ,OAAO,CAACM,GAAG;MAC9B;MACA,IAAIN,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBF,EAAE,CAACY,UAAU,GAAGb,OAAO,CAACG,IAAI;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrG,mBAAmB,CAACgH,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMf,EAAE,GAAG,IAAI,CAACT,UAAU,CAACT,aAAa;IACxC,IAAI+B,IAAI,IAAI,KAAK,EAAE;MACf,OAAOb,EAAE,CAACW,SAAS;IACvB;IACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;MAClB,OAAOb,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACW,SAAS;IAC3D;IACA;IACA,MAAMV,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACzE,KAAK,IAAI,KAAK;IACjD,IAAI4F,IAAI,IAAI,OAAO,EAAE;MACjBA,IAAI,GAAGZ,KAAK,GAAGc,KAAK,GAAGD,IAAI;IAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBA,IAAI,GAAGZ,KAAK,GAAGa,IAAI,GAAGC,KAAK;IAC/B;IACA,IAAId,KAAK,IAAIxJ,oBAAoB,EAAE,IAAI,CAAC,CAAC,kCAAkC;MACvE;MACA;MACA,IAAIoK,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACY,UAAU;MAC1D,CAAC,MACI;QACD,OAAOZ,EAAE,CAACY,UAAU;MACxB;IACJ,CAAC,MACI,IAAIX,KAAK,IAAIxJ,oBAAoB,EAAE,IAAI,CAAC,CAAC,iCAAiC;MAC3E;MACA;MACA,IAAIoK,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACY,UAAU,GAAGZ,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW;MAC1D,CAAC,MACI;QACD,OAAO,CAACT,EAAE,CAACY,UAAU;MACzB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACY,UAAU;MACxB,CAAC,MACI;QACD,OAAOZ,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACY,UAAU;MAC1D;IACJ;EACJ;AACJ;AACAtB,aAAa,CAACnE,IAAI;EAAA,iBAA6FmE,aAAa,EApVX/K,EAAE,mBAoV2BA,EAAE,CAACe,UAAU,GApV1Cf,EAAE,mBAoVqDuH,gBAAgB,GApVvEvH,EAAE,mBAoVkFA,EAAE,CAAC0K,MAAM,GApV7F1K,EAAE,mBAoVwGsC,EAAE,CAACmK,cAAc;AAAA,CAA4D;AACxS1B,aAAa,CAAClE,IAAI,kBArV+F7G,EAAE;EAAA,MAqVhB+K,aAAa;EAAA;EAAA;AAAA,EAAoF;AACpM;EAAA,mDAtViH/K,EAAE,mBAsVnB+K,aAAa,EAAc,CAAC;IAChH9D,IAAI,EAAE9G,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mCAAmC;MAC7CC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEjH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEkG,IAAI,EAAEM;IAAiB,CAAC,EAAE;MAAEN,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE3E,EAAE,CAACmK,cAAc;MAAE3B,UAAU,EAAE,CAAC;QAChJ7D,IAAI,EAAE3G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoM,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB5J,WAAW,CAAC0E,SAAS,EAAEyD,MAAM,EAAExD,QAAQ,EAAE;IACrC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACmF,OAAO,GAAG,IAAI3L,OAAO,EAAE;IAC5B;IACA,IAAI,CAAC4L,eAAe,GAAIC,KAAK,IAAK;MAC9B,IAAI,CAACF,OAAO,CAACvG,IAAI,CAACyG,KAAK,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC9E,SAAS,GAAGN,QAAQ;IACzBwD,MAAM,CAACT,iBAAiB,CAAC,MAAM;MAC3B,IAAIhD,SAAS,CAACqB,SAAS,EAAE;QACrB,MAAMqB,MAAM,GAAG,IAAI,CAACF,UAAU,EAAE;QAChC;QACA;QACAE,MAAM,CAAC4C,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;QACvD1C,MAAM,CAAC4C,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAACF,eAAe,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAACG,MAAM,EAAE,CAAC1E,SAAS,CAAC,MAAO,IAAI,CAAC2E,aAAa,GAAG,IAAK,CAAC;IAC9D,CAAC,CAAC;EACN;EACA9D,WAAW,GAAG;IACV,IAAI,IAAI,CAAC1B,SAAS,CAACqB,SAAS,EAAE;MAC1B,MAAMqB,MAAM,GAAG,IAAI,CAACF,UAAU,EAAE;MAChCE,MAAM,CAAC+C,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACL,eAAe,CAAC;MAC1D1C,MAAM,CAAC+C,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAACL,eAAe,CAAC;IACzE;IACA,IAAI,CAACD,OAAO,CAAC7I,QAAQ,EAAE;EAC3B;EACA;EACAoB,eAAe,GAAG;IACd,IAAI,CAAC,IAAI,CAAC8H,aAAa,EAAE;MACrB,IAAI,CAACE,mBAAmB,EAAE;IAC9B;IACA,MAAMC,MAAM,GAAG;MAAEC,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACL,aAAa,CAACK;IAAO,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAAC7F,SAAS,CAACqB,SAAS,EAAE;MAC3B,IAAI,CAACmE,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOG,MAAM;EACjB;EACA;EACAG,eAAe,GAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,EAAE;IACvD,MAAM;MAAEJ,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACnI,eAAe,EAAE;IAChD,OAAO;MACH2G,GAAG,EAAE0B,cAAc,CAAC1B,GAAG;MACvBH,IAAI,EAAE6B,cAAc,CAAC7B,IAAI;MACzBE,MAAM,EAAE2B,cAAc,CAAC1B,GAAG,GAAGwB,MAAM;MACnC1B,KAAK,EAAE4B,cAAc,CAAC7B,IAAI,GAAG0B,KAAK;MAClCC,MAAM;MACND;IACJ,CAAC;EACL;EACA;EACAI,yBAAyB,GAAG;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAAChG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAO;QAAEgD,GAAG,EAAE,CAAC;QAAEH,IAAI,EAAE;MAAE,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMjE,QAAQ,GAAG,IAAI,CAACM,SAAS;IAC/B,MAAMmC,MAAM,GAAG,IAAI,CAACF,UAAU,EAAE;IAChC,MAAMyD,eAAe,GAAGhG,QAAQ,CAACgG,eAAe;IAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,EAAE;IAC5D,MAAM9B,GAAG,GAAG,CAAC6B,YAAY,CAAC7B,GAAG,IACzBpE,QAAQ,CAACmG,IAAI,CAACzB,SAAS,IACvBjC,MAAM,CAAC2D,OAAO,IACdJ,eAAe,CAACtB,SAAS,IACzB,CAAC;IACL,MAAMT,IAAI,GAAG,CAACgC,YAAY,CAAChC,IAAI,IAC3BjE,QAAQ,CAACmG,IAAI,CAACxB,UAAU,IACxBlC,MAAM,CAAC4D,OAAO,IACdL,eAAe,CAACrB,UAAU,IAC1B,CAAC;IACL,OAAO;MAAEP,GAAG;MAAEH;IAAK,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIqB,MAAM,CAACgB,YAAY,GAAGtB,mBAAmB,EAAE;IACvC,OAAOsB,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACvJ,IAAI,CAAC3B,SAAS,CAACsM,YAAY,CAAC,CAAC,GAAG,IAAI,CAACpB,OAAO;EACvF;EACA;EACA3C,UAAU,GAAG;IACT,OAAO,IAAI,CAACjC,SAAS,CAACkC,WAAW,IAAIC,MAAM;EAC/C;EACA;EACAgD,mBAAmB,GAAG;IAClB,MAAMhD,MAAM,GAAG,IAAI,CAACF,UAAU,EAAE;IAChC,IAAI,CAACgD,aAAa,GAAG,IAAI,CAACxF,SAAS,CAACqB,SAAS,GACvC;MAAEuE,KAAK,EAAElD,MAAM,CAAC8D,UAAU;MAAEX,MAAM,EAAEnD,MAAM,CAAC+D;IAAY,CAAC,GACxD;MAAEb,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EACjC;AACJ;AACAX,aAAa,CAAC/F,IAAI;EAAA,iBAA6F+F,aAAa,EA/dX3M,EAAE,UA+d2BiC,EAAE,CAACG,QAAQ,GA/dxCpC,EAAE,UA+dmDA,EAAE,CAAC0K,MAAM,GA/d9D1K,EAAE,UA+dyEqC,QAAQ;AAAA,CAA6D;AACjQsK,aAAa,CAAChC,KAAK,kBAhe8F3K,EAAE;EAAA,OAgeA2M,aAAa;EAAA,SAAbA,aAAa;EAAA,YAAc;AAAM,EAAG;AACvJ;EAAA,mDAjeiH3M,EAAE,mBAienB2M,aAAa,EAAc,CAAC;IAChH1F,IAAI,EAAE5G,UAAU;IAChB6G,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3D,IAAI,EAAEhF,EAAE,CAACG;IAAS,CAAC,EAAE;MAAE6E,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE4D,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1G7D,IAAI,EAAE3G;MACV,CAAC,EAAE;QACC2G,IAAI,EAAE1G,MAAM;QACZ2G,IAAI,EAAE,CAAC7E,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8L,kBAAkB,GAAG,IAAIlO,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AACA,MAAMmO,oBAAoB,SAASrD,aAAa,CAAC;EAC7ChI,WAAW,CAACiI,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACIkD,mBAAmB,CAACC,WAAW,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACvD,UAAU,CAACT,aAAa;IAChD,OAAO+D,WAAW,KAAK,YAAY,GAAGC,UAAU,CAACrC,WAAW,GAAGqC,UAAU,CAACvC,YAAY;EAC1F;AACJ;AACAoC,oBAAoB,CAACxH,IAAI;EAAA,iBAA6FwH,oBAAoB,EApgBzBpO,EAAE,mBAogByCA,EAAE,CAACe,UAAU,GApgBxDf,EAAE,mBAogBmEuH,gBAAgB,GApgBrFvH,EAAE,mBAogBgGA,EAAE,CAAC0K,MAAM,GApgB3G1K,EAAE,mBAogBsHsC,EAAE,CAACmK,cAAc;AAAA,CAA4D;AACtT2B,oBAAoB,CAACvH,IAAI,kBArgBwF7G,EAAE;EAAA,MAqgBToO,oBAAoB;EAAA,WArgBbpO,EAAE;AAAA,EAqgBmD;AACtK;EAAA,mDAtgBiHA,EAAE,mBAsgBnBoO,oBAAoB,EAAc,CAAC;IACvHnH,IAAI,EAAE9G;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE8G,IAAI,EAAEjH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEkG,IAAI,EAAEM;IAAiB,CAAC,EAAE;MAAEN,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE3E,EAAE,CAACmK,cAAc;MAAE3B,UAAU,EAAE,CAAC;QAChJ7D,IAAI,EAAE3G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkO,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAACzJ,KAAK,IAAI0J,EAAE,CAAC1J,KAAK,IAAIyJ,EAAE,CAACxJ,GAAG,IAAIyJ,EAAE,CAACzJ,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0J,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAGvN,uBAAuB,GAAGC,aAAa;AAC/G;AACA,MAAMuN,wBAAwB,SAAST,oBAAoB,CAAC;EACxD;EACA,IAAIE,WAAW,GAAG;IACd,OAAO,IAAI,CAACQ,YAAY;EAC5B;EACA,IAAIR,WAAW,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACQ,YAAY,KAAKR,WAAW,EAAE;MACnC,IAAI,CAACQ,YAAY,GAAGR,WAAW;MAC/B,IAAI,CAACS,oBAAoB,EAAE;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,UAAU,GAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAU,CAACtI,KAAK,EAAE;IAClB,IAAI,CAACuI,WAAW,GAAGlP,qBAAqB,CAAC2G,KAAK,CAAC;EACnD;EACA3D,WAAW,CAACiI,UAAU,EAAEkE,kBAAkB,EAAEhE,MAAM,EAAE1E,eAAe,EAAE2E,GAAG,EAAEF,gBAAgB,EAAEkE,aAAa,EAAEjH,UAAU,EAAE;IACnH,KAAK,CAAC8C,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IAChD,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACkE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC1I,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC0B,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACT,SAAS,GAAGjH,MAAM,CAAC4B,QAAQ,CAAC;IACjC;IACA,IAAI,CAACgN,gBAAgB,GAAG,IAAInO,OAAO,EAAE;IACrC;IACA,IAAI,CAACoO,qBAAqB,GAAG,IAAIpO,OAAO,EAAE;IAC1C,IAAI,CAAC6N,YAAY,GAAG,UAAU;IAC9B,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC7L,mBAAmB,GAAG,IAAIjC,UAAU,CAAE4H,QAAQ,IAAK,IAAI,CAACvC,eAAe,CAACpD,mBAAmB,CAACkF,SAAS,CAAC9D,KAAK,IAAI8K,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM,IAAI,CAACtE,MAAM,CAACuE,GAAG,CAAC,MAAM1G,QAAQ,CAAC1C,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/L;IACA,IAAI,CAACkL,mBAAmB,GAAG,IAAI,CAACL,qBAAqB;IACrD;AACR;AACA;IACQ,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC1B;IACA,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACC,cAAc,GAAG;MAAE9K,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC1C;IACA,IAAI,CAAC8K,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAAC9C,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAAC+C,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,KAAK;IAC/C;IACA,IAAI,CAACC,yBAAyB,GAAG,KAAK;IACtC;IACA,IAAI,CAACC,wBAAwB,GAAG,EAAE;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAG7O,YAAY,CAAC8O,KAAK;IAC1C,IAAI,CAAC7J,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMC,KAAK,CAAC,gFAAgF,CAAC;IACjG;IACA,IAAI,CAACkM,gBAAgB,GAAGjB,aAAa,CAACnC,MAAM,EAAE,CAAC1E,SAAS,CAAC,MAAM;MAC3D,IAAI,CAACgI,iBAAiB,EAAE;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACpI,UAAU,EAAE;MAClB;MACA,IAAI,CAAC8C,UAAU,CAACT,aAAa,CAACgG,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrE,IAAI,CAACtI,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAoD,QAAQ,GAAG;IACP;IACA,IAAI,CAAC,IAAI,CAAC7D,SAAS,CAACqB,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACZ,UAAU,KAAK,IAAI,EAAE;MAC1B,KAAK,CAACoD,QAAQ,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACJ,MAAM,CAACT,iBAAiB,CAAC,MAAM6E,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;MAC7D,IAAI,CAACiB,oBAAoB,EAAE;MAC3B,IAAI,CAACjK,eAAe,CAAC9C,MAAM,CAAC,IAAI,CAAC;MACjC,IAAI,CAACwE,UAAU,CACVG,eAAe,EAAE,CACjBhF,IAAI;MACT;MACAxB,SAAS,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACAH,SAAS,CAAC,CAAC,EAAEiN,gBAAgB,CAAC,CAAC,CAC1BrG,SAAS,CAAC,MAAM,IAAI,CAAC9B,eAAe,CAACrC,iBAAiB,EAAE,CAAC;MAC9D,IAAI,CAACuM,0BAA0B,EAAE;IACrC,CAAC,CAAC,CAAC;EACP;EACAvH,WAAW,GAAG;IACV,IAAI,CAACrF,MAAM,EAAE;IACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,EAAE;IAC7B;IACA,IAAI,CAACuL,qBAAqB,CAACtL,QAAQ,EAAE;IACrC,IAAI,CAACqL,gBAAgB,CAACrL,QAAQ,EAAE;IAChC,IAAI,CAACqM,gBAAgB,CAAC1H,WAAW,EAAE;IACnC,KAAK,CAACS,WAAW,EAAE;EACvB;EACA;EACAzF,MAAM,CAACiN,KAAK,EAAE;IACV,IAAI,IAAI,CAACC,MAAM,KAAK,OAAO3M,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAACgH,MAAM,CAACT,iBAAiB,CAAC,MAAM;MAChC,IAAI,CAACmG,MAAM,GAAGD,KAAK;MACnB,IAAI,CAACC,MAAM,CAACC,UAAU,CAACxN,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACwN,gBAAgB,CAAC,CAAC,CAAC9G,SAAS,CAACwI,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;QAC7B,IAAID,SAAS,KAAK,IAAI,CAAChB,WAAW,EAAE;UAChC,IAAI,CAACA,WAAW,GAAGgB,SAAS;UAC5B,IAAI,CAACvK,eAAe,CAACpC,mBAAmB,EAAE;QAC9C;QACA,IAAI,CAAC6M,kBAAkB,EAAE;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAnN,MAAM,GAAG;IACL,IAAI,CAAC8M,MAAM,GAAG,IAAI;IAClB,IAAI,CAACxB,gBAAgB,CAAC/I,IAAI,EAAE;EAChC;EACA;EACAzB,aAAa,GAAG;IACZ,OAAO,IAAI,CAACmL,WAAW;EAC3B;EACA;EACA5K,eAAe,GAAG;IACd,OAAO,IAAI,CAAC8H,aAAa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACAnI,gBAAgB,GAAG;IACf,OAAO,IAAI,CAACgL,cAAc;EAC9B;EACAoB,yCAAyC,CAAC5E,IAAI,EAAE;IAC5C,OAAO,IAAI,CAAChC,aAAa,EAAE,CAACC,aAAa,CAACqD,qBAAqB,EAAE,CAACtB,IAAI,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACI3H,mBAAmB,CAACwM,IAAI,EAAE;IACtB,IAAI,IAAI,CAACxB,iBAAiB,KAAKwB,IAAI,EAAE;MACjC,IAAI,CAACxB,iBAAiB,GAAGwB,IAAI;MAC7B,IAAI,CAACpC,oBAAoB,EAAE;MAC3B,IAAI,CAAC2B,0BAA0B,EAAE;IACrC;EACJ;EACA;EACAvK,gBAAgB,CAACiL,KAAK,EAAE;IACpB,IAAI,CAAC5C,WAAW,CAAC,IAAI,CAACsB,cAAc,EAAEsB,KAAK,CAAC,EAAE;MAC1C,IAAI,IAAI,CAACpC,UAAU,EAAE;QACjBoC,KAAK,GAAG;UAAEpM,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACkK,cAAc,CAAC7K,GAAG,EAAEmM,KAAK,CAACnM,GAAG;QAAE,CAAC;MAC3E;MACA,IAAI,CAACoK,qBAAqB,CAAChJ,IAAI,CAAE,IAAI,CAACyJ,cAAc,GAAGsB,KAAK,CAAE;MAC9D,IAAI,CAACV,0BAA0B,CAAC,MAAM,IAAI,CAAClK,eAAe,CAACnC,iBAAiB,EAAE,CAAC;IACnF;EACJ;EACA;AACJ;AACA;EACIgN,+BAA+B,GAAG;IAC9B,OAAO,IAAI,CAACpB,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;EACvF;EACA;AACJ;AACA;AACA;EACI5J,wBAAwB,CAACkL,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;IAC9C;IACAD,MAAM,GAAG,IAAI,CAACtC,UAAU,IAAIuC,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;IAC1D;IACA;IACA,MAAM5F,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACzE,KAAK,IAAI,KAAK;IACjD,MAAM8K,YAAY,GAAG,IAAI,CAAClD,WAAW,IAAI,YAAY;IACrD,MAAMmD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,aAAa,GAAGF,YAAY,IAAI9F,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,IAAIiG,SAAS,GAAI,YAAWF,IAAK,IAAGG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAE,KAAI;IACvE,IAAI,CAACtB,sBAAsB,GAAGsB,MAAM;IACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;MACjBI,SAAS,IAAK,aAAYF,IAAK,SAAQ;MACvC;MACA;MACA;MACA,IAAI,CAACxB,kCAAkC,GAAG,IAAI;IAClD;IACA,IAAI,IAAI,CAAC4B,yBAAyB,IAAIF,SAAS,EAAE;MAC7C;MACA;MACA,IAAI,CAACE,yBAAyB,GAAGF,SAAS;MAC1C,IAAI,CAACjB,0BAA0B,CAAC,MAAM;QAClC,IAAI,IAAI,CAACT,kCAAkC,EAAE;UACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAAC8B,0BAA0B,EAAE;UAChE,IAAI,CAAC7B,kCAAkC,GAAG,KAAK;UAC/C,IAAI,CAAC7J,wBAAwB,CAAC,IAAI,CAAC4J,sBAAsB,CAAC;QAC9D,CAAC,MACI;UACD,IAAI,CAACxJ,eAAe,CAAClC,uBAAuB,EAAE;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,cAAc,CAAC4M,MAAM,EAAE7M,QAAQ,GAAG,MAAM,EAAE;IACtC,MAAM+G,OAAO,GAAG;MAAE/G;IAAS,CAAC;IAC5B,IAAI,IAAI,CAAC6J,WAAW,KAAK,YAAY,EAAE;MACnC9C,OAAO,CAACxG,KAAK,GAAGsM,MAAM;IAC1B,CAAC,MACI;MACD9F,OAAO,CAACM,GAAG,GAAGwF,MAAM;IACxB;IACA,IAAI,CAACpJ,UAAU,CAACqD,QAAQ,CAACC,OAAO,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIjH,aAAa,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIa,mBAAmB,CAACgH,IAAI,EAAE;IACtB;IACA,IAAIhH,mBAAmB;IACvB,IAAI,IAAI,CAAC4C,UAAU,IAAI,IAAI,EAAE;MACzB5C,mBAAmB,GAAIyM,KAAK,IAAK,KAAK,CAACzM,mBAAmB,CAACyM,KAAK,CAAC;IACrE,CAAC,MACI;MACDzM,mBAAmB,GAAIyM,KAAK,IAAK,IAAI,CAAC7J,UAAU,CAAC5C,mBAAmB,CAACyM,KAAK,CAAC;IAC/E;IACA,OAAOtM,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAACgH,IAAI,KAAK,IAAI,CAACgC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAAC0D,qBAAqB,EAAE,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIA,qBAAqB,CAAC1F,IAAI,EAAE;IACxB,IAAI2F,QAAQ;IACZ,MAAM1F,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMd,KAAK,GAAG,IAAI,CAACP,GAAG,EAAEzE,KAAK,IAAI,KAAK;IACtC,IAAI4F,IAAI,IAAI,OAAO,EAAE;MACjB2F,QAAQ,GAAGvG,KAAK,GAAGc,KAAK,GAAGD,IAAI;IACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpB2F,QAAQ,GAAGvG,KAAK,GAAGa,IAAI,GAAGC,KAAK;IACnC,CAAC,MACI,IAAIF,IAAI,EAAE;MACX2F,QAAQ,GAAG3F,IAAI;IACnB,CAAC,MACI;MACD2F,QAAQ,GAAG,IAAI,CAAC3D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;IACjE;IACA,MAAM4D,kBAAkB,GAAG,IAAI,CAAChK,UAAU,CAACgJ,yCAAyC,CAACe,QAAQ,CAAC;IAC9F,MAAME,kBAAkB,GAAG,IAAI,CAACnH,UAAU,CAACT,aAAa,CAACqD,qBAAqB,EAAE,CAACqE,QAAQ,CAAC;IAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;EAClD;EACA;EACAJ,0BAA0B,GAAG;IACzB,MAAMM,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC9H,aAAa;IACpD,OAAO,IAAI,CAAC+D,WAAW,KAAK,YAAY,GAAG8D,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACG,YAAY;EAC7F;EACA;AACJ;AACA;AACA;EACIC,gBAAgB,CAACpB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,MAAM,CAAC4B,gBAAgB,CAACpB,KAAK,EAAE,IAAI,CAAC9C,WAAW,CAAC;EAChE;EACA;EACAgC,iBAAiB,GAAG;IAChB;IACA,IAAI,CAACG,oBAAoB,EAAE;IAC3B,IAAI,CAACjK,eAAe,CAACpC,mBAAmB,EAAE;EAC9C;EACA;EACAqM,oBAAoB,GAAG;IACnB,IAAI,CAACxD,aAAa,GAAG,IAAI,CAAC/E,UAAU,CAACmG,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;EAC9E;EACA;EACAoC,0BAA0B,CAAC+B,QAAQ,EAAE;IACjC,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACtC,wBAAwB,CAACnG,IAAI,CAACyI,QAAQ,CAAC;IAChD;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvC,yBAAyB,EAAE;MACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAAChF,MAAM,CAACT,iBAAiB,CAAC,MAAM6E,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACyB,kBAAkB,EAAE;MAC7B,CAAC,CAAC,CAAC;IACP;EACJ;EACA;EACAA,kBAAkB,GAAG;IACjB,IAAI,CAACf,yBAAyB,GAAG,KAAK;IACtC;IACA;IACA;IACA;IACA,IAAI,CAACmC,eAAe,CAAC9H,aAAa,CAACmI,KAAK,CAACf,SAAS,GAAG,IAAI,CAACE,yBAAyB;IACnF;IACA;IACA;IACA,IAAI,CAAC3G,MAAM,CAACuE,GAAG,CAAC,MAAM,IAAI,CAACP,kBAAkB,CAACyD,YAAY,EAAE,CAAC;IAC7D,MAAMC,uBAAuB,GAAG,IAAI,CAACzC,wBAAwB;IAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;IAClC,KAAK,MAAM0C,EAAE,IAAID,uBAAuB,EAAE;MACtCC,EAAE,EAAE;IACR;EACJ;EACA;EACA9D,oBAAoB,GAAG;IACnB,IAAI,CAACc,mBAAmB,GACpB,IAAI,CAACvB,WAAW,KAAK,YAAY,GAAG,EAAE,GAAI,GAAE,IAAI,CAACqB,iBAAkB,IAAG;IAC1E,IAAI,CAACC,kBAAkB,GACnB,IAAI,CAACtB,WAAW,KAAK,YAAY,GAAI,GAAE,IAAI,CAACqB,iBAAkB,IAAG,GAAG,EAAE;EAC9E;AACJ;AACAd,wBAAwB,CAACjI,IAAI;EAAA,iBAA6FiI,wBAAwB,EAr4BjC7O,EAAE,mBAq4BiDA,EAAE,CAACe,UAAU,GAr4BhEf,EAAE,mBAq4B2EA,EAAE,CAAC8S,iBAAiB,GAr4BjG9S,EAAE,mBAq4B4GA,EAAE,CAAC0K,MAAM,GAr4BvH1K,EAAE,mBAq4BkI6C,uBAAuB,MAr4B3J7C,EAAE,mBAq4BsLsC,EAAE,CAACmK,cAAc,MAr4BzMzM,EAAE,mBAq4BoOuH,gBAAgB,GAr4BtPvH,EAAE,mBAq4BiQ2M,aAAa,GAr4BhR3M,EAAE,mBAq4B2RmO,kBAAkB;AAAA,CAA4D;AAC5dU,wBAAwB,CAACkE,IAAI,kBAt4BoF/S,EAAE;EAAA,MAs4BL6O,wBAAwB;EAAA;EAAA;IAAA;MAt4BrB7O,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAs4Byc,CACpjB;IACI8G,OAAO,EAAEiE,aAAa;IACtBhE,UAAU,EAAE,CAACiM,iBAAiB,EAAErP,QAAQ,KAAKqP,iBAAiB,IAAIrP,QAAQ;IAC1EqD,IAAI,EAAE,CAAC,CAAC,IAAI1G,QAAQ,EAAE,EAAE,IAAIC,MAAM,CAAC4N,kBAAkB,CAAC,CAAC,EAAEU,wBAAwB;EACrF,CAAC,CACJ,GA54B4G7O,EAAE,6BAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,+BA44B6P;MA54B/PA,EAAE,gBA44B0R;MA54B5RA,EAAE,eA44BkS;MA54BpSA,EAAE,uBA44BulB;IAAA;IAAA;MA54BzlBA,EAAE,aA44ByiB;MA54B3iBA,EAAE,6CA44ByiB;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EAAo1D;AACh/E;EAAA,mDA74BiHA,EAAE,mBA64BnB6O,wBAAwB,EAAc,CAAC;IAC3H5H,IAAI,EAAExG,SAAS;IACfyG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAE8L,IAAI,EAAE;QAC5C,OAAO,EAAE,6BAA6B;QACtC,mDAAmD,EAAE,8BAA8B;QACnF,iDAAiD,EAAE;MACvD,CAAC;MAAEC,aAAa,EAAExS,iBAAiB,CAACyS,IAAI;MAAEC,eAAe,EAAEzS,uBAAuB,CAAC0S,MAAM;MAAEjM,UAAU,EAAE,IAAI;MAAEC,SAAS,EAAE,CACpH;QACIP,OAAO,EAAEiE,aAAa;QACtBhE,UAAU,EAAE,CAACiM,iBAAiB,EAAErP,QAAQ,KAAKqP,iBAAiB,IAAIrP,QAAQ;QAC1EqD,IAAI,EAAE,CAAC,CAAC,IAAI1G,QAAQ,EAAE,EAAE,IAAIC,MAAM,CAAC4N,kBAAkB,CAAC,CAAC,EAAEU,wBAAwB;MACrF,CAAC,CACJ;MAAEyE,QAAQ,EAAE,shBAAshB;MAAEC,MAAM,EAAE,CAAC,srDAAsrD;IAAE,CAAC;EACnvE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtM,IAAI,EAAEjH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEkG,IAAI,EAAEjH,EAAE,CAAC8S;IAAkB,CAAC,EAAE;MAAE7L,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE4D,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5I7D,IAAI,EAAE3G;MACV,CAAC,EAAE;QACC2G,IAAI,EAAE1G,MAAM;QACZ2G,IAAI,EAAE,CAACrE,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEoE,IAAI,EAAE3E,EAAE,CAACmK,cAAc;MAAE3B,UAAU,EAAE,CAAC;QAC1C7D,IAAI,EAAE3G;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2G,IAAI,EAAEM;IAAiB,CAAC,EAAE;MAAEN,IAAI,EAAE0F;IAAc,CAAC,EAAE;MAAE1F,IAAI,EAAEmH,oBAAoB;MAAEtD,UAAU,EAAE,CAAC;QAClG7D,IAAI,EAAE3G;MACV,CAAC,EAAE;QACC2G,IAAI,EAAE1G,MAAM;QACZ2G,IAAI,EAAE,CAACiH,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEG,WAAW,EAAE,CAAC;MAC1CrH,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE4O,UAAU,EAAE,CAAC;MACb/H,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAEgD,mBAAmB,EAAE,CAAC;MACtB6D,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEyR,eAAe,EAAE,CAAC;MAClBpL,IAAI,EAAEpG,SAAS;MACfqG,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAEsM,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAACnF,WAAW,EAAEoF,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAMlI,EAAE,GAAGkI,IAAI;EACf,IAAI,CAAClI,EAAE,CAACmC,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAMgG,IAAI,GAAGnI,EAAE,CAACmC,qBAAqB,EAAE;EACvC,IAAIU,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOoF,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACjI,IAAI,GAAGiI,IAAI,CAAChI,KAAK;EACzD;EACA,OAAO8H,SAAS,KAAK,OAAO,GAAGE,IAAI,CAAC9H,GAAG,GAAG8H,IAAI,CAAC/H,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA,MAAMgI,eAAe,CAAC;EAClB;EACA,IAAIC,eAAe,GAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAe,CAACpN,KAAK,EAAE;IACvB,IAAI,CAACqN,gBAAgB,GAAGrN,KAAK;IAC7B,IAAIjE,YAAY,CAACiE,KAAK,CAAC,EAAE;MACrB,IAAI,CAACsN,kBAAkB,CAAC3N,IAAI,CAACK,KAAK,CAAC;IACvC,CAAC,MACI;MACD;MACA,IAAI,CAACsN,kBAAkB,CAAC3N,IAAI,CAAC,IAAI3D,eAAe,CAAClB,YAAY,CAACkF,KAAK,CAAC,GAAGA,KAAK,GAAGuN,KAAK,CAAC3H,IAAI,CAAC5F,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIwN,oBAAoB,GAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoB,CAACrB,EAAE,EAAE;IACzB,IAAI,CAACuB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,qBAAqB,GAAGtB,EAAE,GACzB,CAACrO,KAAK,EAAE6P,IAAI,KAAKxB,EAAE,CAACrO,KAAK,IAAI,IAAI,CAACsL,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC9K,KAAK,GAAG,CAAC,CAAC,EAAEqP,IAAI,CAAC,GACxFxJ,SAAS;EACnB;EACA;EACA,IAAIyJ,qBAAqB,CAAC5N,KAAK,EAAE;IAC7B,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC0N,YAAY,GAAG,IAAI;MACxB,IAAI,CAACG,SAAS,GAAG7N,KAAK;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI8N,8BAA8B,GAAG;IACjC,OAAO,IAAI,CAACC,aAAa,CAACC,aAAa;EAC3C;EACA,IAAIF,8BAA8B,CAACrD,IAAI,EAAE;IACrC,IAAI,CAACsD,aAAa,CAACC,aAAa,GAAG7U,oBAAoB,CAACsR,IAAI,CAAC;EACjE;EACApO,WAAW,EACX;EACA4R,iBAAiB,EACjB;EACAJ,SAAS,EACT;EACAK,QAAQ,EACR;EACAH,aAAa,EACb;EACAnR,SAAS,EAAE4H,MAAM,EAAE;IACf,IAAI,CAACyJ,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACnR,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACuR,UAAU,GAAG,IAAI5T,OAAO,EAAE;IAC/B;IACA,IAAI,CAAC+S,kBAAkB,GAAG,IAAI/S,OAAO,EAAE;IACvC;IACA,IAAI,CAAC4P,UAAU,GAAG,IAAI,CAACmD,kBAAkB,CAAC3Q,IAAI;IAC9C;IACAxB,SAAS,CAAC,IAAI,CAAC;IACf;IACAC,QAAQ,EAAE;IACV;IACA;IACA;IACAC,SAAS,CAAC,CAAC,CAAC+S,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;IAC7D;IACA/S,WAAW,CAAC,CAAC,CAAC,CAAC;IACf;IACA,IAAI,CAACiT,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACb,YAAY,GAAG,KAAK;IACzB,IAAI,CAAChJ,UAAU,GAAG,IAAInK,OAAO,EAAE;IAC/B,IAAI,CAAC4P,UAAU,CAACvI,SAAS,CAACwI,IAAI,IAAI;MAC9B,IAAI,CAACoE,KAAK,GAAGpE,IAAI;MACjB,IAAI,CAACqE,qBAAqB,EAAE;IAChC,CAAC,CAAC;IACF,IAAI,CAAC7R,SAAS,CAACoM,mBAAmB,CAACrM,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACwJ,UAAU,CAAC,CAAC,CAAC9C,SAAS,CAAC8I,KAAK,IAAI;MACnF,IAAI,CAACtB,cAAc,GAAGsB,KAAK;MAC3B,IAAI,IAAI,CAACyD,UAAU,CAACO,SAAS,CAACpE,MAAM,EAAE;QAClC9F,MAAM,CAACuE,GAAG,CAAC,MAAM,IAAI,CAACoF,UAAU,CAACxO,IAAI,CAAC,IAAI,CAACyJ,cAAc,CAAC,CAAC;MAC/D;MACA,IAAI,CAACqF,qBAAqB,EAAE;IAChC,CAAC,CAAC;IACF,IAAI,CAAC7R,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI8O,gBAAgB,CAACpB,KAAK,EAAE9C,WAAW,EAAE;IACjC,IAAI8C,KAAK,CAACpM,KAAK,IAAIoM,KAAK,CAACnM,GAAG,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAI,CAACmM,KAAK,CAACpM,KAAK,GAAG,IAAI,CAAC8K,cAAc,CAAC9K,KAAK,IAAIoM,KAAK,CAACnM,GAAG,GAAG,IAAI,CAAC6K,cAAc,CAAC7K,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAE,0DAAyD,CAAC;IAC3E;IACA;IACA,MAAMmR,kBAAkB,GAAGjE,KAAK,CAACpM,KAAK,GAAG,IAAI,CAAC8K,cAAc,CAAC9K,KAAK;IAClE;IACA,MAAMsQ,QAAQ,GAAGlE,KAAK,CAACnM,GAAG,GAAGmM,KAAK,CAACpM,KAAK;IACxC;IACA;IACA,IAAIuQ,SAAS;IACb,IAAIC,QAAQ;IACZ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,EAAEG,CAAC,EAAE,EAAE;MAC/B,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAClM,GAAG,CAACgN,CAAC,GAAGJ,kBAAkB,CAAC;MAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC3E,MAAM,EAAE;QAC/BuE,SAAS,GAAGC,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ;IACA;IACA,KAAK,IAAIF,CAAC,GAAGH,QAAQ,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAClM,GAAG,CAACgN,CAAC,GAAGJ,kBAAkB,CAAC;MAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC3E,MAAM,EAAE;QAC/BwE,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAACD,IAAI,CAACC,SAAS,CAAC3E,MAAM,GAAG,CAAC,CAAC;QACpD;MACJ;IACJ;IACA,OAAOuE,SAAS,IAAIC,QAAQ,GACtB/B,SAAS,CAACnF,WAAW,EAAE,KAAK,EAAEkH,QAAQ,CAAC,GAAG/B,SAAS,CAACnF,WAAW,EAAE,OAAO,EAAEiH,SAAS,CAAC,GACpF,CAAC;EACX;EACAK,SAAS,GAAG;IACR,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACb,YAAY,EAAE;MACnC;MACA;MACA;MACA,MAAMyB,OAAO,GAAG,IAAI,CAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC;MACtD,IAAI,CAACF,OAAO,EAAE;QACV,IAAI,CAACG,cAAc,EAAE;MACzB,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACJ,OAAO,CAAC;MAC/B;MACA,IAAI,CAACzB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAjL,WAAW,GAAG;IACV,IAAI,CAAC7F,SAAS,CAACQ,MAAM,EAAE;IACvB,IAAI,CAACkQ,kBAAkB,CAAC3N,IAAI,CAACwE,SAAS,CAAC;IACvC,IAAI,CAACmJ,kBAAkB,CAACjQ,QAAQ,EAAE;IAClC,IAAI,CAAC8Q,UAAU,CAAC9Q,QAAQ,EAAE;IAC1B,IAAI,CAACqH,UAAU,CAAC/E,IAAI,EAAE;IACtB,IAAI,CAAC+E,UAAU,CAACrH,QAAQ,EAAE;IAC1B,IAAI,CAAC0Q,aAAa,CAAC3Q,MAAM,EAAE;EAC/B;EACA;EACAqR,qBAAqB,GAAG;IACpB,IAAI,CAAC,IAAI,CAACrF,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACiG,cAAc,GAAG,IAAI,CAACb,KAAK,CAACgB,KAAK,CAAC,IAAI,CAACpG,cAAc,CAAC9K,KAAK,EAAE,IAAI,CAAC8K,cAAc,CAAC7K,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAACgQ,OAAO,EAAE;MACf;MACA;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACL,QAAQ,CAACuB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAAC,CAACK,MAAM,CAAC,CAAC5R,KAAK,EAAE6P,IAAI,KAAK;QAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC1P,KAAK,EAAE6P,IAAI,CAAC,GAAGA,IAAI;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACA;EACAY,iBAAiB,CAACqB,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAID,KAAK,EAAE;MACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACnC,YAAY,GAAG,IAAI;IACxB,OAAOkC,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAGtV,EAAE,EAAE;EAC7C;EACA;EACA8U,cAAc,GAAG;IACb,MAAMS,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAClE,MAAM;IAC/B,IAAIyE,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAAC3D,MAAM;IACrC,OAAOyE,CAAC,EAAE,EAAE;MACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAClM,GAAG,CAACgN,CAAC,CAAC;MAC1CC,IAAI,CAACgB,OAAO,CAAClS,KAAK,GAAG,IAAI,CAACsL,cAAc,CAAC9K,KAAK,GAAGyQ,CAAC;MAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;MACnDhB,IAAI,CAACkB,aAAa,EAAE;IACxB;EACJ;EACA;EACAX,aAAa,CAACJ,OAAO,EAAE;IACnB,IAAI,CAACpB,aAAa,CAACoC,YAAY,CAAChB,OAAO,EAAE,IAAI,CAAClB,iBAAiB,EAAE,CAACmC,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACzC,IAAI,CAAC;IAC1L;IACAwB,OAAO,CAACqB,qBAAqB,CAAEJ,MAAM,IAAK;MACtC,MAAMpB,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAClM,GAAG,CAACqO,MAAM,CAACE,YAAY,CAAC;MAC5DtB,IAAI,CAACgB,OAAO,CAACS,SAAS,GAAGL,MAAM,CAACzC,IAAI;IACxC,CAAC,CAAC;IACF;IACA,MAAMoC,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAClE,MAAM;IAC/B,IAAIyE,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAAC3D,MAAM;IACrC,OAAOyE,CAAC,EAAE,EAAE;MACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAClM,GAAG,CAACgN,CAAC,CAAC;MAC1CC,IAAI,CAACgB,OAAO,CAAClS,KAAK,GAAG,IAAI,CAACsL,cAAc,CAAC9K,KAAK,GAAGyQ,CAAC;MAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;IACvD;EACJ;EACA;EACAC,gCAAgC,CAACD,OAAO,EAAE;IACtCA,OAAO,CAACU,KAAK,GAAGV,OAAO,CAAClS,KAAK,KAAK,CAAC;IACnCkS,OAAO,CAACW,IAAI,GAAGX,OAAO,CAAClS,KAAK,KAAKkS,OAAO,CAACD,KAAK,GAAG,CAAC;IAClDC,OAAO,CAACY,IAAI,GAAGZ,OAAO,CAAClS,KAAK,GAAG,CAAC,KAAK,CAAC;IACtCkS,OAAO,CAACa,GAAG,GAAG,CAACb,OAAO,CAACY,IAAI;EAC/B;EACAL,oBAAoB,CAACH,MAAM,EAAEtS,KAAK,EAAE;IAChC;IACA;IACA;IACA;IACA,OAAO;MACHgT,WAAW,EAAE,IAAI,CAACjD,SAAS;MAC3BmC,OAAO,EAAE;QACLS,SAAS,EAAEL,MAAM,CAACzC,IAAI;QACtB;QACA;QACAP,eAAe,EAAE,IAAI,CAACC,gBAAgB;QACtCvP,KAAK,EAAE,CAAC,CAAC;QACTiS,KAAK,EAAE,CAAC,CAAC;QACTW,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,KAAK;QACXE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE;MACV,CAAC;MACD9S;IACJ,CAAC;EACL;AACJ;AACAqP,eAAe,CAACjN,IAAI;EAAA,iBAA6FiN,eAAe,EA3rCf7T,EAAE,mBA2rC+BA,EAAE,CAACyX,gBAAgB,GA3rCpDzX,EAAE,mBA2rC+DA,EAAE,CAAC0X,WAAW,GA3rC/E1X,EAAE,mBA2rC0FA,EAAE,CAAC2X,eAAe,GA3rC9G3X,EAAE,mBA2rCyH2C,uBAAuB,GA3rClJ3C,EAAE,mBA2rC6J6O,wBAAwB,MA3rCvL7O,EAAE,mBA2rCkNA,EAAE,CAAC0K,MAAM;AAAA,CAA4C;AAC1XmJ,eAAe,CAAChN,IAAI,kBA5rC6F7G,EAAE;EAAA,MA4rCd6T,eAAe;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA5rCH7T,EAAE,oBA4rCgS,CAAC;IAAE8G,OAAO,EAAEnE,uBAAuB;IAAEiV,QAAQ,EAAEhV;EAA6B,CAAC,CAAC;AAAA,EAAiB;AAClf;EAAA,mDA7rCiH5C,EAAE,mBA6rCnB6T,eAAe,EAAc,CAAC;IAClH5M,IAAI,EAAE9G,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CE,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEnE,uBAAuB;QAAEiV,QAAQ,EAAEhV;MAA6B,CAAC,CAAC;MACzFwE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEjH,EAAE,CAACyX;IAAiB,CAAC,EAAE;MAAExQ,IAAI,EAAEjH,EAAE,CAAC0X;IAAY,CAAC,EAAE;MAAEzQ,IAAI,EAAEjH,EAAE,CAAC2X;IAAgB,CAAC,EAAE;MAAE1Q,IAAI,EAAEzE,IAAI,CAACI,4BAA4B;MAAEkI,UAAU,EAAE,CAAC;QAC7K7D,IAAI,EAAE1G,MAAM;QACZ2G,IAAI,EAAE,CAACvE,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEsE,IAAI,EAAE4H,wBAAwB;MAAE/D,UAAU,EAAE,CAAC;QACjD7D,IAAI,EAAEnG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEmG,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoJ,eAAe,EAAE,CAAC;MACnE7M,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE8T,oBAAoB,EAAE,CAAC;MACvBjN,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAEkU,qBAAqB,EAAE,CAAC;MACxBrN,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAEoU,8BAA8B,EAAE,CAAC;MACjCvN,IAAI,EAAE7G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyX,2BAA2B,SAASzJ,oBAAoB,CAAC;EAC3DrL,WAAW,CAACiI,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;EACpD;EACA+F,yCAAyC,CAAC5E,IAAI,EAAE;IAC5C,OAAQ,IAAI,CAAChC,aAAa,EAAE,CAACC,aAAa,CAACqD,qBAAqB,EAAE,CAACtB,IAAI,CAAC,GACpE,IAAI,CAAChH,mBAAmB,CAACgH,IAAI,CAAC;EACtC;AACJ;AACAuL,2BAA2B,CAACjR,IAAI;EAAA,iBAA6FiR,2BAA2B,EAtuCvC7X,EAAE,mBAsuCuDA,EAAE,CAACe,UAAU,GAtuCtEf,EAAE,mBAsuCiFuH,gBAAgB,GAtuCnGvH,EAAE,mBAsuC8GA,EAAE,CAAC0K,MAAM,GAtuCzH1K,EAAE,mBAsuCoIsC,EAAE,CAACmK,cAAc;AAAA,CAA4D;AACpUoL,2BAA2B,CAAChR,IAAI,kBAvuCiF7G,EAAE;EAAA,MAuuCF6X,2BAA2B;EAAA;EAAA;EAAA;EAAA,WAvuC3B7X,EAAE,oBAuuCwJ,CAAC;IAAE8G,OAAO,EAAEqH,kBAAkB;IAAE2J,WAAW,EAAED;EAA4B,CAAC,CAAC,GAvuCrO7X,EAAE;AAAA,EAuuC2Q;AAC9X;EAAA,mDAxuCiHA,EAAE,mBAwuCnB6X,2BAA2B,EAAc,CAAC;IAC9H5Q,IAAI,EAAE9G,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BAA8B;MACxCE,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEqH,kBAAkB;QAAE2J,WAAW,EAAED;MAA4B,CAAC,CAAC;MACtFzQ,UAAU,EAAE,IAAI;MAChB6L,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhM,IAAI,EAAEjH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEkG,IAAI,EAAEM;IAAiB,CAAC,EAAE;MAAEN,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE3E,EAAE,CAACmK,cAAc;MAAE3B,UAAU,EAAE,CAAC;QAChJ7D,IAAI,EAAE3G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyX,0BAA0B,SAAS3J,oBAAoB,CAAC;EAC1DrL,WAAW,CAACkI,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACvC,KAAK,CAAC,IAAIpK,UAAU,CAAC2G,QAAQ,CAACgG,eAAe,CAAC,EAAEzC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IAC9E,IAAI,CAACE,gBAAgB,GAAG,IAAIlK,UAAU,CAAE4H,QAAQ,IAAK,IAAI,CAACmC,MAAM,CAACT,iBAAiB,CAAC,MAAMrJ,SAAS,CAACsG,QAAQ,EAAE,QAAQ,CAAC,CAACrE,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACwJ,UAAU,CAAC,CAAC,CAAC9C,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;EACjL;EACAmI,yCAAyC,CAAC5E,IAAI,EAAE;IAC5C,OAAO,IAAI,CAAChC,aAAa,EAAE,CAACC,aAAa,CAACqD,qBAAqB,EAAE,CAACtB,IAAI,CAAC;EAC3E;AACJ;AACAyL,0BAA0B,CAACnR,IAAI;EAAA,iBAA6FmR,0BAA0B,EAzwCrC/X,EAAE,mBAywCqDuH,gBAAgB,GAzwCvEvH,EAAE,mBAywCkFA,EAAE,CAAC0K,MAAM,GAzwC7F1K,EAAE,mBAywCwGsC,EAAE,CAACmK,cAAc;AAAA,CAA4D;AACxSsL,0BAA0B,CAAClR,IAAI,kBA1wCkF7G,EAAE;EAAA,MA0wCH+X,0BAA0B;EAAA;EAAA;EAAA,WA1wCzB/X,EAAE,oBA0wC+G,CAAC;IAAE8G,OAAO,EAAEqH,kBAAkB;IAAE2J,WAAW,EAAEC;EAA2B,CAAC,CAAC,GA1wC3L/X,EAAE;AAAA,EA0wCiO;AACpV;EAAA,mDA3wCiHA,EAAE,mBA2wCnB+X,0BAA0B,EAAc,CAAC;IAC7H9Q,IAAI,EAAE9G,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDE,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEqH,kBAAkB;QAAE2J,WAAW,EAAEC;MAA2B,CAAC,CAAC;MACrF3Q,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEM;IAAiB,CAAC,EAAE;MAAEN,IAAI,EAAEjH,EAAE,CAAC0K;IAAO,CAAC,EAAE;MAAEzD,IAAI,EAAE3E,EAAE,CAACmK,cAAc;MAAE3B,UAAU,EAAE,CAAC;QACvH7D,IAAI,EAAE3G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0X,mBAAmB,CAAC;AAE1BA,mBAAmB,CAACpR,IAAI;EAAA,iBAA6FoR,mBAAmB;AAAA,CAAkD;AAC1LA,mBAAmB,CAACC,IAAI,kBAhyCyFjY,EAAE;EAAA,MAgyCGgY;AAAmB,EAAuD;AAChMA,mBAAmB,CAACE,IAAI,kBAjyCyFlY,EAAE,qBAiyCyB;AAC5I;EAAA,mDAlyCiHA,EAAE,mBAkyCnBgY,mBAAmB,EAAc,CAAC;IACtH/Q,IAAI,EAAEjG,QAAQ;IACdkG,IAAI,EAAE,CAAC;MACCiR,OAAO,EAAE,CAACpN,aAAa,CAAC;MACxBqN,OAAO,EAAE,CAACrN,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMsN,eAAe,CAAC;AAEtBA,eAAe,CAACzR,IAAI;EAAA,iBAA6FyR,eAAe;AAAA,CAAkD;AAClLA,eAAe,CAACJ,IAAI,kBA/yC6FjY,EAAE;EAAA,MA+yCDqY;AAAe,EAQ1F;AACvCA,eAAe,CAACH,IAAI,kBAxzC6FlY,EAAE;EAAA,UAwzC0BuC,UAAU,EAC/IyV,mBAAmB,EACnBnJ,wBAAwB,EAAEtM,UAAU,EAAEyV,mBAAmB;AAAA,EAAI;AACrE;EAAA,mDA3zCiHhY,EAAE,mBA2zCnBqY,eAAe,EAAc,CAAC;IAClHpR,IAAI,EAAEjG,QAAQ;IACdkG,IAAI,EAAE,CAAC;MACCkR,OAAO,EAAE,CACL7V,UAAU,EACVyV,mBAAmB,EACnBnJ,wBAAwB,EACxBpI,yBAAyB,EACzBoN,eAAe,EACfkE,0BAA0B,EAC1BF,2BAA2B,CAC9B;MACDM,OAAO,EAAE,CACL5V,UAAU,EACVyV,mBAAmB,EACnBvR,yBAAyB,EACzBoN,eAAe,EACfhF,wBAAwB,EACxBkJ,0BAA0B,EAC1BF,2BAA2B;IAEnC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpR,yBAAyB,EAAEsE,aAAa,EAAEiN,mBAAmB,EAAEnE,eAAe,EAAEhF,wBAAwB,EAAET,oBAAoB,EAAEyJ,2BAA2B,EAAEE,0BAA0B,EAAErL,mBAAmB,EAAEpF,mBAAmB,EAAExE,8BAA8B,EAAEyE,gBAAgB,EAAE8Q,eAAe,EAAElK,kBAAkB,EAAEtL,uBAAuB,EAAE8J,aAAa,EAAErG,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}