@echo off
echo Starting Oracul Development Environment...
echo.

echo Starting Backend (ASP.NET Core) on port 5144...
start "Oracul Backend" cmd /k "cd Oracul.Server && dotnet run"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend (Angular) on port 4200...
start "Oracul Frontend" cmd /k "cd oracul.client && npm start"

echo.
echo Development servers are starting...
echo Frontend: http://localhost:4200
echo Backend:  http://localhost:5144
echo.
echo Press any key to exit...
pause > nul
