@echo off
REM Simple Windows batch script for database seeding only

echo.
echo 🔮 Oracul Database Seeding Script
echo.

REM Change to project root directory
cd /d "%~dp0\.."

echo 🌱 Seeding Database with Oracle Profiles...
dotnet run --project Oracul.Server --seed-only

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Database seeded successfully!
    echo The database now contains 5 oracle profiles:
    echo   • Luna Starweaver (Astrologer)
    echo   • <PERSON> Moonchild (Crystal Healer)
    echo   • River Palmistry (Palm Reader)
    echo   • Aurora Wisdom (Spiritual Counselor)
    echo   • Cosmic Dawn (Numerologist)
) else (
    echo.
    echo ❌ Seeding failed!
)

echo.
pause
