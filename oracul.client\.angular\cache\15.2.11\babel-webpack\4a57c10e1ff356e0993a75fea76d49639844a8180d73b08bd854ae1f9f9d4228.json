{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProfileService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/profile`;\n    this.currentProfileSubject = new BehaviorSubject(null);\n    this.currentProfile$ = this.currentProfileSubject.asObservable();\n  }\n  /**\r\n   * Get HTTP headers with authentication token\r\n   */\n  getHttpHeaders() {\n    const token = localStorage.getItem('authToken');\n    let headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n    return headers;\n  }\n  // Profile CRUD Operations\n  getProfile(identifier) {\n    // Check if identifier is a slug or ID\n    const url = isNaN(Number(identifier)) ? `${this.API_URL}/slug/${identifier}` : `${this.API_URL}/${identifier}`;\n    return this.http.get(url).pipe(map(response => response.data), tap(profile => {\n      if (profile) {\n        this.currentProfileSubject.next(profile);\n        // Record profile view\n        this.recordProfileView({\n          profileId: profile.id,\n          referrer: document.referrer,\n          userAgent: navigator.userAgent\n        }).subscribe();\n      }\n    }), catchError(this.handleError));\n  }\n  getCurrentUserProfile() {\n    return this.http.get(`${this.API_URL}/me`, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  updateProfile(updates) {\n    const updateRequest = {\n      professionalTitle: updates.professionalTitle,\n      headline: updates.headline,\n      summary: updates.summary,\n      location: updates.location ? {\n        city: updates.location.city,\n        state: updates.location.state,\n        country: updates.location.country,\n        displayLocation: updates.location.displayLocation\n      } : undefined,\n      contactInfo: updates.contactInfo ? {\n        email: updates.contactInfo.email,\n        isEmailPublic: updates.contactInfo.isEmailPublic,\n        website: updates.contactInfo.website,\n        portfolioUrl: updates.contactInfo.portfolioUrl,\n        businessAddress: updates.contactInfo.businessAddress ? {\n          street: updates.contactInfo.businessAddress.street,\n          city: updates.contactInfo.businessAddress.city,\n          state: updates.contactInfo.businessAddress.state,\n          postalCode: updates.contactInfo.businessAddress.postalCode,\n          country: updates.contactInfo.businessAddress.country,\n          isPublic: updates.contactInfo.businessAddress.isPublic\n        } : undefined\n      } : undefined\n    };\n    return this.http.put(`${this.API_URL}`, updateRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  createProfile(profileData) {\n    const createRequest = {\n      username: profileData.username,\n      firstName: profileData.firstName,\n      lastName: profileData.lastName,\n      professionalTitle: profileData.professionalTitle,\n      headline: profileData.headline,\n      isPublic: profileData.isPublic ?? true\n    };\n    return this.http.post(`${this.API_URL}`, createRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  deleteProfile() {\n    return this.http.delete(`${this.API_URL}`, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(() => void 0), tap(() => this.currentProfileSubject.next(null)), catchError(this.handleError));\n  }\n  // Profile Search\n  searchProfiles(filters, page = 1, limit = 20) {\n    const searchRequest = {\n      location: filters.location,\n      skills: filters.skills || [],\n      sortBy: filters.sortBy || 'relevance',\n      page: page,\n      pageSize: limit\n    };\n    return this.http.post(`${this.API_URL}/search`, searchRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => response.data), catchError(this.handleError));\n  }\n  // Search profiles by term\n  searchProfilesByTerm(searchTerm, page = 1, pageSize = 20) {\n    const searchRequest = {\n      searchTerm: searchTerm,\n      skills: [],\n      sortBy: 'relevance',\n      page: page,\n      pageSize: pageSize\n    };\n    return this.http.post(`${this.API_URL}/search`, searchRequest, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(response => response.data), catchError(this.handleError));\n  }\n  // Get public profiles\n  getPublicProfiles(page = 1, pageSize = 20) {\n    return this.http.get(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`).pipe(map(response => response.data), catchError(this.handleError));\n  }\n  // Skills Management (TODO: Implement in backend)\n  addSkill(skill) {\n    // For now, return a mock skill until backend implements this\n    const newSkill = {\n      ...skill,\n      id: Date.now(),\n      endorsements: 0,\n      isEndorsedByCurrentUser: false\n    };\n    return new Observable(observer => {\n      observer.next(newSkill);\n      observer.complete();\n    });\n  }\n  updateSkill(skillId, updates) {\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      observer.next({\n        ...updates,\n        id: skillId\n      });\n      observer.complete();\n    });\n  }\n  deleteSkill(skillId) {\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      observer.next();\n      observer.complete();\n    });\n  }\n  endorseSkill(request) {\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      observer.next();\n      observer.complete();\n    });\n  }\n  // Experience Management\n  addExperience(experience) {\n    return this.http.post(`${this.API_URL}/me/experiences`, experience).pipe(catchError(this.handleError));\n  }\n  updateExperience(experienceId, updates) {\n    return this.http.put(`${this.API_URL}/me/experiences/${experienceId}`, updates).pipe(catchError(this.handleError));\n  }\n  deleteExperience(experienceId) {\n    return this.http.delete(`${this.API_URL}/me/experiences/${experienceId}`).pipe(catchError(this.handleError));\n  }\n  // Portfolio Management\n  addPortfolioItem(item) {\n    return this.http.post(`${this.API_URL}/me/portfolio`, item).pipe(catchError(this.handleError));\n  }\n  updatePortfolioItem(itemId, updates) {\n    return this.http.put(`${this.API_URL}/me/portfolio/${itemId}`, updates).pipe(catchError(this.handleError));\n  }\n  deletePortfolioItem(itemId) {\n    return this.http.delete(`${this.API_URL}/me/portfolio/${itemId}`).pipe(catchError(this.handleError));\n  }\n  // Achievements & Certifications\n  addAchievement(achievement) {\n    return this.http.post(`${this.API_URL}/me/achievements`, achievement).pipe(catchError(this.handleError));\n  }\n  addCertification(certification) {\n    return this.http.post(`${this.API_URL}/me/certifications`, certification).pipe(catchError(this.handleError));\n  }\n  // Blog Posts\n  getBlogPosts(profileId) {\n    // Blog posts are included in the profile data from the backend\n    // Return empty array for now, as they're part of the main profile\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  // Analytics\n  getProfileAnalytics() {\n    // For now, return mock analytics since backend doesn't have this endpoint yet\n    const mockAnalytics = {\n      profileViews: 0,\n      uniqueVisitors: 0,\n      viewsThisMonth: 0,\n      viewsThisWeek: 0,\n      topReferrers: [],\n      skillEndorsements: 0,\n      blogPostViews: 0,\n      contactButtonClicks: 0\n    };\n    return new Observable(observer => {\n      observer.next(mockAnalytics);\n      observer.complete();\n    });\n  }\n  recordProfileView(request) {\n    return this.http.post(`${this.API_URL}/view`, request, {\n      headers: this.getHttpHeaders()\n    }).pipe(map(() => void 0), catchError(this.handleError));\n  }\n  // File Upload\n  uploadProfilePhoto(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    const token = localStorage.getItem('authToken');\n    let headers = new HttpHeaders();\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n    return this.http.post(`${this.API_URL}/upload/profile-photo`, formData, {\n      headers: headers\n    }).pipe(map(response => ({\n      url: response.data\n    })), catchError(this.handleError));\n  }\n  uploadCoverPhoto(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    const token = localStorage.getItem('authToken');\n    let headers = new HttpHeaders();\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n    return this.http.post(`${this.API_URL}/upload/cover-photo`, formData, {\n      headers: headers\n    }).pipe(map(response => ({\n      url: response.data\n    })), catchError(this.handleError));\n  }\n  // Utility Methods\n  generateProfileSlug(firstName, lastName) {\n    // Generate slug client-side for now\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`.replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n    return new Observable(observer => {\n      observer.next({\n        slug\n      });\n      observer.complete();\n    });\n  }\n  checkSlugAvailability(slug) {\n    // For now, assume all slugs are available\n    // TODO: Implement in backend\n    return new Observable(observer => {\n      observer.next({\n        available: true\n      });\n      observer.complete();\n    });\n  }\n  // Social Sharing\n  getProfileShareData(profileId) {\n    // Generate share data from current profile\n    const currentProfile = this.currentProfileSubject.value;\n    if (currentProfile) {\n      const shareData = {\n        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\n        description: currentProfile.summary || currentProfile.headline || 'Professional profile',\n        imageUrl: currentProfile.profilePhotoUrl || '',\n        url: `${window.location.origin}/profile/${currentProfile.slug}`\n      };\n      return new Observable(observer => {\n        observer.next(shareData);\n        observer.complete();\n      });\n    }\n    return throwError(() => new Error('Profile not found'));\n  }\n  handleError(error) {\n    console.error('ProfileService error:', error);\n    let errorMessage = 'Възникна неочаквана грешка';\n    if (error.error && error.error.message) {\n      // Backend returned an error message\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Няма връзка със сървъра';\n    } else if (error.status === 401) {\n      errorMessage = 'Не сте упълномощени за тази операция';\n    } else if (error.status === 403) {\n      errorMessage = 'Нямате права за тази операция';\n    } else if (error.status === 404) {\n      errorMessage = 'Профилът не е намерен';\n    } else if (error.status >= 500) {\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function ProfileService_Factory(t) {\n      return new (t || ProfileService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileService,\n      factory: ProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,WAAW,QAA2B,sBAAsB;AAC7F,SAASC,UAAU,EAAEC,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mCAAmC;;;AA0B/D,OAAM,MAAOC,cAAc;EAMzBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IALP,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,UAAU;IAElD,0BAAqB,GAAG,IAAIT,eAAe,CAAqB,IAAI,CAAC;IACtE,oBAAe,GAAG,IAAI,CAACU,qBAAqB,CAACC,YAAY,EAAE;EAE3B;EAEvC;;;EAGQC,cAAc;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,GAAG,IAAIlB,WAAW,CAAC;MAC5B,cAAc,EAAE;KACjB,CAAC;IAEF,IAAIe,KAAK,EAAE;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;;IAG3D,OAAOG,OAAO;EAChB;EAEA;EACAE,UAAU,CAACC,UAAkB;IAC3B;IACA,MAAMC,GAAG,GAAGC,KAAK,CAACC,MAAM,CAACH,UAAU,CAAC,CAAC,GACjC,GAAG,IAAI,CAACI,OAAO,SAASJ,UAAU,EAAE,GACpC,GAAG,IAAI,CAACI,OAAO,IAAIJ,UAAU,EAAE;IAEnC,OAAO,IAAI,CAACX,IAAI,CAACgB,GAAG,CAA2BJ,GAAG,CAAC,CAChDK,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAIA,OAAO,EAAE;QACX,IAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC;QACxC;QACA,IAAI,CAACE,iBAAiB,CAAC;UACrBC,SAAS,EAAEH,OAAO,CAACI,EAAE;UACrBC,QAAQ,EAAEC,QAAQ,CAACD,QAAQ;UAC3BE,SAAS,EAAEC,SAAS,CAACD;SACtB,CAAC,CAACE,SAAS,EAAE;;IAElB,CAAC,CAAC,EACFjC,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAC,qBAAqB;IACnB,OAAO,IAAI,CAAC/B,IAAI,CAACgB,GAAG,CAA2B,GAAG,IAAI,CAACD,OAAO,KAAK,EAAE;MACnEP,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI,IAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAE,aAAa,CAACC,OAA6B;IACzC,MAAMC,aAAa,GAAyB;MAC1CC,iBAAiB,EAAEF,OAAO,CAACE,iBAAiB;MAC5CC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;MAC1BC,OAAO,EAAEJ,OAAO,CAACI,OAAO;MACxBC,QAAQ,EAAEL,OAAO,CAACK,QAAQ,GAAG;QAC3BC,IAAI,EAAEN,OAAO,CAACK,QAAQ,CAACC,IAAI;QAC3BC,KAAK,EAAEP,OAAO,CAACK,QAAQ,CAACE,KAAK;QAC7BC,OAAO,EAAER,OAAO,CAACK,QAAQ,CAACG,OAAO;QACjCC,eAAe,EAAET,OAAO,CAACK,QAAQ,CAACI;OACnC,GAAGC,SAAS;MACbC,WAAW,EAAEX,OAAO,CAACW,WAAW,GAAG;QACjCC,KAAK,EAAEZ,OAAO,CAACW,WAAW,CAACC,KAAK;QAChCC,aAAa,EAAEb,OAAO,CAACW,WAAW,CAACE,aAAa;QAChDC,OAAO,EAAEd,OAAO,CAACW,WAAW,CAACG,OAAO;QACpCC,YAAY,EAAEf,OAAO,CAACW,WAAW,CAACI,YAAY;QAC9CC,eAAe,EAAEhB,OAAO,CAACW,WAAW,CAACK,eAAe,GAAG;UACrDC,MAAM,EAAEjB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACC,MAAM;UAClDX,IAAI,EAAEN,OAAO,CAACW,WAAW,CAACK,eAAe,CAACV,IAAI;UAC9CC,KAAK,EAAEP,OAAO,CAACW,WAAW,CAACK,eAAe,CAACT,KAAK;UAChDW,UAAU,EAAElB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACE,UAAU;UAC1DV,OAAO,EAAER,OAAO,CAACW,WAAW,CAACK,eAAe,CAACR,OAAO;UACpDW,QAAQ,EAAEnB,OAAO,CAACW,WAAW,CAACK,eAAe,CAACG;SAC/C,GAAGT;OACL,GAAGA;KACL;IAED,OAAO,IAAI,CAAC3C,IAAI,CAACqD,GAAG,CAA2B,GAAG,IAAI,CAACtC,OAAO,EAAE,EAAEmB,aAAa,EAAE;MAC/E1B,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI,IAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAwB,aAAa,CAACC,WAAiC;IAC7C,MAAMC,aAAa,GAAyB;MAC1CC,QAAQ,EAAEF,WAAW,CAACE,QAAS;MAC/BC,SAAS,EAAEH,WAAW,CAACG,SAAU;MACjCC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;MAC/BxB,iBAAiB,EAAEoB,WAAW,CAACpB,iBAAiB;MAChDC,QAAQ,EAAEmB,WAAW,CAACnB,QAAQ;MAC9BgB,QAAQ,EAAEG,WAAW,CAACH,QAAQ,IAAI;KACnC;IAED,OAAO,IAAI,CAACpD,IAAI,CAAC4D,IAAI,CAA2B,GAAG,IAAI,CAAC7C,OAAO,EAAE,EAAEyC,aAAa,EAAE;MAChFhD,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BxB,GAAG,CAACyB,OAAO,IAAI,IAAI,CAAClB,qBAAqB,CAACmB,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDxB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA+B,aAAa;IACX,OAAO,IAAI,CAAC7D,IAAI,CAAC8D,MAAM,CAAuB,GAAG,IAAI,CAAC/C,OAAO,EAAE,EAAE;MAC/DP,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,EACjBC,GAAG,CAAC,MAAM,IAAI,CAACO,qBAAqB,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC,EAChDzB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAiC,cAAc,CAACC,OAA6B,EAAEC,OAAe,CAAC,EAAEC,QAAgB,EAAE;IAChF,MAAMC,aAAa,GAAyB;MAC1C7B,QAAQ,EAAE0B,OAAO,CAAC1B,QAAQ;MAC1B8B,MAAM,EAAEJ,OAAO,CAACI,MAAM,IAAI,EAAE;MAC5BC,MAAM,EAAEL,OAAO,CAACK,MAAM,IAAI,WAAW;MACrCJ,IAAI,EAAEA,IAAI;MACVK,QAAQ,EAAEJ;KACX;IAED,OAAO,IAAI,CAAClE,IAAI,CAAC4D,IAAI,CAAmC,GAAG,IAAI,CAAC7C,OAAO,SAAS,EAAEoD,aAAa,EAAE;MAC/F3D,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAyC,oBAAoB,CAACC,UAAkB,EAAEP,OAAe,CAAC,EAAEK,WAAmB,EAAE;IAC9E,MAAMH,aAAa,GAAyB;MAC1CK,UAAU,EAAEA,UAAU;MACtBJ,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBJ,IAAI,EAAEA,IAAI;MACVK,QAAQ,EAAEA;KACX;IAED,OAAO,IAAI,CAACtE,IAAI,CAAC4D,IAAI,CAAmC,GAAG,IAAI,CAAC7C,OAAO,SAAS,EAAEoD,aAAa,EAAE;MAC/F3D,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACA2C,iBAAiB,CAACR,OAAe,CAAC,EAAEK,WAAmB,EAAE;IACvD,OAAO,IAAI,CAACtE,IAAI,CAACgB,GAAG,CAAmC,GAAG,IAAI,CAACD,OAAO,gBAAgBkD,IAAI,aAAaK,QAAQ,EAAE,CAAC,CAC/GrD,IAAI,CACHvB,GAAG,CAACwB,QAAQ,IAAIA,QAAQ,CAACC,IAAK,CAAC,EAC/BvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACA4C,QAAQ,CAACC,KAA4E;IACnF;IACA,MAAMC,QAAQ,GAAiB;MAC7B,GAAGD,KAAK;MACRnD,EAAE,EAAEqD,IAAI,CAACC,GAAG,EAAE;MACdC,YAAY,EAAE,CAAC;MACfC,uBAAuB,EAAE;KAC1B;IACD,OAAO,IAAIzF,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAACuD,QAAQ,CAAC;MACvBK,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAC,WAAW,CAACC,OAAe,EAAEnD,OAA8B;IACzD;IACA,OAAO,IAAI1C,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAE,GAAGY,OAAO;QAAET,EAAE,EAAE4D;MAAO,CAAkB,CAAC;MAC1DH,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAG,WAAW,CAACD,OAAe;IACzB;IACA,OAAO,IAAI7F,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,EAAE;MACf4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAI,YAAY,CAACC,OAAgC;IAC3C;IACA,OAAO,IAAIhG,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,EAAE;MACf4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACAM,aAAa,CAACC,UAAsC;IAClD,OAAO,IAAI,CAACzF,IAAI,CAAC4D,IAAI,CAAiB,GAAG,IAAI,CAAC7C,OAAO,iBAAiB,EAAE0E,UAAU,CAAC,CAChFxE,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA4D,gBAAgB,CAACC,YAAoB,EAAE1D,OAAgC;IACrE,OAAO,IAAI,CAACjC,IAAI,CAACqD,GAAG,CAAiB,GAAG,IAAI,CAACtC,OAAO,mBAAmB4E,YAAY,EAAE,EAAE1D,OAAO,CAAC,CAC5FhB,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA8D,gBAAgB,CAACD,YAAoB;IACnC,OAAO,IAAI,CAAC3F,IAAI,CAAC8D,MAAM,CAAO,GAAG,IAAI,CAAC/C,OAAO,mBAAmB4E,YAAY,EAAE,CAAC,CAC5E1E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACA+D,gBAAgB,CAACC,IAA+B;IAC9C,OAAO,IAAI,CAAC9F,IAAI,CAAC4D,IAAI,CAAgB,GAAG,IAAI,CAAC7C,OAAO,eAAe,EAAE+E,IAAI,CAAC,CACvE7E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAiE,mBAAmB,CAACC,MAAc,EAAE/D,OAA+B;IACjE,OAAO,IAAI,CAACjC,IAAI,CAACqD,GAAG,CAAgB,GAAG,IAAI,CAACtC,OAAO,iBAAiBiF,MAAM,EAAE,EAAE/D,OAAO,CAAC,CACnFhB,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAmE,mBAAmB,CAACD,MAAc;IAChC,OAAO,IAAI,CAAChG,IAAI,CAAC8D,MAAM,CAAO,GAAG,IAAI,CAAC/C,OAAO,iBAAiBiF,MAAM,EAAE,CAAC,CACpE/E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACAoE,cAAc,CAACC,WAAoC;IACjD,OAAO,IAAI,CAACnG,IAAI,CAAC4D,IAAI,CAAc,GAAG,IAAI,CAAC7C,OAAO,kBAAkB,EAAEoF,WAAW,CAAC,CAC/ElF,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEAsE,gBAAgB,CAACC,aAAwC;IACvD,OAAO,IAAI,CAACrG,IAAI,CAAC4D,IAAI,CAAgB,GAAG,IAAI,CAAC7C,OAAO,oBAAoB,EAAEsF,aAAa,CAAC,CACrFpF,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAAC;EACvC;EAEA;EACAwE,YAAY,CAAC/E,SAAiB;IAC5B;IACA;IACA,OAAO,IAAIhC,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC,EAAE,CAAC;MACjB4D,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACAqB,mBAAmB;IACjB;IACA,MAAMC,aAAa,GAAqB;MACtCC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAE;KACtB;IACD,OAAO,IAAIzH,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAACmF,aAAa,CAAC;MAC5BvB,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA5D,iBAAiB,CAACiE,OAA2B;IAC3C,OAAO,IAAI,CAACvF,IAAI,CAAC4D,IAAI,CAAuB,GAAG,IAAI,CAAC7C,OAAO,OAAO,EAAEwE,OAAO,EAAE;MAC3E/E,OAAO,EAAE,IAAI,CAACJ,cAAc;KAC7B,CAAC,CACCa,IAAI,CACHvB,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,EACjBE,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAmF,kBAAkB,CAACC,IAAU;IAC3B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAM7G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,GAAG,IAAIlB,WAAW,EAAE;IAC/B,IAAIe,KAAK,EAAE;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;;IAG3D,OAAO,IAAI,CAACL,IAAI,CAAC4D,IAAI,CAAsB,GAAG,IAAI,CAAC7C,OAAO,uBAAuB,EAAEoG,QAAQ,EAAE;MAC3F3G,OAAO,EAAEA;KACV,CAAC,CACCS,IAAI,CACHvB,GAAG,CAACwB,QAAQ,KAAK;MAAEN,GAAG,EAAEM,QAAQ,CAACC;IAAK,CAAE,CAAC,CAAC,EAC1CvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEAwF,gBAAgB,CAACJ,IAAU;IACzB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAM7G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIC,OAAO,GAAG,IAAIlB,WAAW,EAAE;IAC/B,IAAIe,KAAK,EAAE;MACTG,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUJ,KAAK,EAAE,CAAC;;IAG3D,OAAO,IAAI,CAACL,IAAI,CAAC4D,IAAI,CAAsB,GAAG,IAAI,CAAC7C,OAAO,qBAAqB,EAAEoG,QAAQ,EAAE;MACzF3G,OAAO,EAAEA;KACV,CAAC,CACCS,IAAI,CACHvB,GAAG,CAACwB,QAAQ,KAAK;MAAEN,GAAG,EAAEM,QAAQ,CAACC;IAAK,CAAE,CAAC,CAAC,EAC1CvB,UAAU,CAAC,IAAI,CAACkC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAyF,mBAAmB,CAAC7D,SAAiB,EAAEC,QAAgB;IACrD;IACA,MAAM6D,IAAI,GAAG,GAAG9D,SAAS,CAAC+D,WAAW,EAAE,IAAI9D,QAAQ,CAAC8D,WAAW,EAAE,EAAE,CAChEC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAExB,OAAO,IAAInI,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAEmG;MAAI,CAAE,CAAC;MACvBvC,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAyC,qBAAqB,CAACH,IAAY;IAChC;IACA;IACA,OAAO,IAAIjI,UAAU,CAAC0F,QAAQ,IAAG;MAC/BA,QAAQ,CAAC5D,IAAI,CAAC;QAAEuG,SAAS,EAAE;MAAI,CAAE,CAAC;MAClC3C,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;EACA2C,mBAAmB,CAACtG,SAAiB;IACnC;IACA,MAAMuG,cAAc,GAAG,IAAI,CAAC5H,qBAAqB,CAAC6H,KAAK;IACvD,IAAID,cAAc,EAAE;MAClB,MAAME,SAAS,GAAG;QAChBC,KAAK,EAAE,GAAGH,cAAc,CAACpE,SAAS,IAAIoE,cAAc,CAACnE,QAAQ,MAAMmE,cAAc,CAAC3F,iBAAiB,EAAE;QACrG+F,WAAW,EAAEJ,cAAc,CAACzF,OAAO,IAAIyF,cAAc,CAAC1F,QAAQ,IAAI,sBAAsB;QACxF+F,QAAQ,EAAEL,cAAc,CAACM,eAAe,IAAI,EAAE;QAC9CxH,GAAG,EAAE,GAAGyH,MAAM,CAAC/F,QAAQ,CAACgG,MAAM,YAAYR,cAAc,CAACN,IAAI;OAC9D;MAED,OAAO,IAAIjI,UAAU,CAAC0F,QAAQ,IAAG;QAC/BA,QAAQ,CAAC5D,IAAI,CAAC2G,SAAS,CAAC;QACxB/C,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,CAAC;;IAGJ,OAAOzF,UAAU,CAAC,MAAM,IAAI8I,KAAK,CAAC,mBAAmB,CAAC,CAAC;EACzD;EAEQzG,WAAW,CAAC0G,KAAwB;IAC1CC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAE7C,IAAIE,YAAY,GAAG,4BAA4B;IAE/C,IAAIF,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;MACtC;MACAD,YAAY,GAAGF,KAAK,CAACA,KAAK,CAACG,OAAO;KACnC,MAAM,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;MAC7BF,YAAY,GAAG,yBAAyB;KACzC,MAAM,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,sCAAsC;KACtD,MAAM,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,+BAA+B;KAC/C,MAAM,IAAIF,KAAK,CAACI,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,uBAAuB;KACvC,MAAM,IAAIF,KAAK,CAACI,MAAM,IAAI,GAAG,EAAE;MAC9BF,YAAY,GAAG,iDAAiD;;IAGlE,OAAOjJ,UAAU,CAAC,MAAM,IAAI8I,KAAK,CAACG,YAAY,CAAC,CAAC;EAClD;;;uBAhZW5I,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAA+I,SAAd/I,cAAc;MAAAgJ,YAFb;IAAM;EAAA", "names": ["HttpHeaders", "Observable", "BehaviorSubject", "throwError", "map", "tap", "catchError", "environment", "ProfileService", "constructor", "http", "apiUrl", "currentProfileSubject", "asObservable", "getHttpHeaders", "token", "localStorage", "getItem", "headers", "set", "getProfile", "identifier", "url", "isNaN", "Number", "API_URL", "get", "pipe", "response", "data", "profile", "next", "recordProfileView", "profileId", "id", "referrer", "document", "userAgent", "navigator", "subscribe", "handleError", "getCurrentUserProfile", "updateProfile", "updates", "updateRequest", "professional<PERSON>itle", "headline", "summary", "location", "city", "state", "country", "displayLocation", "undefined", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "businessAddress", "street", "postalCode", "isPublic", "put", "createProfile", "profileData", "createRequest", "username", "firstName", "lastName", "post", "deleteProfile", "delete", "searchProfiles", "filters", "page", "limit", "searchRequest", "skills", "sortBy", "pageSize", "searchProfilesByTerm", "searchTerm", "getPublicProfiles", "addSkill", "skill", "newSkill", "Date", "now", "endorsements", "isEndorsedByCurrentUser", "observer", "complete", "updateSkill", "skillId", "deleteSkill", "endorseSkill", "request", "addExperience", "experience", "updateExperience", "experienceId", "deleteExperience", "addPortfolioItem", "item", "updatePortfolioItem", "itemId", "deletePortfolioItem", "addAchievement", "achievement", "addCertification", "certification", "getBlogPosts", "getProfileAnalytics", "mockAnalytics", "profileViews", "uniqueVisitors", "viewsThisMonth", "viewsThisWeek", "topReferrers", "skillEndorsements", "blogPostViews", "contactButtonClicks", "uploadProfilePhoto", "file", "formData", "FormData", "append", "uploadCoverPhoto", "generateProfileSlug", "slug", "toLowerCase", "replace", "checkSlugAvailability", "available", "getProfileShareData", "currentProfile", "value", "shareData", "title", "description", "imageUrl", "profilePhotoUrl", "window", "origin", "Error", "error", "console", "errorMessage", "message", "status", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\services\\profile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\r\nimport { map, tap, catchError } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\nimport {\r\n  UserProfile,\r\n  ProfileUpdateRequest,\r\n  SkillEndorsementRequest,\r\n  ProfileViewRequest,\r\n  ProfileAnalytics,\r\n  ProfileSearchFilters,\r\n  ProfileSearchResult,\r\n  BlogPost,\r\n  Achievement,\r\n  Certification,\r\n  WorkExperience,\r\n  PortfolioItem,\r\n  ProfileSkill\r\n} from '../models/profile.models';\r\nimport {\r\n  ApiResponse,\r\n  ProfileSearchRequest,\r\n  CreateProfileRequest,\r\n  UpdateProfileRequest\r\n} from '../../shared/models/api-response.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProfileService {\r\n  private readonly API_URL = `${environment.apiUrl}/profile`;\r\n\r\n  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);\r\n  public currentProfile$ = this.currentProfileSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Get HTTP headers with authentication token\r\n   */\r\n  private getHttpHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    });\r\n\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return headers;\r\n  }\r\n\r\n  // Profile CRUD Operations\r\n  getProfile(identifier: string): Observable<UserProfile> {\r\n    // Check if identifier is a slug or ID\r\n    const url = isNaN(Number(identifier))\r\n      ? `${this.API_URL}/slug/${identifier}`\r\n      : `${this.API_URL}/${identifier}`;\r\n\r\n    return this.http.get<ApiResponse<UserProfile>>(url)\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => {\r\n          if (profile) {\r\n            this.currentProfileSubject.next(profile);\r\n            // Record profile view\r\n            this.recordProfileView({\r\n              profileId: profile.id,\r\n              referrer: document.referrer,\r\n              userAgent: navigator.userAgent\r\n            }).subscribe();\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  getCurrentUserProfile(): Observable<UserProfile> {\r\n    return this.http.get<ApiResponse<UserProfile>>(`${this.API_URL}/me`, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {\r\n    const updateRequest: UpdateProfileRequest = {\r\n      professionalTitle: updates.professionalTitle,\r\n      headline: updates.headline,\r\n      summary: updates.summary,\r\n      location: updates.location ? {\r\n        city: updates.location.city,\r\n        state: updates.location.state,\r\n        country: updates.location.country,\r\n        displayLocation: updates.location.displayLocation\r\n      } : undefined,\r\n      contactInfo: updates.contactInfo ? {\r\n        email: updates.contactInfo.email,\r\n        isEmailPublic: updates.contactInfo.isEmailPublic,\r\n        website: updates.contactInfo.website,\r\n        portfolioUrl: updates.contactInfo.portfolioUrl,\r\n        businessAddress: updates.contactInfo.businessAddress ? {\r\n          street: updates.contactInfo.businessAddress.street,\r\n          city: updates.contactInfo.businessAddress.city,\r\n          state: updates.contactInfo.businessAddress.state,\r\n          postalCode: updates.contactInfo.businessAddress.postalCode,\r\n          country: updates.contactInfo.businessAddress.country,\r\n          isPublic: updates.contactInfo.businessAddress.isPublic\r\n        } : undefined\r\n      } : undefined\r\n    };\r\n\r\n    return this.http.put<ApiResponse<UserProfile>>(`${this.API_URL}`, updateRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {\r\n    const createRequest: CreateProfileRequest = {\r\n      username: profileData.username!,\r\n      firstName: profileData.firstName!,\r\n      lastName: profileData.lastName!,\r\n      professionalTitle: profileData.professionalTitle,\r\n      headline: profileData.headline,\r\n      isPublic: profileData.isPublic ?? true\r\n    };\r\n\r\n    return this.http.post<ApiResponse<UserProfile>>(`${this.API_URL}`, createRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  deleteProfile(): Observable<void> {\r\n    return this.http.delete<ApiResponse<boolean>>(`${this.API_URL}`, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(() => void 0),\r\n        tap(() => this.currentProfileSubject.next(null)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Profile Search\r\n  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {\r\n    const searchRequest: ProfileSearchRequest = {\r\n      location: filters.location,\r\n      skills: filters.skills || [],\r\n      sortBy: filters.sortBy || 'relevance',\r\n      page: page,\r\n      pageSize: limit\r\n    };\r\n\r\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Search profiles by term\r\n  searchProfilesByTerm(searchTerm: string, page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\r\n    const searchRequest: ProfileSearchRequest = {\r\n      searchTerm: searchTerm,\r\n      skills: [],\r\n      sortBy: 'relevance',\r\n      page: page,\r\n      pageSize: pageSize\r\n    };\r\n\r\n    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Get public profiles\r\n  getPublicProfiles(page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {\r\n    return this.http.get<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`)\r\n      .pipe(\r\n        map(response => response.data!),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Skills Management (TODO: Implement in backend)\r\n  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {\r\n    // For now, return a mock skill until backend implements this\r\n    const newSkill: ProfileSkill = {\r\n      ...skill,\r\n      id: Date.now(),\r\n      endorsements: 0,\r\n      isEndorsedByCurrentUser: false\r\n    };\r\n    return new Observable(observer => {\r\n      observer.next(newSkill);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next({ ...updates, id: skillId } as ProfileSkill);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  deleteSkill(skillId: number): Observable<void> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next();\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  endorseSkill(request: SkillEndorsementRequest): Observable<void> {\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next();\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Experience Management\r\n  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {\r\n    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {\r\n    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deleteExperience(experienceId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Portfolio Management\r\n  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {\r\n    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {\r\n    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deletePortfolioItem(itemId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Achievements & Certifications\r\n  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {\r\n    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {\r\n    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Blog Posts\r\n  getBlogPosts(profileId: number): Observable<BlogPost[]> {\r\n    // Blog posts are included in the profile data from the backend\r\n    // Return empty array for now, as they're part of the main profile\r\n    return new Observable(observer => {\r\n      observer.next([]);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Analytics\r\n  getProfileAnalytics(): Observable<ProfileAnalytics> {\r\n    // For now, return mock analytics since backend doesn't have this endpoint yet\r\n    const mockAnalytics: ProfileAnalytics = {\r\n      profileViews: 0,\r\n      uniqueVisitors: 0,\r\n      viewsThisMonth: 0,\r\n      viewsThisWeek: 0,\r\n      topReferrers: [],\r\n      skillEndorsements: 0,\r\n      blogPostViews: 0,\r\n      contactButtonClicks: 0\r\n    };\r\n    return new Observable(observer => {\r\n      observer.next(mockAnalytics);\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  recordProfileView(request: ProfileViewRequest): Observable<void> {\r\n    return this.http.post<ApiResponse<boolean>>(`${this.API_URL}/view`, request, {\r\n      headers: this.getHttpHeaders()\r\n    })\r\n      .pipe(\r\n        map(() => void 0),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // File Upload\r\n  uploadProfilePhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders();\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/profile-photo`, formData, {\r\n      headers: headers\r\n    })\r\n      .pipe(\r\n        map(response => ({ url: response.data! })),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  uploadCoverPhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    const token = localStorage.getItem('authToken');\r\n    let headers = new HttpHeaders();\r\n    if (token) {\r\n      headers = headers.set('Authorization', `Bearer ${token}`);\r\n    }\r\n\r\n    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/cover-photo`, formData, {\r\n      headers: headers\r\n    })\r\n      .pipe(\r\n        map(response => ({ url: response.data! })),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Utility Methods\r\n  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {\r\n    // Generate slug client-side for now\r\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`\r\n      .replace(/[^a-z0-9-]/g, '-')\r\n      .replace(/-+/g, '-')\r\n      .replace(/^-|-$/g, '');\r\n\r\n    return new Observable(observer => {\r\n      observer.next({ slug });\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {\r\n    // For now, assume all slugs are available\r\n    // TODO: Implement in backend\r\n    return new Observable(observer => {\r\n      observer.next({ available: true });\r\n      observer.complete();\r\n    });\r\n  }\r\n\r\n  // Social Sharing\r\n  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {\r\n    // Generate share data from current profile\r\n    const currentProfile = this.currentProfileSubject.value;\r\n    if (currentProfile) {\r\n      const shareData = {\r\n        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\r\n        description: currentProfile.summary || currentProfile.headline || 'Professional profile',\r\n        imageUrl: currentProfile.profilePhotoUrl || '',\r\n        url: `${window.location.origin}/profile/${currentProfile.slug}`\r\n      };\r\n\r\n      return new Observable(observer => {\r\n        observer.next(shareData);\r\n        observer.complete();\r\n      });\r\n    }\r\n\r\n    return throwError(() => new Error('Profile not found'));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse): Observable<never> {\r\n    console.error('ProfileService error:', error);\r\n\r\n    let errorMessage = 'Възникна неочаквана грешка';\r\n\r\n    if (error.error && error.error.message) {\r\n      // Backend returned an error message\r\n      errorMessage = error.error.message;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Няма връзка със сървъра';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Не сте упълномощени за тази операция';\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Нямате права за тази операция';\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Профилът не е намерен';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}