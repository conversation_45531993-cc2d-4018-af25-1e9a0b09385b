{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDivider {\n  constructor() {\n    this._vertical = false;\n    this._inset = false;\n  }\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n}\nMatDivider.ɵfac = function MatDivider_Factory(t) {\n  return new (t || MatDivider)();\n};\nMatDivider.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDivider,\n  selectors: [[\"mat-divider\"]],\n  hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n  hostVars: 7,\n  hostBindings: function MatDivider_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n      i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n    }\n  },\n  inputs: {\n    vertical: \"vertical\",\n    inset: \"inset\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function MatDivider_Template(rf, ctx) {},\n  styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDividerModule {}\nMatDividerModule.ɵfac = function MatDividerModule_Factory(t) {\n  return new (t || MatDividerModule)();\n};\nMatDividerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatDividerModule\n});\nMatDividerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatDivider, MatCommonModule],\n      declarations: [MatDivider]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "coerceBooleanProperty", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_vertical", "_inset", "vertical", "value", "inset", "ɵfac", "ɵcmp", "type", "args", "selector", "host", "template", "encapsulation", "None", "changeDetection", "OnPush", "styles", "MatDividerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDivider {\n    constructor() {\n        this._vertical = false;\n        this._inset = false;\n    }\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = coerceBooleanProperty(value);\n    }\n    /** Whether the divider is an inset divider. */\n    get inset() {\n        return this._inset;\n    }\n    set inset(value) {\n        this._inset = coerceBooleanProperty(value);\n    }\n}\nMatDivider.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDivider, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatDivider.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatDivider, selector: \"mat-divider\", inputs: { vertical: \"vertical\", inset: \"inset\" }, host: { attributes: { \"role\": \"separator\" }, properties: { \"attr.aria-orientation\": \"vertical ? \\\"vertical\\\" : \\\"horizontal\\\"\", \"class.mat-divider-vertical\": \"vertical\", \"class.mat-divider-horizontal\": \"!vertical\", \"class.mat-divider-inset\": \"inset\" }, classAttribute: \"mat-divider\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDivider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-divider', host: {\n                        'role': 'separator',\n                        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n                        '[class.mat-divider-vertical]': 'vertical',\n                        '[class.mat-divider-horizontal]': '!vertical',\n                        '[class.mat-divider-inset]': 'inset',\n                        'class': 'mat-divider',\n                    }, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"] }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }], inset: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDividerModule {\n}\nMatDividerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDividerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatDividerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDividerModule, declarations: [MatDivider], imports: [MatCommonModule], exports: [MatDivider, MatCommonModule] });\nMatDividerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDividerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatDivider, MatCommonModule],\n                    declarations: [MatDivider],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAW,GAAG;IACV,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACA;EACA,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQ,CAACC,KAAK,EAAE;IAChB,IAAI,CAACH,SAAS,GAAGJ,qBAAqB,CAACO,KAAK,CAAC;EACjD;EACA;EACA,IAAIC,KAAK,GAAG;IACR,OAAO,IAAI,CAACH,MAAM;EACtB;EACA,IAAIG,KAAK,CAACD,KAAK,EAAE;IACb,IAAI,CAACF,MAAM,GAAGL,qBAAqB,CAACO,KAAK,CAAC;EAC9C;AACJ;AACAL,UAAU,CAACO,IAAI;EAAA,iBAA6FP,UAAU;AAAA,CAAmD;AACzKA,UAAU,CAACQ,IAAI,kBADmFhB,EAAE;EAAA,MACJQ,UAAU;EAAA;EAAA,oBAA2G,WAAW;EAAA;EAAA;IAAA;MAD9HR,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA,EACqzB;AACz5B;EAAA,mDAFkGA,EAAE,mBAEJQ,UAAU,EAAc,CAAC;IAC7GS,IAAI,EAAEhB,SAAS;IACfiB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEC,IAAI,EAAE;QAC5B,MAAM,EAAE,WAAW;QACnB,yBAAyB,EAAE,sCAAsC;QACjE,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,WAAW;QAC7C,2BAA2B,EAAE,OAAO;QACpC,OAAO,EAAE;MACb,CAAC;MAAEC,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAEpB,iBAAiB,CAACqB,IAAI;MAAEC,eAAe,EAAErB,uBAAuB,CAACsB,MAAM;MAAEC,MAAM,EAAE,CAAC,6SAA6S;IAAE,CAAC;EAC9a,CAAC,CAAC,QAAkB;IAAEd,QAAQ,EAAE,CAAC;MACzBK,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEU,KAAK,EAAE,CAAC;MACRG,IAAI,EAAEb;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACZ,IAAI;EAAA,iBAA6FY,gBAAgB;AAAA,CAAkD;AACpLA,gBAAgB,CAACC,IAAI,kBA5B6E5B,EAAE;EAAA,MA4Be2B;AAAgB,EAAmG;AACtOA,gBAAgB,CAACE,IAAI,kBA7B6E7B,EAAE;EAAA,UA6B2CO,eAAe,EAAEA,eAAe;AAAA,EAAI;AACnL;EAAA,mDA9BkGP,EAAE,mBA8BJ2B,gBAAgB,EAAc,CAAC;IACnHV,IAAI,EAAEZ,QAAQ;IACda,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAACvB,eAAe,CAAC;MAC1BwB,OAAO,EAAE,CAACvB,UAAU,EAAED,eAAe,CAAC;MACtCyB,YAAY,EAAE,CAACxB,UAAU;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEmB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}