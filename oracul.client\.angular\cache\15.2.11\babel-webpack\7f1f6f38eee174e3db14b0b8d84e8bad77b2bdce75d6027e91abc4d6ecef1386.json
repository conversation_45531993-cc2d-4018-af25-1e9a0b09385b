{"ast": null, "code": "import { of, delay } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MockProfileService {\n  constructor() {\n    this.mockProfiles = [{\n      id: 1,\n      userId: 1,\n      username: 'luna-starweaver',\n      slug: 'luna-starweaver',\n      isPublic: true,\n      profileCompletionPercentage: 92,\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=150&h=150&fit=crop&crop=face',\n      coverPhotoUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=300&fit=crop',\n      firstName: 'Luna',\n      lastName: 'Starweaver',\n      professionalTitle: 'Professional Astrologer & Cosmic Guide',\n      headline: 'Illuminating life paths through ancient wisdom and celestial insights. Helping souls navigate their cosmic journey for over 12 years.',\n      location: {\n        city: 'Sedona',\n        state: 'AZ',\n        country: 'USA',\n        displayLocation: 'Sedona, AZ'\n      },\n      contactInfo: {\n        email: '<EMAIL>',\n        isEmailPublic: true,\n        website: 'https://starweaver-astrology.com',\n        portfolioUrl: 'https://luna-cosmic-readings.com',\n        phoneNumbers: [{\n          id: 1,\n          number: '+1 (928) 555-STAR',\n          type: 'business',\n          isPublic: true,\n          isPrimary: true\n        }],\n        businessAddress: {\n          street: '777 Crystal Vortex Lane',\n          city: 'Sedona',\n          state: 'AZ',\n          postalCode: '86336',\n          country: 'USA',\n          isPublic: true\n        }\n      },\n      summary: 'Gifted astrologer and spiritual guide with over 12 years of experience helping individuals discover their cosmic purpose. Specializing in natal chart readings, relationship compatibility, career guidance, and spiritual awakening through the wisdom of the stars. My intuitive approach combines traditional astrological techniques with modern psychological insights to provide transformative guidance for your soul\\'s journey.',\n      skills: [{\n        id: 1,\n        name: 'Natal Chart Reading',\n        category: 'Core Astrology',\n        endorsements: 89,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'expert'\n      }, {\n        id: 2,\n        name: 'Synastry & Compatibility',\n        category: 'Relationship Astrology',\n        endorsements: 76,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'expert'\n      }, {\n        id: 3,\n        name: 'Transit Forecasting',\n        category: 'Predictive Astrology',\n        endorsements: 82,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'expert'\n      }, {\n        id: 4,\n        name: 'Tarot Reading',\n        category: 'Divination Arts',\n        endorsements: 65,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'advanced'\n      }, {\n        id: 5,\n        name: 'Crystal Healing',\n        category: 'Energy Work',\n        endorsements: 58,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'advanced'\n      }, {\n        id: 6,\n        name: 'Vedic Astrology',\n        category: 'Ancient Systems',\n        endorsements: 43,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'intermediate'\n      }, {\n        id: 7,\n        name: 'Chakra Balancing',\n        category: 'Energy Work',\n        endorsements: 52,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'advanced'\n      }, {\n        id: 8,\n        name: 'Moon Phase Rituals',\n        category: 'Lunar Magic',\n        endorsements: 71,\n        isEndorsedByCurrentUser: false,\n        proficiencyLevel: 'expert'\n      }],\n      blogPosts: [{\n        id: 1,\n        title: 'Mercury Retrograde: Navigating Communication Challenges',\n        excerpt: 'Discover how to harness the transformative energy of Mercury retrograde periods and turn apparent setbacks into opportunities for growth and reflection.',\n        publishedAt: new Date('2024-01-15'),\n        readCount: 2847,\n        tags: ['Mercury Retrograde', 'Planetary Transits', 'Communication'],\n        featuredImageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=200&fit=crop',\n        slug: 'mercury-retrograde-navigation-guide'\n      }, {\n        id: 2,\n        title: 'Full Moon Manifestation: Lunar Cycles for Personal Growth',\n        excerpt: 'Learn to align your intentions with the powerful energy of the full moon and create meaningful transformation in your life through lunar wisdom.',\n        publishedAt: new Date('2024-02-20'),\n        readCount: 3156,\n        tags: ['Full Moon', 'Manifestation', 'Lunar Cycles', 'Spiritual Growth'],\n        featuredImageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=200&fit=crop',\n        slug: 'full-moon-manifestation-guide'\n      }, {\n        id: 3,\n        title: 'Understanding Your North Node: Your Soul\\'s Purpose',\n        excerpt: 'Explore the profound meaning of your North Node placement and how it reveals your karmic path and spiritual mission in this lifetime.',\n        publishedAt: new Date('2024-03-10'),\n        readCount: 1923,\n        tags: ['North Node', 'Soul Purpose', 'Karmic Astrology', 'Spiritual Path'],\n        featuredImageUrl: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=200&fit=crop',\n        slug: 'north-node-soul-purpose-guide'\n      }],\n      achievements: [{\n        id: 1,\n        title: 'Certified Professional Astrologer (CPA)',\n        description: 'Achieved professional certification from the International Society for Astrological Research, demonstrating mastery of traditional and modern astrological techniques.',\n        achievedAt: new Date('2019-08-15'),\n        organization: 'International Society for Astrological Research',\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop'\n      }, {\n        id: 2,\n        title: 'Spiritual Guidance Excellence Award',\n        description: 'Recognized for outstanding contribution to spiritual healing and guidance in the metaphysical community.',\n        achievedAt: new Date('2023-11-01'),\n        organization: 'Sedona Spiritual Council'\n      }, {\n        id: 3,\n        title: 'Featured Astrologer - Cosmic Wisdom Magazine',\n        description: 'Selected as featured astrologer for the annual \"Rising Stars in Astrology\" edition, highlighting innovative approaches to chart interpretation.',\n        achievedAt: new Date('2023-03-21'),\n        organization: 'Cosmic Wisdom Magazine'\n      }],\n      certifications: [{\n        id: 1,\n        name: 'Certified Professional Astrologer (CPA)',\n        issuingOrganization: 'International Society for Astrological Research',\n        issueDate: new Date('2019-08-15'),\n        expirationDate: new Date('2025-08-15'),\n        credentialId: 'ISAR-CPA-2019-7734',\n        credentialUrl: 'https://isar-astrology.com/verification'\n      }, {\n        id: 2,\n        name: 'Advanced Tarot Certification',\n        issuingOrganization: 'American Tarot Association',\n        issueDate: new Date('2020-12-01'),\n        credentialId: 'ATA-ADV-2020-9821'\n      }, {\n        id: 3,\n        name: 'Crystal Healing Practitioner',\n        issuingOrganization: 'Crystal Healing Institute',\n        issueDate: new Date('2021-06-30'),\n        expirationDate: new Date('2026-06-30'),\n        credentialId: 'CHI-PRAC-2021-5567'\n      }],\n      experiences: [{\n        id: 1,\n        company: 'Starweaver Astrology Studio',\n        position: 'Founder & Lead Astrologer',\n        startDate: new Date('2018-01-01'),\n        isCurrent: true,\n        description: 'Founded and operate a thriving astrology practice offering personalized readings, workshops, and spiritual guidance. Specialize in natal chart interpretation, relationship compatibility, and life transition guidance.',\n        location: 'Sedona, AZ',\n        companyLogoUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=48&h=48&fit=crop',\n        achievements: ['Built client base of 500+ regular customers', 'Conducted over 2,000 successful readings', 'Developed signature \"Cosmic Life Path\" methodology', 'Featured in 3 major spiritual wellness publications']\n      }, {\n        id: 2,\n        company: 'Mystic Moon Wellness Center',\n        position: 'Senior Astrologer & Workshop Leader',\n        startDate: new Date('2015-06-01'),\n        endDate: new Date('2017-12-31'),\n        isCurrent: false,\n        description: 'Provided astrological consultations and led group workshops on lunar cycles, planetary transits, and spiritual awakening. Mentored junior practitioners in chart interpretation techniques.',\n        location: 'Santa Fe, NM',\n        companyLogoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop',\n        achievements: ['Increased center\\'s astrology revenue by 150%', 'Developed popular \"Moon Goddess\" workshop series', 'Trained 12 apprentice astrologers', 'Maintained 98% client satisfaction rating']\n      }, {\n        id: 3,\n        company: 'Crystal Visions Metaphysical Shop',\n        position: 'Resident Astrologer',\n        startDate: new Date('2012-03-01'),\n        endDate: new Date('2015-05-31'),\n        isCurrent: false,\n        description: 'Provided walk-in astrological consultations and tarot readings. Educated customers on crystal healing properties and lunar cycle practices.',\n        location: 'Asheville, NC',\n        companyLogoUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=48&h=48&fit=crop',\n        achievements: ['Performed over 1,500 readings', 'Established loyal customer following', 'Created educational crystal healing guides', 'Organized monthly new moon circles']\n      }],\n      portfolioItems: [{\n        id: 1,\n        title: 'Cosmic Love Compatibility Reading',\n        description: 'Comprehensive synastry analysis for a celebrity couple, revealing deep karmic connections and relationship dynamics through detailed chart comparison.',\n        imageUrls: ['https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'],\n        projectUrl: 'https://starweaver-astrology.com/case-studies/cosmic-love',\n        technologies: ['Synastry Analysis', 'Composite Charts', 'Transit Forecasting', 'Karmic Astrology'],\n        completedAt: new Date('2023-08-15'),\n        clientName: 'Celebrity Couple (Confidential)',\n        category: 'Relationship Reading',\n        testimonial: {\n          id: 1,\n          clientName: 'Sarah M.',\n          clientTitle: 'Entertainment Industry Professional',\n          testimonialText: 'Luna\\'s reading was incredibly accurate and insightful. She helped us understand our relationship dynamics on a soul level.',\n          rating: 5,\n          givenAt: new Date('2023-08-20')\n        }\n      }, {\n        id: 2,\n        title: 'Corporate Executive Career Guidance',\n        description: 'In-depth natal chart analysis focusing on career path, leadership potential, and optimal timing for major business decisions.',\n        imageUrls: ['https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=300&fit=crop'],\n        projectUrl: 'https://starweaver-astrology.com/case-studies/executive-guidance',\n        technologies: ['Natal Chart Reading', 'Midheaven Analysis', 'Saturn Return Guidance', 'Eclipse Timing'],\n        completedAt: new Date('2023-11-20'),\n        clientName: 'Fortune 500 Executive',\n        category: 'Career Guidance',\n        testimonial: {\n          id: 2,\n          clientName: 'Michael R.',\n          clientTitle: 'CEO',\n          testimonialText: 'The timing guidance Luna provided was spot-on. I made the career move exactly when she suggested and it was incredibly successful.',\n          rating: 5,\n          givenAt: new Date('2023-12-01')\n        }\n      }, {\n        id: 3,\n        title: 'Spiritual Awakening Journey Map',\n        description: 'Transformative reading combining natal chart analysis with North Node exploration to guide a client through their spiritual awakening process.',\n        imageUrls: ['https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=300&fit=crop'],\n        projectUrl: 'https://starweaver-astrology.com/case-studies/spiritual-awakening',\n        technologies: ['North Node Analysis', 'Spiritual Astrology', 'Chakra Alignment', 'Past Life Regression'],\n        completedAt: new Date('2024-01-10'),\n        clientName: 'Spiritual Seeker',\n        category: 'Spiritual Guidance'\n      }],\n      socialLinks: [{\n        id: 1,\n        platform: 'instagram',\n        url: 'https://instagram.com/luna_starweaver',\n        displayName: '@luna_starweaver',\n        isPublic: true\n      }, {\n        id: 2,\n        platform: 'youtube',\n        url: 'https://youtube.com/c/StarweaverAstrology',\n        displayName: 'Starweaver Astrology',\n        isPublic: true\n      }, {\n        id: 3,\n        platform: 'twitter',\n        url: 'https://twitter.com/luna_cosmic',\n        displayName: '@luna_cosmic',\n        isPublic: true\n      }, {\n        id: 4,\n        platform: 'facebook',\n        url: 'https://facebook.com/StarweaverAstrologyStudio',\n        displayName: 'Starweaver Astrology Studio',\n        isPublic: true\n      }],\n      profileViews: 3847,\n      lastViewedAt: new Date('2024-01-20'),\n      createdAt: new Date('2023-01-01'),\n      updatedAt: new Date('2024-01-20')\n    }];\n    this.mockAnalytics = {\n      profileViews: 3847,\n      uniqueVisitors: 2156,\n      viewsThisMonth: 287,\n      viewsThisWeek: 73,\n      topReferrers: ['instagram.com', 'youtube.com', 'google.com', 'astrology.com'],\n      skillEndorsements: 536,\n      blogPostViews: 7926,\n      contactButtonClicks: 89\n    };\n  }\n  // Profile CRUD Operations\n  getProfile(identifier) {\n    const profile = this.mockProfiles.find(p => p.slug === identifier || p.id.toString() === identifier);\n    if (profile) {\n      return of(profile).pipe(delay(500)); // Simulate network delay\n    }\n\n    throw new Error('Profile not found');\n  }\n  getCurrentUserProfile() {\n    return of(this.mockProfiles[0]).pipe(delay(300));\n  }\n  updateProfile(updates) {\n    const profile = {\n      ...this.mockProfiles[0],\n      ...updates\n    };\n    this.mockProfiles[0] = profile;\n    return of(profile).pipe(delay(800));\n  }\n  createProfile(profileData) {\n    const newProfile = {\n      ...this.mockProfiles[0],\n      ...profileData,\n      id: Date.now()\n    };\n    this.mockProfiles.push(newProfile);\n    return of(newProfile).pipe(delay(1000));\n  }\n  deleteProfile() {\n    return of(void 0).pipe(delay(500));\n  }\n  // Profile Search\n  searchProfiles(filters, page = 1, limit = 20) {\n    const results = {\n      profiles: this.mockProfiles,\n      totalCount: this.mockProfiles.length,\n      currentPage: page,\n      totalPages: 1,\n      pageSize: limit\n    };\n    return of(results).pipe(delay(600));\n  }\n  // Skills Management\n  addSkill(skill) {\n    const newSkill = {\n      ...skill,\n      id: Date.now(),\n      endorsements: 0,\n      isEndorsedByCurrentUser: false\n    };\n    return of(newSkill).pipe(delay(400));\n  }\n  updateSkill(skillId, updates) {\n    const skill = this.mockProfiles[0].skills.find(s => s.id === skillId);\n    if (skill) {\n      Object.assign(skill, updates);\n      return of(skill).pipe(delay(400));\n    }\n    throw new Error('Skill not found');\n  }\n  deleteSkill(skillId) {\n    return of(void 0).pipe(delay(300));\n  }\n  endorseSkill(request) {\n    return of(void 0).pipe(delay(400));\n  }\n  // Experience Management\n  addExperience(experience) {\n    const newExperience = {\n      ...experience,\n      id: Date.now()\n    };\n    return of(newExperience).pipe(delay(500));\n  }\n  updateExperience(experienceId, updates) {\n    const experience = this.mockProfiles[0].experiences.find(e => e.id === experienceId);\n    if (experience) {\n      Object.assign(experience, updates);\n      return of(experience).pipe(delay(500));\n    }\n    throw new Error('Experience not found');\n  }\n  deleteExperience(experienceId) {\n    return of(void 0).pipe(delay(300));\n  }\n  // Portfolio Management\n  addPortfolioItem(item) {\n    const newItem = {\n      ...item,\n      id: Date.now()\n    };\n    return of(newItem).pipe(delay(600));\n  }\n  updatePortfolioItem(itemId, updates) {\n    const item = this.mockProfiles[0].portfolioItems.find(i => i.id === itemId);\n    if (item) {\n      Object.assign(item, updates);\n      return of(item).pipe(delay(500));\n    }\n    throw new Error('Portfolio item not found');\n  }\n  deletePortfolioItem(itemId) {\n    return of(void 0).pipe(delay(300));\n  }\n  // Achievements & Certifications\n  addAchievement(achievement) {\n    const newAchievement = {\n      ...achievement,\n      id: Date.now()\n    };\n    return of(newAchievement).pipe(delay(400));\n  }\n  addCertification(certification) {\n    const newCertification = {\n      ...certification,\n      id: Date.now()\n    };\n    return of(newCertification).pipe(delay(400));\n  }\n  // Blog Posts\n  getBlogPosts(profileId) {\n    return of(this.mockProfiles[0].blogPosts).pipe(delay(400));\n  }\n  // Analytics\n  getProfileAnalytics() {\n    return of(this.mockAnalytics).pipe(delay(500));\n  }\n  recordProfileView(request) {\n    return of(void 0).pipe(delay(200));\n  }\n  // File Upload\n  uploadProfilePhoto(file) {\n    // Simulate file upload\n    const mockUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face';\n    return of({\n      url: mockUrl\n    }).pipe(delay(1500));\n  }\n  uploadCoverPhoto(file) {\n    // Simulate file upload\n    const mockUrl = 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=300&fit=crop';\n    return of({\n      url: mockUrl\n    }).pipe(delay(1500));\n  }\n  // Utility Methods\n  generateProfileSlug(firstName, lastName) {\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`;\n    return of({\n      slug\n    }).pipe(delay(300));\n  }\n  checkSlugAvailability(slug) {\n    const available = !this.mockProfiles.some(p => p.slug === slug);\n    return of({\n      available\n    }).pipe(delay(300));\n  }\n  // Social Sharing\n  getProfileShareData(profileId) {\n    const profile = this.mockProfiles[0];\n    return of({\n      title: `${profile.firstName} ${profile.lastName} - ${profile.professionalTitle}`,\n      description: profile.summary || 'Professional profile',\n      imageUrl: profile.profilePhotoUrl || '',\n      url: `${window.location.origin}/profile/${profile.slug}`\n    }).pipe(delay(300));\n  }\n  static {\n    this.ɵfac = function MockProfileService_Factory(t) {\n      return new (t || MockProfileService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MockProfileService,\n      factory: MockProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAqBA,EAAE,EAAEC,KAAK,QAAQ,MAAM;;AAoB5C,OAAM,MAAOC,kBAAkB;EAH/BC;IAIU,iBAAY,GAAkB,CACpC;MACEC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE,iBAAiB;MACvBC,QAAQ,EAAE,IAAI;MACdC,2BAA2B,EAAE,EAAE;MAC/BC,eAAe,EAAE,6FAA6F;MAC9GC,aAAa,EAAE,mFAAmF;MAClGC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,YAAY;MACtBC,iBAAiB,EAAE,wCAAwC;MAC3DC,QAAQ,EAAE,uIAAuI;MACjJC,QAAQ,EAAE;QACRC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,KAAK;QACdC,eAAe,EAAE;OAClB;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,+BAA+B;QACtCC,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE,kCAAkC;QAC3CC,YAAY,EAAE,kCAAkC;QAChDC,YAAY,EAAE,CACZ;UACEtB,EAAE,EAAE,CAAC;UACLuB,MAAM,EAAE,mBAAmB;UAC3BC,IAAI,EAAE,UAAU;UAChBpB,QAAQ,EAAE,IAAI;UACdqB,SAAS,EAAE;SACZ,CACF;QACDC,eAAe,EAAE;UACfC,MAAM,EAAE,yBAAyB;UACjCd,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE,IAAI;UACXc,UAAU,EAAE,OAAO;UACnBb,OAAO,EAAE,KAAK;UACdX,QAAQ,EAAE;;OAEb;MACDyB,OAAO,EAAE,0aAA0a;MACnbC,MAAM,EAAE,CACN;QAAE9B,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,qBAAqB;QAAEC,QAAQ,EAAE,gBAAgB;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAChJ;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,0BAA0B;QAAEC,QAAQ,EAAE,wBAAwB;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EAC7J;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,qBAAqB;QAAEC,QAAQ,EAAE,sBAAsB;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,EACtJ;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,eAAe;QAAEC,QAAQ,EAAE,iBAAiB;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC7I;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,iBAAiB;QAAEC,QAAQ,EAAE,aAAa;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC3I;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,iBAAiB;QAAEC,QAAQ,EAAE,iBAAiB;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAc,CAAE,EACnJ;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,kBAAkB;QAAEC,QAAQ,EAAE,aAAa;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAU,CAAE,EAC5I;QAAEnC,EAAE,EAAE,CAAC;QAAE+B,IAAI,EAAE,oBAAoB;QAAEC,QAAQ,EAAE,aAAa;QAAEC,YAAY,EAAE,EAAE;QAAEC,uBAAuB,EAAE,KAAK;QAAEC,gBAAgB,EAAE;MAAQ,CAAE,CAC7I;MACDC,SAAS,EAAE,CACT;QACEpC,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,yDAAyD;QAChEC,OAAO,EAAE,0JAA0J;QACnKC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACnCC,SAAS,EAAE,IAAI;QACfC,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,eAAe,CAAC;QACnEC,gBAAgB,EAAE,mFAAmF;QACrGxC,IAAI,EAAE;OACP,EACD;QACEH,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,2DAA2D;QAClEC,OAAO,EAAE,kJAAkJ;QAC3JC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACnCC,SAAS,EAAE,IAAI;QACfC,IAAI,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,CAAC;QACxEC,gBAAgB,EAAE,mFAAmF;QACrGxC,IAAI,EAAE;OACP,EACD;QACEH,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,qDAAqD;QAC5DC,OAAO,EAAE,uIAAuI;QAChJC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACnCC,SAAS,EAAE,IAAI;QACfC,IAAI,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;QAC1EC,gBAAgB,EAAE,mFAAmF;QACrGxC,IAAI,EAAE;OACP,CACF;MACDyC,YAAY,EAAE,CACZ;QACE5C,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,yCAAyC;QAChDQ,WAAW,EAAE,wKAAwK;QACrLC,UAAU,EAAE,IAAIN,IAAI,CAAC,YAAY,CAAC;QAClCO,YAAY,EAAE,iDAAiD;QAC/DC,QAAQ,EAAE;OACX,EACD;QACEhD,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,qCAAqC;QAC5CQ,WAAW,EAAE,0GAA0G;QACvHC,UAAU,EAAE,IAAIN,IAAI,CAAC,YAAY,CAAC;QAClCO,YAAY,EAAE;OACf,EACD;QACE/C,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,8CAA8C;QACrDQ,WAAW,EAAE,iJAAiJ;QAC9JC,UAAU,EAAE,IAAIN,IAAI,CAAC,YAAY,CAAC;QAClCO,YAAY,EAAE;OACf,CACF;MACDE,cAAc,EAAE,CACd;QACEjD,EAAE,EAAE,CAAC;QACL+B,IAAI,EAAE,yCAAyC;QAC/CmB,mBAAmB,EAAE,iDAAiD;QACtEC,SAAS,EAAE,IAAIX,IAAI,CAAC,YAAY,CAAC;QACjCY,cAAc,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;QACtCa,YAAY,EAAE,oBAAoB;QAClCC,aAAa,EAAE;OAChB,EACD;QACEtD,EAAE,EAAE,CAAC;QACL+B,IAAI,EAAE,8BAA8B;QACpCmB,mBAAmB,EAAE,4BAA4B;QACjDC,SAAS,EAAE,IAAIX,IAAI,CAAC,YAAY,CAAC;QACjCa,YAAY,EAAE;OACf,EACD;QACErD,EAAE,EAAE,CAAC;QACL+B,IAAI,EAAE,8BAA8B;QACpCmB,mBAAmB,EAAE,2BAA2B;QAChDC,SAAS,EAAE,IAAIX,IAAI,CAAC,YAAY,CAAC;QACjCY,cAAc,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;QACtCa,YAAY,EAAE;OACf,CACF;MACDE,WAAW,EAAE,CACX;QACEvD,EAAE,EAAE,CAAC;QACLwD,OAAO,EAAE,6BAA6B;QACtCC,QAAQ,EAAE,2BAA2B;QACrCC,SAAS,EAAE,IAAIlB,IAAI,CAAC,YAAY,CAAC;QACjCmB,SAAS,EAAE,IAAI;QACfd,WAAW,EAAE,0NAA0N;QACvOjC,QAAQ,EAAE,YAAY;QACtBgD,cAAc,EAAE,iFAAiF;QACjGhB,YAAY,EAAE,CACZ,6CAA6C,EAC7C,0CAA0C,EAC1C,oDAAoD,EACpD,qDAAqD;OAExD,EACD;QACE5C,EAAE,EAAE,CAAC;QACLwD,OAAO,EAAE,6BAA6B;QACtCC,QAAQ,EAAE,qCAAqC;QAC/CC,SAAS,EAAE,IAAIlB,IAAI,CAAC,YAAY,CAAC;QACjCqB,OAAO,EAAE,IAAIrB,IAAI,CAAC,YAAY,CAAC;QAC/BmB,SAAS,EAAE,KAAK;QAChBd,WAAW,EAAE,6LAA6L;QAC1MjC,QAAQ,EAAE,cAAc;QACxBgD,cAAc,EAAE,iFAAiF;QACjGhB,YAAY,EAAE,CACZ,+CAA+C,EAC/C,kDAAkD,EAClD,mCAAmC,EACnC,2CAA2C;OAE9C,EACD;QACE5C,EAAE,EAAE,CAAC;QACLwD,OAAO,EAAE,mCAAmC;QAC5CC,QAAQ,EAAE,qBAAqB;QAC/BC,SAAS,EAAE,IAAIlB,IAAI,CAAC,YAAY,CAAC;QACjCqB,OAAO,EAAE,IAAIrB,IAAI,CAAC,YAAY,CAAC;QAC/BmB,SAAS,EAAE,KAAK;QAChBd,WAAW,EAAE,6IAA6I;QAC1JjC,QAAQ,EAAE,eAAe;QACzBgD,cAAc,EAAE,iFAAiF;QACjGhB,YAAY,EAAE,CACZ,+BAA+B,EAC/B,sCAAsC,EACtC,4CAA4C,EAC5C,oCAAoC;OAEvC,CACF;MACDkB,cAAc,EAAE,CACd;QACE9D,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,mCAAmC;QAC1CQ,WAAW,EAAE,wJAAwJ;QACrKkB,SAAS,EAAE,CAAC,mFAAmF,CAAC;QAChGC,UAAU,EAAE,2DAA2D;QACvEC,YAAY,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,kBAAkB,CAAC;QAClGC,WAAW,EAAE,IAAI1B,IAAI,CAAC,YAAY,CAAC;QACnC2B,UAAU,EAAE,iCAAiC;QAC7CnC,QAAQ,EAAE,sBAAsB;QAChCoC,WAAW,EAAE;UACXpE,EAAE,EAAE,CAAC;UACLmE,UAAU,EAAE,UAAU;UACtBE,WAAW,EAAE,qCAAqC;UAClDC,eAAe,EAAE,6HAA6H;UAC9IC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,IAAIhC,IAAI,CAAC,YAAY;;OAEjC,EACD;QACExC,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,qCAAqC;QAC5CQ,WAAW,EAAE,+HAA+H;QAC5IkB,SAAS,EAAE,CAAC,mFAAmF,CAAC;QAChGC,UAAU,EAAE,kEAAkE;QAC9EC,YAAY,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,gBAAgB,CAAC;QACvGC,WAAW,EAAE,IAAI1B,IAAI,CAAC,YAAY,CAAC;QACnC2B,UAAU,EAAE,uBAAuB;QACnCnC,QAAQ,EAAE,iBAAiB;QAC3BoC,WAAW,EAAE;UACXpE,EAAE,EAAE,CAAC;UACLmE,UAAU,EAAE,YAAY;UACxBE,WAAW,EAAE,KAAK;UAClBC,eAAe,EAAE,oIAAoI;UACrJC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,IAAIhC,IAAI,CAAC,YAAY;;OAEjC,EACD;QACExC,EAAE,EAAE,CAAC;QACLqC,KAAK,EAAE,iCAAiC;QACxCQ,WAAW,EAAE,gJAAgJ;QAC7JkB,SAAS,EAAE,CAAC,mFAAmF,CAAC;QAChGC,UAAU,EAAE,mEAAmE;QAC/EC,YAAY,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;QACxGC,WAAW,EAAE,IAAI1B,IAAI,CAAC,YAAY,CAAC;QACnC2B,UAAU,EAAE,kBAAkB;QAC9BnC,QAAQ,EAAE;OACX,CACF;MACDyC,WAAW,EAAE,CACX;QACEzE,EAAE,EAAE,CAAC;QACL0E,QAAQ,EAAE,WAAW;QACrBC,GAAG,EAAE,uCAAuC;QAC5CC,WAAW,EAAE,kBAAkB;QAC/BxE,QAAQ,EAAE;OACX,EACD;QACEJ,EAAE,EAAE,CAAC;QACL0E,QAAQ,EAAE,SAAS;QACnBC,GAAG,EAAE,2CAA2C;QAChDC,WAAW,EAAE,sBAAsB;QACnCxE,QAAQ,EAAE;OACX,EACD;QACEJ,EAAE,EAAE,CAAC;QACL0E,QAAQ,EAAE,SAAS;QACnBC,GAAG,EAAE,iCAAiC;QACtCC,WAAW,EAAE,cAAc;QAC3BxE,QAAQ,EAAE;OACX,EACD;QACEJ,EAAE,EAAE,CAAC;QACL0E,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,gDAAgD;QACrDC,WAAW,EAAE,6BAA6B;QAC1CxE,QAAQ,EAAE;OACX,CACF;MACDyE,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAItC,IAAI,CAAC,YAAY,CAAC;MACpCuC,SAAS,EAAE,IAAIvC,IAAI,CAAC,YAAY,CAAC;MACjCwC,SAAS,EAAE,IAAIxC,IAAI,CAAC,YAAY;KACjC,CACF;IAEO,kBAAa,GAAqB;MACxCqC,YAAY,EAAE,IAAI;MAClBI,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC;MAC7EC,iBAAiB,EAAE,GAAG;MACtBC,aAAa,EAAE,IAAI;MACnBC,mBAAmB,EAAE;KACtB;;EAED;EACAC,UAAU,CAACC,UAAkB;IAC3B,MAAMC,OAAO,GAAG,IAAI,CAACC,YAAY,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1F,IAAI,KAAKsF,UAAU,IAAII,CAAC,CAAC7F,EAAE,CAAC8F,QAAQ,EAAE,KAAKL,UAAU,CAAC;IACpG,IAAIC,OAAO,EAAE;MACX,OAAO9F,EAAE,CAAC8F,OAAO,CAAC,CAACK,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;;IAEvC,MAAM,IAAImG,KAAK,CAAC,mBAAmB,CAAC;EACtC;EAEAC,qBAAqB;IACnB,OAAOrG,EAAE,CAAC,IAAI,CAAC+F,YAAY,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAClD;EAEAqG,aAAa,CAACC,OAA6B;IACzC,MAAMT,OAAO,GAAG;MAAE,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;MAAE,GAAGQ;IAAO,CAAiB;IACtE,IAAI,CAACR,YAAY,CAAC,CAAC,CAAC,GAAGD,OAAO;IAC9B,OAAO9F,EAAE,CAAC8F,OAAO,CAAC,CAACK,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACrC;EAEAuG,aAAa,CAACC,WAAiC;IAC7C,MAAMC,UAAU,GAAG;MAAE,GAAG,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC;MAAE,GAAGU,WAAW;MAAErG,EAAE,EAAEwC,IAAI,CAAC+D,GAAG;IAAE,CAAE;IAC9E,IAAI,CAACZ,YAAY,CAACa,IAAI,CAACF,UAAU,CAAC;IAClC,OAAO1G,EAAE,CAAC0G,UAAU,CAAC,CAACP,IAAI,CAAClG,KAAK,CAAC,IAAI,CAAC,CAAC;EACzC;EAEA4G,aAAa;IACX,OAAO7G,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;EACA6G,cAAc,CAACC,OAA6B,EAAEC,OAAe,CAAC,EAAEC,QAAgB,EAAE;IAChF,MAAMC,OAAO,GAAwB;MACnCC,QAAQ,EAAE,IAAI,CAACpB,YAAY;MAC3BqB,UAAU,EAAE,IAAI,CAACrB,YAAY,CAACsB,MAAM;MACpCC,WAAW,EAAEN,IAAI;MACjBO,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAEP;KACX;IACD,OAAOjH,EAAE,CAACkH,OAAO,CAAC,CAACf,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACrC;EAEA;EACAwH,QAAQ,CAACC,KAA4E;IACnF,MAAMC,QAAQ,GAAiB;MAC7B,GAAGD,KAAK;MACRtH,EAAE,EAAEwC,IAAI,CAAC+D,GAAG,EAAE;MACdtE,YAAY,EAAE,CAAC;MACfC,uBAAuB,EAAE;KAC1B;IACD,OAAOtC,EAAE,CAAC2H,QAAQ,CAAC,CAACxB,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACtC;EAEA2H,WAAW,CAACC,OAAe,EAAEtB,OAA8B;IACzD,MAAMmB,KAAK,GAAG,IAAI,CAAC3B,YAAY,CAAC,CAAC,CAAC,CAAC7D,MAAM,CAAC8D,IAAI,CAAC8B,CAAC,IAAIA,CAAC,CAAC1H,EAAE,KAAKyH,OAAO,CAAC;IACrE,IAAIH,KAAK,EAAE;MACTK,MAAM,CAACC,MAAM,CAACN,KAAK,EAAEnB,OAAO,CAAC;MAC7B,OAAOvG,EAAE,CAAC0H,KAAK,CAAC,CAACvB,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;;IAEnC,MAAM,IAAImG,KAAK,CAAC,iBAAiB,CAAC;EACpC;EAEA6B,WAAW,CAACJ,OAAe;IACzB,OAAO7H,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEAiI,YAAY,CAACC,OAAgC;IAC3C,OAAOnI,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;EACAmI,aAAa,CAACC,UAAsC;IAClD,MAAMC,aAAa,GAAmB;MAAE,GAAGD,UAAU;MAAEjI,EAAE,EAAEwC,IAAI,CAAC+D,GAAG;IAAE,CAAE;IACvE,OAAO3G,EAAE,CAACsI,aAAa,CAAC,CAACnC,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAC3C;EAEAsI,gBAAgB,CAACC,YAAoB,EAAEjC,OAAgC;IACrE,MAAM8B,UAAU,GAAG,IAAI,CAACtC,YAAY,CAAC,CAAC,CAAC,CAACpC,WAAW,CAACqC,IAAI,CAACyC,CAAC,IAAIA,CAAC,CAACrI,EAAE,KAAKoI,YAAY,CAAC;IACpF,IAAIH,UAAU,EAAE;MACdN,MAAM,CAACC,MAAM,CAACK,UAAU,EAAE9B,OAAO,CAAC;MAClC,OAAOvG,EAAE,CAACqI,UAAU,CAAC,CAAClC,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;;IAExC,MAAM,IAAImG,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEAsC,gBAAgB,CAACF,YAAoB;IACnC,OAAOxI,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;EACA0I,gBAAgB,CAACC,IAA+B;IAC9C,MAAMC,OAAO,GAAkB;MAAE,GAAGD,IAAI;MAAExI,EAAE,EAAEwC,IAAI,CAAC+D,GAAG;IAAE,CAAE;IAC1D,OAAO3G,EAAE,CAAC6I,OAAO,CAAC,CAAC1C,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACrC;EAEA6I,mBAAmB,CAACC,MAAc,EAAExC,OAA+B;IACjE,MAAMqC,IAAI,GAAG,IAAI,CAAC7C,YAAY,CAAC,CAAC,CAAC,CAAC7B,cAAc,CAAC8B,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAAC5I,EAAE,KAAK2I,MAAM,CAAC;IAC3E,IAAIH,IAAI,EAAE;MACRb,MAAM,CAACC,MAAM,CAACY,IAAI,EAAErC,OAAO,CAAC;MAC5B,OAAOvG,EAAE,CAAC4I,IAAI,CAAC,CAACzC,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;;IAElC,MAAM,IAAImG,KAAK,CAAC,0BAA0B,CAAC;EAC7C;EAEA6C,mBAAmB,CAACF,MAAc;IAChC,OAAO/I,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;EACAiJ,cAAc,CAACC,WAAoC;IACjD,MAAMC,cAAc,GAAgB;MAAE,GAAGD,WAAW;MAAE/I,EAAE,EAAEwC,IAAI,CAAC+D,GAAG;IAAE,CAAE;IACtE,OAAO3G,EAAE,CAACoJ,cAAc,CAAC,CAACjD,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAC5C;EAEAoJ,gBAAgB,CAACC,aAAwC;IACvD,MAAMC,gBAAgB,GAAkB;MAAE,GAAGD,aAAa;MAAElJ,EAAE,EAAEwC,IAAI,CAAC+D,GAAG;IAAE,CAAE;IAC5E,OAAO3G,EAAE,CAACuJ,gBAAgB,CAAC,CAACpD,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAC9C;EAEA;EACAuJ,YAAY,CAACC,SAAiB;IAC5B,OAAOzJ,EAAE,CAAC,IAAI,CAAC+F,YAAY,CAAC,CAAC,CAAC,CAACvD,SAAS,CAAC,CAAC2D,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAC5D;EAEA;EACAyJ,mBAAmB;IACjB,OAAO1J,EAAE,CAAC,IAAI,CAAC2J,aAAa,CAAC,CAACxD,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAChD;EAEA2J,iBAAiB,CAACzB,OAA2B;IAC3C,OAAOnI,EAAE,CAAC,KAAK,CAAC,CAAC,CAACmG,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;EACA4J,kBAAkB,CAACC,IAAU;IAC3B;IACA,MAAMC,OAAO,GAAG,6FAA6F;IAC7G,OAAO/J,EAAE,CAAC;MAAE+E,GAAG,EAAEgF;IAAO,CAAE,CAAC,CAAC5D,IAAI,CAAClG,KAAK,CAAC,IAAI,CAAC,CAAC;EAC/C;EAEA+J,gBAAgB,CAACF,IAAU;IACzB;IACA,MAAMC,OAAO,GAAG,gFAAgF;IAChG,OAAO/J,EAAE,CAAC;MAAE+E,GAAG,EAAEgF;IAAO,CAAE,CAAC,CAAC5D,IAAI,CAAClG,KAAK,CAAC,IAAI,CAAC,CAAC;EAC/C;EAEA;EACAgK,mBAAmB,CAACrJ,SAAiB,EAAEC,QAAgB;IACrD,MAAMN,IAAI,GAAG,GAAGK,SAAS,CAACsJ,WAAW,EAAE,IAAIrJ,QAAQ,CAACqJ,WAAW,EAAE,EAAE;IACnE,OAAOlK,EAAE,CAAC;MAAEO;IAAI,CAAE,CAAC,CAAC4F,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACtC;EAEAkK,qBAAqB,CAAC5J,IAAY;IAChC,MAAM6J,SAAS,GAAG,CAAC,IAAI,CAACrE,YAAY,CAACsE,IAAI,CAACpE,CAAC,IAAIA,CAAC,CAAC1F,IAAI,KAAKA,IAAI,CAAC;IAC/D,OAAOP,EAAE,CAAC;MAAEoK;IAAS,CAAE,CAAC,CAACjE,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EAC3C;EAEA;EACAqK,mBAAmB,CAACb,SAAiB;IACnC,MAAM3D,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;IACpC,OAAO/F,EAAE,CAAC;MACRyC,KAAK,EAAE,GAAGqD,OAAO,CAAClF,SAAS,IAAIkF,OAAO,CAACjF,QAAQ,MAAMiF,OAAO,CAAChF,iBAAiB,EAAE;MAChFmC,WAAW,EAAE6C,OAAO,CAAC7D,OAAO,IAAI,sBAAsB;MACtDmB,QAAQ,EAAE0C,OAAO,CAACpF,eAAe,IAAI,EAAE;MACvCqE,GAAG,EAAE,GAAGwF,MAAM,CAACvJ,QAAQ,CAACwJ,MAAM,YAAY1E,OAAO,CAACvF,IAAI;KACvD,CAAC,CAAC4F,IAAI,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC;EACrB;;;uBArcWC,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAuK,SAAlBvK,kBAAkB;MAAAwK,YAFjB;IAAM;EAAA", "names": ["of", "delay", "MockProfileService", "constructor", "id", "userId", "username", "slug", "isPublic", "profileCompletionPercentage", "profilePhotoUrl", "coverPhotoUrl", "firstName", "lastName", "professional<PERSON>itle", "headline", "location", "city", "state", "country", "displayLocation", "contactInfo", "email", "isEmailPublic", "website", "portfolioUrl", "phoneNumbers", "number", "type", "isPrimary", "businessAddress", "street", "postalCode", "summary", "skills", "name", "category", "endorsements", "isEndorsedByCurrentUser", "proficiencyLevel", "blogPosts", "title", "excerpt", "publishedAt", "Date", "readCount", "tags", "featuredImageUrl", "achievements", "description", "achievedAt", "organization", "imageUrl", "certifications", "issuingOrganization", "issueDate", "expirationDate", "credentialId", "credentialUrl", "experiences", "company", "position", "startDate", "isCurrent", "companyLogoUrl", "endDate", "portfolioItems", "imageUrls", "projectUrl", "technologies", "completedAt", "clientName", "testimonial", "clientTitle", "testimonialText", "rating", "givenAt", "socialLinks", "platform", "url", "displayName", "profileViews", "lastViewedAt", "createdAt", "updatedAt", "uniqueVisitors", "viewsThisMonth", "viewsThisWeek", "topReferrers", "skillEndorsements", "blogPostViews", "contactButtonClicks", "getProfile", "identifier", "profile", "mockProfiles", "find", "p", "toString", "pipe", "Error", "getCurrentUserProfile", "updateProfile", "updates", "createProfile", "profileData", "newProfile", "now", "push", "deleteProfile", "searchProfiles", "filters", "page", "limit", "results", "profiles", "totalCount", "length", "currentPage", "totalPages", "pageSize", "addSkill", "skill", "newSkill", "updateSkill", "skillId", "s", "Object", "assign", "deleteSkill", "endorseSkill", "request", "addExperience", "experience", "newExperience", "updateExperience", "experienceId", "e", "deleteExperience", "addPortfolioItem", "item", "newItem", "updatePortfolioItem", "itemId", "i", "deletePortfolioItem", "addAchievement", "achievement", "newAchievement", "addCertification", "certification", "newCertification", "getBlogPosts", "profileId", "getProfileAnalytics", "mockAnalytics", "recordProfileView", "uploadProfilePhoto", "file", "mockUrl", "uploadCoverPhoto", "generateProfileSlug", "toLowerCase", "checkSlugAvailability", "available", "some", "getProfileShareData", "window", "origin", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\services\\mock-profile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, of, delay } from 'rxjs';\r\nimport {\r\n  UserProfile,\r\n  ProfileUpdateRequest,\r\n  SkillEndorsementRequest,\r\n  ProfileViewRequest,\r\n  ProfileAnalytics,\r\n  ProfileSearchFilters,\r\n  ProfileSearchResult,\r\n  BlogPost,\r\n  Achievement,\r\n  Certification,\r\n  WorkExperience,\r\n  PortfolioItem,\r\n  ProfileSkill\r\n} from '../models/profile.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class MockProfileService {\r\n  private mockProfiles: UserProfile[] = [\r\n    {\r\n      id: 1,\r\n      userId: 1,\r\n      username: 'luna-starweaver',\r\n      slug: 'luna-starweaver',\r\n      isPublic: true,\r\n      profileCompletionPercentage: 92,\r\n      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=150&h=150&fit=crop&crop=face',\r\n      coverPhotoUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=300&fit=crop',\r\n      firstName: 'Luna',\r\n      lastName: 'Starweaver',\r\n      professionalTitle: 'Professional Astrologer & Cosmic Guide',\r\n      headline: 'Illuminating life paths through ancient wisdom and celestial insights. Helping souls navigate their cosmic journey for over 12 years.',\r\n      location: {\r\n        city: 'Sedona',\r\n        state: 'AZ',\r\n        country: 'USA',\r\n        displayLocation: 'Sedona, AZ'\r\n      },\r\n      contactInfo: {\r\n        email: '<EMAIL>',\r\n        isEmailPublic: true,\r\n        website: 'https://starweaver-astrology.com',\r\n        portfolioUrl: 'https://luna-cosmic-readings.com',\r\n        phoneNumbers: [\r\n          {\r\n            id: 1,\r\n            number: '+1 (928) 555-STAR',\r\n            type: 'business',\r\n            isPublic: true,\r\n            isPrimary: true\r\n          }\r\n        ],\r\n        businessAddress: {\r\n          street: '777 Crystal Vortex Lane',\r\n          city: 'Sedona',\r\n          state: 'AZ',\r\n          postalCode: '86336',\r\n          country: 'USA',\r\n          isPublic: true\r\n        }\r\n      },\r\n      summary: 'Gifted astrologer and spiritual guide with over 12 years of experience helping individuals discover their cosmic purpose. Specializing in natal chart readings, relationship compatibility, career guidance, and spiritual awakening through the wisdom of the stars. My intuitive approach combines traditional astrological techniques with modern psychological insights to provide transformative guidance for your soul\\'s journey.',\r\n      skills: [\r\n        { id: 1, name: 'Natal Chart Reading', category: 'Core Astrology', endorsements: 89, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },\r\n        { id: 2, name: 'Synastry & Compatibility', category: 'Relationship Astrology', endorsements: 76, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },\r\n        { id: 3, name: 'Transit Forecasting', category: 'Predictive Astrology', endorsements: 82, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' },\r\n        { id: 4, name: 'Tarot Reading', category: 'Divination Arts', endorsements: 65, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },\r\n        { id: 5, name: 'Crystal Healing', category: 'Energy Work', endorsements: 58, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },\r\n        { id: 6, name: 'Vedic Astrology', category: 'Ancient Systems', endorsements: 43, isEndorsedByCurrentUser: false, proficiencyLevel: 'intermediate' },\r\n        { id: 7, name: 'Chakra Balancing', category: 'Energy Work', endorsements: 52, isEndorsedByCurrentUser: false, proficiencyLevel: 'advanced' },\r\n        { id: 8, name: 'Moon Phase Rituals', category: 'Lunar Magic', endorsements: 71, isEndorsedByCurrentUser: false, proficiencyLevel: 'expert' }\r\n      ],\r\n      blogPosts: [\r\n        {\r\n          id: 1,\r\n          title: 'Mercury Retrograde: Navigating Communication Challenges',\r\n          excerpt: 'Discover how to harness the transformative energy of Mercury retrograde periods and turn apparent setbacks into opportunities for growth and reflection.',\r\n          publishedAt: new Date('2024-01-15'),\r\n          readCount: 2847,\r\n          tags: ['Mercury Retrograde', 'Planetary Transits', 'Communication'],\r\n          featuredImageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=200&fit=crop',\r\n          slug: 'mercury-retrograde-navigation-guide'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: 'Full Moon Manifestation: Lunar Cycles for Personal Growth',\r\n          excerpt: 'Learn to align your intentions with the powerful energy of the full moon and create meaningful transformation in your life through lunar wisdom.',\r\n          publishedAt: new Date('2024-02-20'),\r\n          readCount: 3156,\r\n          tags: ['Full Moon', 'Manifestation', 'Lunar Cycles', 'Spiritual Growth'],\r\n          featuredImageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=200&fit=crop',\r\n          slug: 'full-moon-manifestation-guide'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: 'Understanding Your North Node: Your Soul\\'s Purpose',\r\n          excerpt: 'Explore the profound meaning of your North Node placement and how it reveals your karmic path and spiritual mission in this lifetime.',\r\n          publishedAt: new Date('2024-03-10'),\r\n          readCount: 1923,\r\n          tags: ['North Node', 'Soul Purpose', 'Karmic Astrology', 'Spiritual Path'],\r\n          featuredImageUrl: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=200&fit=crop',\r\n          slug: 'north-node-soul-purpose-guide'\r\n        }\r\n      ],\r\n      achievements: [\r\n        {\r\n          id: 1,\r\n          title: 'Certified Professional Astrologer (CPA)',\r\n          description: 'Achieved professional certification from the International Society for Astrological Research, demonstrating mastery of traditional and modern astrological techniques.',\r\n          achievedAt: new Date('2019-08-15'),\r\n          organization: 'International Society for Astrological Research',\r\n          imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: 'Spiritual Guidance Excellence Award',\r\n          description: 'Recognized for outstanding contribution to spiritual healing and guidance in the metaphysical community.',\r\n          achievedAt: new Date('2023-11-01'),\r\n          organization: 'Sedona Spiritual Council'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: 'Featured Astrologer - Cosmic Wisdom Magazine',\r\n          description: 'Selected as featured astrologer for the annual \"Rising Stars in Astrology\" edition, highlighting innovative approaches to chart interpretation.',\r\n          achievedAt: new Date('2023-03-21'),\r\n          organization: 'Cosmic Wisdom Magazine'\r\n        }\r\n      ],\r\n      certifications: [\r\n        {\r\n          id: 1,\r\n          name: 'Certified Professional Astrologer (CPA)',\r\n          issuingOrganization: 'International Society for Astrological Research',\r\n          issueDate: new Date('2019-08-15'),\r\n          expirationDate: new Date('2025-08-15'),\r\n          credentialId: 'ISAR-CPA-2019-7734',\r\n          credentialUrl: 'https://isar-astrology.com/verification'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: 'Advanced Tarot Certification',\r\n          issuingOrganization: 'American Tarot Association',\r\n          issueDate: new Date('2020-12-01'),\r\n          credentialId: 'ATA-ADV-2020-9821'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: 'Crystal Healing Practitioner',\r\n          issuingOrganization: 'Crystal Healing Institute',\r\n          issueDate: new Date('2021-06-30'),\r\n          expirationDate: new Date('2026-06-30'),\r\n          credentialId: 'CHI-PRAC-2021-5567'\r\n        }\r\n      ],\r\n      experiences: [\r\n        {\r\n          id: 1,\r\n          company: 'Starweaver Astrology Studio',\r\n          position: 'Founder & Lead Astrologer',\r\n          startDate: new Date('2018-01-01'),\r\n          isCurrent: true,\r\n          description: 'Founded and operate a thriving astrology practice offering personalized readings, workshops, and spiritual guidance. Specialize in natal chart interpretation, relationship compatibility, and life transition guidance.',\r\n          location: 'Sedona, AZ',\r\n          companyLogoUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=48&h=48&fit=crop',\r\n          achievements: [\r\n            'Built client base of 500+ regular customers',\r\n            'Conducted over 2,000 successful readings',\r\n            'Developed signature \"Cosmic Life Path\" methodology',\r\n            'Featured in 3 major spiritual wellness publications'\r\n          ]\r\n        },\r\n        {\r\n          id: 2,\r\n          company: 'Mystic Moon Wellness Center',\r\n          position: 'Senior Astrologer & Workshop Leader',\r\n          startDate: new Date('2015-06-01'),\r\n          endDate: new Date('2017-12-31'),\r\n          isCurrent: false,\r\n          description: 'Provided astrological consultations and led group workshops on lunar cycles, planetary transits, and spiritual awakening. Mentored junior practitioners in chart interpretation techniques.',\r\n          location: 'Santa Fe, NM',\r\n          companyLogoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop',\r\n          achievements: [\r\n            'Increased center\\'s astrology revenue by 150%',\r\n            'Developed popular \"Moon Goddess\" workshop series',\r\n            'Trained 12 apprentice astrologers',\r\n            'Maintained 98% client satisfaction rating'\r\n          ]\r\n        },\r\n        {\r\n          id: 3,\r\n          company: 'Crystal Visions Metaphysical Shop',\r\n          position: 'Resident Astrologer',\r\n          startDate: new Date('2012-03-01'),\r\n          endDate: new Date('2015-05-31'),\r\n          isCurrent: false,\r\n          description: 'Provided walk-in astrological consultations and tarot readings. Educated customers on crystal healing properties and lunar cycle practices.',\r\n          location: 'Asheville, NC',\r\n          companyLogoUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=48&h=48&fit=crop',\r\n          achievements: [\r\n            'Performed over 1,500 readings',\r\n            'Established loyal customer following',\r\n            'Created educational crystal healing guides',\r\n            'Organized monthly new moon circles'\r\n          ]\r\n        }\r\n      ],\r\n      portfolioItems: [\r\n        {\r\n          id: 1,\r\n          title: 'Cosmic Love Compatibility Reading',\r\n          description: 'Comprehensive synastry analysis for a celebrity couple, revealing deep karmic connections and relationship dynamics through detailed chart comparison.',\r\n          imageUrls: ['https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'],\r\n          projectUrl: 'https://starweaver-astrology.com/case-studies/cosmic-love',\r\n          technologies: ['Synastry Analysis', 'Composite Charts', 'Transit Forecasting', 'Karmic Astrology'],\r\n          completedAt: new Date('2023-08-15'),\r\n          clientName: 'Celebrity Couple (Confidential)',\r\n          category: 'Relationship Reading',\r\n          testimonial: {\r\n            id: 1,\r\n            clientName: 'Sarah M.',\r\n            clientTitle: 'Entertainment Industry Professional',\r\n            testimonialText: 'Luna\\'s reading was incredibly accurate and insightful. She helped us understand our relationship dynamics on a soul level.',\r\n            rating: 5,\r\n            givenAt: new Date('2023-08-20')\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          title: 'Corporate Executive Career Guidance',\r\n          description: 'In-depth natal chart analysis focusing on career path, leadership potential, and optimal timing for major business decisions.',\r\n          imageUrls: ['https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=300&fit=crop'],\r\n          projectUrl: 'https://starweaver-astrology.com/case-studies/executive-guidance',\r\n          technologies: ['Natal Chart Reading', 'Midheaven Analysis', 'Saturn Return Guidance', 'Eclipse Timing'],\r\n          completedAt: new Date('2023-11-20'),\r\n          clientName: 'Fortune 500 Executive',\r\n          category: 'Career Guidance',\r\n          testimonial: {\r\n            id: 2,\r\n            clientName: 'Michael R.',\r\n            clientTitle: 'CEO',\r\n            testimonialText: 'The timing guidance Luna provided was spot-on. I made the career move exactly when she suggested and it was incredibly successful.',\r\n            rating: 5,\r\n            givenAt: new Date('2023-12-01')\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          title: 'Spiritual Awakening Journey Map',\r\n          description: 'Transformative reading combining natal chart analysis with North Node exploration to guide a client through their spiritual awakening process.',\r\n          imageUrls: ['https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=300&fit=crop'],\r\n          projectUrl: 'https://starweaver-astrology.com/case-studies/spiritual-awakening',\r\n          technologies: ['North Node Analysis', 'Spiritual Astrology', 'Chakra Alignment', 'Past Life Regression'],\r\n          completedAt: new Date('2024-01-10'),\r\n          clientName: 'Spiritual Seeker',\r\n          category: 'Spiritual Guidance'\r\n        }\r\n      ],\r\n      socialLinks: [\r\n        {\r\n          id: 1,\r\n          platform: 'instagram',\r\n          url: 'https://instagram.com/luna_starweaver',\r\n          displayName: '@luna_starweaver',\r\n          isPublic: true\r\n        },\r\n        {\r\n          id: 2,\r\n          platform: 'youtube',\r\n          url: 'https://youtube.com/c/StarweaverAstrology',\r\n          displayName: 'Starweaver Astrology',\r\n          isPublic: true\r\n        },\r\n        {\r\n          id: 3,\r\n          platform: 'twitter',\r\n          url: 'https://twitter.com/luna_cosmic',\r\n          displayName: '@luna_cosmic',\r\n          isPublic: true\r\n        },\r\n        {\r\n          id: 4,\r\n          platform: 'facebook',\r\n          url: 'https://facebook.com/StarweaverAstrologyStudio',\r\n          displayName: 'Starweaver Astrology Studio',\r\n          isPublic: true\r\n        }\r\n      ],\r\n      profileViews: 3847,\r\n      lastViewedAt: new Date('2024-01-20'),\r\n      createdAt: new Date('2023-01-01'),\r\n      updatedAt: new Date('2024-01-20')\r\n    }\r\n  ];\r\n\r\n  private mockAnalytics: ProfileAnalytics = {\r\n    profileViews: 3847,\r\n    uniqueVisitors: 2156,\r\n    viewsThisMonth: 287,\r\n    viewsThisWeek: 73,\r\n    topReferrers: ['instagram.com', 'youtube.com', 'google.com', 'astrology.com'],\r\n    skillEndorsements: 536,\r\n    blogPostViews: 7926,\r\n    contactButtonClicks: 89\r\n  };\r\n\r\n  // Profile CRUD Operations\r\n  getProfile(identifier: string): Observable<UserProfile> {\r\n    const profile = this.mockProfiles.find(p => p.slug === identifier || p.id.toString() === identifier);\r\n    if (profile) {\r\n      return of(profile).pipe(delay(500)); // Simulate network delay\r\n    }\r\n    throw new Error('Profile not found');\r\n  }\r\n\r\n  getCurrentUserProfile(): Observable<UserProfile> {\r\n    return of(this.mockProfiles[0]).pipe(delay(300));\r\n  }\r\n\r\n  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {\r\n    const profile = { ...this.mockProfiles[0], ...updates } as UserProfile;\r\n    this.mockProfiles[0] = profile;\r\n    return of(profile).pipe(delay(800));\r\n  }\r\n\r\n  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {\r\n    const newProfile = { ...this.mockProfiles[0], ...profileData, id: Date.now() };\r\n    this.mockProfiles.push(newProfile);\r\n    return of(newProfile).pipe(delay(1000));\r\n  }\r\n\r\n  deleteProfile(): Observable<void> {\r\n    return of(void 0).pipe(delay(500));\r\n  }\r\n\r\n  // Profile Search\r\n  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {\r\n    const results: ProfileSearchResult = {\r\n      profiles: this.mockProfiles,\r\n      totalCount: this.mockProfiles.length,\r\n      currentPage: page,\r\n      totalPages: 1,\r\n      pageSize: limit\r\n    };\r\n    return of(results).pipe(delay(600));\r\n  }\r\n\r\n  // Skills Management\r\n  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {\r\n    const newSkill: ProfileSkill = {\r\n      ...skill,\r\n      id: Date.now(),\r\n      endorsements: 0,\r\n      isEndorsedByCurrentUser: false\r\n    };\r\n    return of(newSkill).pipe(delay(400));\r\n  }\r\n\r\n  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {\r\n    const skill = this.mockProfiles[0].skills.find(s => s.id === skillId);\r\n    if (skill) {\r\n      Object.assign(skill, updates);\r\n      return of(skill).pipe(delay(400));\r\n    }\r\n    throw new Error('Skill not found');\r\n  }\r\n\r\n  deleteSkill(skillId: number): Observable<void> {\r\n    return of(void 0).pipe(delay(300));\r\n  }\r\n\r\n  endorseSkill(request: SkillEndorsementRequest): Observable<void> {\r\n    return of(void 0).pipe(delay(400));\r\n  }\r\n\r\n  // Experience Management\r\n  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {\r\n    const newExperience: WorkExperience = { ...experience, id: Date.now() };\r\n    return of(newExperience).pipe(delay(500));\r\n  }\r\n\r\n  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {\r\n    const experience = this.mockProfiles[0].experiences.find(e => e.id === experienceId);\r\n    if (experience) {\r\n      Object.assign(experience, updates);\r\n      return of(experience).pipe(delay(500));\r\n    }\r\n    throw new Error('Experience not found');\r\n  }\r\n\r\n  deleteExperience(experienceId: number): Observable<void> {\r\n    return of(void 0).pipe(delay(300));\r\n  }\r\n\r\n  // Portfolio Management\r\n  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {\r\n    const newItem: PortfolioItem = { ...item, id: Date.now() };\r\n    return of(newItem).pipe(delay(600));\r\n  }\r\n\r\n  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {\r\n    const item = this.mockProfiles[0].portfolioItems.find(i => i.id === itemId);\r\n    if (item) {\r\n      Object.assign(item, updates);\r\n      return of(item).pipe(delay(500));\r\n    }\r\n    throw new Error('Portfolio item not found');\r\n  }\r\n\r\n  deletePortfolioItem(itemId: number): Observable<void> {\r\n    return of(void 0).pipe(delay(300));\r\n  }\r\n\r\n  // Achievements & Certifications\r\n  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {\r\n    const newAchievement: Achievement = { ...achievement, id: Date.now() };\r\n    return of(newAchievement).pipe(delay(400));\r\n  }\r\n\r\n  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {\r\n    const newCertification: Certification = { ...certification, id: Date.now() };\r\n    return of(newCertification).pipe(delay(400));\r\n  }\r\n\r\n  // Blog Posts\r\n  getBlogPosts(profileId: number): Observable<BlogPost[]> {\r\n    return of(this.mockProfiles[0].blogPosts).pipe(delay(400));\r\n  }\r\n\r\n  // Analytics\r\n  getProfileAnalytics(): Observable<ProfileAnalytics> {\r\n    return of(this.mockAnalytics).pipe(delay(500));\r\n  }\r\n\r\n  recordProfileView(request: ProfileViewRequest): Observable<void> {\r\n    return of(void 0).pipe(delay(200));\r\n  }\r\n\r\n  // File Upload\r\n  uploadProfilePhoto(file: File): Observable<{ url: string }> {\r\n    // Simulate file upload\r\n    const mockUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face';\r\n    return of({ url: mockUrl }).pipe(delay(1500));\r\n  }\r\n\r\n  uploadCoverPhoto(file: File): Observable<{ url: string }> {\r\n    // Simulate file upload\r\n    const mockUrl = 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=300&fit=crop';\r\n    return of({ url: mockUrl }).pipe(delay(1500));\r\n  }\r\n\r\n  // Utility Methods\r\n  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {\r\n    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`;\r\n    return of({ slug }).pipe(delay(300));\r\n  }\r\n\r\n  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {\r\n    const available = !this.mockProfiles.some(p => p.slug === slug);\r\n    return of({ available }).pipe(delay(300));\r\n  }\r\n\r\n  // Social Sharing\r\n  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {\r\n    const profile = this.mockProfiles[0];\r\n    return of({\r\n      title: `${profile.firstName} ${profile.lastName} - ${profile.professionalTitle}`,\r\n      description: profile.summary || 'Professional profile',\r\n      imageUrl: profile.profilePhotoUrl || '',\r\n      url: `${window.location.origin}/profile/${profile.slug}`\r\n    }).pipe(delay(300));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}