import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Translations, BULGARIAN_TRANSLATIONS } from './translations';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguage = 'bg';
  private translations: Translations = BULGARIAN_TRANSLATIONS;
  private languageSubject = new BehaviorSubject<string>(this.currentLanguage);

  constructor() {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      this.currentLanguage = savedLanguage;
      this.languageSubject.next(this.currentLanguage);
    }
  }

  get currentLanguage$(): Observable<string> {
    return this.languageSubject.asObservable();
  }

  getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  setLanguage(language: string): void {
    this.currentLanguage = language;
    localStorage.setItem('language', language);
    this.languageSubject.next(language);
    
    // For now, we only support Bulgarian
    this.translations = BULGARIAN_TRANSLATIONS;
  }

  // Helper method to get nested translation values
  translate(key: string): string {
    const keys = key.split('.');
    let value: any = this.translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key; // Return the key if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  }

  // Convenience methods for common sections
  get common() {
    return this.translations.common;
  }

  get nav() {
    return this.translations.nav;
  }

  get auth() {
    return this.translations.auth;
  }

  get home() {
    return this.translations.home;
  }

  get dashboard() {
    return this.translations.dashboard;
  }

  get profile() {
    return this.translations.profile;
  }

  get articles() {
    return this.translations.articles;
  }

  get horoscope() {
    return this.translations.horoscope;
  }

  get themes() {
    return this.translations.themes;
  }

  get errors() {
    return this.translations.errors;
  }
}
