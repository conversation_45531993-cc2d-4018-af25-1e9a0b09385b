{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { BULGARIAN_TRANSLATIONS } from './translations';\nimport * as i0 from \"@angular/core\";\nexport class TranslationService {\n  constructor() {\n    this.currentLanguage = 'bg';\n    this.translations = BULGARIAN_TRANSLATIONS;\n    this.languageSubject = new BehaviorSubject(this.currentLanguage);\n    // Load saved language preference\n    const savedLanguage = localStorage.getItem('language');\n    if (savedLanguage) {\n      this.currentLanguage = savedLanguage;\n      this.languageSubject.next(this.currentLanguage);\n    }\n  }\n  get currentLanguage$() {\n    return this.languageSubject.asObservable();\n  }\n  getCurrentLanguage() {\n    return this.currentLanguage;\n  }\n  setLanguage(language) {\n    this.currentLanguage = language;\n    localStorage.setItem('language', language);\n    this.languageSubject.next(language);\n    // For now, we only support Bulgarian\n    this.translations = BULGARIAN_TRANSLATIONS;\n  }\n  // Helper method to get nested translation values\n  translate(key) {\n    const keys = key.split('.');\n    let value = this.translations;\n    for (const k of keys) {\n      if (value && typeof value === 'object' && k in value) {\n        value = value[k];\n      } else {\n        console.warn(`Translation key not found: ${key}`);\n        return key; // Return the key if translation not found\n      }\n    }\n\n    return typeof value === 'string' ? value : key;\n  }\n  // Convenience methods for common sections\n  get common() {\n    return this.translations.common;\n  }\n  get nav() {\n    return this.translations.nav;\n  }\n  get auth() {\n    return this.translations.auth;\n  }\n  get home() {\n    return this.translations.home;\n  }\n  get dashboard() {\n    return this.translations.dashboard;\n  }\n  get profile() {\n    return this.translations.profile;\n  }\n  get articles() {\n    return this.translations.articles;\n  }\n  get horoscope() {\n    return this.translations.horoscope;\n  }\n  get themes() {\n    return this.translations.themes;\n  }\n  get errors() {\n    return this.translations.errors;\n  }\n  static {\n    this.ɵfac = function TranslationService_Factory(t) {\n      return new (t || TranslationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TranslationService,\n      factory: TranslationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAAuBC,sBAAsB,QAAQ,gBAAgB;;AAKrE,OAAM,MAAOC,kBAAkB;EAK7BC;IAJQ,oBAAe,GAAG,IAAI;IACtB,iBAAY,GAAiBF,sBAAsB;IACnD,oBAAe,GAAG,IAAID,eAAe,CAAS,IAAI,CAACI,eAAe,CAAC;IAGzE;IACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACtD,IAAIF,aAAa,EAAE;MACjB,IAAI,CAACD,eAAe,GAAGC,aAAa;MACpC,IAAI,CAACG,eAAe,CAACC,IAAI,CAAC,IAAI,CAACL,eAAe,CAAC;;EAEnD;EAEA,IAAIM,gBAAgB;IAClB,OAAO,IAAI,CAACF,eAAe,CAACG,YAAY,EAAE;EAC5C;EAEAC,kBAAkB;IAChB,OAAO,IAAI,CAACR,eAAe;EAC7B;EAEAS,WAAW,CAACC,QAAgB;IAC1B,IAAI,CAACV,eAAe,GAAGU,QAAQ;IAC/BR,YAAY,CAACS,OAAO,CAAC,UAAU,EAAED,QAAQ,CAAC;IAC1C,IAAI,CAACN,eAAe,CAACC,IAAI,CAACK,QAAQ,CAAC;IAEnC;IACA,IAAI,CAACE,YAAY,GAAGf,sBAAsB;EAC5C;EAEA;EACAgB,SAAS,CAACC,GAAW;IACnB,MAAMC,IAAI,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IAC3B,IAAIC,KAAK,GAAQ,IAAI,CAACL,YAAY;IAElC,KAAK,MAAMM,CAAC,IAAIH,IAAI,EAAE;MACpB,IAAIE,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,CAAC,IAAID,KAAK,EAAE;QACpDA,KAAK,GAAGA,KAAK,CAACC,CAAC,CAAC;OACjB,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,8BAA8BN,GAAG,EAAE,CAAC;QACjD,OAAOA,GAAG,CAAC,CAAC;;;;IAIhB,OAAO,OAAOG,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGH,GAAG;EAChD;EAEA;EACA,IAAIO,MAAM;IACR,OAAO,IAAI,CAACT,YAAY,CAACS,MAAM;EACjC;EAEA,IAAIC,GAAG;IACL,OAAO,IAAI,CAACV,YAAY,CAACU,GAAG;EAC9B;EAEA,IAAIC,IAAI;IACN,OAAO,IAAI,CAACX,YAAY,CAACW,IAAI;EAC/B;EAEA,IAAIC,IAAI;IACN,OAAO,IAAI,CAACZ,YAAY,CAACY,IAAI;EAC/B;EAEA,IAAIC,SAAS;IACX,OAAO,IAAI,CAACb,YAAY,CAACa,SAAS;EACpC;EAEA,IAAIC,OAAO;IACT,OAAO,IAAI,CAACd,YAAY,CAACc,OAAO;EAClC;EAEA,IAAIC,QAAQ;IACV,OAAO,IAAI,CAACf,YAAY,CAACe,QAAQ;EACnC;EAEA,IAAIC,SAAS;IACX,OAAO,IAAI,CAAChB,YAAY,CAACgB,SAAS;EACpC;EAEA,IAAIC,MAAM;IACR,OAAO,IAAI,CAACjB,YAAY,CAACiB,MAAM;EACjC;EAEA,IAAIC,MAAM;IACR,OAAO,IAAI,CAAClB,YAAY,CAACkB,MAAM;EACjC;;;uBAvFWhC,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAiC,SAAlBjC,kBAAkB;MAAAkC,YAFjB;IAAM;EAAA", "names": ["BehaviorSubject", "BULGARIAN_TRANSLATIONS", "TranslationService", "constructor", "currentLanguage", "savedLanguage", "localStorage", "getItem", "languageSubject", "next", "currentLanguage$", "asObservable", "getCurrentLanguage", "setLanguage", "language", "setItem", "translations", "translate", "key", "keys", "split", "value", "k", "console", "warn", "common", "nav", "auth", "home", "dashboard", "profile", "articles", "horoscope", "themes", "errors", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\core\\i18n\\translation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { Translations, BULGARIAN_TRANSLATIONS } from './translations';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TranslationService {\r\n  private currentLanguage = 'bg';\r\n  private translations: Translations = BULGARIAN_TRANSLATIONS;\r\n  private languageSubject = new BehaviorSubject<string>(this.currentLanguage);\r\n\r\n  constructor() {\r\n    // Load saved language preference\r\n    const savedLanguage = localStorage.getItem('language');\r\n    if (savedLanguage) {\r\n      this.currentLanguage = savedLanguage;\r\n      this.languageSubject.next(this.currentLanguage);\r\n    }\r\n  }\r\n\r\n  get currentLanguage$(): Observable<string> {\r\n    return this.languageSubject.asObservable();\r\n  }\r\n\r\n  getCurrentLanguage(): string {\r\n    return this.currentLanguage;\r\n  }\r\n\r\n  setLanguage(language: string): void {\r\n    this.currentLanguage = language;\r\n    localStorage.setItem('language', language);\r\n    this.languageSubject.next(language);\r\n    \r\n    // For now, we only support Bulgarian\r\n    this.translations = BULGARIAN_TRANSLATIONS;\r\n  }\r\n\r\n  // Helper method to get nested translation values\r\n  translate(key: string): string {\r\n    const keys = key.split('.');\r\n    let value: any = this.translations;\r\n    \r\n    for (const k of keys) {\r\n      if (value && typeof value === 'object' && k in value) {\r\n        value = value[k];\r\n      } else {\r\n        console.warn(`Translation key not found: ${key}`);\r\n        return key; // Return the key if translation not found\r\n      }\r\n    }\r\n    \r\n    return typeof value === 'string' ? value : key;\r\n  }\r\n\r\n  // Convenience methods for common sections\r\n  get common() {\r\n    return this.translations.common;\r\n  }\r\n\r\n  get nav() {\r\n    return this.translations.nav;\r\n  }\r\n\r\n  get auth() {\r\n    return this.translations.auth;\r\n  }\r\n\r\n  get home() {\r\n    return this.translations.home;\r\n  }\r\n\r\n  get dashboard() {\r\n    return this.translations.dashboard;\r\n  }\r\n\r\n  get profile() {\r\n    return this.translations.profile;\r\n  }\r\n\r\n  get articles() {\r\n    return this.translations.articles;\r\n  }\r\n\r\n  get horoscope() {\r\n    return this.translations.horoscope;\r\n  }\r\n\r\n  get themes() {\r\n    return this.translations.themes;\r\n  }\r\n\r\n  get errors() {\r\n    return this.translations.errors;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}