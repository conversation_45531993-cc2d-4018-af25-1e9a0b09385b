# Database Management Scripts for Oracul Application
# PowerShell script for Windows environment

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("reset", "seed", "migrate", "drop", "recreate", "status")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString = "Server=(localdb)\\mssqllocaldb;Database=OraculDb;Trusted_Connection=true;MultipleActiveResultSets=true"
)

# Set working directory to project root
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-Host "🔮 Oracul Database Management Script" -ForegroundColor Magenta
Write-Host "Action: $Action" -ForegroundColor Cyan
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Gray
Write-Host ""

function Show-Status {
    Write-Host "📊 Database Status Check" -ForegroundColor Yellow
    
    try {
        # Check if database exists
        $result = dotnet ef database list --project Oracul.Data --startup-project Oracul.Server 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database connection successful" -ForegroundColor Green
            Write-Host $result
            
            # Check for pending migrations
            $migrations = dotnet ef migrations list --project Oracul.Data --startup-project Oracul.Server 2>&1
            Write-Host "📋 Migration Status:" -ForegroundColor Cyan
            Write-Host $migrations
        } else {
            Write-Host "❌ Database connection failed" -ForegroundColor Red
            Write-Host $result
        }
    }
    catch {
        Write-Host "❌ Error checking database status: $_" -ForegroundColor Red
    }
}

function Drop-Database {
    Write-Host "🗑️ Dropping Database..." -ForegroundColor Red
    
    try {
        dotnet ef database drop --project Oracul.Data --startup-project Oracul.Server --force
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database dropped successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to drop database" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error dropping database: $_" -ForegroundColor Red
    }
}

function Run-Migrations {
    Write-Host "🔄 Running Database Migrations..." -ForegroundColor Blue
    
    try {
        dotnet ef database update --project Oracul.Data --startup-project Oracul.Server
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migrations applied successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to apply migrations" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error running migrations: $_" -ForegroundColor Red
    }
}

function Seed-Database {
    Write-Host "🌱 Seeding Database with Oracle Profiles..." -ForegroundColor Green
    
    try {
        # Build the project first
        Write-Host "🔨 Building project..." -ForegroundColor Yellow
        dotnet build Oracul.Server
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Build failed" -ForegroundColor Red
            return
        }
        
        # Run the seeding command
        Write-Host "🌱 Running seeding service..." -ForegroundColor Yellow
        dotnet run --project Oracul.Server --seed-only
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database seeded successfully with 5 oracle profiles" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to seed database" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error seeding database: $_" -ForegroundColor Red
    }
}

function Reset-Database {
    Write-Host "🔄 Resetting Database (Drop + Migrate + Seed)..." -ForegroundColor Magenta
    
    Drop-Database
    Start-Sleep -Seconds 2
    Run-Migrations
    Start-Sleep -Seconds 2
    Seed-Database
    
    Write-Host ""
    Write-Host "🎉 Database reset complete!" -ForegroundColor Green
    Write-Host "The database now contains 5 fresh oracle profiles:" -ForegroundColor Cyan
    Write-Host "  • Luna Starweaver (Astrologer)" -ForegroundColor White
    Write-Host "  • Sage Moonchild (Crystal Healer)" -ForegroundColor White
    Write-Host "  • River Palmistry (Palm Reader)" -ForegroundColor White
    Write-Host "  • Aurora Wisdom (Spiritual Counselor)" -ForegroundColor White
    Write-Host "  • Cosmic Dawn (Numerologist)" -ForegroundColor White
}

function Recreate-Database {
    Write-Host "🏗️ Recreating Database (Drop + Migrate only)..." -ForegroundColor Blue
    
    Drop-Database
    Start-Sleep -Seconds 2
    Run-Migrations
    
    Write-Host ""
    Write-Host "✅ Database recreated with empty tables" -ForegroundColor Green
    Write-Host "Run 'seed' action to populate with oracle profiles" -ForegroundColor Cyan
}

# Main script logic
switch ($Action.ToLower()) {
    "status" {
        Show-Status
    }
    "drop" {
        Write-Host "⚠️ WARNING: This will permanently delete all data!" -ForegroundColor Red
        $confirm = Read-Host "Type 'YES' to confirm"
        if ($confirm -eq "YES") {
            Drop-Database
        } else {
            Write-Host "❌ Operation cancelled" -ForegroundColor Yellow
        }
    }
    "migrate" {
        Run-Migrations
    }
    "seed" {
        Seed-Database
    }
    "recreate" {
        Write-Host "⚠️ WARNING: This will drop and recreate the database!" -ForegroundColor Red
        $confirm = Read-Host "Type 'YES' to confirm"
        if ($confirm -eq "YES") {
            Recreate-Database
        } else {
            Write-Host "❌ Operation cancelled" -ForegroundColor Yellow
        }
    }
    "reset" {
        Write-Host "⚠️ WARNING: This will completely reset the database with fresh oracle data!" -ForegroundColor Red
        $confirm = Read-Host "Type 'YES' to confirm"
        if ($confirm -eq "YES") {
            Reset-Database
        } else {
            Write-Host "❌ Operation cancelled" -ForegroundColor Yellow
        }
    }
    default {
        Write-Host "❌ Invalid action: $Action" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available actions:" -ForegroundColor Cyan
        Write-Host "  status   - Check database status and migrations" -ForegroundColor White
        Write-Host "  migrate  - Apply pending migrations" -ForegroundColor White
        Write-Host "  seed     - Seed database with oracle profiles" -ForegroundColor White
        Write-Host "  recreate - Drop and recreate empty database" -ForegroundColor White
        Write-Host "  reset    - Full reset (drop + migrate + seed)" -ForegroundColor White
        Write-Host "  drop     - Drop database (WARNING: deletes all data)" -ForegroundColor White
        Write-Host ""
        Write-Host "Usage examples:" -ForegroundColor Yellow
        Write-Host "  .\Scripts\database-management.ps1 -Action status" -ForegroundColor Gray
        Write-Host "  .\Scripts\database-management.ps1 -Action reset" -ForegroundColor Gray
        Write-Host "  .\Scripts\database-management.ps1 -Action seed" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Script completed." -ForegroundColor Gray
