{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/table\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"../material-demo.component\";\nfunction DashboardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 6);\n    i0.ɵɵelement(4, \"mat-spinner\", 7);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Loading weather data...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"em\");\n    i0.ɵɵtext(9, \"Please refresh once the ASP.NET backend has started.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction DashboardComponent_mat_card_17_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const forecast_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, forecast_r12.date, \"short\"));\n  }\n}\nfunction DashboardComponent_mat_card_17_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Temp. (\\u00B0C) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 20)(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r13.temperatureC, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Temp. (\\u00B0F) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 20)(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r14.temperatureF, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 19)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Summary \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 20)(1, \"span\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(forecast_r15.summary);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 23);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 24);\n  }\n}\nfunction DashboardComponent_mat_card_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 8)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"table_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Weather Forecast Data \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 9)(8, \"table\", 10);\n    i0.ɵɵelementContainerStart(9, 11);\n    i0.ɵɵtemplate(10, DashboardComponent_mat_card_17_th_10_Template, 4, 0, \"th\", 12);\n    i0.ɵɵtemplate(11, DashboardComponent_mat_card_17_td_11_Template, 3, 4, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 14);\n    i0.ɵɵtemplate(13, DashboardComponent_mat_card_17_th_13_Template, 4, 0, \"th\", 12);\n    i0.ɵɵtemplate(14, DashboardComponent_mat_card_17_td_14_Template, 3, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 15);\n    i0.ɵɵtemplate(16, DashboardComponent_mat_card_17_th_16_Template, 4, 0, \"th\", 12);\n    i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_td_17_Template, 3, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 16);\n    i0.ɵɵtemplate(19, DashboardComponent_mat_card_17_th_19_Template, 4, 0, \"th\", 12);\n    i0.ɵɵtemplate(20, DashboardComponent_mat_card_17_td_20_Template, 3, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(21, DashboardComponent_mat_card_17_tr_21_Template, 1, 0, \"tr\", 17);\n    i0.ɵɵtemplate(22, DashboardComponent_mat_card_17_tr_22_Template, 1, 0, \"tr\", 18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.forecasts);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n  }\n}\nexport class DashboardComponent {\n  constructor(http) {\n    this.http = http;\n    this.forecasts = [];\n    this.displayedColumns = ['date', 'temperatureC', 'temperatureF', 'summary'];\n  }\n  ngOnInit() {\n    this.getForecasts();\n  }\n  getForecasts() {\n    this.http.get('/weatherforecast').subscribe(result => {\n      this.forecasts = result;\n    }, error => {\n      console.error(error);\n    });\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 19,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [1, \"header-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"data-card\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"data-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"weather-table\", 3, \"dataSource\"], [\"matColumnDef\", \"date\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"temperatureC\"], [\"matColumnDef\", \"temperatureF\"], [\"matColumnDef\", \"summary\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"temperature\"], [1, \"summary\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"wb_sunny\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Weather Forecast Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Real-time weather data from the server \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\");\n          i0.ɵɵtext(11, \"This component demonstrates fetching data from the server using Angular Material components.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n            return ctx.getForecasts();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" Refresh Data \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, DashboardComponent_div_16_Template, 10, 0, \"div\", 3);\n          i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_Template, 23, 3, \"mat-card\", 4);\n          i0.ɵɵelement(18, \"app-material-demo\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", !ctx.forecasts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.forecasts);\n        }\n      },\n      dependencies: [i2.NgIf, i3.MatButton, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, i5.MatTable, i5.MatHeaderCellDef, i5.MatHeaderRowDef, i5.MatColumnDef, i5.MatCellDef, i5.MatRowDef, i5.MatHeaderCell, i5.MatCell, i5.MatHeaderRow, i5.MatRow, i6.MatIcon, i7.MatProgressSpinner, i8.MaterialDemoComponent, i2.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  color: #673ab7;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  margin: 20px 0;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  padding: 40px 20px;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%]   .mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin: 0 auto 20px;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 10px 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%] {\\r\\n  margin: 20px 0;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  color: #673ab7;\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  overflow-x: auto;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  min-width: 600px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\r\\n  background-color: #f5f5f5;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n  font-size: 18px;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n.temperature[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #ff9800;\\r\\n}\\r\\n\\r\\n.summary[_ngcontent-%COMP%] {\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .dashboard-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n  \\r\\n  .weather-table[_ngcontent-%COMP%] {\\r\\n    min-width: 500px;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;IAuBEA,8BAAkD;IAI1CA,iCAAyC;IACzCA,yBAAG;IAAAA,uCAAuB;IAAAA,iBAAI;IAC9BA,yBAAG;IAAIA,oEAAoD;IAAAA,iBAAK;;;;;IAmB9DA,8BAAsC;IAC1BA,8BAAc;IAAAA,iBAAW;IACnCA,sBACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IAAAA,YAAkC;;IAAAA,iBAAK;;;;IAAvCA,eAAkC;IAAlCA,sEAAkC;;;;;IAK1EA,8BAAsC;IAC1BA,0BAAU;IAAAA,iBAAW;IAC/BA,iCACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IACZA,YAA4B;IAAAA,iBAAO;;;;IAAnCA,eAA4B;IAA5BA,8DAA4B;;;;;IAMxDA,8BAAsC;IAC1BA,0BAAU;IAAAA,iBAAW;IAC/BA,iCACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IACZA,YAA4B;IAAAA,iBAAO;;;;IAAnCA,eAA4B;IAA5BA,8DAA4B;;;;;IAMxDA,8BAAsC;IAC1BA,2BAAW;IAAAA,iBAAW;IAChCA,yBACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IAChBA,YAAsB;IAAAA,iBAAO;;;;IAA7BA,eAAsB;IAAtBA,0CAAsB;;;;;IAIhDA,yBAA4D;;;;;IAC5DA,yBAAkE;;;;;IArD1EA,mCAA8C;IAG9BA,2BAAW;IAAAA,iBAAW;IAChCA,uCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAIZA,iCAAkC;IAChCA,gFAGK;IACLA,gFAA+E;IACjFA,0BAAe;IAGfA,kCAA0C;IACxCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAGfA,kCAA0C;IACxCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAGfA,kCAAqC;IACnCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAEfA,gFAA4D;IAC5DA,gFAAkE;IACpEA,iBAAQ;;;;IA7CSA,eAAwB;IAAxBA,6CAAwB;IA2CnBA,gBAAiC;IAAjCA,yDAAiC;IACpBA,eAA0B;IAA1BA,0DAA0B;;;AC1ErE,OAAM,MAAOC,kBAAkB;EAI7BC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAHjB,cAAS,GAAsB,EAAE;IACjC,qBAAgB,GAAa,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,CAAC;EAEhD;EAEvCC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAY;IACV,IAAI,CAACF,IAAI,CAACG,GAAG,CAAoB,kBAAkB,CAAC,CAACC,SAAS,CAC3DC,MAAM,IAAI;MACT,IAAI,CAACC,SAAS,GAAGD,MAAM;IACzB,CAAC,EACAE,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,CACF;EACH;;;uBAnBWT,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAW;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDd/BhB,8BAAiC;UAKfA,wBAAQ;UAAAA,iBAAW;UAC7BA,4CACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,wDACF;UAAAA,iBAAoB;UAEtBA,wCAAkB;UACbA,6GAA4F;UAAAA,iBAAI;UACnGA,kCAAmE;UAAzBA;YAAA,OAASiB,kBAAc;UAAA,EAAC;UAChEjB,iCAAU;UAAAA,wBAAO;UAAAA,iBAAW;UAC5BA,+BACF;UAAAA,iBAAS;UAKbA,sEAUM;UAGNA,gFAyDW;UAGXA,qCAAuC;UACzCA,iBAAM;;;UA1EEA,gBAAgB;UAAhBA,qCAAgB;UAaXA,eAAe;UAAfA,oCAAe", "names": ["i0", "DashboardComponent", "constructor", "http", "ngOnInit", "getForecasts", "get", "subscribe", "result", "forecasts", "error", "console", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\dashboard\\dashboard.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\dashboard\\dashboard.component.ts"], "sourcesContent": ["<!-- Dashboard Content -->\r\n<div class=\"dashboard-container\">\r\n  <!-- Header Card -->\r\n  <mat-card class=\"header-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>wb_sunny</mat-icon>\r\n        Weather Forecast Dashboard\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Real-time weather data from the server\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>This component demonstrates fetching data from the server using Angular Material components.</p>\r\n      <button mat-raised-button color=\"primary\" (click)=\"getForecasts()\">\r\n        <mat-icon>refresh</mat-icon>\r\n        Refresh Data\r\n      </button>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Loading Spinner -->\r\n  <div *ngIf=\"!forecasts\" class=\"loading-container\">\r\n    <mat-card>\r\n      <mat-card-content>\r\n        <div class=\"loading-content\">\r\n          <mat-spinner diameter=\"50\"></mat-spinner>\r\n          <p>Loading weather data...</p>\r\n          <p><em>Please refresh once the ASP.NET backend has started.</em></p>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Weather Data Table -->\r\n  <mat-card *ngIf=\"forecasts\" class=\"data-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>table_chart</mat-icon>\r\n        Weather Forecast Data\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <div class=\"table-container\">\r\n        <table mat-table [dataSource]=\"forecasts\" class=\"weather-table\">\r\n          <!-- Date Column -->\r\n          <ng-container matColumnDef=\"date\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>calendar_today</mat-icon>\r\n              Date\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">{{ forecast.date | date:'short' }}</td>\r\n          </ng-container>\r\n\r\n          <!-- Temperature C Column -->\r\n          <ng-container matColumnDef=\"temperatureC\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>thermostat</mat-icon>\r\n              Temp. (°C)\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"temperature\">{{ forecast.temperatureC }}°</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Temperature F Column -->\r\n          <ng-container matColumnDef=\"temperatureF\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>thermostat</mat-icon>\r\n              Temp. (°F)\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"temperature\">{{ forecast.temperatureF }}°</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Summary Column -->\r\n          <ng-container matColumnDef=\"summary\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>description</mat-icon>\r\n              Summary\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"summary\">{{ forecast.summary }}</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Material Design Demo -->\r\n  <app-material-demo></app-material-demo>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\n\r\ninterface WeatherForecast {\r\n  date: string;\r\n  temperatureC: number;\r\n  temperatureF: number;\r\n  summary: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css']\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  public forecasts: WeatherForecast[] = [];\r\n  public displayedColumns: string[] = ['date', 'temperatureC', 'temperatureF', 'summary'];\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  ngOnInit() {\r\n    this.getForecasts();\r\n  }\r\n\r\n  getForecasts() {\r\n    this.http.get<WeatherForecast[]>('/weatherforecast').subscribe(\r\n      (result) => {\r\n        this.forecasts = result;\r\n      },\r\n      (error) => {\r\n        console.error(error);\r\n      }\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}