namespace Oracul.Server.Services
{
    public interface IPasswordHashingService
    {
        /// <summary>
        /// Hashes a password using a secure algorithm
        /// </summary>
        /// <param name="password">The plain text password</param>
        /// <returns>The hashed password</returns>
        string HashPassword(string password);

        /// <summary>
        /// Verifies a password against its hash
        /// </summary>
        /// <param name="password">The plain text password</param>
        /// <param name="hash">The stored password hash</param>
        /// <returns>True if the password matches the hash</returns>
        bool VerifyPassword(string password, string hash);

        /// <summary>
        /// Generates a secure random token
        /// </summary>
        /// <param name="length">The length of the token</param>
        /// <returns>A secure random token</returns>
        string GenerateSecureToken(int length = 32);
    }
}
