import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ThemeConfig, ThemeColors, DEFAULT_THEME, MYSTICAL_PURPLE_THEME, AVAILABLE_THEMES } from './theme.config';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_STORAGE_KEY = 'oracul-theme';
  private currentThemeSubject = new BehaviorSubject<ThemeConfig>(MYSTICAL_PURPLE_THEME);
  public currentTheme$ = this.currentThemeSubject.asObservable();

  constructor() {
    this.loadSavedTheme();
  }

  private loadSavedTheme(): void {
    const savedThemeName = localStorage.getItem(this.THEME_STORAGE_KEY);
    if (savedThemeName) {
      const savedTheme = AVAILABLE_THEMES.find(theme => theme.name === savedThemeName);
      if (savedTheme) {
        this.setTheme(savedTheme);
        return;
      }
    }
    // Apply default theme
    this.applyTheme(MYSTICAL_PURPLE_THEME);
  }

  setTheme(theme: ThemeConfig): void {
    this.currentThemeSubject.next(theme);
    this.applyTheme(theme);
    localStorage.setItem(this.THEME_STORAGE_KEY, theme.name);
  }

  setThemeByName(themeName: string): void {
    const theme = AVAILABLE_THEMES.find(t => t.name === themeName);
    if (theme) {
      this.setTheme(theme);
    }
  }

  getCurrentTheme(): ThemeConfig {
    return this.currentThemeSubject.value;
  }

  getAvailableThemes(): ThemeConfig[] {
    return AVAILABLE_THEMES;
  }

  private applyTheme(theme: ThemeConfig): void {
    const root = document.documentElement;
    const body = document.body;
    const colors = theme.colors;

    // Add transition class to prevent flicker
    body.classList.add('theme-changing');

    // Apply CSS custom properties
    root.style.setProperty('--theme-primary', colors.primary);
    root.style.setProperty('--theme-primary-light', colors.primaryLight);
    root.style.setProperty('--theme-primary-dark', colors.primaryDark);
    root.style.setProperty('--theme-accent', colors.accent);
    root.style.setProperty('--theme-accent-light', colors.accentLight);
    root.style.setProperty('--theme-accent-dark', colors.accentDark);
    root.style.setProperty('--theme-warn', colors.warn);
    root.style.setProperty('--theme-success', colors.success);
    root.style.setProperty('--theme-error', colors.error);
    root.style.setProperty('--theme-background', colors.background);
    root.style.setProperty('--theme-surface', colors.surface);

    // Text colors
    root.style.setProperty('--theme-text-primary', colors.text.primary);
    root.style.setProperty('--theme-text-secondary', colors.text.secondary);
    root.style.setProperty('--theme-text-disabled', colors.text.disabled);
    root.style.setProperty('--theme-text-hint', colors.text.hint);

    // Gradients
    root.style.setProperty('--theme-gradient-primary', colors.gradient.primary);
    root.style.setProperty('--theme-gradient-secondary', colors.gradient.secondary);
    root.style.setProperty('--theme-gradient-auth', colors.gradient.auth);

    // OAuth colors
    root.style.setProperty('--theme-google-bg', colors.oauth.google.background);
    root.style.setProperty('--theme-google-border', colors.oauth.google.border);
    root.style.setProperty('--theme-google-text', colors.oauth.google.text);
    root.style.setProperty('--theme-google-hover', colors.oauth.google.hover);

    root.style.setProperty('--theme-facebook-bg', colors.oauth.facebook.background);
    root.style.setProperty('--theme-facebook-border', colors.oauth.facebook.border);
    root.style.setProperty('--theme-facebook-text', colors.oauth.facebook.text);
    root.style.setProperty('--theme-facebook-hover', colors.oauth.facebook.hover);

    // Update body class for theme-specific styling
    body.className = body.className.replace(/theme-\w+/g, '');
    body.classList.add(`theme-${theme.name}`);

    // Force a repaint to ensure Material components pick up the new colors
    setTimeout(() => {
      body.classList.remove('theme-changing');

      // Force style recalculation for all Material components
      const materialSelectors = [
        '.mat-mdc-raised-button', '.mat-mdc-outlined-button', '.mat-mdc-unelevated-button', '.mat-mdc-button',
        '.mat-mdc-form-field', '.mat-mdc-checkbox', '.mat-mdc-radio-button', '.mat-mdc-slide-toggle',
        '.mat-mdc-progress-bar', '.mat-mdc-progress-spinner', '.mat-mdc-tab', '.mat-mdc-chip',
        '.mat-toolbar', '.mat-mdc-menu-panel', '.mat-mdc-snack-bar-container', '.mat-card'
      ];

      materialSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          const element = el as HTMLElement;
          // Force recomputation of styles
          const computedStyle = window.getComputedStyle(element);
          element.style.cssText = element.style.cssText;
        });
      });

      // Dispatch a custom event to notify components of theme change
      window.dispatchEvent(new CustomEvent('themeChanged', { detail: theme }));
    }, 100);
  }

  // Helper method to create custom theme
  createCustomTheme(name: string, colors: Partial<ThemeColors>): ThemeConfig {
    const baseTheme = MYSTICAL_PURPLE_THEME;
    return {
      name,
      colors: {
        ...baseTheme.colors,
        ...colors,
        text: {
          ...baseTheme.colors.text,
          ...(colors.text || {})
        },
        gradient: {
          ...baseTheme.colors.gradient,
          ...(colors.gradient || {})
        },
        oauth: {
          google: {
            ...baseTheme.colors.oauth.google,
            ...(colors.oauth?.google || {})
          },
          facebook: {
            ...baseTheme.colors.oauth.facebook,
            ...(colors.oauth?.facebook || {})
          }
        }
      }
    };
  }

  // Export current theme configuration
  exportTheme(): string {
    return JSON.stringify(this.getCurrentTheme(), null, 2);
  }

  // Import theme from JSON
  importTheme(themeJson: string): boolean {
    try {
      const theme: ThemeConfig = JSON.parse(themeJson);
      if (this.validateTheme(theme)) {
        this.setTheme(theme);
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  private validateTheme(theme: any): theme is ThemeConfig {
    return theme &&
           typeof theme.name === 'string' &&
           theme.colors &&
           typeof theme.colors.primary === 'string' &&
           typeof theme.colors.accent === 'string';
  }
}
