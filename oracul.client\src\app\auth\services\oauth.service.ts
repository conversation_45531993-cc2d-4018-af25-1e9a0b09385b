import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../../environments/environment';

declare global {
  interface Window {
    google: any;
    FB: any;
    fbAsyncInit: () => void;
  }
}

export interface OAuthUser {
  provider: 'google' | 'facebook';
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profilePictureUrl?: string;
  accessToken: string;
}

@Injectable({
  providedIn: 'root'
})
export class OAuthService {
  private googleInitialized = false;
  private facebookInitialized = false;

  // Configuration from environment
  private readonly GOOGLE_CLIENT_ID = environment.oauth.google.clientId;
  private readonly FACEBOOK_APP_ID = environment.oauth.facebook.appId;

  constructor() {
    this.initializeGoogle();
    this.initializeFacebook();
  }

  private initializeGoogle(): void {
    if (typeof window !== 'undefined') {
      // Load Google Sign-In script
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        this.googleInitialized = true;
      };
      document.head.appendChild(script);
    }
  }

  private initializeFacebook(): void {
    if (typeof window !== 'undefined') {
      // Load Facebook SDK
      window.fbAsyncInit = () => {
        window.FB.init({
          appId: this.FACEBOOK_APP_ID,
          cookie: true,
          xfbml: true,
          version: 'v18.0'
        });
        this.facebookInitialized = true;
      };

      // Load Facebook SDK script
      const script = document.createElement('script');
      script.src = 'https://connect.facebook.net/en_US/sdk.js';
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }
  }

  signInWithGoogle(): Observable<OAuthUser> {
    return new Observable(observer => {
      if (!this.googleInitialized || !window.google) {
        observer.error('Google Sign-In not initialized');
        return;
      }

      window.google.accounts.id.initialize({
        client_id: this.GOOGLE_CLIENT_ID,
        callback: (response: any) => {
          try {
            // Decode JWT token to get user info
            const payload = JSON.parse(atob(response.credential.split('.')[1]));

            const user: OAuthUser = {
              provider: 'google',
              id: payload.sub,
              email: payload.email,
              firstName: payload.given_name,
              lastName: payload.family_name,
              profilePictureUrl: payload.picture,
              accessToken: response.credential
            };

            observer.next(user);
            observer.complete();
          } catch (error) {
            observer.error('Failed to parse Google response');
          }
        }
      });

      window.google.accounts.id.prompt();
    });
  }

  signInWithFacebook(): Observable<OAuthUser> {
    return new Observable(observer => {
      if (!this.facebookInitialized || !window.FB) {
        observer.error('Facebook SDK not initialized');
        return;
      }

      window.FB.login((response: any) => {
        if (response.authResponse) {
          // Get user profile information
          window.FB.api('/me', { fields: 'name,email,first_name,last_name,picture' }, (userInfo: any) => {
            const user: OAuthUser = {
              provider: 'facebook',
              id: userInfo.id,
              email: userInfo.email,
              firstName: userInfo.first_name,
              lastName: userInfo.last_name,
              profilePictureUrl: userInfo.picture?.data?.url,
              accessToken: response.authResponse.accessToken
            };

            observer.next(user);
            observer.complete();
          });
        } else {
          observer.error('Facebook login cancelled or failed');
        }
      }, { scope: 'email,public_profile' });
    });
  }

  // Alternative method using popup for Google
  signInWithGooglePopup(): Observable<OAuthUser> {
    return new Observable(observer => {
      if (!this.googleInitialized || !window.google) {
        observer.error('Google Sign-In not initialized');
        return;
      }

      const client = window.google.accounts.oauth2.initTokenClient({
        client_id: this.GOOGLE_CLIENT_ID,
        scope: 'email profile',
        callback: (response: any) => {
          if (response.access_token) {
            // Use the access token to get user profile
            fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${response.access_token}`)
              .then(res => res.json())
              .then(userInfo => {
                const user: OAuthUser = {
                  provider: 'google',
                  id: userInfo.id,
                  email: userInfo.email,
                  firstName: userInfo.given_name,
                  lastName: userInfo.family_name,
                  profilePictureUrl: userInfo.picture,
                  accessToken: response.access_token
                };

                observer.next(user);
                observer.complete();
              })
              .catch(error => observer.error('Failed to get user profile'));
          } else {
            observer.error('Google login failed');
          }
        }
      });

      client.requestAccessToken();
    });
  }

  isGoogleReady(): boolean {
    return this.googleInitialized && !!window.google;
  }

  isFacebookReady(): boolean {
    return this.facebookInitialized && !!window.FB;
  }
}
