{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatButtonToggleModule } from '@angular/material/button-toggle';\nimport { MatPaginatorModule } from '@angular/material/paginator';\n// Profile Components\nimport { ProfileViewComponent } from './components/profile-view/profile-view.component';\nimport { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\nimport { ProfileSearchComponent } from './components/profile-search/profile-search.component';\nimport { ProfileCardComponent } from './components/profile-card/profile-card.component';\nimport { SkillsManagementComponent } from './components/skills-management/skills-management.component';\nimport { ExperienceManagementComponent } from './components/experience-management/experience-management.component';\nimport { PortfolioManagementComponent } from './components/portfolio-management/portfolio-management.component';\nimport { ProfileAnalyticsComponent } from './components/profile-analytics/profile-analytics.component';\n// Services\nimport { ProfileService } from './services/profile.service';\n// Guards\nimport { ProfileOwnerGuard } from './guards/profile-owner.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'profile/edit',\n  component: ProfileEditComponent,\n  canActivate: [ProfileOwnerGuard]\n}, {\n  path: 'profiles/search',\n  component: ProfileSearchComponent\n}, {\n  path: 'profile/manage/skills',\n  component: SkillsManagementComponent,\n  canActivate: [ProfileOwnerGuard]\n}, {\n  path: 'profile/manage/experience',\n  component: ExperienceManagementComponent,\n  canActivate: [ProfileOwnerGuard]\n}, {\n  path: 'profile/manage/portfolio',\n  component: PortfolioManagementComponent,\n  canActivate: [ProfileOwnerGuard]\n}, {\n  path: 'profile/analytics',\n  component: ProfileAnalyticsComponent,\n  canActivate: [ProfileOwnerGuard]\n}, {\n  path: 'profile/:identifier',\n  component: ProfileViewComponent\n}];\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [\n      // Use real ProfileService connected to backend API\n      ProfileService, ProfileOwnerGuard],\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule.forChild(routes),\n      // Material Modules\n      MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatProgressBarModule, MatProgressSpinnerModule, MatChipsModule, MatMenuModule, MatDialogModule, MatSnackBarModule, MatTooltipModule, MatTabsModule, MatExpansionModule, MatSlideToggleModule, MatDividerModule, MatButtonToggleModule, MatPaginatorModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileViewComponent, ProfileEditComponent, ProfileSearchComponent, ProfileCardComponent, SkillsManagementComponent, ExperienceManagementComponent, PortfolioManagementComponent, ProfileAnalyticsComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, i1.RouterModule,\n    // Material Modules\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatProgressBarModule, MatProgressSpinnerModule, MatChipsModule, MatMenuModule, MatDialogModule, MatSnackBarModule, MatTooltipModule, MatTabsModule, MatExpansionModule, MatSlideToggleModule, MatDividerModule, MatButtonToggleModule, MatPaginatorModule],\n    exports: [ProfileViewComponent, ProfileEditComponent, ProfileSearchComponent, ProfileCardComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,kBAAkB,QAAQ,6BAA6B;AAEhE;AACA,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,6BAA6B,QAAQ,oEAAoE;AAClH,SAASC,4BAA4B,QAAQ,kEAAkE;AAC/G,SAASC,yBAAyB,QAAQ,4DAA4D;AAEtG;AACA,SAASC,cAAc,QAAQ,4BAA4B;AAE3D;AACA,SAASC,iBAAiB,QAAQ,8BAA8B;;;AAEhE,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEX,oBAAoB;EAC/BY,WAAW,EAAE,CAACJ,iBAAiB;CAChC,EACD;EACEE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEV;CACZ,EACD;EACES,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAER,yBAAyB;EACpCS,WAAW,EAAE,CAACJ,iBAAiB;CAChC,EACD;EACEE,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEP,6BAA6B;EACxCQ,WAAW,EAAE,CAACJ,iBAAiB;CAChC,EACD;EACEE,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEN,4BAA4B;EACvCO,WAAW,EAAE,CAACJ,iBAAiB;CAChC,EACD;EACEE,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEL,yBAAyB;EACpCM,WAAW,EAAE,CAACJ,iBAAiB;CAChC,EACD;EACEE,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEZ;CACZ,CACF;AAqDD,OAAM,MAAOc,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAZb;MACT;MACAN,cAAc,EACdC,iBAAiB,CAClB;MAAAM,UA/BCvC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,CAACqC,QAAQ,CAACN,MAAM,CAAC;MAE7B;MACA9B,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,oBAAoB,EACpBC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EAClBC,oBAAoB,EACpBC,gBAAgB,EAChBC,qBAAqB,EACrBC,kBAAkB;IAAA;EAAA;;;2EAcTe,aAAa;IAAAG,eAjDtBjB,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,EACzBC,6BAA6B,EAC7BC,4BAA4B,EAC5BC,yBAAyB;IAAAQ,UAGzBvC,YAAY,EACZC,mBAAmB,EACnBC,WAAW;IAGX;IACAE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,oBAAoB,EACpBC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EAClBC,oBAAoB,EACpBC,gBAAgB,EAChBC,qBAAqB,EACrBC,kBAAkB;IAAAmB,UAQlBlB,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB;EAAA;AAAA", "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatProgressBarModule", "MatProgressSpinnerModule", "MatChipsModule", "MatMenuModule", "MatDialogModule", "MatSnackBarModule", "MatTooltipModule", "MatTabsModule", "MatExpansionModule", "MatSlideToggleModule", "MatDividerModule", "MatButtonToggleModule", "MatPaginatorModule", "ProfileViewComponent", "ProfileEditComponent", "ProfileSearchComponent", "ProfileCardComponent", "SkillsManagementComponent", "ExperienceManagementComponent", "PortfolioManagementComponent", "ProfileAnalyticsComponent", "ProfileService", "ProfileOwnerGuard", "routes", "path", "component", "canActivate", "ProfileModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n// Angular Material Modules\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatExpansionModule } from '@angular/material/expansion';\r\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatDividerModule } from '@angular/material/divider';\r\nimport { MatButtonToggleModule } from '@angular/material/button-toggle';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\n\r\n// Profile Components\r\nimport { ProfileViewComponent } from './components/profile-view/profile-view.component';\r\nimport { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\r\nimport { ProfileSearchComponent } from './components/profile-search/profile-search.component';\r\nimport { ProfileCardComponent } from './components/profile-card/profile-card.component';\r\nimport { SkillsManagementComponent } from './components/skills-management/skills-management.component';\r\nimport { ExperienceManagementComponent } from './components/experience-management/experience-management.component';\r\nimport { PortfolioManagementComponent } from './components/portfolio-management/portfolio-management.component';\r\nimport { ProfileAnalyticsComponent } from './components/profile-analytics/profile-analytics.component';\r\n\r\n// Services\r\nimport { ProfileService } from './services/profile.service';\r\n\r\n// Guards\r\nimport { ProfileOwnerGuard } from './guards/profile-owner.guard';\r\n\r\nconst routes = [\r\n  {\r\n    path: 'profile/edit',\r\n    component: ProfileEditComponent,\r\n    canActivate: [ProfileOwnerGuard]\r\n  },\r\n  {\r\n    path: 'profiles/search',\r\n    component: ProfileSearchComponent\r\n  },\r\n  {\r\n    path: 'profile/manage/skills',\r\n    component: SkillsManagementComponent,\r\n    canActivate: [ProfileOwnerGuard]\r\n  },\r\n  {\r\n    path: 'profile/manage/experience',\r\n    component: ExperienceManagementComponent,\r\n    canActivate: [ProfileOwnerGuard]\r\n  },\r\n  {\r\n    path: 'profile/manage/portfolio',\r\n    component: PortfolioManagementComponent,\r\n    canActivate: [ProfileOwnerGuard]\r\n  },\r\n  {\r\n    path: 'profile/analytics',\r\n    component: ProfileAnalyticsComponent,\r\n    canActivate: [ProfileOwnerGuard]\r\n  },\r\n  {\r\n    path: 'profile/:identifier',\r\n    component: ProfileViewComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProfileViewComponent,\r\n    ProfileEditComponent,\r\n    ProfileSearchComponent,\r\n    ProfileCardComponent,\r\n    SkillsManagementComponent,\r\n    ExperienceManagementComponent,\r\n    PortfolioManagementComponent,\r\n    ProfileAnalyticsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    RouterModule.forChild(routes),\r\n\r\n    // Material Modules\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatCheckboxModule,\r\n    MatProgressBarModule,\r\n    MatProgressSpinnerModule,\r\n    MatChipsModule,\r\n    MatMenuModule,\r\n    MatDialogModule,\r\n    MatSnackBarModule,\r\n    MatTooltipModule,\r\n    MatTabsModule,\r\n    MatExpansionModule,\r\n    MatSlideToggleModule,\r\n    MatDividerModule,\r\n    MatButtonToggleModule,\r\n    MatPaginatorModule\r\n  ],\r\n  providers: [\r\n    // Use real ProfileService connected to backend API\r\n    ProfileService,\r\n    ProfileOwnerGuard\r\n  ],\r\n  exports: [\r\n    ProfileViewComponent,\r\n    ProfileEditComponent,\r\n    ProfileSearchComponent,\r\n    ProfileCardComponent\r\n  ]\r\n})\r\nexport class ProfileModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}