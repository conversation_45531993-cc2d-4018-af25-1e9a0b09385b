namespace Oracul.Data.Models
{
    /// <summary>
    /// Junction entity for User-Role many-to-many relationship
    /// </summary>
    public class UserRole : BaseEntity
    {
        public int UserId { get; set; }
        public int RoleId { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Role Role { get; set; } = null!;
    }
}
