{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/oracle.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"@angular/material/chips\";\nfunction OracleProfileComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\", 5)(2, \"mat-card-content\")(3, \"div\", 6);\n    i0.ɵɵelement(4, \"mat-spinner\", 7);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Loading oracle profile...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction OracleProfileComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-card\", 9)(2, \"mat-card-content\")(3, \"div\", 10)(4, \"mat-icon\", 11);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function OracleProfileComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Go Back \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction OracleProfileComponent_div_3_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 33);\n    i0.ɵɵlistener(\"error\", function OracleProfileComponent_div_3_img_6_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.oracle.profilePictureUrl = null);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.oracle.profilePictureUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.getFullName());\n  }\n}\nfunction OracleProfileComponent_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OracleProfileComponent_div_3_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 35)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getExperienceText(), \" \");\n  }\n}\nfunction OracleProfileComponent_div_3_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.formatPrice(ctx_r8.oracle.hourlyRate), \" per hour \");\n  }\n}\nfunction OracleProfileComponent_div_3_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function OracleProfileComponent_div_3_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.contactOracle());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleProfileComponent_div_3_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function OracleProfileComponent_div_3_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.callOracle());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Call \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 37)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" About \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r11.oracle.about);\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_25_mat_chip_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialization_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", specialization_r23, \" \");\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Specializations \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 40)(8, \"mat-chip-set\");\n    i0.ɵɵtemplate(9, OracleProfileComponent_div_3_mat_card_25_mat_chip_9_Template, 2, 1, \"mat-chip\", 41);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.oracle.specializations);\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_26_mat_chip_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const language_r25 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", language_r25, \" \");\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 42)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Languages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 40)(8, \"mat-chip-set\");\n    i0.ɵɵtemplate(9, OracleProfileComponent_div_3_mat_card_26_mat_chip_9_Template, 2, 1, \"mat-chip\", 41);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.oracle.languages);\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const service_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(service_r29.description);\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 56)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const service_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", service_r29.notes, \" \");\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 49)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_p_7_Template, 2, 1, \"p\", 50);\n    i0.ɵɵtemplate(8, OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_p_8_Template, 4, 1, \"p\", 51);\n    i0.ɵɵelementStart(9, \"div\", 52)(10, \"span\", 53);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 54)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Book Service \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const service_r29 = ctx.$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(service_r29.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.formatDuration(service_r29.durationMinutes));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", service_r29.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", service_r29.notes);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r28.formatPrice(service_r29.price));\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h3\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵtemplate(4, OracleProfileComponent_div_3_mat_card_27_div_7_mat_card_4_Template, 17, 5, \"mat-card\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r27 = ctx.$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r27);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.getServicesByCategory()[category_r27]);\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 43)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"room_service\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Services & Pricing \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\");\n    i0.ɵɵtemplate(7, OracleProfileComponent_div_3_mat_card_27_div_7_Template, 5, 2, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.getCategories());\n  }\n}\nfunction OracleProfileComponent_div_3_mat_card_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 57)(1, \"mat-card-content\")(2, \"div\", 58)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"No Services Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"This oracle hasn't added any services yet.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction OracleProfileComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"mat-card\", 14)(2, \"mat-card-content\")(3, \"div\", 15)(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵtemplate(6, OracleProfileComponent_div_3_img_6_Template, 1, 2, \"img\", 18);\n    i0.ɵɵtemplate(7, OracleProfileComponent_div_3_div_7_Template, 3, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"mat-icon\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 21)(13, \"h1\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, OracleProfileComponent_div_3_p_15_Template, 4, 1, \"p\", 22);\n    i0.ɵɵtemplate(16, OracleProfileComponent_div_3_p_16_Template, 4, 1, \"p\", 23);\n    i0.ɵɵelementStart(17, \"div\", 24);\n    i0.ɵɵtemplate(18, OracleProfileComponent_div_3_button_18_Template, 4, 0, \"button\", 25);\n    i0.ɵɵtemplate(19, OracleProfileComponent_div_3_button_19_Template, 4, 0, \"button\", 26);\n    i0.ɵɵelementStart(20, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function OracleProfileComponent_div_3_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.goBack());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Back \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(24, OracleProfileComponent_div_3_mat_card_24_Template, 9, 1, \"mat-card\", 28);\n    i0.ɵɵtemplate(25, OracleProfileComponent_div_3_mat_card_25_Template, 10, 1, \"mat-card\", 29);\n    i0.ɵɵtemplate(26, OracleProfileComponent_div_3_mat_card_26_Template, 10, 1, \"mat-card\", 30);\n    i0.ɵɵtemplate(27, OracleProfileComponent_div_3_mat_card_27_Template, 8, 1, \"mat-card\", 31);\n    i0.ɵɵtemplate(28, OracleProfileComponent_div_3_mat_card_28_Template, 9, 0, \"mat-card\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"has-image\", ctx_r2.oracle.profilePictureUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.profilePictureUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.oracle.profilePictureUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"available\", ctx_r2.oracle.isAvailable);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.oracle.isAvailable ? \"check_circle\" : \"schedule\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.oracle.isAvailable ? \"Available\" : \"Busy\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getFullName());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.yearsOfExperience);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.hourlyRate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.phoneNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.about);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.specializations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.languages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.oracle.services.length === 0);\n  }\n}\nexport class OracleProfileComponent {\n  constructor(route, router, oracleService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.oracleService = oracleService;\n    this.snackBar = snackBar;\n    this.oracle = null;\n    this.loading = true;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const id = +params['id'];\n      if (id) {\n        this.loadOracleProfile(id);\n      } else {\n        this.error = 'Invalid oracle ID';\n        this.loading = false;\n      }\n    });\n  }\n  loadOracleProfile(id) {\n    this.loading = true;\n    this.error = null;\n    this.oracleService.getOracleProfile(id).subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.oracle = response.data;\n        } else {\n          this.error = response.message || 'Failed to load oracle profile';\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading oracle profile:', error);\n        this.error = 'Failed to load oracle profile';\n        this.loading = false;\n        this.snackBar.open('Failed to load oracle profile', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  getFullName() {\n    if (!this.oracle) return '';\n    return `${this.oracle.firstName} ${this.oracle.lastName}`;\n  }\n  getExperienceText() {\n    if (!this.oracle?.yearsOfExperience) return 'Experience not specified';\n    const years = this.oracle.yearsOfExperience;\n    return years === 1 ? '1 year of experience' : `${years} years of experience`;\n  }\n  getServicesByCategory() {\n    if (!this.oracle?.services) return {};\n    return this.oracle.services.reduce((acc, service) => {\n      if (!acc[service.category]) {\n        acc[service.category] = [];\n      }\n      acc[service.category].push(service);\n      return acc;\n    }, {});\n  }\n  getCategories() {\n    return Object.keys(this.getServicesByCategory());\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatDuration(minutes) {\n    if (minutes < 60) {\n      return `${minutes} min`;\n    }\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    if (remainingMinutes === 0) {\n      return `${hours}h`;\n    }\n    return `${hours}h ${remainingMinutes}min`;\n  }\n  goBack() {\n    this.router.navigate(['/oracles']);\n  }\n  contactOracle() {\n    if (this.oracle?.email) {\n      window.location.href = `mailto:${this.oracle.email}`;\n    }\n  }\n  callOracle() {\n    if (this.oracle?.phoneNumber) {\n      window.location.href = `tel:${this.oracle.phoneNumber}`;\n    }\n  }\n  static {\n    this.ɵfac = function OracleProfileComponent_Factory(t) {\n      return new (t || OracleProfileComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.OracleService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OracleProfileComponent,\n      selectors: [[\"app-oracle-profile\"]],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"profile-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-card\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-content\"], [\"color\", \"warn\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"profile-content\"], [1, \"profile-header\"], [1, \"header-content\"], [1, \"profile-image-section\"], [1, \"profile-image\"], [3, \"src\", \"alt\", \"error\", 4, \"ngIf\"], [\"class\", \"default-avatar\", 4, \"ngIf\"], [1, \"availability-badge\"], [1, \"profile-info\"], [\"class\", \"experience\", 4, \"ngIf\"], [\"class\", \"hourly-rate\", 4, \"ngIf\"], [1, \"contact-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"class\", \"about-section\", 4, \"ngIf\"], [\"class\", \"specializations-section\", 4, \"ngIf\"], [\"class\", \"languages-section\", 4, \"ngIf\"], [\"class\", \"services-section\", 4, \"ngIf\"], [\"class\", \"no-services-section\", 4, \"ngIf\"], [3, \"src\", \"alt\", \"error\"], [1, \"default-avatar\"], [1, \"experience\"], [1, \"hourly-rate\"], [1, \"about-section\"], [1, \"about-text\"], [1, \"specializations-section\"], [1, \"chips-container\"], [4, \"ngFor\", \"ngForOf\"], [1, \"languages-section\"], [1, \"services-section\"], [\"class\", \"service-category\", 4, \"ngFor\", \"ngForOf\"], [1, \"service-category\"], [1, \"category-title\"], [1, \"services-grid\"], [\"class\", \"service-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"service-card\"], [\"class\", \"service-description\", 4, \"ngIf\"], [\"class\", \"service-notes\", 4, \"ngIf\"], [1, \"service-price\"], [1, \"price\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [1, \"service-description\"], [1, \"service-notes\"], [1, \"no-services-section\"], [1, \"no-services-content\"]],\n      template: function OracleProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, OracleProfileComponent_div_1_Template, 7, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, OracleProfileComponent_div_2_Template, 14, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, OracleProfileComponent_div_3_Template, 29, 18, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.oracle && !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatIcon, i8.MatProgressSpinner, i9.MatChip, i9.MatChipSet],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  gap: 20px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 400px;\\n}\\n\\n.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n  text-align: center;\\n}\\n\\n.error-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  align-items: flex-start;\\n}\\n\\n.profile-image-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 200px;\\n}\\n\\n.profile-image[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 4px solid #e0e0e0;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.availability-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  background-color: #f5f5f5;\\n  color: #666;\\n}\\n\\n.availability-badge.available[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n\\n.availability-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.profile-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 2.5rem;\\n  font-weight: 400;\\n  color: #333;\\n}\\n\\n.experience[_ngcontent-%COMP%], .hourly-rate[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 8px 0;\\n  font-size: 1.1rem;\\n  color: #666;\\n}\\n\\n.experience[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .hourly-rate[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6f00;\\n}\\n\\n.contact-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-top: 24px;\\n  flex-wrap: wrap;\\n}\\n\\n\\n.about-section[_ngcontent-%COMP%], .specializations-section[_ngcontent-%COMP%], .languages-section[_ngcontent-%COMP%], .services-section[_ngcontent-%COMP%], .no-services-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.about-text[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.6;\\n  color: #555;\\n  margin: 0;\\n}\\n\\n\\n.chips-container[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n\\nmat-chip-set[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\nmat-chip[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  color: #333;\\n}\\n\\n\\n.service-category[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0 0 16px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n\\n.services-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 16px;\\n}\\n\\n.service-card[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  transition: box-shadow 0.3s ease;\\n}\\n\\n.service-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.service-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 8px 0;\\n  line-height: 1.5;\\n}\\n\\n.service-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  color: #555;\\n  font-style: italic;\\n  margin: 8px 0;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.service-notes[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  margin-top: 2px;\\n  color: #2196f3;\\n}\\n\\n.service-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 16px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #2e7d32;\\n}\\n\\n\\n.no-services-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n\\n.no-services-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #bbb;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .profile-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    text-align: center;\\n  }\\n\\n  .profile-image-section[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n\\n  .profile-image[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n\\n  .profile-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .contact-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n\\n  .services-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .profile-image[_ngcontent-%COMP%] {\\n    width: 120px;\\n    height: 120px;\\n  }\\n\\n  .profile-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n\\n  .contact-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;IAGEA,8BAA+C;IAIvCA,iCAAyC;IACzCA,yBAAG;IAAAA,yCAAyB;IAAAA,iBAAI;;;;;;IAOxCA,8BAAuD;IAIxBA,qBAAK;IAAAA,iBAAW;IACvCA,0BAAI;IAAAA,qBAAK;IAAAA,iBAAK;IACdA,yBAAG;IAAAA,YAAW;IAAAA,iBAAI;IAClBA,mCAA6D;IAAnBA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IAC1DA,iCAAU;IAAAA,2BAAU;IAAAA,iBAAW;IAC/BA,0BACF;IAAAA,iBAAS;;;;IAJNA,eAAW;IAAXA,kCAAW;;;;;;IAkBVA,+BAKE;IADAA;MAAAA;MAAA;MAAA,OAASA,kDAA2B,IAAI;IAAA,EAAC;IAJ3CA,iBAKE;;;;IAHAA,uEAAgC;;;;;IAIlCA,+BAA8D;IAClDA,sBAAM;IAAAA,iBAAW;;;;;IAW/BA,6BAAuD;IAC3CA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,2DACF;;;;;IACAA,6BAAiD;IACrCA,4BAAY;IAAAA,iBAAW;IACjCA,YACF;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,sFACF;;;;;;IAIEA,kCAAyF;IAA/CA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IACjEA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAC1BA,yBACF;IAAAA,iBAAS;;;;;;IACTA,kCAA6E;IAAlDA;MAAAA;MAAA;MAAA,OAASA,mCAAY;IAAA,EAAC;IAC/CA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAC1BA,sBACF;IAAAA,iBAAS;;;;;IAYnBA,oCAAqD;IAGrCA,sBAAM;IAAAA,iBAAW;IAC3BA,uBACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IACMA,YAAkB;IAAAA,iBAAI;;;;IAAtBA,eAAkB;IAAlBA,0CAAkB;;;;;IAepCA,gCAAgE;IAC9DA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,mDACF;;;;;IAZRA,oCAAoF;IAGpEA,0BAAU;IAAAA,iBAAW;IAC/BA,iCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAGZA,oGAEW;IACbA,iBAAe;;;;IAHwBA,eAAyB;IAAzBA,wDAAyB;;;;;IAmB9DA,gCAAoD;IAClDA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,6CACF;;;;;IAZRA,oCAAwE;IAGxDA,wBAAQ;IAAAA,iBAAW;IAC7BA,2BACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAGZA,oGAEW;IACbA,iBAAe;;;;IAHkBA,eAAmB;IAAnBA,kDAAmB;;;;;IA0B9CA,6BAA2D;IAAAA,YAAyB;IAAAA,iBAAI;;;;IAA7BA,eAAyB;IAAzBA,6CAAyB;;;;;IACpFA,6BAA+C;IACnCA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,kDACF;;;;;IAVJA,oCAAyF;IAErEA,YAAkB;IAAAA,iBAAiB;IACnDA,yCAAmB;IAAAA,YAA6C;IAAAA,iBAAoB;IAEtFA,wCAAkB;IAChBA,uGAAwF;IACxFA,uGAGI;IACJA,+BAA2B;IACLA,aAAgC;IAAAA,iBAAO;IAG/DA,yCAAkB;IAEJA,8BAAa;IAAAA,iBAAW;IAClCA,+BACF;IAAAA,iBAAS;;;;;IAjBOA,eAAkB;IAAlBA,sCAAkB;IACfA,eAA6C;IAA7CA,yEAA6C;IAG5DA,eAAyB;IAAzBA,8CAAyB;IACzBA,eAAmB;IAAnBA,wCAAmB;IAKDA,eAAgC;IAAhCA,4DAAgC;;;;;IAf9DA,+BAAuE;IAC1CA,YAAc;IAAAA,iBAAK;IAC9CA,+BAA2B;IACzBA,2GAqBW;IACbA,iBAAM;;;;;IAxBqBA,eAAc;IAAdA,kCAAc;IAETA,eAAoC;IAApCA,uEAAoC;;;;;IAX1EA,oCAAsE;IAGtDA,4BAAY;IAAAA,iBAAW;IACjCA,oCACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAChBA,0FA0BM;IACRA,iBAAmB;;;;IA3BSA,eAAkB;IAAlBA,iDAAkB;;;;;IA+BhDA,oCAA2E;IAG3DA,oBAAI;IAAAA,iBAAW;IACzBA,0BAAI;IAAAA,qCAAqB;IAAAA,iBAAK;IAC9BA,yBAAG;IAAAA,0DAA0C;IAAAA,iBAAI;;;;;;IAtJzDA,+BAAwD;IAO5CA,8EAKE;IACFA,8EAEM;IACRA,iBAAM;IACNA,+BAAuE;IAC3DA,aAAsD;IAAAA,iBAAW;IAC3EA,aACF;IAAAA,iBAAM;IAGRA,gCAA0B;IACpBA,aAAmB;IAAAA,iBAAK;IAC5BA,4EAGI;IACJA,4EAGI;IAGJA,gCAA6B;IAC3BA,sFAGS;IACTA,sFAGS;IACTA,mCAA8C;IAAnBA;MAAAA;MAAA;MAAA,OAASA,+BAAQ;IAAA,EAAC;IAC3CA,iCAAU;IAAAA,2BAAU;IAAAA,iBAAW;IAC/BA,uBACF;IAAAA,iBAAS;IAQnBA,0FAUW;IAGXA,2FAgBW;IAGXA,2FAgBW;IAGXA,0FAoCW;IAGXA,0FAQW;IACbA,iBAAM;;;;IApJ+BA,eAA4C;IAA5CA,4DAA4C;IAElEA,eAA8B;IAA9BA,sDAA8B;IAK3BA,eAA+B;IAA/BA,uDAA+B;IAIPA,eAAsC;IAAtCA,sDAAsC;IAC1DA,eAAsD;IAAtDA,6EAAsD;IAChEA,eACF;IADEA,iFACF;IAIIA,eAAmB;IAAnBA,0CAAmB;IACAA,eAA8B;IAA9BA,sDAA8B;IAI7BA,eAAuB;IAAvBA,+CAAuB;IAOwBA,eAAkB;IAAlBA,0CAAkB;IAIpCA,eAAwB;IAAxBA,gDAAwB;IAepDA,eAAkB;IAAlBA,0CAAkB;IAaRA,eAAuC;IAAvCA,+DAAuC;IAmB7CA,eAAiC;IAAjCA,yDAAiC;IAmBlCA,eAAgC;IAAhCA,wDAAgC;IAuC7BA,eAAkC;IAAlCA,0DAAkC;;;ACvK7E,OAAM,MAAOC,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,QAAqB;IAHrB,UAAK,GAALH,KAAK;IACL,WAAM,GAANC,MAAM;IACN,kBAAa,GAAbC,aAAa;IACb,aAAQ,GAARC,QAAQ;IARlB,WAAM,GAAyB,IAAI;IACnC,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;EAOvB;EAEJC,QAAQ;IACN,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,MAAME,EAAE,GAAG,CAACF,MAAM,CAAC,IAAI,CAAC;MACxB,IAAIE,EAAE,EAAE;QACN,IAAI,CAACC,iBAAiB,CAACD,EAAE,CAAC;OAC3B,MAAM;QACL,IAAI,CAACE,KAAK,GAAG,mBAAmB;QAChC,IAAI,CAACC,OAAO,GAAG,KAAK;;IAExB,CAAC,CAAC;EACJ;EAEQF,iBAAiB,CAACD,EAAU;IAClC,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACP,aAAa,CAACS,gBAAgB,CAACJ,EAAE,CAAC,CAACD,SAAS,CAAC;MAChDM,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACC,MAAM,GAAGH,QAAQ,CAACE,IAAI;SAC5B,MAAM;UACL,IAAI,CAACN,KAAK,GAAGI,QAAQ,CAACI,OAAO,IAAI,+BAA+B;;QAElE,IAAI,CAACP,OAAO,GAAG,KAAK;MACtB,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACfS,OAAO,CAACT,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACA,KAAK,GAAG,+BAA+B;QAC5C,IAAI,CAACC,OAAO,GAAG,KAAK;QACpB,IAAI,CAACP,QAAQ,CAACgB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAC3DC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEAC,WAAW;IACT,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE,OAAO,EAAE;IAC3B,OAAO,GAAG,IAAI,CAACA,MAAM,CAACO,SAAS,IAAI,IAAI,CAACP,MAAM,CAACQ,QAAQ,EAAE;EAC3D;EAEAC,iBAAiB;IACf,IAAI,CAAC,IAAI,CAACT,MAAM,EAAEU,iBAAiB,EAAE,OAAO,0BAA0B;IACtE,MAAMC,KAAK,GAAG,IAAI,CAACX,MAAM,CAACU,iBAAiB;IAC3C,OAAOC,KAAK,KAAK,CAAC,GAAG,sBAAsB,GAAG,GAAGA,KAAK,sBAAsB;EAC9E;EAEAC,qBAAqB;IACnB,IAAI,CAAC,IAAI,CAACZ,MAAM,EAAEa,QAAQ,EAAE,OAAO,EAAE;IAErC,OAAO,IAAI,CAACb,MAAM,CAACa,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAI;MAClD,IAAI,CAACD,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,EAAE;QAC1BF,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,GAAG,EAAE;;MAE5BF,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,CAACC,IAAI,CAACF,OAAO,CAAC;MACnC,OAAOD,GAAG;IACZ,CAAC,EAAE,EAAmC,CAAC;EACzC;EAEAI,aAAa;IACX,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACT,qBAAqB,EAAE,CAAC;EAClD;EAEAU,WAAW,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EAEAM,cAAc,CAACC,OAAe;IAC5B,IAAIA,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGA,OAAO,MAAM;;IAEzB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,IAAII,gBAAgB,KAAK,CAAC,EAAE;MAC1B,OAAO,GAAGH,KAAK,GAAG;;IAEpB,OAAO,GAAGA,KAAK,KAAKG,gBAAgB,KAAK;EAC3C;EAEAC,MAAM;IACJ,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAC,aAAa;IACX,IAAI,IAAI,CAACrC,MAAM,EAAEsC,KAAK,EAAE;MACtBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU,IAAI,CAACzC,MAAM,CAACsC,KAAK,EAAE;;EAExD;EAEAI,UAAU;IACR,IAAI,IAAI,CAAC1C,MAAM,EAAE2C,WAAW,EAAE;MAC5BJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO,IAAI,CAACzC,MAAM,CAAC2C,WAAW,EAAE;;EAE3D;;;uBA7GW7D,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAA8D;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDTnCnE,8BAA+B;UAE7BA,uEASM;UAGNA,wEAcM;UAGNA,yEA0JM;UACRA,iBAAM;;;UAxLEA,eAAa;UAAbA,kCAAa;UAYbA,eAAuB;UAAvBA,gDAAuB;UAiBvBA,eAAwB;UAAxBA,iDAAwB", "names": ["i0", "OracleProfileComponent", "constructor", "route", "router", "oracleService", "snackBar", "ngOnInit", "params", "subscribe", "id", "loadOracleProfile", "error", "loading", "getOracleProfile", "next", "response", "success", "data", "oracle", "message", "console", "open", "duration", "panelClass", "getFullName", "firstName", "lastName", "getExperienceText", "yearsOfExperience", "years", "getServicesByCategory", "services", "reduce", "acc", "service", "category", "push", "getCategories", "Object", "keys", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "formatDuration", "minutes", "hours", "Math", "floor", "remainingMinutes", "goBack", "navigate", "contactOracle", "email", "window", "location", "href", "callO<PERSON>le", "phoneNumber", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\oracle-profile\\oracle-profile.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\oracle-profile\\oracle-profile.component.ts"], "sourcesContent": ["<!-- Oracle Profile Page -->\n<div class=\"profile-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-card class=\"loading-card\">\n      <mat-card-content>\n        <div class=\"loading-content\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading oracle profile...</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !loading\" class=\"error-container\">\n    <mat-card class=\"error-card\">\n      <mat-card-content>\n        <div class=\"error-content\">\n          <mat-icon color=\"warn\">error</mat-icon>\n          <h3>Error</h3>\n          <p>{{ error }}</p>\n          <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\n            <mat-icon>arrow_back</mat-icon>\n            Go Back\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Oracle Profile Content -->\n  <div *ngIf=\"oracle && !loading\" class=\"profile-content\">\n    <!-- Header Section -->\n    <mat-card class=\"profile-header\">\n      <mat-card-content>\n        <div class=\"header-content\">\n          <div class=\"profile-image-section\">\n            <div class=\"profile-image\" [class.has-image]=\"oracle.profilePictureUrl\">\n              <img\n                *ngIf=\"oracle.profilePictureUrl\"\n                [src]=\"oracle.profilePictureUrl\"\n                [alt]=\"getFullName()\"\n                (error)=\"oracle.profilePictureUrl = null\"\n              />\n              <div *ngIf=\"!oracle.profilePictureUrl\" class=\"default-avatar\">\n                <mat-icon>person</mat-icon>\n              </div>\n            </div>\n            <div class=\"availability-badge\" [class.available]=\"oracle.isAvailable\">\n              <mat-icon>{{ oracle.isAvailable ? 'check_circle' : 'schedule' }}</mat-icon>\n              {{ oracle.isAvailable ? 'Available' : 'Busy' }}\n            </div>\n          </div>\n          \n          <div class=\"profile-info\">\n            <h1>{{ getFullName() }}</h1>\n            <p class=\"experience\" *ngIf=\"oracle.yearsOfExperience\">\n              <mat-icon>star</mat-icon>\n              {{ getExperienceText() }}\n            </p>\n            <p class=\"hourly-rate\" *ngIf=\"oracle.hourlyRate\">\n              <mat-icon>attach_money</mat-icon>\n              {{ formatPrice(oracle.hourlyRate) }} per hour\n            </p>\n            \n            <!-- Contact Actions -->\n            <div class=\"contact-actions\">\n              <button mat-raised-button color=\"primary\" (click)=\"contactOracle()\" *ngIf=\"oracle.email\">\n                <mat-icon>email</mat-icon>\n                Contact\n              </button>\n              <button mat-stroked-button (click)=\"callOracle()\" *ngIf=\"oracle.phoneNumber\">\n                <mat-icon>phone</mat-icon>\n                Call\n              </button>\n              <button mat-stroked-button (click)=\"goBack()\">\n                <mat-icon>arrow_back</mat-icon>\n                Back\n              </button>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- About Section -->\n    <mat-card class=\"about-section\" *ngIf=\"oracle.about\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>person</mat-icon>\n          About\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p class=\"about-text\">{{ oracle.about }}</p>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Specializations Section -->\n    <mat-card class=\"specializations-section\" *ngIf=\"oracle.specializations.length > 0\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>psychology</mat-icon>\n          Specializations\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"chips-container\">\n          <mat-chip-set>\n            <mat-chip *ngFor=\"let specialization of oracle.specializations\">\n              {{ specialization }}\n            </mat-chip>\n          </mat-chip-set>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Languages Section -->\n    <mat-card class=\"languages-section\" *ngIf=\"oracle.languages.length > 0\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>language</mat-icon>\n          Languages\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"chips-container\">\n          <mat-chip-set>\n            <mat-chip *ngFor=\"let language of oracle.languages\">\n              {{ language }}\n            </mat-chip>\n          </mat-chip-set>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Services Section -->\n    <mat-card class=\"services-section\" *ngIf=\"oracle.services.length > 0\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>room_service</mat-icon>\n          Services & Pricing\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div *ngFor=\"let category of getCategories()\" class=\"service-category\">\n          <h3 class=\"category-title\">{{ category }}</h3>\n          <div class=\"services-grid\">\n            <mat-card *ngFor=\"let service of getServicesByCategory()[category]\" class=\"service-card\">\n              <mat-card-header>\n                <mat-card-title>{{ service.name }}</mat-card-title>\n                <mat-card-subtitle>{{ formatDuration(service.durationMinutes) }}</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <p *ngIf=\"service.description\" class=\"service-description\">{{ service.description }}</p>\n                <p *ngIf=\"service.notes\" class=\"service-notes\">\n                  <mat-icon>info</mat-icon>\n                  {{ service.notes }}\n                </p>\n                <div class=\"service-price\">\n                  <span class=\"price\">{{ formatPrice(service.price) }}</span>\n                </div>\n              </mat-card-content>\n              <mat-card-actions>\n                <button mat-raised-button color=\"accent\">\n                  <mat-icon>shopping_cart</mat-icon>\n                  Book Service\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- No Services Message -->\n    <mat-card class=\"no-services-section\" *ngIf=\"oracle.services.length === 0\">\n      <mat-card-content>\n        <div class=\"no-services-content\">\n          <mat-icon>info</mat-icon>\n          <h3>No Services Available</h3>\n          <p>This oracle hasn't added any services yet.</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { OracleService, OracleProfile } from '../services/oracle.service';\n\n@Component({\n  selector: 'app-oracle-profile',\n  templateUrl: './oracle-profile.component.html',\n  styleUrls: ['./oracle-profile.component.css']\n})\nexport class OracleProfileComponent implements OnInit {\n  oracle: OracleProfile | null = null;\n  loading = true;\n  error: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private oracleService: OracleService,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    this.route.params.subscribe(params => {\n      const id = +params['id'];\n      if (id) {\n        this.loadOracleProfile(id);\n      } else {\n        this.error = 'Invalid oracle ID';\n        this.loading = false;\n      }\n    });\n  }\n\n  private loadOracleProfile(id: number): void {\n    this.loading = true;\n    this.error = null;\n\n    this.oracleService.getOracleProfile(id).subscribe({\n      next: (response) => {\n        if (response.success && response.data) {\n          this.oracle = response.data;\n        } else {\n          this.error = response.message || 'Failed to load oracle profile';\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading oracle profile:', error);\n        this.error = 'Failed to load oracle profile';\n        this.loading = false;\n        this.snackBar.open('Failed to load oracle profile', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  getFullName(): string {\n    if (!this.oracle) return '';\n    return `${this.oracle.firstName} ${this.oracle.lastName}`;\n  }\n\n  getExperienceText(): string {\n    if (!this.oracle?.yearsOfExperience) return 'Experience not specified';\n    const years = this.oracle.yearsOfExperience;\n    return years === 1 ? '1 year of experience' : `${years} years of experience`;\n  }\n\n  getServicesByCategory(): { [category: string]: any[] } {\n    if (!this.oracle?.services) return {};\n    \n    return this.oracle.services.reduce((acc, service) => {\n      if (!acc[service.category]) {\n        acc[service.category] = [];\n      }\n      acc[service.category].push(service);\n      return acc;\n    }, {} as { [category: string]: any[] });\n  }\n\n  getCategories(): string[] {\n    return Object.keys(this.getServicesByCategory());\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatDuration(minutes: number): string {\n    if (minutes < 60) {\n      return `${minutes} min`;\n    }\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    if (remainingMinutes === 0) {\n      return `${hours}h`;\n    }\n    return `${hours}h ${remainingMinutes}min`;\n  }\n\n  goBack(): void {\n    this.router.navigate(['/oracles']);\n  }\n\n  contactOracle(): void {\n    if (this.oracle?.email) {\n      window.location.href = `mailto:${this.oracle.email}`;\n    }\n  }\n\n  callOracle(): void {\n    if (this.oracle?.phoneNumber) {\n      window.location.href = `tel:${this.oracle.phoneNumber}`;\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}