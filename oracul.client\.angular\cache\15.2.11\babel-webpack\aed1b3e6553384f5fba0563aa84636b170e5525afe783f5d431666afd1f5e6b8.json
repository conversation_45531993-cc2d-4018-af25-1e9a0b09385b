{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\nfunction MatDrawerContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\nfunction MatDrawerContainer_mat_drawer_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\nfunction MatSidenavContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\nfunction MatSidenavContainer_mat_sidenav_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\";\nconst matDrawerAnimations = {\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: trigger('transform', [\n  // We remove the `transform` here completely, rather than setting it to zero, because:\n  // 1. Having a transform can cause elements with ripples or an animated\n  //    transform to shift around in Chrome with an RTL layout (see #10023).\n  // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  state('open, open-instant', style({\n    'transform': 'none',\n    'visibility': 'visible'\n  })), state('void', style({\n    // Avoids the shadow showing up when closed in SSR.\n    'box-shadow': 'none',\n    'visibility': 'hidden'\n  })), transition('void => open-instant', animate('0ms')), transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'))])\n};\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n  constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n    super(elementRef, scrollDispatcher, ngZone);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._container = _container;\n  }\n  ngAfterContentInit() {\n    this._container._contentMarginChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n}\nMatDrawerContent.ɵfac = function MatDrawerContent_Factory(t) {\n  return new (t || MatDrawerContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatDrawerContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nMatDrawerContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawerContent,\n  selectors: [[\"mat-drawer-content\"]],\n  hostAttrs: [1, \"mat-drawer-content\"],\n  hostVars: 4,\n  hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: MatDrawerContent\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatDrawerContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatDrawerContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n  /** The side that the drawer is attached to. */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    // Make sure we have a valid value.\n    value = value === 'end' ? 'end' : 'start';\n    if (value !== this._position) {\n      // Static inputs in Ivy are set before the element is in the DOM.\n      if (this._isAttached) {\n        this._updatePositionInParent(value);\n      }\n      this._position = value;\n      this.onPositionChanged.emit();\n    }\n  }\n  /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n  get mode() {\n    return this._mode;\n  }\n  set mode(value) {\n    this._mode = value;\n    this._updateFocusTrapState();\n    this._modeChanged.next();\n  }\n  /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n  get disableClose() {\n    return this._disableClose;\n  }\n  set disableClose(value) {\n    this._disableClose = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer should focus the first focusable element automatically when opened.\n   * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n   * enabled, focus will be moved into the sidenav in `side` mode as well.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n   * instead.\n   */\n  get autoFocus() {\n    const value = this._autoFocus;\n    // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n    // because we don't know how the sidenav is being used, but in some cases it still makes\n    // sense to do it. The consumer can explicitly set `autoFocus`.\n    if (value == null) {\n      if (this.mode === 'side') {\n        return 'dialog';\n      } else {\n        return 'first-tabbable';\n      }\n    }\n    return value;\n  }\n  set autoFocus(value) {\n    if (value === 'true' || value === 'false' || value == null) {\n      value = coerceBooleanProperty(value);\n    }\n    this._autoFocus = value;\n  }\n  /**\n   * Whether the drawer is opened. We overload this because we trigger an event when it\n   * starts or end.\n   */\n  get opened() {\n    return this._opened;\n  }\n  set opened(value) {\n    this.toggle(coerceBooleanProperty(value));\n  }\n  constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._focusMonitor = _focusMonitor;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._interactivityChecker = _interactivityChecker;\n    this._doc = _doc;\n    this._container = _container;\n    this._elementFocusedBeforeDrawerWasOpened = null;\n    /** Whether the drawer is initialized. Used for disabling the initial animation. */\n    this._enableAnimations = false;\n    this._position = 'start';\n    this._mode = 'over';\n    this._disableClose = false;\n    this._opened = false;\n    /** Emits whenever the drawer has started animating. */\n    this._animationStarted = new Subject();\n    /** Emits whenever the drawer is done animating. */\n    this._animationEnd = new Subject();\n    /** Current state of the sidenav animation. */\n    this._animationState = 'void';\n    /** Event emitted when the drawer open state is changed. */\n    this.openedChange =\n    // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter( /* isAsync */true);\n    /** Event emitted when the drawer has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the drawer has started opening. */\n    this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n    /** Event emitted when the drawer has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the drawer has started closing. */\n    this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Event emitted when the drawer's position changes. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onPositionChanged = new EventEmitter();\n    /**\n     * An observable that emits when the drawer mode changes. This is used by the drawer container to\n     * to know when to when the mode changes so it can adapt the margins on the content.\n     */\n    this._modeChanged = new Subject();\n    this.openedChange.subscribe(opened => {\n      if (opened) {\n        if (this._doc) {\n          this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n        }\n        this._takeFocus();\n      } else if (this._isFocusWithinDrawer()) {\n        this._restoreFocus(this._openedVia || 'program');\n      }\n    });\n    /**\n     * Listen to `keydown` events outside the zone so that change detection is not run every\n     * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n     * and we don't have close disabled.\n     */\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._elementRef.nativeElement, 'keydown').pipe(filter(event => {\n        return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n      }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n        this.close();\n        event.stopPropagation();\n        event.preventDefault();\n      }));\n    });\n    // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n    this._animationEnd.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      const {\n        fromState,\n        toState\n      } = event;\n      if (toState.indexOf('open') === 0 && fromState === 'void' || toState === 'void' && fromState.indexOf('open') === 0) {\n        this.openedChange.emit(this._opened);\n      }\n    });\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves focus into the drawer. Note that this works even if\n   * the focus trap is disabled in `side` mode.\n   */\n  _takeFocus() {\n    if (!this._focusTrap) {\n      return;\n    }\n    const element = this._elementRef.nativeElement;\n    // When autoFocus is not on the sidenav, if the element cannot be focused or does\n    // not exist, focus the sidenav itself so the keyboard navigation still works.\n    // We need to check that `focus` is a function due to Universal.\n    switch (this.autoFocus) {\n      case false:\n      case 'dialog':\n        return;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n          if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n            element.focus();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this.autoFocus);\n        break;\n    }\n  }\n  /**\n   * Restores focus to the element that was originally focused when the drawer opened.\n   * If no element was focused at that time, the focus will be restored to the drawer.\n   */\n  _restoreFocus(focusOrigin) {\n    if (this.autoFocus === 'dialog') {\n      return;\n    }\n    if (this._elementFocusedBeforeDrawerWasOpened) {\n      this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n    } else {\n      this._elementRef.nativeElement.blur();\n    }\n    this._elementFocusedBeforeDrawerWasOpened = null;\n  }\n  /** Whether focus is currently within the drawer. */\n  _isFocusWithinDrawer() {\n    const activeEl = this._doc.activeElement;\n    return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n  }\n  ngAfterViewInit() {\n    this._isAttached = true;\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n    this._updateFocusTrapState();\n    // Only update the DOM position when the sidenav is positioned at\n    // the end since we project the sidenav before the content by default.\n    if (this._position === 'end') {\n      this._updatePositionInParent('end');\n    }\n  }\n  ngAfterContentChecked() {\n    // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n    // drawers that are open by default. When we're on the server, we shouldn't enable the\n    // animations, because we don't want the drawer to animate the first time the user sees\n    // the page.\n    if (this._platform.isBrowser) {\n      this._enableAnimations = true;\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n    this._anchor?.remove();\n    this._anchor = null;\n    this._animationStarted.complete();\n    this._animationEnd.complete();\n    this._modeChanged.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Open the drawer.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  open(openedVia) {\n    return this.toggle(true, openedVia);\n  }\n  /** Close the drawer. */\n  close() {\n    return this.toggle(false);\n  }\n  /** Closes the drawer with context that the backdrop was clicked. */\n  _closeViaBackdropClick() {\n    // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n    // don't need to check whether focus is currently in the drawer, as clicking on the\n    // backdrop causes blurs the active element.\n    return this._setOpen( /* isOpen */false, /* restoreFocus */true, 'mouse');\n  }\n  /**\n   * Toggle this drawer.\n   * @param isOpen Whether the drawer should be open.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n  toggle(isOpen = !this.opened, openedVia) {\n    // If the focus is currently inside the drawer content and we are closing the drawer,\n    // restore the focus to the initially focused element (when the drawer opened).\n    if (isOpen && openedVia) {\n      this._openedVia = openedVia;\n    }\n    const result = this._setOpen(isOpen, /* restoreFocus */!isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n    if (!isOpen) {\n      this._openedVia = null;\n    }\n    return result;\n  }\n  /**\n   * Toggles the opened state of the drawer.\n   * @param isOpen Whether the drawer should open or close.\n   * @param restoreFocus Whether focus should be restored on close.\n   * @param focusOrigin Origin to use when restoring focus.\n   */\n  _setOpen(isOpen, restoreFocus, focusOrigin) {\n    this._opened = isOpen;\n    if (isOpen) {\n      this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n    } else {\n      this._animationState = 'void';\n      if (restoreFocus) {\n        this._restoreFocus(focusOrigin);\n      }\n    }\n    this._updateFocusTrapState();\n    return new Promise(resolve => {\n      this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n    });\n  }\n  _getWidth() {\n    return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n  }\n  /** Updates the enabled state of the focus trap. */\n  _updateFocusTrapState() {\n    if (this._focusTrap) {\n      // The focus trap is only enabled when the drawer is open in any mode other than side.\n      this._focusTrap.enabled = this.opened && this.mode !== 'side';\n    }\n  }\n  /**\n   * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n   * when it's in the `end` position so that it comes after the content and the visual order\n   * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n   * started off as `end` and was changed to `start`.\n   */\n  _updatePositionInParent(newPosition) {\n    const element = this._elementRef.nativeElement;\n    const parent = element.parentNode;\n    if (newPosition === 'end') {\n      if (!this._anchor) {\n        this._anchor = this._doc.createComment('mat-drawer-anchor');\n        parent.insertBefore(this._anchor, element);\n      }\n      parent.appendChild(element);\n    } else if (this._anchor) {\n      this._anchor.parentNode.insertBefore(element, this._anchor);\n    }\n  }\n}\nMatDrawer.ɵfac = function MatDrawer_Factory(t) {\n  return new (t || MatDrawer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusTrapFactory), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.InteractivityChecker), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MAT_DRAWER_CONTAINER, 8));\n};\nMatDrawer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawer,\n  selectors: [[\"mat-drawer\"]],\n  viewQuery: function MatDrawer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n    }\n  },\n  hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\"],\n  hostVars: 12,\n  hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵsyntheticHostListener(\"@transform.start\", function MatDrawer_animation_transform_start_HostBindingHandler($event) {\n        return ctx._animationStarted.next($event);\n      })(\"@transform.done\", function MatDrawer_animation_transform_done_HostBindingHandler($event) {\n        return ctx._animationEnd.next($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"align\", null);\n      i0.ɵɵsyntheticHostProperty(\"@transform\", ctx._animationState);\n      i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened);\n    }\n  },\n  inputs: {\n    position: \"position\",\n    mode: \"mode\",\n    disableClose: \"disableClose\",\n    autoFocus: \"autoFocus\",\n    opened: \"opened\"\n  },\n  outputs: {\n    openedChange: \"openedChange\",\n    _openedStream: \"opened\",\n    openedStart: \"openedStart\",\n    _closedStream: \"closed\",\n    closedStart: \"closedStart\",\n    onPositionChanged: \"positionChanged\"\n  },\n  exportAs: [\"matDrawer\"],\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 0,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n  template: function MatDrawer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n  },\n  dependencies: [i1.CdkScrollable],\n  encapsulation: 2,\n  data: {\n    animation: [matDrawerAnimations.transformDrawer]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer',\n      exportAs: 'matDrawer',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        'tabIndex': '-1',\n        '[@transform]': '_animationState',\n        '(@transform.start)': '_animationStarted.next($event)',\n        '(@transform.done)': '_animationEnd.next($event)'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i2.FocusTrapFactory\n    }, {\n      type: i2.FocusMonitor\n    }, {\n      type: i3.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.InteractivityChecker\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DRAWER_CONTAINER]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    disableClose: [{\n      type: Input\n    }],\n    autoFocus: [{\n      type: Input\n    }],\n    opened: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    openedStart: [{\n      type: Output\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    closedStart: [{\n      type: Output\n    }],\n    onPositionChanged: [{\n      type: Output,\n      args: ['positionChanged']\n    }],\n    _content: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n  /** The drawer child with the `start` position. */\n  get start() {\n    return this._start;\n  }\n  /** The drawer child with the `end` position. */\n  get end() {\n    return this._end;\n  }\n  /**\n   * Whether to automatically resize the container whenever\n   * the size of any of its drawers changes.\n   *\n   * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n   * the drawers on every change detection cycle. Can be configured globally via the\n   * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n   */\n  get autosize() {\n    return this._autosize;\n  }\n  set autosize(value) {\n    this._autosize = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n   * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n   * mode as well.\n   */\n  get hasBackdrop() {\n    if (this._backdropOverride == null) {\n      return !this._start || this._start.mode !== 'side' || !this._end || this._end.mode !== 'side';\n    }\n    return this._backdropOverride;\n  }\n  set hasBackdrop(value) {\n    this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n  }\n  /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n  get scrollable() {\n    return this._userContent || this._content;\n  }\n  constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n    this._dir = _dir;\n    this._element = _element;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** Drawers that belong to this container. */\n    this._drawers = new QueryList();\n    /** Event emitted when the drawer backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Emits on every ngDoCheck. Used for debouncing reflows. */\n    this._doCheckSubject = new Subject();\n    /**\n     * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n     * drawer is open. We use margin rather than transform even for push mode because transform breaks\n     * fixed position elements inside of the transformed element.\n     */\n    this._contentMargins = {\n      left: null,\n      right: null\n    };\n    this._contentMarginChanges = new Subject();\n    // If a `Dir` directive exists up the tree, listen direction changes\n    // and update the left/right properties to point to the proper start/end.\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._validateDrawers();\n        this.updateContentMargins();\n      });\n    }\n    // Since the minimum width of the sidenav depends on the viewport width,\n    // we need to recompute the margins if the viewport changes.\n    viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    this._autosize = defaultAutosize;\n  }\n  ngAfterContentInit() {\n    this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n      this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n      this._drawers.notifyOnChanges();\n    });\n    this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n      this._validateDrawers();\n      this._drawers.forEach(drawer => {\n        this._watchDrawerToggle(drawer);\n        this._watchDrawerPosition(drawer);\n        this._watchDrawerMode(drawer);\n      });\n      if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n        this.updateContentMargins();\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n    // Avoid hitting the NgZone through the debounce timeout.\n    this._ngZone.runOutsideAngular(() => {\n      this._doCheckSubject.pipe(debounceTime(10),\n      // Arbitrary debounce time, less than a frame at 60fps\n      takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    });\n  }\n  ngOnDestroy() {\n    this._contentMarginChanges.complete();\n    this._doCheckSubject.complete();\n    this._drawers.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Calls `open` of both start and end drawers */\n  open() {\n    this._drawers.forEach(drawer => drawer.open());\n  }\n  /** Calls `close` of both start and end drawers */\n  close() {\n    this._drawers.forEach(drawer => drawer.close());\n  }\n  /**\n   * Recalculates and updates the inline styles for the content. Note that this should be used\n   * sparingly, because it causes a reflow.\n   */\n  updateContentMargins() {\n    // 1. For drawers in `over` mode, they don't affect the content.\n    // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n    //    left margin (for left drawer) or right margin (for right the drawer).\n    // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n    //    adding to the left or right margin and simultaneously subtracting the same amount of\n    //    margin from the other side.\n    let left = 0;\n    let right = 0;\n    if (this._left && this._left.opened) {\n      if (this._left.mode == 'side') {\n        left += this._left._getWidth();\n      } else if (this._left.mode == 'push') {\n        const width = this._left._getWidth();\n        left += width;\n        right -= width;\n      }\n    }\n    if (this._right && this._right.opened) {\n      if (this._right.mode == 'side') {\n        right += this._right._getWidth();\n      } else if (this._right.mode == 'push') {\n        const width = this._right._getWidth();\n        right += width;\n        left -= width;\n      }\n    }\n    // If either `right` or `left` is zero, don't set a style to the element. This\n    // allows users to specify a custom size via CSS class in SSR scenarios where the\n    // measured widths will always be zero. Note that we reset to `null` here, rather\n    // than below, in order to ensure that the types in the `if` below are consistent.\n    left = left || null;\n    right = right || null;\n    if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n      this._contentMargins = {\n        left,\n        right\n      };\n      // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n      // to do it only when something changed, otherwise we can end up hitting the zone too often.\n      this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n    }\n  }\n  ngDoCheck() {\n    // If users opted into autosizing, do a check every change detection cycle.\n    if (this._autosize && this._isPushed()) {\n      // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n      this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n    }\n  }\n  /**\n   * Subscribes to drawer events in order to set a class on the main container element when the\n   * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n   * is properly hidden.\n   */\n  _watchDrawerToggle(drawer) {\n    drawer._animationStarted.pipe(filter(event => event.fromState !== event.toState), takeUntil(this._drawers.changes)).subscribe(event => {\n      // Set the transition class on the container so that the animations occur. This should not\n      // be set initially because animations should only be triggered via a change in state.\n      if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n        this._element.nativeElement.classList.add('mat-drawer-transition');\n      }\n      this.updateContentMargins();\n      this._changeDetectorRef.markForCheck();\n    });\n    if (drawer.mode !== 'side') {\n      drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n    }\n  }\n  /**\n   * Subscribes to drawer onPositionChanged event in order to\n   * re-validate drawers when the position changes.\n   */\n  _watchDrawerPosition(drawer) {\n    if (!drawer) {\n      return;\n    }\n    // NOTE: We need to wait for the microtask queue to be empty before validating,\n    // since both drawers may be swapping positions at the same time.\n    drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._validateDrawers();\n      });\n    });\n  }\n  /** Subscribes to changes in drawer mode so we can run change detection. */\n  _watchDrawerMode(drawer) {\n    if (drawer) {\n      drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n        this.updateContentMargins();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n  _setContainerClass(isAdd) {\n    const classList = this._element.nativeElement.classList;\n    const className = 'mat-drawer-container-has-open';\n    if (isAdd) {\n      classList.add(className);\n    } else {\n      classList.remove(className);\n    }\n  }\n  /** Validate the state of the drawer children components. */\n  _validateDrawers() {\n    this._start = this._end = null;\n    // Ensure that we have at most one start and one end drawer.\n    this._drawers.forEach(drawer => {\n      if (drawer.position == 'end') {\n        if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('end');\n        }\n        this._end = drawer;\n      } else {\n        if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('start');\n        }\n        this._start = drawer;\n      }\n    });\n    this._right = this._left = null;\n    // Detect if we're LTR or RTL.\n    if (this._dir && this._dir.value === 'rtl') {\n      this._left = this._end;\n      this._right = this._start;\n    } else {\n      this._left = this._start;\n      this._right = this._end;\n    }\n  }\n  /** Whether the container is being pushed to the side by one of the drawers. */\n  _isPushed() {\n    return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n  }\n  _onBackdropClicked() {\n    this.backdropClick.emit();\n    this._closeModalDrawersViaBackdrop();\n  }\n  _closeModalDrawersViaBackdrop() {\n    // Close all open drawers where closing is not disabled and the mode is not `side`.\n    [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._canHaveBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n  }\n  _isShowingBackdrop() {\n    return this._isDrawerOpen(this._start) && this._canHaveBackdrop(this._start) || this._isDrawerOpen(this._end) && this._canHaveBackdrop(this._end);\n  }\n  _canHaveBackdrop(drawer) {\n    return drawer.mode !== 'side' || !!this._backdropOverride;\n  }\n  _isDrawerOpen(drawer) {\n    return drawer != null && drawer.opened;\n  }\n}\nMatDrawerContainer.ɵfac = function MatDrawerContainer_Factory(t) {\n  return new (t || MatDrawerContainer)(i0.ɵɵdirectiveInject(i4.Directionality, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MAT_DRAWER_DEFAULT_AUTOSIZE), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\nMatDrawerContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawerContainer,\n  selectors: [[\"mat-drawer-container\"]],\n  contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n    }\n  },\n  viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatDrawerContent, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-drawer-container\"],\n  hostVars: 2,\n  hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n    }\n  },\n  inputs: {\n    autosize: \"autosize\",\n    hasBackdrop: \"hasBackdrop\"\n  },\n  outputs: {\n    backdropClick: \"backdropClick\"\n  },\n  exportAs: [\"matDrawerContainer\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_DRAWER_CONTAINER,\n    useExisting: MatDrawerContainer\n  }])],\n  ngContentSelectors: _c3,\n  decls: 4,\n  vars: 2,\n  consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n  template: function MatDrawerContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵtemplate(0, MatDrawerContainer_div_0_Template, 1, 2, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵtemplate(3, MatDrawerContainer_mat_drawer_content_3_Template, 2, 0, \"mat-drawer-content\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx._content);\n    }\n  },\n  dependencies: [i5.NgIf, MatDrawerContent],\n  styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-container',\n      exportAs: 'matDrawerContainer',\n      host: {\n        'class': 'mat-drawer-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], function () {\n    return [{\n      type: i4.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatDrawer, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatDrawerContent]\n    }],\n    _userContent: [{\n      type: ViewChild,\n      args: [MatDrawerContent]\n    }],\n    autosize: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    backdropClick: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavContent extends MatDrawerContent {\n  constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n    super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n  }\n}\nMatSidenavContent.ɵfac = function MatSidenavContent_Factory(t) {\n  return new (t || MatSidenavContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatSidenavContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nMatSidenavContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenavContent,\n  selectors: [[\"mat-sidenav-content\"]],\n  hostAttrs: [1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n  hostVars: 4,\n  hostBindings: function MatSidenavContent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: MatSidenavContent\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatSidenavContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content mat-sidenav-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSidenavContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatSidenavContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass MatSidenav extends MatDrawer {\n  constructor() {\n    super(...arguments);\n    this._fixedInViewport = false;\n    this._fixedTopGap = 0;\n    this._fixedBottomGap = 0;\n  }\n  /** Whether the sidenav is fixed in the viewport. */\n  get fixedInViewport() {\n    return this._fixedInViewport;\n  }\n  set fixedInViewport(value) {\n    this._fixedInViewport = coerceBooleanProperty(value);\n  }\n  /**\n   * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n   * mode.\n   */\n  get fixedTopGap() {\n    return this._fixedTopGap;\n  }\n  set fixedTopGap(value) {\n    this._fixedTopGap = coerceNumberProperty(value);\n  }\n  /**\n   * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n   * fixed mode.\n   */\n  get fixedBottomGap() {\n    return this._fixedBottomGap;\n  }\n  set fixedBottomGap(value) {\n    this._fixedBottomGap = coerceNumberProperty(value);\n  }\n}\nMatSidenav.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSidenav_BaseFactory;\n  return function MatSidenav_Factory(t) {\n    return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(t || MatSidenav);\n  };\n}();\nMatSidenav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenav,\n  selectors: [[\"mat-sidenav\"]],\n  hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\", \"mat-sidenav\"],\n  hostVars: 17,\n  hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"align\", null);\n      i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n      i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened)(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n    }\n  },\n  inputs: {\n    fixedInViewport: \"fixedInViewport\",\n    fixedTopGap: \"fixedTopGap\",\n    fixedBottomGap: \"fixedBottomGap\"\n  },\n  exportAs: [\"matSidenav\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 0,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n  template: function MatSidenav_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n  },\n  dependencies: [i1.CdkScrollable],\n  encapsulation: 2,\n  data: {\n    animation: [matDrawerAnimations.transformDrawer]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenav, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav',\n      exportAs: 'matSidenav',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer mat-sidenav',\n        'tabIndex': '-1',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, {\n    fixedInViewport: [{\n      type: Input\n    }],\n    fixedTopGap: [{\n      type: Input\n    }],\n    fixedBottomGap: [{\n      type: Input\n    }]\n  });\n})();\nclass MatSidenavContainer extends MatDrawerContainer {}\nMatSidenavContainer.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSidenavContainer_BaseFactory;\n  return function MatSidenavContainer_Factory(t) {\n    return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(t || MatSidenavContainer);\n  };\n}();\nMatSidenavContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenavContainer,\n  selectors: [[\"mat-sidenav-container\"]],\n  contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n  hostVars: 2,\n  hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n    }\n  },\n  exportAs: [\"matSidenavContainer\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_DRAWER_CONTAINER,\n    useExisting: MatSidenavContainer\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c5,\n  decls: 4,\n  vars: 2,\n  consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n  template: function MatSidenavContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c4);\n      i0.ɵɵtemplate(0, MatSidenavContainer_div_0_Template, 1, 2, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵtemplate(3, MatSidenavContainer_mat_sidenav_content_3_Template, 2, 0, \"mat-sidenav-content\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx._content);\n    }\n  },\n  dependencies: [i5.NgIf, MatSidenavContent],\n  styles: [_c6],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-container',\n      exportAs: 'matSidenavContainer',\n      host: {\n        'class': 'mat-drawer-container mat-sidenav-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], null, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatSidenav, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatSidenavContent]\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavModule {}\nMatSidenavModule.ɵfac = function MatSidenavModule_Factory(t) {\n  return new (t || MatSidenavModule)();\n};\nMatSidenavModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSidenavModule\n});\nMatSidenavModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n      exports: [CdkScrollableModule, MatCommonModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent],\n      declarations: [MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };", "map": {"version": 3, "names": ["i1", "CdkScrollable", "CdkScrollableModule", "i5", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "EventEmitter", "Optional", "Input", "Output", "ViewChild", "QueryList", "ContentChildren", "ContentChild", "NgModule", "MatCommonModule", "i2", "i4", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i3", "Subject", "fromEvent", "merge", "filter", "map", "mapTo", "takeUntil", "distinctUntilChanged", "take", "startWith", "debounceTime", "trigger", "state", "style", "transition", "animate", "ANIMATION_MODULE_TYPE", "matDrawerAnimations", "transformDrawer", "throwMatDuplicatedDrawerError", "position", "Error", "MAT_DRAWER_DEFAULT_AUTOSIZE", "providedIn", "factory", "MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY", "MAT_DRAWER_CONTAINER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_changeDetectorRef", "_container", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "ngAfterContentInit", "_contentMarginChanges", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "ChangeDetectorRef", "Mat<PERSON>rawerContainer", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NgZone", "ɵcmp", "provide", "useExisting", "type", "args", "selector", "template", "host", "changeDetection", "OnPush", "encapsulation", "None", "providers", "decorators", "<PERSON><PERSON><PERSON><PERSON>", "_position", "value", "_isAttached", "_updatePositionInParent", "onPositionChanged", "emit", "mode", "_mode", "_updateFocusTrapState", "_modeChanged", "next", "disableClose", "_disableClose", "autoFocus", "_autoFocus", "opened", "_opened", "toggle", "_elementRef", "_focusTrapFactory", "_focusMonitor", "_platform", "_ngZone", "_interactivityC<PERSON>cker", "_doc", "_elementFocusedBeforeDrawerWasOpened", "_enableAnimations", "_animationStarted", "_animationEnd", "_animationState", "openedChange", "_openedStream", "pipe", "o", "openedStart", "e", "fromState", "toState", "indexOf", "undefined", "_closedStream", "closedStart", "_destroyed", "activeElement", "_takeFocus", "_isFocusWithinDrawer", "_restoreFocus", "_openedVia", "runOutsideAngular", "nativeElement", "event", "keyCode", "run", "close", "stopPropagation", "preventDefault", "x", "y", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "elementToFocus", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "hasMovedFocus", "<PERSON><PERSON><PERSON><PERSON>", "focusVia", "blur", "activeEl", "contains", "ngAfterViewInit", "create", "ngAfterContentChecked", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "destroy", "_anchor", "remove", "complete", "open", "openedVia", "_closeViaBackdropClick", "_setOpen", "isOpen", "result", "restoreFocus", "Promise", "resolve", "_getWidth", "offsetWidth", "enabled", "newPosition", "parent", "parentNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "FocusTrapFactory", "FocusMonitor", "Platform", "InteractivityChecker", "exportAs", "animations", "_content", "start", "_start", "end", "_end", "autosize", "_autosize", "hasBackdrop", "_backdropOverride", "scrollable", "_userContent", "_dir", "_element", "viewportRuler", "defaultAutosize", "_animationMode", "_drawers", "backdropClick", "_doCheckSubject", "_contentMargins", "left", "right", "change", "_validateDrawers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_allDrawers", "changes", "drawer", "reset", "item", "notifyOn<PERSON><PERSON>es", "for<PERSON>ach", "_watchDrawerToggle", "_watchDrawerPosition", "_watchDrawerMode", "length", "_isDrawerOpen", "_left", "width", "_right", "ngDoCheck", "_isPushed", "classList", "add", "_setContainerClass", "onMicrotaskEmpty", "isAdd", "className", "ngDevMode", "_onBackdropClicked", "_closeModalDrawersViaBackdrop", "_canHaveBackdrop", "_isShowingBackdrop", "Directionality", "ViewportRuler", "NgIf", "styles", "descendants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "container", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "_fixedInViewport", "_fixedTopGap", "_fixedBottomGap", "fixedInViewport", "fixedTopGap", "fixedBottomGap", "MatSidenavModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/sidenav.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nconst matDrawerAnimations = {\n    /** Animation that slides a drawer in and out. */\n    transformDrawer: trigger('transform', [\n        // We remove the `transform` here completely, rather than setting it to zero, because:\n        // 1. Having a transform can cause elements with ripples or an animated\n        //    transform to shift around in Chrome with an RTL layout (see #10023).\n        // 2. 3d transforms causes text to appear blurry on IE and Edge.\n        state('open, open-instant', style({\n            'transform': 'none',\n            'visibility': 'visible',\n        })),\n        state('void', style({\n            // Avoids the shadow showing up when closed in SSR.\n            'box-shadow': 'none',\n            'visibility': 'hidden',\n        })),\n        transition('void => open-instant', animate('0ms')),\n        transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')),\n    ]),\n};\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n    throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n    providedIn: 'root',\n    factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n    return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n    constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n        super(elementRef, scrollDispatcher, ngZone);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._container = _container;\n    }\n    ngAfterContentInit() {\n        this._container._contentMarginChanges.subscribe(() => {\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n}\nMatDrawerContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawerContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatDrawerContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawerContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatDrawerContent, selector: \"mat-drawer-content\", host: { properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: MatDrawerContent,\n        },\n    ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawerContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-drawer-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatDrawerContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatDrawerContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatDrawerContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n    /** The side that the drawer is attached to. */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        // Make sure we have a valid value.\n        value = value === 'end' ? 'end' : 'start';\n        if (value !== this._position) {\n            // Static inputs in Ivy are set before the element is in the DOM.\n            if (this._isAttached) {\n                this._updatePositionInParent(value);\n            }\n            this._position = value;\n            this.onPositionChanged.emit();\n        }\n    }\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        this._mode = value;\n        this._updateFocusTrapState();\n        this._modeChanged.next();\n    }\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n    get disableClose() {\n        return this._disableClose;\n    }\n    set disableClose(value) {\n        this._disableClose = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n    get autoFocus() {\n        const value = this._autoFocus;\n        // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n        // because we don't know how the sidenav is being used, but in some cases it still makes\n        // sense to do it. The consumer can explicitly set `autoFocus`.\n        if (value == null) {\n            if (this.mode === 'side') {\n                return 'dialog';\n            }\n            else {\n                return 'first-tabbable';\n            }\n        }\n        return value;\n    }\n    set autoFocus(value) {\n        if (value === 'true' || value === 'false' || value == null) {\n            value = coerceBooleanProperty(value);\n        }\n        this._autoFocus = value;\n    }\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n    get opened() {\n        return this._opened;\n    }\n    set opened(value) {\n        this.toggle(coerceBooleanProperty(value));\n    }\n    constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._focusMonitor = _focusMonitor;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._interactivityChecker = _interactivityChecker;\n        this._doc = _doc;\n        this._container = _container;\n        this._elementFocusedBeforeDrawerWasOpened = null;\n        /** Whether the drawer is initialized. Used for disabling the initial animation. */\n        this._enableAnimations = false;\n        this._position = 'start';\n        this._mode = 'over';\n        this._disableClose = false;\n        this._opened = false;\n        /** Emits whenever the drawer has started animating. */\n        this._animationStarted = new Subject();\n        /** Emits whenever the drawer is done animating. */\n        this._animationEnd = new Subject();\n        /** Current state of the sidenav animation. */\n        this._animationState = 'void';\n        /** Event emitted when the drawer open state is changed. */\n        this.openedChange = \n        // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n        new EventEmitter(/* isAsync */ true);\n        /** Event emitted when the drawer has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the drawer has started opening. */\n        this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n        /** Event emitted when the drawer has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the drawer has started closing. */\n        this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Event emitted when the drawer's position changes. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onPositionChanged = new EventEmitter();\n        /**\n         * An observable that emits when the drawer mode changes. This is used by the drawer container to\n         * to know when to when the mode changes so it can adapt the margins on the content.\n         */\n        this._modeChanged = new Subject();\n        this.openedChange.subscribe((opened) => {\n            if (opened) {\n                if (this._doc) {\n                    this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n                }\n                this._takeFocus();\n            }\n            else if (this._isFocusWithinDrawer()) {\n                this._restoreFocus(this._openedVia || 'program');\n            }\n        });\n        /**\n         * Listen to `keydown` events outside the zone so that change detection is not run every\n         * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n         * and we don't have close disabled.\n         */\n        this._ngZone.runOutsideAngular(() => {\n            fromEvent(this._elementRef.nativeElement, 'keydown')\n                .pipe(filter(event => {\n                return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n            }), takeUntil(this._destroyed))\n                .subscribe(event => this._ngZone.run(() => {\n                this.close();\n                event.stopPropagation();\n                event.preventDefault();\n            }));\n        });\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._animationEnd\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe((event) => {\n            const { fromState, toState } = event;\n            if ((toState.indexOf('open') === 0 && fromState === 'void') ||\n                (toState === 'void' && fromState.indexOf('open') === 0)) {\n                this.openedChange.emit(this._opened);\n            }\n        });\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n    _takeFocus() {\n        if (!this._focusTrap) {\n            return;\n        }\n        const element = this._elementRef.nativeElement;\n        // When autoFocus is not on the sidenav, if the element cannot be focused or does\n        // not exist, focus the sidenav itself so the keyboard navigation still works.\n        // We need to check that `focus` is a function due to Universal.\n        switch (this.autoFocus) {\n            case false:\n            case 'dialog':\n                return;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n                    if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n                        element.focus();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this.autoFocus);\n                break;\n        }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n    _restoreFocus(focusOrigin) {\n        if (this.autoFocus === 'dialog') {\n            return;\n        }\n        if (this._elementFocusedBeforeDrawerWasOpened) {\n            this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n        }\n        else {\n            this._elementRef.nativeElement.blur();\n        }\n        this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n    _isFocusWithinDrawer() {\n        const activeEl = this._doc.activeElement;\n        return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n    ngAfterViewInit() {\n        this._isAttached = true;\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        this._updateFocusTrapState();\n        // Only update the DOM position when the sidenav is positioned at\n        // the end since we project the sidenav before the content by default.\n        if (this._position === 'end') {\n            this._updatePositionInParent('end');\n        }\n    }\n    ngAfterContentChecked() {\n        // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n        // drawers that are open by default. When we're on the server, we shouldn't enable the\n        // animations, because we don't want the drawer to animate the first time the user sees\n        // the page.\n        if (this._platform.isBrowser) {\n            this._enableAnimations = true;\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n        this._anchor?.remove();\n        this._anchor = null;\n        this._animationStarted.complete();\n        this._animationEnd.complete();\n        this._modeChanged.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    open(openedVia) {\n        return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n    close() {\n        return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n    _closeViaBackdropClick() {\n        // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n        // don't need to check whether focus is currently in the drawer, as clicking on the\n        // backdrop causes blurs the active element.\n        return this._setOpen(/* isOpen */ false, /* restoreFocus */ true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    toggle(isOpen = !this.opened, openedVia) {\n        // If the focus is currently inside the drawer content and we are closing the drawer,\n        // restore the focus to the initially focused element (when the drawer opened).\n        if (isOpen && openedVia) {\n            this._openedVia = openedVia;\n        }\n        const result = this._setOpen(isOpen, \n        /* restoreFocus */ !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n        if (!isOpen) {\n            this._openedVia = null;\n        }\n        return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n        this._opened = isOpen;\n        if (isOpen) {\n            this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n        }\n        else {\n            this._animationState = 'void';\n            if (restoreFocus) {\n                this._restoreFocus(focusOrigin);\n            }\n        }\n        this._updateFocusTrapState();\n        return new Promise(resolve => {\n            this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n        });\n    }\n    _getWidth() {\n        return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n    _updateFocusTrapState() {\n        if (this._focusTrap) {\n            // The focus trap is only enabled when the drawer is open in any mode other than side.\n            this._focusTrap.enabled = this.opened && this.mode !== 'side';\n        }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n    _updatePositionInParent(newPosition) {\n        const element = this._elementRef.nativeElement;\n        const parent = element.parentNode;\n        if (newPosition === 'end') {\n            if (!this._anchor) {\n                this._anchor = this._doc.createComment('mat-drawer-anchor');\n                parent.insertBefore(this._anchor, element);\n            }\n            parent.appendChild(element);\n        }\n        else if (this._anchor) {\n            this._anchor.parentNode.insertBefore(element, this._anchor);\n        }\n    }\n}\nMatDrawer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawer, deps: [{ token: i0.ElementRef }, { token: i2.FocusTrapFactory }, { token: i2.FocusMonitor }, { token: i3.Platform }, { token: i0.NgZone }, { token: i2.InteractivityChecker }, { token: DOCUMENT, optional: true }, { token: MAT_DRAWER_CONTAINER, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatDrawer, selector: \"mat-drawer\", inputs: { position: \"position\", mode: \"mode\", disableClose: \"disableClose\", autoFocus: \"autoFocus\", opened: \"opened\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", openedStart: \"openedStart\", _closedStream: \"closed\", closedStart: \"closedStart\", onPositionChanged: \"positionChanged\" }, host: { attributes: { \"tabIndex\": \"-1\" }, listeners: { \"@transform.start\": \"_animationStarted.next($event)\", \"@transform.done\": \"_animationEnd.next($event)\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"@transform\": \"_animationState\" }, classAttribute: \"mat-drawer\" }, viewQueries: [{ propertyName: \"_content\", first: true, predicate: [\"content\"], descendants: true }], exportAs: [\"matDrawer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer', exportAs: 'matDrawer', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        'tabIndex': '-1',\n                        '[@transform]': '_animationState',\n                        '(@transform.start)': '_animationStarted.next($event)',\n                        '(@transform.done)': '_animationEnd.next($event)',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i2.FocusTrapFactory }, { type: i2.FocusMonitor }, { type: i3.Platform }, { type: i0.NgZone }, { type: i2.InteractivityChecker }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDrawerContainer, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DRAWER_CONTAINER]\n                }] }]; }, propDecorators: { position: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], disableClose: [{\n                type: Input\n            }], autoFocus: [{\n                type: Input\n            }], opened: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], openedStart: [{\n                type: Output\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], closedStart: [{\n                type: Output\n            }], onPositionChanged: [{\n                type: Output,\n                args: ['positionChanged']\n            }], _content: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n    /** The drawer child with the `start` position. */\n    get start() {\n        return this._start;\n    }\n    /** The drawer child with the `end` position. */\n    get end() {\n        return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n    get autosize() {\n        return this._autosize;\n    }\n    set autosize(value) {\n        this._autosize = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n    get hasBackdrop() {\n        if (this._backdropOverride == null) {\n            return !this._start || this._start.mode !== 'side' || !this._end || this._end.mode !== 'side';\n        }\n        return this._backdropOverride;\n    }\n    set hasBackdrop(value) {\n        this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n    get scrollable() {\n        return this._userContent || this._content;\n    }\n    constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n        this._dir = _dir;\n        this._element = _element;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** Drawers that belong to this container. */\n        this._drawers = new QueryList();\n        /** Event emitted when the drawer backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Emits on every ngDoCheck. Used for debouncing reflows. */\n        this._doCheckSubject = new Subject();\n        /**\n         * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n         * drawer is open. We use margin rather than transform even for push mode because transform breaks\n         * fixed position elements inside of the transformed element.\n         */\n        this._contentMargins = { left: null, right: null };\n        this._contentMarginChanges = new Subject();\n        // If a `Dir` directive exists up the tree, listen direction changes\n        // and update the left/right properties to point to the proper start/end.\n        if (_dir) {\n            _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                this._validateDrawers();\n                this.updateContentMargins();\n            });\n        }\n        // Since the minimum width of the sidenav depends on the viewport width,\n        // we need to recompute the margins if the viewport changes.\n        viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateContentMargins());\n        this._autosize = defaultAutosize;\n    }\n    ngAfterContentInit() {\n        this._allDrawers.changes\n            .pipe(startWith(this._allDrawers), takeUntil(this._destroyed))\n            .subscribe((drawer) => {\n            this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n            this._drawers.notifyOnChanges();\n        });\n        this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n            this._validateDrawers();\n            this._drawers.forEach((drawer) => {\n                this._watchDrawerToggle(drawer);\n                this._watchDrawerPosition(drawer);\n                this._watchDrawerMode(drawer);\n            });\n            if (!this._drawers.length ||\n                this._isDrawerOpen(this._start) ||\n                this._isDrawerOpen(this._end)) {\n                this.updateContentMargins();\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n        // Avoid hitting the NgZone through the debounce timeout.\n        this._ngZone.runOutsideAngular(() => {\n            this._doCheckSubject\n                .pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n            takeUntil(this._destroyed))\n                .subscribe(() => this.updateContentMargins());\n        });\n    }\n    ngOnDestroy() {\n        this._contentMarginChanges.complete();\n        this._doCheckSubject.complete();\n        this._drawers.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n    open() {\n        this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n    close() {\n        this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n    updateContentMargins() {\n        // 1. For drawers in `over` mode, they don't affect the content.\n        // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n        //    left margin (for left drawer) or right margin (for right the drawer).\n        // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n        //    adding to the left or right margin and simultaneously subtracting the same amount of\n        //    margin from the other side.\n        let left = 0;\n        let right = 0;\n        if (this._left && this._left.opened) {\n            if (this._left.mode == 'side') {\n                left += this._left._getWidth();\n            }\n            else if (this._left.mode == 'push') {\n                const width = this._left._getWidth();\n                left += width;\n                right -= width;\n            }\n        }\n        if (this._right && this._right.opened) {\n            if (this._right.mode == 'side') {\n                right += this._right._getWidth();\n            }\n            else if (this._right.mode == 'push') {\n                const width = this._right._getWidth();\n                right += width;\n                left -= width;\n            }\n        }\n        // If either `right` or `left` is zero, don't set a style to the element. This\n        // allows users to specify a custom size via CSS class in SSR scenarios where the\n        // measured widths will always be zero. Note that we reset to `null` here, rather\n        // than below, in order to ensure that the types in the `if` below are consistent.\n        left = left || null;\n        right = right || null;\n        if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n            this._contentMargins = { left, right };\n            // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n            // to do it only when something changed, otherwise we can end up hitting the zone too often.\n            this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n        }\n    }\n    ngDoCheck() {\n        // If users opted into autosizing, do a check every change detection cycle.\n        if (this._autosize && this._isPushed()) {\n            // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n            this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n        }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n    _watchDrawerToggle(drawer) {\n        drawer._animationStarted\n            .pipe(filter((event) => event.fromState !== event.toState), takeUntil(this._drawers.changes))\n            .subscribe((event) => {\n            // Set the transition class on the container so that the animations occur. This should not\n            // be set initially because animations should only be triggered via a change in state.\n            if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n                this._element.nativeElement.classList.add('mat-drawer-transition');\n            }\n            this.updateContentMargins();\n            this._changeDetectorRef.markForCheck();\n        });\n        if (drawer.mode !== 'side') {\n            drawer.openedChange\n                .pipe(takeUntil(this._drawers.changes))\n                .subscribe(() => this._setContainerClass(drawer.opened));\n        }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n    _watchDrawerPosition(drawer) {\n        if (!drawer) {\n            return;\n        }\n        // NOTE: We need to wait for the microtask queue to be empty before validating,\n        // since both drawers may be swapping positions at the same time.\n        drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n            this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n                this._validateDrawers();\n            });\n        });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n    _watchDrawerMode(drawer) {\n        if (drawer) {\n            drawer._modeChanged\n                .pipe(takeUntil(merge(this._drawers.changes, this._destroyed)))\n                .subscribe(() => {\n                this.updateContentMargins();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n    _setContainerClass(isAdd) {\n        const classList = this._element.nativeElement.classList;\n        const className = 'mat-drawer-container-has-open';\n        if (isAdd) {\n            classList.add(className);\n        }\n        else {\n            classList.remove(className);\n        }\n    }\n    /** Validate the state of the drawer children components. */\n    _validateDrawers() {\n        this._start = this._end = null;\n        // Ensure that we have at most one start and one end drawer.\n        this._drawers.forEach(drawer => {\n            if (drawer.position == 'end') {\n                if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('end');\n                }\n                this._end = drawer;\n            }\n            else {\n                if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('start');\n                }\n                this._start = drawer;\n            }\n        });\n        this._right = this._left = null;\n        // Detect if we're LTR or RTL.\n        if (this._dir && this._dir.value === 'rtl') {\n            this._left = this._end;\n            this._right = this._start;\n        }\n        else {\n            this._left = this._start;\n            this._right = this._end;\n        }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n    _isPushed() {\n        return ((this._isDrawerOpen(this._start) && this._start.mode != 'over') ||\n            (this._isDrawerOpen(this._end) && this._end.mode != 'over'));\n    }\n    _onBackdropClicked() {\n        this.backdropClick.emit();\n        this._closeModalDrawersViaBackdrop();\n    }\n    _closeModalDrawersViaBackdrop() {\n        // Close all open drawers where closing is not disabled and the mode is not `side`.\n        [this._start, this._end]\n            .filter(drawer => drawer && !drawer.disableClose && this._canHaveBackdrop(drawer))\n            .forEach(drawer => drawer._closeViaBackdropClick());\n    }\n    _isShowingBackdrop() {\n        return ((this._isDrawerOpen(this._start) && this._canHaveBackdrop(this._start)) ||\n            (this._isDrawerOpen(this._end) && this._canHaveBackdrop(this._end)));\n    }\n    _canHaveBackdrop(drawer) {\n        return drawer.mode !== 'side' || !!this._backdropOverride;\n    }\n    _isDrawerOpen(drawer) {\n        return drawer != null && drawer.opened;\n    }\n}\nMatDrawerContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawerContainer, deps: [{ token: i4.Directionality, optional: true }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: MAT_DRAWER_DEFAULT_AUTOSIZE }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawerContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatDrawerContainer, selector: \"mat-drawer-container\", inputs: { autosize: \"autosize\", hasBackdrop: \"hasBackdrop\" }, outputs: { backdropClick: \"backdropClick\" }, host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container\" }, providers: [\n        {\n            provide: MAT_DRAWER_CONTAINER,\n            useExisting: MatDrawerContainer,\n        },\n    ], queries: [{ propertyName: \"_content\", first: true, predicate: MatDrawerContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatDrawer, descendants: true }], viewQueries: [{ propertyName: \"_userContent\", first: true, predicate: MatDrawerContent, descendants: true }], exportAs: [\"matDrawerContainer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatDrawerContent, selector: \"mat-drawer-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatDrawerContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer-container', exportAs: 'matDrawerContainer', host: {\n                        'class': 'mat-drawer-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatDrawerContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], ctorParameters: function () { return [{ type: i4.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatDrawer, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatDrawerContent]\n            }], _userContent: [{\n                type: ViewChild,\n                args: [MatDrawerContent]\n            }], autosize: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], backdropClick: [{\n                type: Output\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavContent extends MatDrawerContent {\n    constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n        super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n    }\n}\nMatSidenavContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatSidenavContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nMatSidenavContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSidenavContent, selector: \"mat-sidenav-content\", host: { properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content mat-sidenav-content\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: MatSidenavContent,\n        },\n    ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-sidenav-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content mat-sidenav-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatSidenavContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatSidenavContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatSidenavContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\nclass MatSidenav extends MatDrawer {\n    constructor() {\n        super(...arguments);\n        this._fixedInViewport = false;\n        this._fixedTopGap = 0;\n        this._fixedBottomGap = 0;\n    }\n    /** Whether the sidenav is fixed in the viewport. */\n    get fixedInViewport() {\n        return this._fixedInViewport;\n    }\n    set fixedInViewport(value) {\n        this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n    get fixedTopGap() {\n        return this._fixedTopGap;\n    }\n    set fixedTopGap(value) {\n        this._fixedTopGap = coerceNumberProperty(value);\n    }\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n    get fixedBottomGap() {\n        return this._fixedBottomGap;\n    }\n    set fixedBottomGap(value) {\n        this._fixedBottomGap = coerceNumberProperty(value);\n    }\n}\nMatSidenav.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenav, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSidenav.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSidenav, selector: \"mat-sidenav\", inputs: { fixedInViewport: \"fixedInViewport\", fixedTopGap: \"fixedTopGap\", fixedBottomGap: \"fixedBottomGap\" }, host: { attributes: { \"tabIndex\": \"-1\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"class.mat-sidenav-fixed\": \"fixedInViewport\", \"style.top.px\": \"fixedInViewport ? fixedTopGap : null\", \"style.bottom.px\": \"fixedInViewport ? fixedBottomGap : null\" }, classAttribute: \"mat-drawer mat-sidenav\" }, exportAs: [\"matSidenav\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenav, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav', exportAs: 'matSidenav', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer mat-sidenav',\n                        'tabIndex': '-1',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n                        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n                        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], propDecorators: { fixedInViewport: [{\n                type: Input\n            }], fixedTopGap: [{\n                type: Input\n            }], fixedBottomGap: [{\n                type: Input\n            }] } });\nclass MatSidenavContainer extends MatDrawerContainer {\n}\nMatSidenavContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSidenavContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSidenavContainer, selector: \"mat-sidenav-container\", host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container mat-sidenav-container\" }, providers: [\n        {\n            provide: MAT_DRAWER_CONTAINER,\n            useExisting: MatSidenavContainer,\n        },\n    ], queries: [{ propertyName: \"_content\", first: true, predicate: MatSidenavContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatSidenav, descendants: true }], exportAs: [\"matSidenavContainer\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatSidenavContent, selector: \"mat-sidenav-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav-container', exportAs: 'matSidenavContainer', host: {\n                        'class': 'mat-drawer-container mat-sidenav-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatSidenavContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatSidenav, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatSidenavContent]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavModule {\n}\nMatSidenavModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSidenavModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavModule, declarations: [MatDrawer,\n        MatDrawerContainer,\n        MatDrawerContent,\n        MatSidenav,\n        MatSidenavContainer,\n        MatSidenavContent], imports: [CommonModule, MatCommonModule, CdkScrollableModule], exports: [CdkScrollableModule,\n        MatCommonModule,\n        MatDrawer,\n        MatDrawerContainer,\n        MatDrawerContent,\n        MatSidenav,\n        MatSidenavContainer,\n        MatSidenavContent] });\nMatSidenavModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavModule, imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSidenavModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatCommonModule,\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                    declarations: [\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC/N,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,OAAO,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AAChD,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,IAAI,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AACnH,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,qBAAqB,QAAQ,sCAAsC;;AAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAAA;AAAA;EAAA;IAAA,YA0DwGxC,EAAE;IAAFA,EAAE,4BAmvBwY;IAnvB1YA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAmvBmS,2BAAoB;IAAA,EAAE;IAnvB3TA,EAAE,eAmvB8Y;EAAA;EAAA;IAAA,eAnvBhZA,EAAE;IAAFA,EAAE,6DAmvBuY;EAAA;AAAA;AAAA;EAAA;IAnvBzYA,EAAE,wCAmvBwiB;IAnvB1iBA,EAAE,mBAmvBqkB;IAnvBvkBA,EAAE,eAmvB4lB;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA,YAnvB9lBA,EAAE;IAAFA,EAAE,4BA44BoT;IA54BtTA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aA44B+M,2BAAoB;IAAA,EAAE;IA54BvOA,EAAE,eA44B0T;EAAA;EAAA;IAAA,eA54B5TA,EAAE;IAAFA,EAAE,6DA44BmT;EAAA;AAAA;AAAA;EAAA;IA54BrTA,EAAE,yCA44Bud;IA54BzdA,EAAE,mBA44Bof;IA54BtfA,EAAE,eA44B4gB;EAAA;AAAA;AAAA;AAAA;AAAA;AAl8BtnB,MAAMyC,mBAAmB,GAAG;EACxB;EACAC,eAAe,EAAEP,OAAO,CAAC,WAAW,EAAE;EAClC;EACA;EACA;EACA;EACAC,KAAK,CAAC,oBAAoB,EAAEC,KAAK,CAAC;IAC9B,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE;EAClB,CAAC,CAAC,CAAC,EACHD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChB;IACA,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAClB,CAAC,CAAC,CAAC,EACHC,UAAU,CAAC,sBAAsB,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,EAClDD,UAAU,CAAC,qCAAqC,EAAEC,OAAO,CAAC,wCAAwC,CAAC,CAAC,CACvG;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASI,6BAA6B,CAACC,QAAQ,EAAE;EAC7C,MAAMC,KAAK,CAAE,gDAA+CD,QAAS,IAAG,CAAC;AAC7E;AACA;AACA,MAAME,2BAA2B,GAAG,IAAI7C,cAAc,CAAC,6BAA6B,EAAE;EAClF8C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,IAAIjD,cAAc,CAAC,sBAAsB,CAAC;AACvE;AACA,SAASgD,mCAAmC,GAAG;EAC3C,OAAO,KAAK;AAChB;AACA,MAAME,gBAAgB,SAASxD,aAAa,CAAC;EACzCyD,WAAW,CAACC,kBAAkB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;IAC9E,KAAK,CAACF,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;IAC3C,IAAI,CAACJ,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAI,kBAAkB,GAAG;IACjB,IAAI,CAACJ,UAAU,CAACK,qBAAqB,CAACC,SAAS,CAAC,MAAM;MAClD,IAAI,CAACP,kBAAkB,CAACQ,YAAY,EAAE;IAC1C,CAAC,CAAC;EACN;AACJ;AACAV,gBAAgB,CAACW,IAAI;EAAA,iBAA6FX,gBAAgB,EAA1BnD,EAAE,mBAA0CA,EAAE,CAAC+D,iBAAiB,GAAhE/D,EAAE,mBAA2EE,UAAU,CAAC,MAAM8D,kBAAkB,CAAC,GAAjHhE,EAAE,mBAA4HA,EAAE,CAACiE,UAAU,GAA3IjE,EAAE,mBAAsJN,EAAE,CAACwE,gBAAgB,GAA3KlE,EAAE,mBAAsLA,EAAE,CAACmE,MAAM;AAAA,CAA4C;AACrVhB,gBAAgB,CAACiB,IAAI,kBADmFpE,EAAE;EAAA,MACJmD,gBAAgB;EAAA;EAAA;EAAA;EAAA;IAAA;MADdnD,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE,oBACiP,CACnV;IACIqE,OAAO,EAAE1E,aAAa;IACtB2E,WAAW,EAAEnB;EACjB,CAAC,CACJ,GANmGnD,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAM1B;IAAA;EAAA;EAAA;EAAA;AAAA,EAAkH;AAClM;EAAA,mDAPwGA,EAAE,mBAOVmD,gBAAgB,EAAc,CAAC;IACnHoB,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE,2BAA2B;MACrCC,IAAI,EAAE;QACF,OAAO,EAAE,oBAAoB;QAC7B,wBAAwB,EAAE,iCAAiC;QAC3D,yBAAyB,EAAE;MAC/B,CAAC;MACDC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAC/CC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MACrCC,SAAS,EAAE,CACP;QACIX,OAAO,EAAE1E,aAAa;QACtB2E,WAAW,EAAEnB;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoB,IAAI,EAAEvE,EAAE,CAAC+D;IAAkB,CAAC,EAAE;MAAEQ,IAAI,EAAEP,kBAAkB;MAAEiB,UAAU,EAAE,CAAC;QACvGV,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAACtE,UAAU,CAAC,MAAM8D,kBAAkB,CAAC;MAC/C,CAAC;IAAE,CAAC,EAAE;MAAEO,IAAI,EAAEvE,EAAE,CAACiE;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAE7E,EAAE,CAACwE;IAAiB,CAAC,EAAE;MAAEK,IAAI,EAAEvE,EAAE,CAACmE;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG;AACA;AACA;AACA,MAAMe,SAAS,CAAC;EACZ;EACA,IAAItC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACuC,SAAS;EACzB;EACA,IAAIvC,QAAQ,CAACwC,KAAK,EAAE;IAChB;IACAA,KAAK,GAAGA,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;IACzC,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;MAC1B;MACA,IAAI,IAAI,CAACE,WAAW,EAAE;QAClB,IAAI,CAACC,uBAAuB,CAACF,KAAK,CAAC;MACvC;MACA,IAAI,CAACD,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACG,iBAAiB,CAACC,IAAI,EAAE;IACjC;EACJ;EACA;EACA,IAAIC,IAAI,GAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAI,CAACL,KAAK,EAAE;IACZ,IAAI,CAACM,KAAK,GAAGN,KAAK;IAClB,IAAI,CAACO,qBAAqB,EAAE;IAC5B,IAAI,CAACC,YAAY,CAACC,IAAI,EAAE;EAC5B;EACA;EACA,IAAIC,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAACV,KAAK,EAAE;IACpB,IAAI,CAACW,aAAa,GAAG5E,qBAAqB,CAACiE,KAAK,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIY,SAAS,GAAG;IACZ,MAAMZ,KAAK,GAAG,IAAI,CAACa,UAAU;IAC7B;IACA;IACA;IACA,IAAIb,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACK,IAAI,KAAK,MAAM,EAAE;QACtB,OAAO,QAAQ;MACnB,CAAC,MACI;QACD,OAAO,gBAAgB;MAC3B;IACJ;IACA,OAAOL,KAAK;EAChB;EACA,IAAIY,SAAS,CAACZ,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,IAAI,IAAI,EAAE;MACxDA,KAAK,GAAGjE,qBAAqB,CAACiE,KAAK,CAAC;IACxC;IACA,IAAI,CAACa,UAAU,GAAGb,KAAK;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIc,MAAM,GAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAM,CAACd,KAAK,EAAE;IACd,IAAI,CAACgB,MAAM,CAACjF,qBAAqB,CAACiE,KAAK,CAAC,CAAC;EAC7C;EACAhC,WAAW,CAACiD,WAAW,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,IAAI,EAAErD,UAAU,EAAE;IACpH,IAAI,CAAC+C,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACrD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACsD,oCAAoC,GAAG,IAAI;IAChD;IACA,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC1B,SAAS,GAAG,OAAO;IACxB,IAAI,CAACO,KAAK,GAAG,MAAM;IACnB,IAAI,CAACK,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB;IACA,IAAI,CAACW,iBAAiB,GAAG,IAAItF,OAAO,EAAE;IACtC;IACA,IAAI,CAACuF,aAAa,GAAG,IAAIvF,OAAO,EAAE;IAClC;IACA,IAAI,CAACwF,eAAe,GAAG,MAAM;IAC7B;IACA,IAAI,CAACC,YAAY;IACjB;IACA,IAAI1G,YAAY,EAAC,aAAc,IAAI,CAAC;IACpC;IACA,IAAI,CAAC2G,aAAa,GAAG,IAAI,CAACD,YAAY,CAACE,IAAI,CAACxF,MAAM,CAACyF,CAAC,IAAIA,CAAC,CAAC,EAAExF,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAACyF,WAAW,GAAG,IAAI,CAACP,iBAAiB,CAACK,IAAI,CAACxF,MAAM,CAAC2F,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKD,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE5F,KAAK,CAAC6F,SAAS,CAAC,CAAC;IAC3I;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACV,YAAY,CAACE,IAAI,CAACxF,MAAM,CAACyF,CAAC,IAAI,CAACA,CAAC,CAAC,EAAExF,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAACgG,WAAW,GAAG,IAAI,CAACd,iBAAiB,CAACK,IAAI,CAACxF,MAAM,CAAC2F,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKD,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,KAAK,MAAM,CAAC,EAAE3F,KAAK,CAAC6F,SAAS,CAAC,CAAC;IAChI;IACA,IAAI,CAACG,UAAU,GAAG,IAAIrG,OAAO,EAAE;IAC/B;IACA;IACA,IAAI,CAAC+D,iBAAiB,GAAG,IAAIhF,YAAY,EAAE;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAACqF,YAAY,GAAG,IAAIpE,OAAO,EAAE;IACjC,IAAI,CAACyF,YAAY,CAACrD,SAAS,CAAEsC,MAAM,IAAK;MACpC,IAAIA,MAAM,EAAE;QACR,IAAI,IAAI,CAACS,IAAI,EAAE;UACX,IAAI,CAACC,oCAAoC,GAAG,IAAI,CAACD,IAAI,CAACmB,aAAa;QACvE;QACA,IAAI,CAACC,UAAU,EAAE;MACrB,CAAC,MACI,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;QAClC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,UAAU,IAAI,SAAS,CAAC;MACpD;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACzB,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;MACjC1G,SAAS,CAAC,IAAI,CAAC4E,WAAW,CAAC+B,aAAa,EAAE,SAAS,CAAC,CAC/CjB,IAAI,CAACxF,MAAM,CAAC0G,KAAK,IAAI;QACtB,OAAOA,KAAK,CAACC,OAAO,KAAKjH,MAAM,IAAI,CAAC,IAAI,CAACyE,YAAY,IAAI,CAACxE,cAAc,CAAC+G,KAAK,CAAC;MACnF,CAAC,CAAC,EAAEvG,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CAC1BjE,SAAS,CAACyE,KAAK,IAAI,IAAI,CAAC5B,OAAO,CAAC8B,GAAG,CAAC,MAAM;QAC3C,IAAI,CAACC,KAAK,EAAE;QACZH,KAAK,CAACI,eAAe,EAAE;QACvBJ,KAAK,CAACK,cAAc,EAAE;MAC1B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC3B,aAAa,CACbI,IAAI,CAACpF,oBAAoB,CAAC,CAAC4G,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACpB,SAAS,KAAKqB,CAAC,CAACrB,SAAS,IAAIoB,CAAC,CAACnB,OAAO,KAAKoB,CAAC,CAACpB,OAAO;IACjE,CAAC,CAAC,CAAC,CACE5D,SAAS,CAAEyE,KAAK,IAAK;MACtB,MAAM;QAAEd,SAAS;QAAEC;MAAQ,CAAC,GAAGa,KAAK;MACpC,IAAKb,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAIF,SAAS,KAAK,MAAM,IACrDC,OAAO,KAAK,MAAM,IAAID,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAE,EAAE;QACzD,IAAI,CAACR,YAAY,CAACzB,IAAI,CAAC,IAAI,CAACW,OAAO,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI0C,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACrC,qBAAqB,CAACsC,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAACxC,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;QACjC,MAAMe,QAAQ,GAAG,MAAM;UACnBJ,OAAO,CAACK,mBAAmB,CAAC,MAAM,EAAED,QAAQ,CAAC;UAC7CJ,OAAO,CAACK,mBAAmB,CAAC,WAAW,EAAED,QAAQ,CAAC;UAClDJ,OAAO,CAACM,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACDN,OAAO,CAACO,gBAAgB,CAAC,MAAM,EAAEH,QAAQ,CAAC;QAC1CJ,OAAO,CAACO,gBAAgB,CAAC,WAAW,EAAEH,QAAQ,CAAC;MACnD,CAAC,CAAC;IACN;IACAJ,OAAO,CAACQ,KAAK,CAACP,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIQ,mBAAmB,CAAC9E,QAAQ,EAAEsE,OAAO,EAAE;IACnC,IAAIS,cAAc,GAAG,IAAI,CAACnD,WAAW,CAAC+B,aAAa,CAACqB,aAAa,CAAChF,QAAQ,CAAC;IAC3E,IAAI+E,cAAc,EAAE;MAChB,IAAI,CAACX,WAAW,CAACW,cAAc,EAAET,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIhB,UAAU,GAAG;IACT,IAAI,CAAC,IAAI,CAAC2B,UAAU,EAAE;MAClB;IACJ;IACA,MAAMZ,OAAO,GAAG,IAAI,CAACzC,WAAW,CAAC+B,aAAa;IAC9C;IACA;IACA;IACA,QAAQ,IAAI,CAACpC,SAAS;MAClB,KAAK,KAAK;MACV,KAAK,QAAQ;QACT;MACJ,KAAK,IAAI;MACT,KAAK,gBAAgB;QACjB,IAAI,CAAC0D,UAAU,CAACC,4BAA4B,EAAE,CAACC,IAAI,CAACC,aAAa,IAAI;UACjE,IAAI,CAACA,aAAa,IAAI,OAAO,IAAI,CAACxD,WAAW,CAAC+B,aAAa,CAACkB,KAAK,KAAK,UAAU,EAAE;YAC9ER,OAAO,CAACQ,KAAK,EAAE;UACnB;QACJ,CAAC,CAAC;QACF;MACJ,KAAK,eAAe;QAChB,IAAI,CAACC,mBAAmB,CAAC,0CAA0C,CAAC;QACpE;MACJ;QACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAACvD,SAAS,CAAC;QACxC;IAAM;EAElB;EACA;AACJ;AACA;AACA;EACIiC,aAAa,CAAC6B,WAAW,EAAE;IACvB,IAAI,IAAI,CAAC9D,SAAS,KAAK,QAAQ,EAAE;MAC7B;IACJ;IACA,IAAI,IAAI,CAACY,oCAAoC,EAAE;MAC3C,IAAI,CAACL,aAAa,CAACwD,QAAQ,CAAC,IAAI,CAACnD,oCAAoC,EAAEkD,WAAW,CAAC;IACvF,CAAC,MACI;MACD,IAAI,CAACzD,WAAW,CAAC+B,aAAa,CAAC4B,IAAI,EAAE;IACzC;IACA,IAAI,CAACpD,oCAAoC,GAAG,IAAI;EACpD;EACA;EACAoB,oBAAoB,GAAG;IACnB,MAAMiC,QAAQ,GAAG,IAAI,CAACtD,IAAI,CAACmB,aAAa;IACxC,OAAO,CAAC,CAACmC,QAAQ,IAAI,IAAI,CAAC5D,WAAW,CAAC+B,aAAa,CAAC8B,QAAQ,CAACD,QAAQ,CAAC;EAC1E;EACAE,eAAe,GAAG;IACd,IAAI,CAAC9E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACqE,UAAU,GAAG,IAAI,CAACpD,iBAAiB,CAAC8D,MAAM,CAAC,IAAI,CAAC/D,WAAW,CAAC+B,aAAa,CAAC;IAC/E,IAAI,CAACzC,qBAAqB,EAAE;IAC5B;IACA;IACA,IAAI,IAAI,CAACR,SAAS,KAAK,KAAK,EAAE;MAC1B,IAAI,CAACG,uBAAuB,CAAC,KAAK,CAAC;IACvC;EACJ;EACA+E,qBAAqB,GAAG;IACpB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC7D,SAAS,CAAC8D,SAAS,EAAE;MAC1B,IAAI,CAACzD,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACA0D,WAAW,GAAG;IACV,IAAI,IAAI,CAACb,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACc,OAAO,EAAE;IAC7B;IACA,IAAI,CAACC,OAAO,EAAEC,MAAM,EAAE;IACtB,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC3D,iBAAiB,CAAC6D,QAAQ,EAAE;IACjC,IAAI,CAAC5D,aAAa,CAAC4D,QAAQ,EAAE;IAC7B,IAAI,CAAC/E,YAAY,CAAC+E,QAAQ,EAAE;IAC5B,IAAI,CAAC9C,UAAU,CAAChC,IAAI,EAAE;IACtB,IAAI,CAACgC,UAAU,CAAC8C,QAAQ,EAAE;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAI,CAACC,SAAS,EAAE;IACZ,OAAO,IAAI,CAACzE,MAAM,CAAC,IAAI,EAAEyE,SAAS,CAAC;EACvC;EACA;EACArC,KAAK,GAAG;IACJ,OAAO,IAAI,CAACpC,MAAM,CAAC,KAAK,CAAC;EAC7B;EACA;EACA0E,sBAAsB,GAAG;IACrB;IACA;IACA;IACA,OAAO,IAAI,CAACC,QAAQ,EAAC,YAAa,KAAK,EAAE,kBAAmB,IAAI,EAAE,OAAO,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3E,MAAM,CAAC4E,MAAM,GAAG,CAAC,IAAI,CAAC9E,MAAM,EAAE2E,SAAS,EAAE;IACrC;IACA;IACA,IAAIG,MAAM,IAAIH,SAAS,EAAE;MACrB,IAAI,CAAC3C,UAAU,GAAG2C,SAAS;IAC/B;IACA,MAAMI,MAAM,GAAG,IAAI,CAACF,QAAQ,CAACC,MAAM,EACnC,kBAAmB,CAACA,MAAM,IAAI,IAAI,CAAChD,oBAAoB,EAAE,EAAE,IAAI,CAACE,UAAU,IAAI,SAAS,CAAC;IACxF,IAAI,CAAC8C,MAAM,EAAE;MACT,IAAI,CAAC9C,UAAU,GAAG,IAAI;IAC1B;IACA,OAAO+C,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,QAAQ,CAACC,MAAM,EAAEE,YAAY,EAAEpB,WAAW,EAAE;IACxC,IAAI,CAAC3D,OAAO,GAAG6E,MAAM;IACrB,IAAIA,MAAM,EAAE;MACR,IAAI,CAAChE,eAAe,GAAG,IAAI,CAACH,iBAAiB,GAAG,MAAM,GAAG,cAAc;IAC3E,CAAC,MACI;MACD,IAAI,CAACG,eAAe,GAAG,MAAM;MAC7B,IAAIkE,YAAY,EAAE;QACd,IAAI,CAACjD,aAAa,CAAC6B,WAAW,CAAC;MACnC;IACJ;IACA,IAAI,CAACnE,qBAAqB,EAAE;IAC5B,OAAO,IAAIwF,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACnE,YAAY,CAACE,IAAI,CAACnF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4B,SAAS,CAACgH,IAAI,IAAIQ,OAAO,CAACR,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;IACvF,CAAC,CAAC;EACN;EACAS,SAAS,GAAG;IACR,OAAO,IAAI,CAAChF,WAAW,CAAC+B,aAAa,GAAG,IAAI,CAAC/B,WAAW,CAAC+B,aAAa,CAACkD,WAAW,IAAI,CAAC,GAAG,CAAC;EAC/F;EACA;EACA3F,qBAAqB,GAAG;IACpB,IAAI,IAAI,CAAC+D,UAAU,EAAE;MACjB;MACA,IAAI,CAACA,UAAU,CAAC6B,OAAO,GAAG,IAAI,CAACrF,MAAM,IAAI,IAAI,CAACT,IAAI,KAAK,MAAM;IACjE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIH,uBAAuB,CAACkG,WAAW,EAAE;IACjC,MAAM1C,OAAO,GAAG,IAAI,CAACzC,WAAW,CAAC+B,aAAa;IAC9C,MAAMqD,MAAM,GAAG3C,OAAO,CAAC4C,UAAU;IACjC,IAAIF,WAAW,KAAK,KAAK,EAAE;MACvB,IAAI,CAAC,IAAI,CAACf,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC9D,IAAI,CAACgF,aAAa,CAAC,mBAAmB,CAAC;QAC3DF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACnB,OAAO,EAAE3B,OAAO,CAAC;MAC9C;MACA2C,MAAM,CAACI,WAAW,CAAC/C,OAAO,CAAC;IAC/B,CAAC,MACI,IAAI,IAAI,CAAC2B,OAAO,EAAE;MACnB,IAAI,CAACA,OAAO,CAACiB,UAAU,CAACE,YAAY,CAAC9C,OAAO,EAAE,IAAI,CAAC2B,OAAO,CAAC;IAC/D;EACJ;AACJ;AACAvF,SAAS,CAACpB,IAAI;EAAA,iBAA6FoB,SAAS,EA3YZlF,EAAE,mBA2Y4BA,EAAE,CAACiE,UAAU,GA3Y3CjE,EAAE,mBA2YsDiB,EAAE,CAAC6K,gBAAgB,GA3Y3E9L,EAAE,mBA2YsFiB,EAAE,CAAC8K,YAAY,GA3YvG/L,EAAE,mBA2YkHuB,EAAE,CAACyK,QAAQ,GA3Y/HhM,EAAE,mBA2Y0IA,EAAE,CAACmE,MAAM,GA3YrJnE,EAAE,mBA2YgKiB,EAAE,CAACgL,oBAAoB,GA3YzLjM,EAAE,mBA2YoMF,QAAQ,MA3Y9ME,EAAE,mBA2YyOkD,oBAAoB;AAAA,CAA4D;AACnagC,SAAS,CAACd,IAAI,kBA5Y0FpE,EAAE;EAAA,MA4YXkF,SAAS;EAAA;EAAA;IAAA;MA5YAlF,EAAE;IAAA;IAAA;MAAA;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA,wBA4Y6W,IAAI;EAAA;EAAA;IAAA;MA5YnXA,EAAE;QAAA,OA4YX,kCAA8B;MAAA;QAAA,OAA9B,8BAA0B;MAAA;IAAA;IAAA;MA5YjBA,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,+BA4YygC;MA5Y3gCA,EAAE,gBA4YwiC;MA5Y1iCA,EAAE,eA4YkjC;IAAA;EAAA;EAAA,eAAiDN,EAAE,CAACC,aAAa;EAAA;EAAA;IAAA,WAAgE,CAAC8C,mBAAmB,CAACC,eAAe;EAAC;EAAA;AAAA,EAAiG;AACn6C;EAAA,mDA7YwG1C,EAAE,mBA6YVkF,SAAS,EAAc,CAAC;IAC5GX,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEyH,QAAQ,EAAE,WAAW;MAAEC,UAAU,EAAE,CAAC1J,mBAAmB,CAACC,eAAe,CAAC;MAAEiC,IAAI,EAAE;QACrG,OAAO,EAAE,YAAY;QACrB;QACA,cAAc,EAAE,MAAM;QACtB,wBAAwB,EAAE,oBAAoB;QAC9C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,2BAA2B,EAAE,QAAQ;QACrC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,iBAAiB;QACjC,oBAAoB,EAAE,gCAAgC;QACtD,mBAAmB,EAAE;MACzB,CAAC;MAAEC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAAEC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MAAEL,QAAQ,EAAE;IAAiH,CAAC;EACnO,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEvE,EAAE,CAACiE;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAEtD,EAAE,CAAC6K;IAAiB,CAAC,EAAE;MAAEvH,IAAI,EAAEtD,EAAE,CAAC8K;IAAa,CAAC,EAAE;MAAExH,IAAI,EAAEhD,EAAE,CAACyK;IAAS,CAAC,EAAE;MAAEzH,IAAI,EAAEvE,EAAE,CAACmE;IAAO,CAAC,EAAE;MAAEI,IAAI,EAAEtD,EAAE,CAACgL;IAAqB,CAAC,EAAE;MAAE1H,IAAI,EAAEmD,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAChOV,IAAI,EAAE/D;MACV,CAAC,EAAE;QACC+D,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAAC1E,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEyE,IAAI,EAAEP,kBAAkB;MAAEiB,UAAU,EAAE,CAAC;QAC3CV,IAAI,EAAE/D;MACV,CAAC,EAAE;QACC+D,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAACtB,oBAAoB;MAC/B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEN,QAAQ,EAAE,CAAC;MACvC2B,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEgF,IAAI,EAAE,CAAC;MACPlB,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEqF,YAAY,EAAE,CAAC;MACfvB,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEuF,SAAS,EAAE,CAAC;MACZzB,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEyF,MAAM,EAAE,CAAC;MACT3B,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEwG,YAAY,EAAE,CAAC;MACf1C,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEwG,aAAa,EAAE,CAAC;MAChB3C,IAAI,EAAE7D,MAAM;MACZ8D,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE6C,WAAW,EAAE,CAAC;MACd9C,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEiH,aAAa,EAAE,CAAC;MAChBpD,IAAI,EAAE7D,MAAM;MACZ8D,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEoD,WAAW,EAAE,CAAC;MACdrD,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE6E,iBAAiB,EAAE,CAAC;MACpBhB,IAAI,EAAE7D,MAAM;MACZ8D,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE4H,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAE5D,SAAS;MACf6D,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMR,kBAAkB,CAAC;EACrB;EACA,IAAIqI,KAAK,GAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA;EACA,IAAIC,GAAG,GAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,QAAQ,GAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQ,CAACrH,KAAK,EAAE;IAChB,IAAI,CAACsH,SAAS,GAAGvL,qBAAqB,CAACiE,KAAK,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIuH,WAAW,GAAG;IACd,IAAI,IAAI,CAACC,iBAAiB,IAAI,IAAI,EAAE;MAChC,OAAO,CAAC,IAAI,CAACN,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC7G,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC+G,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC/G,IAAI,KAAK,MAAM;IACjG;IACA,OAAO,IAAI,CAACmH,iBAAiB;EACjC;EACA,IAAID,WAAW,CAACvH,KAAK,EAAE;IACnB,IAAI,CAACwH,iBAAiB,GAAGxH,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGjE,qBAAqB,CAACiE,KAAK,CAAC;EAChF;EACA;EACA,IAAIyH,UAAU,GAAG;IACb,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI,CAACV,QAAQ;EAC7C;EACAhJ,WAAW,CAAC2J,IAAI,EAAEC,QAAQ,EAAEvG,OAAO,EAAEpD,kBAAkB,EAAE4J,aAAa,EAAEC,eAAe,GAAG,KAAK,EAAEC,cAAc,EAAE;IAC7G,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACvG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACpD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC8J,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIxM,SAAS,EAAE;IAC/B;IACA,IAAI,CAACyM,aAAa,GAAG,IAAI9M,YAAY,EAAE;IACvC;IACA,IAAI,CAACsH,UAAU,GAAG,IAAIrG,OAAO,EAAE;IAC/B;IACA,IAAI,CAAC8L,eAAe,GAAG,IAAI9L,OAAO,EAAE;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC+L,eAAe,GAAG;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;IAClD,IAAI,CAAC9J,qBAAqB,GAAG,IAAInC,OAAO,EAAE;IAC1C;IACA;IACA,IAAIuL,IAAI,EAAE;MACNA,IAAI,CAACW,MAAM,CAACvG,IAAI,CAACrF,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CAACjE,SAAS,CAAC,MAAM;QACzD,IAAI,CAAC+J,gBAAgB,EAAE;QACvB,IAAI,CAACC,oBAAoB,EAAE;MAC/B,CAAC,CAAC;IACN;IACA;IACA;IACAX,aAAa,CACRS,MAAM,EAAE,CACRvG,IAAI,CAACrF,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CAChCjE,SAAS,CAAC,MAAM,IAAI,CAACgK,oBAAoB,EAAE,CAAC;IACjD,IAAI,CAAClB,SAAS,GAAGQ,eAAe;EACpC;EACAxJ,kBAAkB,GAAG;IACjB,IAAI,CAACmK,WAAW,CAACC,OAAO,CACnB3G,IAAI,CAAClF,SAAS,CAAC,IAAI,CAAC4L,WAAW,CAAC,EAAE/L,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CAC7DjE,SAAS,CAAEmK,MAAM,IAAK;MACvB,IAAI,CAACX,QAAQ,CAACY,KAAK,CAACD,MAAM,CAACpM,MAAM,CAACsM,IAAI,IAAI,CAACA,IAAI,CAAC3K,UAAU,IAAI2K,IAAI,CAAC3K,UAAU,KAAK,IAAI,CAAC,CAAC;MACxF,IAAI,CAAC8J,QAAQ,CAACc,eAAe,EAAE;IACnC,CAAC,CAAC;IACF,IAAI,CAACd,QAAQ,CAACU,OAAO,CAAC3G,IAAI,CAAClF,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC2B,SAAS,CAAC,MAAM;MACxD,IAAI,CAAC+J,gBAAgB,EAAE;MACvB,IAAI,CAACP,QAAQ,CAACe,OAAO,CAAEJ,MAAM,IAAK;QAC9B,IAAI,CAACK,kBAAkB,CAACL,MAAM,CAAC;QAC/B,IAAI,CAACM,oBAAoB,CAACN,MAAM,CAAC;QACjC,IAAI,CAACO,gBAAgB,CAACP,MAAM,CAAC;MACjC,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAACX,QAAQ,CAACmB,MAAM,IACrB,IAAI,CAACC,aAAa,CAAC,IAAI,CAAClC,MAAM,CAAC,IAC/B,IAAI,CAACkC,aAAa,CAAC,IAAI,CAAChC,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACoB,oBAAoB,EAAE;MAC/B;MACA,IAAI,CAACvK,kBAAkB,CAACQ,YAAY,EAAE;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAAC4C,OAAO,CAAC0B,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACmF,eAAe,CACfnG,IAAI,CAACjF,YAAY,CAAC,EAAE,CAAC;MAAE;MAC5BJ,SAAS,CAAC,IAAI,CAAC+F,UAAU,CAAC,CAAC,CACtBjE,SAAS,CAAC,MAAM,IAAI,CAACgK,oBAAoB,EAAE,CAAC;IACrD,CAAC,CAAC;EACN;EACArD,WAAW,GAAG;IACV,IAAI,CAAC5G,qBAAqB,CAACgH,QAAQ,EAAE;IACrC,IAAI,CAAC2C,eAAe,CAAC3C,QAAQ,EAAE;IAC/B,IAAI,CAACyC,QAAQ,CAAC5C,OAAO,EAAE;IACvB,IAAI,CAAC3C,UAAU,CAAChC,IAAI,EAAE;IACtB,IAAI,CAACgC,UAAU,CAAC8C,QAAQ,EAAE;EAC9B;EACA;EACAC,IAAI,GAAG;IACH,IAAI,CAACwC,QAAQ,CAACe,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAACnD,IAAI,EAAE,CAAC;EAClD;EACA;EACApC,KAAK,GAAG;IACJ,IAAI,CAAC4E,QAAQ,CAACe,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAACvF,KAAK,EAAE,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACIoF,oBAAoB,GAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIJ,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACgB,KAAK,IAAI,IAAI,CAACA,KAAK,CAACvI,MAAM,EAAE;MACjC,IAAI,IAAI,CAACuI,KAAK,CAAChJ,IAAI,IAAI,MAAM,EAAE;QAC3B+H,IAAI,IAAI,IAAI,CAACiB,KAAK,CAACpD,SAAS,EAAE;MAClC,CAAC,MACI,IAAI,IAAI,CAACoD,KAAK,CAAChJ,IAAI,IAAI,MAAM,EAAE;QAChC,MAAMiJ,KAAK,GAAG,IAAI,CAACD,KAAK,CAACpD,SAAS,EAAE;QACpCmC,IAAI,IAAIkB,KAAK;QACbjB,KAAK,IAAIiB,KAAK;MAClB;IACJ;IACA,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACzI,MAAM,EAAE;MACnC,IAAI,IAAI,CAACyI,MAAM,CAAClJ,IAAI,IAAI,MAAM,EAAE;QAC5BgI,KAAK,IAAI,IAAI,CAACkB,MAAM,CAACtD,SAAS,EAAE;MACpC,CAAC,MACI,IAAI,IAAI,CAACsD,MAAM,CAAClJ,IAAI,IAAI,MAAM,EAAE;QACjC,MAAMiJ,KAAK,GAAG,IAAI,CAACC,MAAM,CAACtD,SAAS,EAAE;QACrCoC,KAAK,IAAIiB,KAAK;QACdlB,IAAI,IAAIkB,KAAK;MACjB;IACJ;IACA;IACA;IACA;IACA;IACAlB,IAAI,GAAGA,IAAI,IAAI,IAAI;IACnBC,KAAK,GAAGA,KAAK,IAAI,IAAI;IACrB,IAAID,IAAI,KAAK,IAAI,CAACD,eAAe,CAACC,IAAI,IAAIC,KAAK,KAAK,IAAI,CAACF,eAAe,CAACE,KAAK,EAAE;MAC5E,IAAI,CAACF,eAAe,GAAG;QAAEC,IAAI;QAAEC;MAAM,CAAC;MACtC;MACA;MACA,IAAI,CAAChH,OAAO,CAAC8B,GAAG,CAAC,MAAM,IAAI,CAAC5E,qBAAqB,CAACkC,IAAI,CAAC,IAAI,CAAC0H,eAAe,CAAC,CAAC;IACjF;EACJ;EACAqB,SAAS,GAAG;IACR;IACA,IAAI,IAAI,CAAClC,SAAS,IAAI,IAAI,CAACmC,SAAS,EAAE,EAAE;MACpC;MACA,IAAI,CAACpI,OAAO,CAAC0B,iBAAiB,CAAC,MAAM,IAAI,CAACmF,eAAe,CAACzH,IAAI,EAAE,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIuI,kBAAkB,CAACL,MAAM,EAAE;IACvBA,MAAM,CAACjH,iBAAiB,CACnBK,IAAI,CAACxF,MAAM,CAAE0G,KAAK,IAAKA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACb,OAAO,CAAC,EAAE1F,SAAS,CAAC,IAAI,CAACsL,QAAQ,CAACU,OAAO,CAAC,CAAC,CAC5FlK,SAAS,CAAEyE,KAAK,IAAK;MACtB;MACA;MACA,IAAIA,KAAK,CAACb,OAAO,KAAK,cAAc,IAAI,IAAI,CAAC2F,cAAc,KAAK,gBAAgB,EAAE;QAC9E,IAAI,CAACH,QAAQ,CAAC5E,aAAa,CAAC0G,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtE;MACA,IAAI,CAACnB,oBAAoB,EAAE;MAC3B,IAAI,CAACvK,kBAAkB,CAACQ,YAAY,EAAE;IAC1C,CAAC,CAAC;IACF,IAAIkK,MAAM,CAACtI,IAAI,KAAK,MAAM,EAAE;MACxBsI,MAAM,CAAC9G,YAAY,CACdE,IAAI,CAACrF,SAAS,CAAC,IAAI,CAACsL,QAAQ,CAACU,OAAO,CAAC,CAAC,CACtClK,SAAS,CAAC,MAAM,IAAI,CAACoL,kBAAkB,CAACjB,MAAM,CAAC7H,MAAM,CAAC,CAAC;IAChE;EACJ;EACA;AACJ;AACA;AACA;EACImI,oBAAoB,CAACN,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;IACA;IACAA,MAAM,CAACxI,iBAAiB,CAAC4B,IAAI,CAACrF,SAAS,CAAC,IAAI,CAACsL,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAClK,SAAS,CAAC,MAAM;MAC5E,IAAI,CAAC6C,OAAO,CAACwI,gBAAgB,CAAC9H,IAAI,CAACnF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4B,SAAS,CAAC,MAAM;QACxD,IAAI,CAAC+J,gBAAgB,EAAE;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAW,gBAAgB,CAACP,MAAM,EAAE;IACrB,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACnI,YAAY,CACduB,IAAI,CAACrF,SAAS,CAACJ,KAAK,CAAC,IAAI,CAAC0L,QAAQ,CAACU,OAAO,EAAE,IAAI,CAACjG,UAAU,CAAC,CAAC,CAAC,CAC9DjE,SAAS,CAAC,MAAM;QACjB,IAAI,CAACgK,oBAAoB,EAAE;QAC3B,IAAI,CAACvK,kBAAkB,CAACQ,YAAY,EAAE;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;EACAmL,kBAAkB,CAACE,KAAK,EAAE;IACtB,MAAMJ,SAAS,GAAG,IAAI,CAAC9B,QAAQ,CAAC5E,aAAa,CAAC0G,SAAS;IACvD,MAAMK,SAAS,GAAG,+BAA+B;IACjD,IAAID,KAAK,EAAE;MACPJ,SAAS,CAACC,GAAG,CAACI,SAAS,CAAC;IAC5B,CAAC,MACI;MACDL,SAAS,CAACpE,MAAM,CAACyE,SAAS,CAAC;IAC/B;EACJ;EACA;EACAxB,gBAAgB,GAAG;IACf,IAAI,CAACrB,MAAM,GAAG,IAAI,CAACE,IAAI,GAAG,IAAI;IAC9B;IACA,IAAI,CAACY,QAAQ,CAACe,OAAO,CAACJ,MAAM,IAAI;MAC5B,IAAIA,MAAM,CAACnL,QAAQ,IAAI,KAAK,EAAE;QAC1B,IAAI,IAAI,CAAC4J,IAAI,IAAI,IAAI,KAAK,OAAO4C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UACtEzM,6BAA6B,CAAC,KAAK,CAAC;QACxC;QACA,IAAI,CAAC6J,IAAI,GAAGuB,MAAM;MACtB,CAAC,MACI;QACD,IAAI,IAAI,CAACzB,MAAM,IAAI,IAAI,KAAK,OAAO8C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UACxEzM,6BAA6B,CAAC,OAAO,CAAC;QAC1C;QACA,IAAI,CAAC2J,MAAM,GAAGyB,MAAM;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAACY,MAAM,GAAG,IAAI,CAACF,KAAK,GAAG,IAAI;IAC/B;IACA,IAAI,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3H,KAAK,KAAK,KAAK,EAAE;MACxC,IAAI,CAACqJ,KAAK,GAAG,IAAI,CAACjC,IAAI;MACtB,IAAI,CAACmC,MAAM,GAAG,IAAI,CAACrC,MAAM;IAC7B,CAAC,MACI;MACD,IAAI,CAACmC,KAAK,GAAG,IAAI,CAACnC,MAAM;MACxB,IAAI,CAACqC,MAAM,GAAG,IAAI,CAACnC,IAAI;IAC3B;EACJ;EACA;EACAqC,SAAS,GAAG;IACR,OAAS,IAAI,CAACL,aAAa,CAAC,IAAI,CAAClC,MAAM,CAAC,IAAI,IAAI,CAACA,MAAM,CAAC7G,IAAI,IAAI,MAAM,IACjE,IAAI,CAAC+I,aAAa,CAAC,IAAI,CAAChC,IAAI,CAAC,IAAI,IAAI,CAACA,IAAI,CAAC/G,IAAI,IAAI,MAAO;EACnE;EACA4J,kBAAkB,GAAG;IACjB,IAAI,CAAChC,aAAa,CAAC7H,IAAI,EAAE;IACzB,IAAI,CAAC8J,6BAA6B,EAAE;EACxC;EACAA,6BAA6B,GAAG;IAC5B;IACA,CAAC,IAAI,CAAChD,MAAM,EAAE,IAAI,CAACE,IAAI,CAAC,CACnB7K,MAAM,CAACoM,MAAM,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACjI,YAAY,IAAI,IAAI,CAACyJ,gBAAgB,CAACxB,MAAM,CAAC,CAAC,CACjFI,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAACjD,sBAAsB,EAAE,CAAC;EAC3D;EACA0E,kBAAkB,GAAG;IACjB,OAAS,IAAI,CAAChB,aAAa,CAAC,IAAI,CAAClC,MAAM,CAAC,IAAI,IAAI,CAACiD,gBAAgB,CAAC,IAAI,CAACjD,MAAM,CAAC,IACzE,IAAI,CAACkC,aAAa,CAAC,IAAI,CAAChC,IAAI,CAAC,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,IAAI,CAAC/C,IAAI,CAAE;EAC3E;EACA+C,gBAAgB,CAACxB,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACtI,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,CAACmH,iBAAiB;EAC7D;EACA4B,aAAa,CAACT,MAAM,EAAE;IAClB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC7H,MAAM;EAC1C;AACJ;AACAlC,kBAAkB,CAACF,IAAI;EAAA,iBAA6FE,kBAAkB,EA7uB9BhE,EAAE,mBA6uB8CkB,EAAE,CAACuO,cAAc,MA7uBjEzP,EAAE,mBA6uB4FA,EAAE,CAACiE,UAAU,GA7uB3GjE,EAAE,mBA6uBsHA,EAAE,CAACmE,MAAM,GA7uBjInE,EAAE,mBA6uB4IA,EAAE,CAAC+D,iBAAiB,GA7uBlK/D,EAAE,mBA6uB6KN,EAAE,CAACgQ,aAAa,GA7uB/L1P,EAAE,mBA6uB0M8C,2BAA2B,GA7uBvO9C,EAAE,mBA6uBkPwC,qBAAqB;AAAA,CAA4D;AAC7awB,kBAAkB,CAACI,IAAI,kBA9uBiFpE,EAAE;EAAA,MA8uBFgE,kBAAkB;EAAA;EAAA;IAAA;MA9uBlBhE,EAAE,0BAmvBrCmD,gBAAgB;MAnvBmBnD,EAAE,0BAmvB4CkF,SAAS;IAAA;IAAA;MAAA;MAnvBvDlF,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,aAmvBmJmD,gBAAgB;IAAA;IAAA;MAAA;MAnvBrKnD,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBA8uBiT,CACnZ;IACIqE,OAAO,EAAEnB,oBAAoB;IAC7BoB,WAAW,EAAEN;EACjB,CAAC,CACJ;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAnvBmGhE,EAAE;MAAFA,EAAE,iEAmvB8Y;MAnvBhZA,EAAE,gBAmvBic;MAnvBncA,EAAE,mBAmvB8f;MAnvBhgBA,EAAE,+FAmvB4lB;IAAA;IAAA;MAnvB9lBA,EAAE,oCAmvB6U;MAnvB/UA,EAAE,aAmvBqiB;MAnvBviBA,EAAE,kCAmvBqiB;IAAA;EAAA;EAAA,eAAqzEH,EAAE,CAAC8P,IAAI,EAA6FxM,gBAAgB;EAAA;EAAA;EAAA;AAAA,EAAoI;AAC5rG;EAAA,mDApvBwGnD,EAAE,mBAovBVgE,kBAAkB,EAAc,CAAC;IACrHO,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEyH,QAAQ,EAAE,oBAAoB;MAAEvH,IAAI,EAAE;QACrE,OAAO,EAAE,sBAAsB;QAC/B,gDAAgD,EAAE;MACtD,CAAC;MAAEC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAAEC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MAAEC,SAAS,EAAE,CAClG;QACIX,OAAO,EAAEnB,oBAAoB;QAC7BoB,WAAW,EAAEN;MACjB,CAAC,CACJ;MAAEU,QAAQ,EAAE,0WAA0W;MAAEkL,MAAM,EAAE,CAAC,msEAAmsE;IAAE,CAAC;EACplF,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErL,IAAI,EAAErD,EAAE,CAACuO,cAAc;MAAExK,UAAU,EAAE,CAAC;QACtEV,IAAI,EAAE/D;MACV,CAAC;IAAE,CAAC,EAAE;MAAE+D,IAAI,EAAEvE,EAAE,CAACiE;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAEvE,EAAE,CAACmE;IAAO,CAAC,EAAE;MAAEI,IAAI,EAAEvE,EAAE,CAAC+D;IAAkB,CAAC,EAAE;MAAEQ,IAAI,EAAE7E,EAAE,CAACgQ;IAAc,CAAC,EAAE;MAAEnL,IAAI,EAAEmD,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAC5IV,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAAC1B,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAEyB,IAAI,EAAEmD,SAAS;MAAEzC,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAE/D;MACV,CAAC,EAAE;QACC+D,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAAChC,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqL,WAAW,EAAE,CAAC;MAC1CtJ,IAAI,EAAE1D,eAAe;MACrB2D,IAAI,EAAE,CAACU,SAAS,EAAE;QACV;QACA;QACA2K,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEzD,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAEzD,YAAY;MAClB0D,IAAI,EAAE,CAACrB,gBAAgB;IAC3B,CAAC,CAAC;IAAE2J,YAAY,EAAE,CAAC;MACfvI,IAAI,EAAE5D,SAAS;MACf6D,IAAI,EAAE,CAACrB,gBAAgB;IAC3B,CAAC,CAAC;IAAEsJ,QAAQ,EAAE,CAAC;MACXlI,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEkM,WAAW,EAAE,CAAC;MACdpI,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAE4M,aAAa,EAAE,CAAC;MAChB9I,IAAI,EAAE7D;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoP,iBAAiB,SAAS3M,gBAAgB,CAAC;EAC7CC,WAAW,CAAC2M,iBAAiB,EAAEC,SAAS,EAAEzM,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;IAC5E,KAAK,CAACsM,iBAAiB,EAAEC,SAAS,EAAEzM,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;EAC7E;AACJ;AACAqM,iBAAiB,CAAChM,IAAI;EAAA,iBAA6FgM,iBAAiB,EA1yB5B9P,EAAE,mBA0yB4CA,EAAE,CAAC+D,iBAAiB,GA1yBlE/D,EAAE,mBA0yB6EE,UAAU,CAAC,MAAM+P,mBAAmB,CAAC,GA1yBpHjQ,EAAE,mBA0yB+HA,EAAE,CAACiE,UAAU,GA1yB9IjE,EAAE,mBA0yByJN,EAAE,CAACwE,gBAAgB,GA1yB9KlE,EAAE,mBA0yByLA,EAAE,CAACmE,MAAM;AAAA,CAA4C;AACxV2L,iBAAiB,CAAC1L,IAAI,kBA3yBkFpE,EAAE;EAAA,MA2yBH8P,iBAAiB;EAAA;EAAA;EAAA;EAAA;IAAA;MA3yBhB9P,EAAE;IAAA;EAAA;EAAA,WAAFA,EAAE,oBA2yBwQ,CAC1W;IACIqE,OAAO,EAAE1E,aAAa;IACtB2E,WAAW,EAAEwL;EACjB,CAAC,CACJ,GAhzBmG9P,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBAgzB1B;IAAA;EAAA;EAAA;EAAA;AAAA,EAAkH;AAClM;EAAA,mDAjzBwGA,EAAE,mBAizBV8P,iBAAiB,EAAc,CAAC;IACpHvL,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE,2BAA2B;MACrCC,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wBAAwB,EAAE,iCAAiC;QAC3D,yBAAyB,EAAE;MAC/B,CAAC;MACDC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAC/CC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MACrCC,SAAS,EAAE,CACP;QACIX,OAAO,EAAE1E,aAAa;QACtB2E,WAAW,EAAEwL;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvL,IAAI,EAAEvE,EAAE,CAAC+D;IAAkB,CAAC,EAAE;MAAEQ,IAAI,EAAE0L,mBAAmB;MAAEhL,UAAU,EAAE,CAAC;QACxGV,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAACtE,UAAU,CAAC,MAAM+P,mBAAmB,CAAC;MAChD,CAAC;IAAE,CAAC,EAAE;MAAE1L,IAAI,EAAEvE,EAAE,CAACiE;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAE7E,EAAE,CAACwE;IAAiB,CAAC,EAAE;MAAEK,IAAI,EAAEvE,EAAE,CAACmE;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG,MAAM+L,UAAU,SAAShL,SAAS,CAAC;EAC/B9B,WAAW,GAAG;IACV,KAAK,CAAC,GAAG+M,SAAS,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC5B;EACA;EACA,IAAIC,eAAe,GAAG;IAClB,OAAO,IAAI,CAACH,gBAAgB;EAChC;EACA,IAAIG,eAAe,CAACnL,KAAK,EAAE;IACvB,IAAI,CAACgL,gBAAgB,GAAGjP,qBAAqB,CAACiE,KAAK,CAAC;EACxD;EACA;AACJ;AACA;AACA;EACI,IAAIoL,WAAW,GAAG;IACd,OAAO,IAAI,CAACH,YAAY;EAC5B;EACA,IAAIG,WAAW,CAACpL,KAAK,EAAE;IACnB,IAAI,CAACiL,YAAY,GAAGjP,oBAAoB,CAACgE,KAAK,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACI,IAAIqL,cAAc,GAAG;IACjB,OAAO,IAAI,CAACH,eAAe;EAC/B;EACA,IAAIG,cAAc,CAACrL,KAAK,EAAE;IACtB,IAAI,CAACkL,eAAe,GAAGlP,oBAAoB,CAACgE,KAAK,CAAC;EACtD;AACJ;AACA8K,UAAU,CAACpM,IAAI;EAAA;EAAA;IAAA,8DA32ByF9D,EAAE,uBA22BEkQ,UAAU,SAAVA,UAAU;EAAA;AAAA,GAAqD;AAC3KA,UAAU,CAAC9L,IAAI,kBA52ByFpE,EAAE;EAAA,MA42BVkQ,UAAU;EAAA;EAAA,wBAA2K,IAAI;EAAA;EAAA;IAAA;MA52BjLlQ,EAAE;MAAFA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,+BA42BmxB;MA52BrxBA,EAAE,gBA42BkzB;MA52BpzBA,EAAE,eA42B4zB;IAAA;EAAA;EAAA,eAAiDN,EAAE,CAACC,aAAa;EAAA;EAAA;IAAA,WAAgE,CAAC8C,mBAAmB,CAACC,eAAe;EAAC;EAAA;AAAA,EAAiG;AAC7qC;EAAA,mDA72BwG1C,EAAE,mBA62BVkQ,UAAU,EAAc,CAAC;IAC7G3L,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEyH,QAAQ,EAAE,YAAY;MAAEC,UAAU,EAAE,CAAC1J,mBAAmB,CAACC,eAAe,CAAC;MAAEiC,IAAI,EAAE;QACvG,OAAO,EAAE,wBAAwB;QACjC,UAAU,EAAE,IAAI;QAChB;QACA,cAAc,EAAE,MAAM;QACtB,wBAAwB,EAAE,oBAAoB;QAC9C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,yBAAyB,EAAE,iBAAiB;QAC5C,2BAA2B,EAAE,QAAQ;QACrC,2BAA2B,EAAE,iBAAiB;QAC9C,gBAAgB,EAAE,sCAAsC;QACxD,mBAAmB,EAAE;MACzB,CAAC;MAAEC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAAEC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MAAEL,QAAQ,EAAE;IAAiH,CAAC;EACnO,CAAC,CAAC,QAAkB;IAAE6L,eAAe,EAAE,CAAC;MAChChM,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAE+P,WAAW,EAAE,CAAC;MACdjM,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEgQ,cAAc,EAAE,CAAC;MACjBlM,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwP,mBAAmB,SAASjM,kBAAkB,CAAC;AAErDiM,mBAAmB,CAACnM,IAAI;EAAA;EAAA;IAAA,gFAt4BgF9D,EAAE,uBAs4BWiQ,mBAAmB,SAAnBA,mBAAmB;EAAA;AAAA,GAAqD;AAC7LA,mBAAmB,CAAC7L,IAAI,kBAv4BgFpE,EAAE;EAAA,MAu4BDiQ,mBAAmB;EAAA;EAAA;IAAA;MAv4BpBjQ,EAAE,0BA44BrC8P,iBAAiB;MA54BkB9P,EAAE,0BA44B6CkQ,UAAU;IAAA;IAAA;MAAA;MA54BzDlQ,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAu4B+N,CACjU;IACIqE,OAAO,EAAEnB,oBAAoB;IAC7BoB,WAAW,EAAE2L;EACjB,CAAC,CACJ,GA54BmGjQ,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,kEA44B0T;MA54B5TA,EAAE,gBA44B8W;MA54BhXA,EAAE,mBA44B4a;MA54B9aA,EAAE,kGA44B4gB;IAAA;IAAA;MA54B9gBA,EAAE,oCA44ByP;MA54B3PA,EAAE,aA44Bod;MA54BtdA,EAAE,kCA44Bod;IAAA;EAAA;EAAA,eAAszEH,EAAE,CAAC8P,IAAI,EAA6FG,iBAAiB;EAAA;EAAA;EAAA;AAAA,EAAqI;AAC9mG;EAAA,mDA74BwG9P,EAAE,mBA64BViQ,mBAAmB,EAAc,CAAC;IACtH1L,IAAI,EAAEpE,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAuB;MAAEyH,QAAQ,EAAE,qBAAqB;MAAEvH,IAAI,EAAE;QACvE,OAAO,EAAE,4CAA4C;QACrD,gDAAgD,EAAE;MACtD,CAAC;MAAEC,eAAe,EAAExE,uBAAuB,CAACyE,MAAM;MAAEC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAAI;MAAEC,SAAS,EAAE,CAClG;QACIX,OAAO,EAAEnB,oBAAoB;QAC7BoB,WAAW,EAAE2L;MACjB,CAAC,CACJ;MAAEvL,QAAQ,EAAE,8WAA8W;MAAEkL,MAAM,EAAE,CAAC,msEAAmsE;IAAE,CAAC;EACxlF,CAAC,CAAC,QAAkB;IAAE/B,WAAW,EAAE,CAAC;MAC5BtJ,IAAI,EAAE1D,eAAe;MACrB2D,IAAI,EAAE,CAAC0L,UAAU,EAAE;QACX;QACA;QACAL,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEzD,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAEzD,YAAY;MAClB0D,IAAI,EAAE,CAACsL,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,CAAC;AAEvBA,gBAAgB,CAAC5M,IAAI;EAAA,iBAA6F4M,gBAAgB;AAAA,CAAkD;AACpLA,gBAAgB,CAACC,IAAI,kBA96BmF3Q,EAAE;EAAA,MA86BS0Q;AAAgB,EAYtG;AAC7BA,gBAAgB,CAACE,IAAI,kBA37BmF5Q,EAAE;EAAA,UA27BqCD,YAAY,EAAEiB,eAAe,EAAEpB,mBAAmB,EAAEA,mBAAmB,EAC9MoB,eAAe;AAAA,EAAI;AAC3B;EAAA,mDA77BwGhB,EAAE,mBA67BV0Q,gBAAgB,EAAc,CAAC;IACnHnM,IAAI,EAAExD,QAAQ;IACdyD,IAAI,EAAE,CAAC;MACCqM,OAAO,EAAE,CAAC9Q,YAAY,EAAEiB,eAAe,EAAEpB,mBAAmB,CAAC;MAC7DkR,OAAO,EAAE,CACLlR,mBAAmB,EACnBoB,eAAe,EACfkE,SAAS,EACTlB,kBAAkB,EAClBb,gBAAgB,EAChB+M,UAAU,EACVD,mBAAmB,EACnBH,iBAAiB,CACpB;MACDiB,YAAY,EAAE,CACV7L,SAAS,EACTlB,kBAAkB,EAClBb,gBAAgB,EAChB+M,UAAU,EACVD,mBAAmB,EACnBH,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAShN,2BAA2B,EAAEG,mCAAmC,EAAEiC,SAAS,EAAElB,kBAAkB,EAAEb,gBAAgB,EAAE+M,UAAU,EAAED,mBAAmB,EAAEH,iBAAiB,EAAEY,gBAAgB,EAAEjO,mBAAmB,EAAEE,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}