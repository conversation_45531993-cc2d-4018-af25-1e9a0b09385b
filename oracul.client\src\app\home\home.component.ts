import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserProfile } from '../profile/models/profile.models';
import { ProfileService } from '../profile/services/profile.service';
import { TranslationService } from '../core/i18n/translation.service';
import { AvatarService } from '../shared/services/avatar.service';

export interface Article {
  id: number;
  title: string;
  excerpt: string;
  author: string;
  publishedAt: Date;
  readTime: number;
  category: string;
  imageUrl: string;
  slug: string;
}

export interface HoroscopeSign {
  id: number;
  name: string;
  symbol: string;
  element: string;
  dateRange: string;
  todayPrediction: string;
  luckyNumber: number;
  luckyColor: string;
  compatibility: string[];
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  featuredProfiles: UserProfile[] = [];
  featuredArticles: Article[] = [];
  horoscopeSigns: HoroscopeSign[] = [];
  isLoading = true;

  constructor(
    private profileService: ProfileService,
    private router: Router,
    public t: TranslationService,
    public avatarService: AvatarService
  ) {}

  ngOnInit(): void {
    this.loadHomeData();
  }

  private loadHomeData(): void {
    this.isLoading = true;

    // Load featured profiles from backend
    this.profileService.getPublicProfiles(1, 6).subscribe({
      next: (result) => {
        this.featuredProfiles = result.profiles;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading featured profiles:', error);
        // Fallback to single profile if public profiles fail
        this.profileService.getProfile('luna-starweaver').subscribe({
          next: (profile) => {
            this.featuredProfiles = [profile];
            this.isLoading = false;
          },
          error: (err) => {
            console.error('Error loading profile:', err);
            this.featuredProfiles = [];
            this.isLoading = false;
          }
        });
      }
    });

    // Load featured articles (still using mock data for now)
    this.featuredArticles = this.getMockArticles();

    // Load horoscope data (still using mock data for now)
    this.horoscopeSigns = this.getMockHoroscope();
  }

  private getMockArticles(): Article[] {
    return [
      {
        id: 1,
        title: 'Разбиране на вашата натална карта: Ръководство за начинаещи',
        excerpt: 'Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.',
        author: 'Луна Звездоплетка',
        publishedAt: new Date('2024-01-15'),
        readTime: 8,
        category: 'Основи на Астрологията',
        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',
        slug: 'understanding-birth-chart-beginners-guide'
      },
      {
        id: 2,
        title: 'Ръководство за оцеляване при Меркурий ретроград 2024',
        excerpt: 'Навигирайте предизвикателствата на Меркурий ретроград с увереност. Научете практически съвети за превръщане на космическия хаос в възможности за растеж.',
        author: 'Космически Мъдрец',
        publishedAt: new Date('2024-02-01'),
        readTime: 6,
        category: 'Планетарни Транзити',
        imageUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop',
        slug: 'mercury-retrograde-survival-guide-2024'
      },
      {
        id: 3,
        title: 'Ритуали на пълнолунието за манифестация',
        excerpt: 'Използвайте мощната енергия на пълнолунието, за да манифестирате най-дълбоките си желания и да освободите това, което вече не ви служи.',
        author: 'Лунна Мистичка',
        publishedAt: new Date('2024-02-10'),
        readTime: 10,
        category: 'Лунна Магия',
        imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop',
        slug: 'full-moon-rituals-manifestation'
      },
      {
        id: 4,
        title: 'Кристално лечение: Избор на правилните камъни',
        excerpt: 'Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.',
        author: 'Пазител на Кристали',
        publishedAt: new Date('2024-02-20'),
        readTime: 7,
        category: 'Кристално Лечение',
        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop',
        slug: 'crystal-healing-choosing-right-stones'
      }
    ];
  }

  private getMockHoroscope(): HoroscopeSign[] {
    return [
      {
        id: 1,
        name: 'Овен',
        symbol: '♈',
        element: 'Огън',
        dateRange: '21 март - 19 април',
        todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',
        luckyNumber: 7,
        luckyColor: 'Червен',
        compatibility: ['Лъв', 'Стрелец', 'Близнаци']
      },
      {
        id: 2,
        name: 'Телец',
        symbol: '♉',
        element: 'Земя',
        dateRange: '20 април - 20 май',
        todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',
        luckyNumber: 3,
        luckyColor: 'Зелен',
        compatibility: ['Дева', 'Козирог', 'Рак']
      },
      {
        id: 3,
        name: 'Близнаци',
        symbol: '♊',
        element: 'Въздух',
        dateRange: '21 май - 20 юни',
        todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',
        luckyNumber: 5,
        luckyColor: 'Жълт',
        compatibility: ['Везни', 'Водолей', 'Овен']
      },
      {
        id: 4,
        name: 'Рак',
        symbol: '♋',
        element: 'Вода',
        dateRange: '21 юни - 22 юли',
        todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',
        luckyNumber: 2,
        luckyColor: 'Сребърен',
        compatibility: ['Скорпион', 'Риби', 'Телец']
      },
      {
        id: 5,
        name: 'Leo',
        symbol: '♌',
        element: 'Fire',
        dateRange: 'Jul 23 - Aug 22',
        todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',
        luckyNumber: 1,
        luckyColor: 'Gold',
        compatibility: ['Aries', 'Sagittarius', 'Gemini']
      },
      {
        id: 6,
        name: 'Virgo',
        symbol: '♍',
        element: 'Earth',
        dateRange: 'Aug 23 - Sep 22',
        todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',
        luckyNumber: 6,
        luckyColor: 'Navy Blue',
        compatibility: ['Taurus', 'Capricorn', 'Cancer']
      }
    ];
  }

  navigateToLogin(): void {
    console.log('Navigating to login...');
    this.router.navigate(['/login']);
  }

  navigateToRegister(): void {
    console.log('Navigating to register...');
    this.router.navigate(['/register']);
  }

  viewProfile(profile: UserProfile): void {
    this.router.navigate(['/profile', profile.slug]);
  }

  readArticle(article: Article): void {
    // For now, just navigate to login to read full article
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: `/articles/${article.slug}` }
    });
  }

  viewHoroscope(sign: HoroscopeSign): void {
    // For now, just navigate to login to view detailed horoscope
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: `/horoscope/${sign.name.toLowerCase()}` }
    });
  }

  searchProfiles(): void {
    this.router.navigate(['/profiles/search']);
  }

  getTotalEndorsements(profile: UserProfile): number {
    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);
  }


}
