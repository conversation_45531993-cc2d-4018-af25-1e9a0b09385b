{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nfunction MatMenuItem__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 3);\n    i0.ɵɵelement(1, \"polygon\", 4);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.panelId)(\"ngClass\", ctx_r0._classList)(\"@transformMenu\", ctx_r0._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0.ariaLabelledby || null)(\"aria-describedby\", ctx_r0.ariaDescribedby || null);\n  }\n}\nconst _c3 = [\"*\"];\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {}));\n/**\n * Single item inside of a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem extends _MatMenuItemBase {\n  constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n    super();\n    this._elementRef = _elementRef;\n    this._document = _document;\n    this._focusMonitor = _focusMonitor;\n    this._parentMenu = _parentMenu;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ARIA role for the menu item. */\n    this.role = 'menuitem';\n    /** Stream that emits when the menu item is hovered. */\n    this._hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    this._focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    this._highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    this._triggersSubmenu = false;\n    _parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return clone.textContent?.trim() || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n}\nMatMenuItem.ɵfac = function MatMenuItem_Factory(t) {\n  return new (t || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nMatMenuItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatMenuItem,\n  selectors: [[\"\", \"mat-menu-item\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-mdc-focus-indicator\", \"mdc-list-item\"],\n  hostVars: 8,\n  hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n        return ctx._checkDisabled($event);\n      })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n        return ctx._handleMouseEnter();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n      i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    role: \"role\"\n  },\n  exportAs: [\"matMenuItem\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c2,\n  decls: 5,\n  vars: 3,\n  consts: [[1, \"mdc-list-item__primary-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"class\", \"mat-mdc-menu-submenu-icon\", \"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", 4, \"ngIf\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n  template: function MatMenuItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"span\", 0);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 1);\n      i0.ɵɵtemplate(4, MatMenuItem__svg_svg_4_Template, 2, 0, \"svg\", 2);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._triggersSubmenu);\n    }\n  },\n  dependencies: [i2.NgIf, i3.MatRipple],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      inputs: ['disabled', 'disableRipple'],\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator mdc-list-item',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mdc-list-item__primary-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    role: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\nclass _MatMenuContentBase {\n  constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n    this._template = _template;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._injector = _injector;\n    this._viewContainerRef = _viewContainerRef;\n    this._document = _document;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Emits when the menu content has been attached. */\n    this._attached = new Subject();\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n    this._changeDetectorRef?.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n}\n_MatMenuContentBase.ɵfac = function _MatMenuContentBase_Factory(t) {\n  return new (t || _MatMenuContentBase)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n_MatMenuContentBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuContentBase\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuContentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent extends _MatMenuContentBase {}\nMatMenuContent.ɵfac = /* @__PURE__ */function () {\n  let ɵMatMenuContent_BaseFactory;\n  return function MatMenuContent_Factory(t) {\n    return (ɵMatMenuContent_BaseFactory || (ɵMatMenuContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuContent)))(t || MatMenuContent);\n  };\n}();\nMatMenuContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatMenuContent,\n  selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_CONTENT,\n    useExisting: MatMenuContent\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [state('void', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))]),\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [\n  // TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', style({\n    opacity: 1\n  })), transition('void => *', [style({\n    opacity: 0\n  }), animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Base class with all of the `MatMenu` functionality. */\nclass _MatMenuBase {\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /** Whether the menu should overlap its trigger. */\n  get overlapTrigger() {\n    return this._overlapTrigger;\n  }\n  set overlapTrigger(value) {\n    this._overlapTrigger = coerceBooleanProperty(value);\n  }\n  /** Whether the menu has a backdrop. */\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        this._classList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        this._classList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  constructor(_elementRef, _ngZone, _defaultOptions,\n  // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n  _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._xPosition = this._defaultOptions.xPosition;\n    this._yPosition = this._defaultOptions.yPosition;\n    /** Only the direct descendant menu items. */\n    this._directDescendantItems = new QueryList();\n    /** Config object to be passed into the menu's ngClass */\n    this._classList = {};\n    /** Current state of the panel animation. */\n    this._panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    this._animationDone = new Subject();\n    /** Class or list of classes to be added to the overlay panel. */\n    this.overlayPanelClass = this._defaultOptions.overlayPanelClass || '';\n    /** Class to be added to the backdrop element. */\n    this.backdropClass = this._defaultOptions.backdropClass;\n    this._overlapTrigger = this._defaultOptions.overlapTrigger;\n    this._hasBackdrop = this._defaultOptions.hasBackdrop;\n    /** Event emitted when the menu is closed. */\n    this.closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    this.close = this.closed;\n    this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusSubscription?.unsubscribe();\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n    // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n    event.stopPropagation();\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusSubscription?.unsubscribe();\n    this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n  setElevation(depth) {\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n    if (!customElevation || customElevation === this._previousElevation) {\n      if (this._previousElevation) {\n        this._classList[this._previousElevation] = false;\n      }\n      this._classList[newElevation] = true;\n      this._previousElevation = newElevation;\n    }\n  }\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    const classes = this._classList;\n    classes['mat-menu-before'] = posX === 'before';\n    classes['mat-menu-after'] = posX === 'after';\n    classes['mat-menu-above'] = posY === 'above';\n    classes['mat-menu-below'] = posY === 'below';\n    // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n    this._changeDetectorRef?.markForCheck();\n  }\n  /** Starts the enter animation. */\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n  /** Resets the panel animation to its initial state. */\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(event) {\n    this._animationDone.next(event);\n    this._isAnimating = false;\n  }\n  _onAnimationStart(event) {\n    this._isAnimating = true;\n    // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n}\n_MatMenuBase.ɵfac = function _MatMenuBase_Factory(t) {\n  return new (t || _MatMenuBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n_MatMenuBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuBase,\n  contentQueries: function _MatMenuBase_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n    }\n  },\n  viewQuery: function _MatMenuBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n    }\n  },\n  inputs: {\n    backdropClass: \"backdropClass\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n    xPosition: \"xPosition\",\n    yPosition: \"yPosition\",\n    overlapTrigger: \"overlapTrigger\",\n    hasBackdrop: \"hasBackdrop\",\n    panelClass: [\"class\", \"panelClass\"],\n    classList: \"classList\"\n  },\n  outputs: {\n    closed: \"closed\",\n    close: \"close\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenu extends _MatMenuBase {\n  constructor(_elementRef, _ngZone, _defaultOptions, changeDetectorRef) {\n    super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n    this._elevationPrefix = 'mat-elevation-z';\n    this._baseElevation = 8;\n  }\n}\nMatMenu.ɵfac = function MatMenu_Factory(t) {\n  return new (t || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nMatMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatMenu,\n  selectors: [[\"mat-menu\"]],\n  hostVars: 3,\n  hostBindings: function MatMenu_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n    }\n  },\n  exportAs: [\"matMenu\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_PANEL,\n    useExisting: MatMenu\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c3,\n  decls: 1,\n  vars: 0,\n  consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", \"mat-mdc-elevation-specific\", 3, \"id\", \"ngClass\", \"keydown\", \"click\"], [1, \"mat-mdc-menu-content\", \"mdc-list\"]],\n  template: function MatMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 6, \"ng-template\");\n    }\n  },\n  dependencies: [i2.NgClass],\n  styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;position:relative}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item.mdc-list-item{align-items:center}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item .mdc-list-item__primary-text{white-space:normal}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mdc-menu-surface mdc-menu-surface--open mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content mdc-list\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;position:relative}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item.mdc-list-item{align-items:center}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item .mdc-list-item__primary-text{white-space:normal}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\nclass _MatMenuTriggerBase {\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu,\n  // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n  // tslint:disable-next-line: lightweight-tokens\n  _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n    this._overlay = _overlay;\n    this._element = _element;\n    this._viewContainerRef = _viewContainerRef;\n    this._menuItemInstance = _menuItemInstance;\n    this._dir = _dir;\n    this._focusMonitor = _focusMonitor;\n    this._ngZone = _ngZone;\n    this._overlayRef = null;\n    this._menuOpen = false;\n    this._closingActionsSubscription = Subscription.EMPTY;\n    this._hoverSubscription = Subscription.EMPTY;\n    this._menuCloseSubscription = Subscription.EMPTY;\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Handles touch start events on the trigger.\n     * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n     */\n    this._handleTouchStart = event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    };\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    this._openedBy = undefined;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    this.restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    this.menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    this.menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuClose = this.menuClosed;\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n    _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n    this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    this._initMenu(menu);\n    if (menu instanceof _MatMenuBase) {\n      menu._startAnimation();\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n    const menu = this.menu;\n    this._closingActionsSubscription.unsubscribe();\n    this._overlayRef.detach();\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    if (menu instanceof _MatMenuBase) {\n      menu._resetAnimation();\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1),\n        // Interrupt if the content got re-attached.\n        takeUntil(menu.lazyContent._attached)).subscribe({\n          next: () => menu.lazyContent.detach(),\n          // No matter whether the content got re-attached, reset the menu.\n          complete: () => this._setIsMenuOpen(false)\n        });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n      menu?.lazyContent?.detach();\n    }\n  }\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n  _initMenu(menu) {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    this._setMenuElevation(menu);\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n  }\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n  _setMenuElevation(menu) {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n      menu.setElevation(depth);\n    }\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = this._overlay.create(config);\n      // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n      this._overlayRef.keydownEvents().subscribe();\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n        // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n        } else {\n          menu.setPositionClasses(posX, posY);\n        }\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n    this._hoverSubscription = this._parentMaterialMenu._hovered()\n    // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n    // with different data and triggers), we have to delay it by a tick to ensure that\n    // it won't be closed immediately after it is opened.\n    .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n      this._openedBy = 'mouse';\n      // If the same menu is used between multiple triggers, it might still be animating\n      // while the new trigger tries to re-open it. Wait for the animation to finish\n      // before doing so. Also interrupt if the user moves to another item.\n      if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n        // We need the `delay(0)` here in order to avoid\n        // 'changed after checked' errors in some cases. See #12194.\n        this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n      } else {\n        this.openMenu();\n      }\n    });\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n}\n_MatMenuTriggerBase.ɵfac = function _MatMenuTriggerBase_Factory(t) {\n  return new (t || _MatMenuTriggerBase)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n_MatMenuTriggerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuTriggerBase,\n  hostVars: 3,\n  hostBindings: function _MatMenuTriggerBase_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatMenuTriggerBase_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      })(\"mousedown\", function _MatMenuTriggerBase_mousedown_HostBindingHandler($event) {\n        return ctx._handleMousedown($event);\n      })(\"keydown\", function _MatMenuTriggerBase_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n    }\n  },\n  inputs: {\n    _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n    menu: [\"matMenuTriggerFor\", \"menu\"],\n    menuData: [\"matMenuTriggerData\", \"menuData\"],\n    restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n  },\n  outputs: {\n    menuOpened: \"menuOpened\",\n    onMenuOpen: \"onMenuOpen\",\n    menuClosed: \"menuClosed\",\n    onMenuClose: \"onMenuClose\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuTriggerBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: MatMenuItem,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }, {\n      type: i3$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger extends _MatMenuTriggerBase {}\nMatMenuTrigger.ɵfac = /* @__PURE__ */function () {\n  let ɵMatMenuTrigger_BaseFactory;\n  return function MatMenuTrigger_Factory(t) {\n    return (ɵMatMenuTrigger_BaseFactory || (ɵMatMenuTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuTrigger)))(t || MatMenuTrigger);\n  };\n}();\nMatMenuTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatMenuTrigger,\n  selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n  exportAs: [\"matMenuTrigger\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger'\n      },\n      exportAs: 'matMenuTrigger'\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatMenuModule {}\nMatMenuModule.ɵfac = function MatMenuModule_Factory(t) {\n  return new (t || MatMenuModule)();\n};\nMatMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatMenuModule\n});\nMatMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Optional", "Input", "Directive", "QueryList", "EventEmitter", "TemplateRef", "ContentChildren", "ViewChild", "ContentChild", "Output", "inject", "ChangeDetectorRef", "Self", "NgModule", "i1", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "coerceBooleanProperty", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "asapScheduler", "startWith", "switchMap", "take", "takeUntil", "filter", "delay", "i3", "mixinDisableRipple", "mixinDisabled", "MatRippleModule", "MatCommonModule", "i2", "DOCUMENT", "CommonModule", "TemplatePortal", "DomPortalOutlet", "trigger", "state", "style", "transition", "animate", "i3$1", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "normalizePassiveListenerOptions", "CdkScrollableModule", "MAT_MENU_PANEL", "_MatMenuItemBase", "MatMenuItem", "constructor", "_elementRef", "_document", "_focusMonitor", "_parentMenu", "_changeDetectorRef", "role", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "next", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "disabled", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_hasFocus", "activeElement", "ɵfac", "ElementRef", "FocusMonitor", "ɵcmp", "NgIf", "<PERSON><PERSON><PERSON><PERSON>", "type", "args", "selector", "exportAs", "inputs", "host", "changeDetection", "OnPush", "encapsulation", "None", "template", "undefined", "decorators", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "_MatMenuContentBase", "_template", "_componentFactoryResolver", "_appRef", "_injector", "_viewContainerRef", "_attached", "attach", "context", "_portal", "detach", "_outlet", "createElement", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "isAttached", "dispose", "ComponentFactoryResolver", "ApplicationRef", "Injector", "ViewContainerRef", "ɵdir", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provide", "useExisting", "providers", "matMenuAnimations", "transformMenu", "opacity", "transform", "fadeInItems", "menuPanelUid", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "_MatMenuBase", "_xPosition", "value", "ngDevMode", "setPositionClasses", "_yPosition", "_overlapTrigger", "hasBackdrop", "_hasBackdrop", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "split", "for<PERSON>ach", "className", "_classList", "classList", "_ngZone", "_defaultOptions", "_directDescendantItems", "_panelAnimationState", "_animationDone", "overlayPanelClass", "closed", "close", "panelId", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "_keyManager", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "emit", "changes", "pipe", "items", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "_firstItemFocusSubscription", "unsubscribe", "itemChanges", "_item", "_handleKeydown", "keyCode", "parentMenu", "direction", "setFocusOrigin", "onKeydown", "focusFirstItem", "onStable", "menuPanel", "first", "closest", "contains", "document", "setFirstItemActive", "resetActiveItem", "setElevation", "depth", "elevation", "_baseElevation", "newElevation", "_elevationPrefix", "customElevation", "Object", "keys", "find", "startsWith", "_previousElevation", "posX", "posY", "_startAnimation", "_resetAnimation", "_onAnimationDone", "_isAnimating", "_onAnimationStart", "toState", "scrollTop", "_allItems", "reset", "notifyOn<PERSON><PERSON>es", "NgZone", "descendants", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "templateRef", "lazyContent", "MatMenu", "changeDetectorRef", "Ng<PERSON><PERSON>", "animations", "styles", "MAT_MENU_SCROLL_STRATEGY", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "passiveEventListenerOptions", "passive", "MENU_PANEL_TOP_PADDING", "_MatMenuTriggerBase", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "_menuCloseSubscription", "_parentMaterialMenu", "reason", "_destroyMenu", "_menuItemInstance", "_overlay", "_element", "scrollStrategy", "_dir", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "EMPTY", "_hoverSubscription", "_handleTouchStart", "_openedBy", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "_scrollStrategy", "addEventListener", "_handleHover", "removeEventListener", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "_getPortal", "menuData", "_menuClosingActions", "_initMenu", "withLockedPosition", "reapplyLastPosition", "updatePosition", "_setIsMenuOpen", "_setMenuElevation", "isOpen", "config", "_getOverlayConfig", "_subscribeToPositions", "create", "keydownEvents", "position", "flexibleConnectedTo", "withGrowAfterOpen", "withTransformOriginOn", "position<PERSON><PERSON>es", "change", "connectionPair", "overlayX", "overlayY", "run", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "_parentInnerPadding", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "Directionality", "MatMenuTrigger", "MatMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {\n}));\n/**\n * Single item inside of a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem extends _MatMenuItemBase {\n    constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n        super();\n        this._elementRef = _elementRef;\n        this._document = _document;\n        this._focusMonitor = _focusMonitor;\n        this._parentMenu = _parentMenu;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** ARIA role for the menu item. */\n        this.role = 'menuitem';\n        /** Stream that emits when the menu item is hovered. */\n        this._hovered = new Subject();\n        /** Stream that emits when the menu item is focused. */\n        this._focused = new Subject();\n        /** Whether the menu item is highlighted. */\n        this._highlighted = false;\n        /** Whether the menu item acts as a trigger for a sub-menu. */\n        this._triggersSubmenu = false;\n        _parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n}\nMatMenuItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuItem, deps: [{ token: i0.ElementRef }, { token: DOCUMENT }, { token: i1.FocusMonitor }, { token: MAT_MENU_PANEL, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatMenuItem.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatMenuItem, selector: \"[mat-menu-item]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", role: \"role\" }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-mdc-focus-indicator mdc-list-item\" }, exportAs: [\"matMenuItem\"], usesInheritance: true, ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mdc-list-item__primary-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\", dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', inputs: ['disabled', 'disableRipple'], host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator mdc-list-item',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mdc-list-item__primary-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { role: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\nclass _MatMenuContentBase {\n    constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n        this._template = _template;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._injector = _injector;\n        this._viewContainerRef = _viewContainerRef;\n        this._document = _document;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Emits when the menu content has been attached. */\n        this._attached = new Subject();\n    }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        // @breaking-change 9.0.0 Make change detector ref required\n        this._changeDetectorRef?.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        if (this._outlet) {\n            this._outlet.dispose();\n        }\n    }\n}\n_MatMenuContentBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuContentBase, deps: [{ token: i0.TemplateRef }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.Injector }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuContentBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatMenuContentBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuContentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.Injector }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent extends _MatMenuContentBase {\n}\nMatMenuContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuContent, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatMenuContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatMenuContent, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: trigger('transformMenu', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(0.8)',\n        })),\n        transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1)',\n        }))),\n        transition('* => void', animate('100ms 25ms linear', style({ opacity: 0 }))),\n    ]),\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: trigger('fadeInItems', [\n        // TODO(crisbeto): this is inside the `transformMenu`\n        // now. Remove next time we do breaking changes.\n        state('showing', style({ opacity: 1 })),\n        transition('void => *', [\n            style({ opacity: 0 }),\n            animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\n/** Base class with all of the `MatMenu` functionality. */\nclass _MatMenuBase {\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** Whether the menu should overlap its trigger. */\n    get overlapTrigger() {\n        return this._overlapTrigger;\n    }\n    set overlapTrigger(value) {\n        this._overlapTrigger = coerceBooleanProperty(value);\n    }\n    /** Whether the menu has a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                this._classList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                this._classList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    constructor(_elementRef, _ngZone, _defaultOptions, \n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    _changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._xPosition = this._defaultOptions.xPosition;\n        this._yPosition = this._defaultOptions.yPosition;\n        /** Only the direct descendant menu items. */\n        this._directDescendantItems = new QueryList();\n        /** Config object to be passed into the menu's ngClass */\n        this._classList = {};\n        /** Current state of the panel animation. */\n        this._panelAnimationState = 'void';\n        /** Emits whenever an animation on the menu completes. */\n        this._animationDone = new Subject();\n        /** Class or list of classes to be added to the overlay panel. */\n        this.overlayPanelClass = this._defaultOptions.overlayPanelClass || '';\n        /** Class to be added to the backdrop element. */\n        this.backdropClass = this._defaultOptions.backdropClass;\n        this._overlapTrigger = this._defaultOptions.overlapTrigger;\n        this._hasBackdrop = this._defaultOptions.hasBackdrop;\n        /** Event emitted when the menu is closed. */\n        this.closed = new EventEmitter();\n        /**\n         * Event emitted when the menu is closed.\n         * @deprecated Switch to `closed` instead\n         * @breaking-change 8.0.0\n         */\n        this.close = this.closed;\n        this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusSubscription?.unsubscribe();\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n        // Don't allow the event to propagate if we've already handled it, or it may\n        // end up reaching other overlays that were opened earlier (see #22694).\n        event.stopPropagation();\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusSubscription?.unsubscribe();\n        this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            let menuPanel = null;\n            if (this._directDescendantItems.length) {\n                // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n                // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n                // because the panel is inside an `ng-template`. We work around it by starting from one of\n                // the items and walking up the DOM.\n                menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n            }\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * Sets the menu panel elevation.\n     * @param depth Number of parent menus that come before the menu.\n     */\n    setElevation(depth) {\n        // The elevation starts at the base and increases by one for each level.\n        // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n        const elevation = Math.min(this._baseElevation + depth, 24);\n        const newElevation = `${this._elevationPrefix}${elevation}`;\n        const customElevation = Object.keys(this._classList).find(className => {\n            return className.startsWith(this._elevationPrefix);\n        });\n        if (!customElevation || customElevation === this._previousElevation) {\n            if (this._previousElevation) {\n                this._classList[this._previousElevation] = false;\n            }\n            this._classList[newElevation] = true;\n            this._previousElevation = newElevation;\n        }\n    }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        const classes = this._classList;\n        classes['mat-menu-before'] = posX === 'before';\n        classes['mat-menu-after'] = posX === 'after';\n        classes['mat-menu-above'] = posY === 'above';\n        classes['mat-menu-below'] = posY === 'below';\n        // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n        this._changeDetectorRef?.markForCheck();\n    }\n    /** Starts the enter animation. */\n    _startAnimation() {\n        // @breaking-change 8.0.0 Combine with _resetAnimation.\n        this._panelAnimationState = 'enter';\n    }\n    /** Resets the panel animation to its initial state. */\n    _resetAnimation() {\n        // @breaking-change 8.0.0 Combine with _startAnimation.\n        this._panelAnimationState = 'void';\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(event) {\n        this._animationDone.next(event);\n        this._isAnimating = false;\n    }\n    _onAnimationStart(event) {\n        this._isAnimating = true;\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n            event.element.scrollTop = 0;\n        }\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n}\n_MatMenuBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuBase, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatMenuBase, inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: \"overlapTrigger\", hasBackdrop: \"hasBackdrop\", panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\nclass MatMenu extends _MatMenuBase {\n    constructor(_elementRef, _ngZone, _defaultOptions, changeDetectorRef) {\n        super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n        this._elevationPrefix = 'mat-elevation-z';\n        this._baseElevation = 8;\n    }\n}\nMatMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenu, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatMenu, selector: \"mat-menu\", host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], exportAs: [\"matMenu\"], usesInheritance: true, ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mdc-menu-surface mdc-menu-surface--open mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content mdc-list\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;position:relative}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item.mdc-list-item{align-items:center}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item .mdc-list-item__primary-text{white-space:normal}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mdc-menu-surface mdc-menu-surface--open mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content mdc-list\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\".mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform,opacity;z-index:8;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;position:relative}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item.mdc-list-item{align-items:center}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item .mdc-list-item__primary-text{white-space:normal}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\nclass _MatMenuTriggerBase {\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu, \n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n        this._overlay = _overlay;\n        this._element = _element;\n        this._viewContainerRef = _viewContainerRef;\n        this._menuItemInstance = _menuItemInstance;\n        this._dir = _dir;\n        this._focusMonitor = _focusMonitor;\n        this._ngZone = _ngZone;\n        this._overlayRef = null;\n        this._menuOpen = false;\n        this._closingActionsSubscription = Subscription.EMPTY;\n        this._hoverSubscription = Subscription.EMPTY;\n        this._menuCloseSubscription = Subscription.EMPTY;\n        this._changeDetectorRef = inject(ChangeDetectorRef);\n        /**\n         * Handles touch start events on the trigger.\n         * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n         */\n        this._handleTouchStart = (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        };\n        // Tracking input type is necessary so it's possible to only auto-focus\n        // the first item of the list when the menu is opened via the keyboard\n        this._openedBy = undefined;\n        /**\n         * Whether focus should be restored when the menu is closed.\n         * Note that disabling this option can have accessibility implications\n         * and it's up to you to manage focus, if you decide to turn it off.\n         */\n        this.restoreFocus = true;\n        /** Event emitted when the associated menu is opened. */\n        this.menuOpened = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is opened.\n         * @deprecated Switch to `menuOpened` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuOpen = this.menuOpened;\n        /** Event emitted when the associated menu is closed. */\n        this.menuClosed = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is closed.\n         * @deprecated Switch to `menuClosed` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuClose = this.menuClosed;\n        this._scrollStrategy = scrollStrategy;\n        this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n        _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n        this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        overlayRef.attach(this._getPortal(menu));\n        if (menu.lazyContent) {\n            menu.lazyContent.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        this._initMenu(menu);\n        if (menu instanceof _MatMenuBase) {\n            menu._startAnimation();\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        if (!this._overlayRef || !this.menuOpen) {\n            return;\n        }\n        const menu = this.menu;\n        this._closingActionsSubscription.unsubscribe();\n        this._overlayRef.detach();\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        if (menu instanceof _MatMenuBase) {\n            menu._resetAnimation();\n            if (menu.lazyContent) {\n                // Wait for the exit animation to finish before detaching the content.\n                menu._animationDone\n                    .pipe(filter(event => event.toState === 'void'), take(1), \n                // Interrupt if the content got re-attached.\n                takeUntil(menu.lazyContent._attached))\n                    .subscribe({\n                    next: () => menu.lazyContent.detach(),\n                    // No matter whether the content got re-attached, reset the menu.\n                    complete: () => this._setIsMenuOpen(false),\n                });\n            }\n            else {\n                this._setIsMenuOpen(false);\n            }\n        }\n        else {\n            this._setIsMenuOpen(false);\n            menu?.lazyContent?.detach();\n        }\n    }\n    /**\n     * This method sets the menu state to open and focuses the first item if\n     * the menu was opened via the keyboard.\n     */\n    _initMenu(menu) {\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        this._setMenuElevation(menu);\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n    }\n    /** Updates the menu elevation based on the amount of parent menus that it has. */\n    _setMenuElevation(menu) {\n        if (menu.setElevation) {\n            let depth = 0;\n            let parentMenu = menu.parentMenu;\n            while (parentMenu) {\n                depth++;\n                parentMenu = parentMenu.parentMenu;\n            }\n            menu.setElevation(depth);\n        }\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = this._overlay.create(config);\n            // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n            // Ideally we'd also have our keyboard event logic in here, however doing so will\n            // break anybody that may have implemented the `MatMenuPanel` themselves.\n            this._overlayRef.keydownEvents().subscribe();\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: this._overlay\n                .position()\n                .flexibleConnectedTo(this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                // @breaking-change 15.0.0 Remove null check for `ngZone`.\n                // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n                // updating something in the view so we need to bring it back in.\n                if (this._ngZone) {\n                    this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n                }\n                else {\n                    menu.setPositionClasses(posX, posY);\n                }\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n            return;\n        }\n        this._hoverSubscription = this._parentMaterialMenu\n            ._hovered()\n            // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n            // with different data and triggers), we have to delay it by a tick to ensure that\n            // it won't be closed immediately after it is opened.\n            .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler))\n            .subscribe(() => {\n            this._openedBy = 'mouse';\n            // If the same menu is used between multiple triggers, it might still be animating\n            // while the new trigger tries to re-open it. Wait for the animation to finish\n            // before doing so. Also interrupt if the user moves to another item.\n            if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n                // We need the `delay(0)` here in order to avoid\n                // 'changed after checked' errors in some cases. See #12194.\n                this.menu._animationDone\n                    .pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered()))\n                    .subscribe(() => this.openMenu());\n            }\n            else {\n                this.openMenu();\n            }\n        });\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n}\n_MatMenuTriggerBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuTriggerBase, deps: [{ token: i1$1.Overlay }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: MAT_MENU_SCROLL_STRATEGY }, { token: MAT_MENU_PANEL, optional: true }, { token: MatMenuItem, optional: true, self: true }, { token: i3$1.Directionality, optional: true }, { token: i1.FocusMonitor }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuTriggerBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: _MatMenuTriggerBase, inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu.panelId : null\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: _MatMenuTriggerBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_SCROLL_STRATEGY]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: MatMenuItem, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i3$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i1.FocusMonitor }, { type: i0.NgZone }]; }, propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger extends _MatMenuTriggerBase {\n}\nMatMenuTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatMenuTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatMenuTrigger, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", host: { classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatMenuModule {\n}\nMatMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuModule, declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger], imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule], exports: [CdkScrollableModule,\n        MatMenu,\n        MatCommonModule,\n        MatMenuItem,\n        MatMenuContent,\n        MatMenuTrigger] });\nMatMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAC5Q,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AACtH,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACtE,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AACrF,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC5G,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,mBAAmB,QAAQ,wBAAwB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAAA;EAAA;IAkHmGjE,EAAE,iBACyhC;IAD3hCA,EAAE,4BACyhC;IAD3hCA,EAAE,2BAC2jC;IAD7jCA,EAAE,eACikC;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA,YADnkCA,EAAE;IAAFA,EAAE,4BAwlBy3B;IAxlB33BA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAwlBod,6BAAsB;IAAA,EAAE;MAxlB9eA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAwlB4f,mBAAY,OAAO,CAAC;IAAA,EAApC;MAxlB9eA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAwlBgmB,gCAAyB;IAAA,EAA7I;MAxlB9eA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,aAwlBypB,+BAAwB;IAAA,EAArM;IAxlB9eA,EAAE,4BAwlB46B;IAxlB96BA,EAAE,gBAwlB68B;IAxlB/8BA,EAAE,eAwlBy9B;EAAA;EAAA;IAAA,eAxlB39BA,EAAE;IAAFA,EAAE,iCAwlBoa;IAxlBtaA,EAAE,oDAwlBwwB;EAAA;AAAA;AAAA;AAtsB72B,MAAMkE,cAAc,GAAG,IAAIjE,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkE,gBAAgB,GAAGtB,kBAAkB,CAACC,aAAa,CAAC,MAAM,EAC/D,CAAC,CAAC;AACH;AACA;AACA;AACA,MAAMsB,WAAW,SAASD,gBAAgB,CAAC;EACvCE,WAAW,CAACC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAE;IAChF,KAAK,EAAE;IACP,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI3C,OAAO,EAAE;IAC7B;IACA,IAAI,CAAC4C,QAAQ,GAAG,IAAI5C,OAAO,EAAE;IAC7B;IACA,IAAI,CAAC6C,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7BN,WAAW,EAAEO,OAAO,GAAG,IAAI,CAAC;EAChC;EACA;EACAC,KAAK,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACX,aAAa,IAAIU,MAAM,EAAE;MAC9B,IAAI,CAACV,aAAa,CAACY,QAAQ,CAAC,IAAI,CAACC,eAAe,EAAE,EAAEH,MAAM,EAAEC,OAAO,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACE,eAAe,EAAE,CAACJ,KAAK,CAACE,OAAO,CAAC;IACzC;IACA,IAAI,CAACN,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;EAC5B;EACAC,eAAe,GAAG;IACd,IAAI,IAAI,CAACf,aAAa,EAAE;MACpB;MACA;MACA;MACA,IAAI,CAACA,aAAa,CAACgB,OAAO,CAAC,IAAI,CAAClB,WAAW,EAAE,KAAK,CAAC;IACvD;EACJ;EACAmB,WAAW,GAAG;IACV,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACkB,cAAc,CAAC,IAAI,CAACpB,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAACG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACkB,UAAU,EAAE;MACjD,IAAI,CAAClB,WAAW,CAACkB,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACf,QAAQ,CAACgB,QAAQ,EAAE;IACxB,IAAI,CAACf,QAAQ,CAACe,QAAQ,EAAE;EAC5B;EACA;EACAC,YAAY,GAAG;IACX,OAAO,IAAI,CAACC,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAT,eAAe,GAAG;IACd,OAAO,IAAI,CAACf,WAAW,CAACyB,aAAa;EACzC;EACA;EACAC,cAAc,CAACC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACH,QAAQ,EAAE;MACfG,KAAK,CAACC,cAAc,EAAE;MACtBD,KAAK,CAACE,eAAe,EAAE;IAC3B;EACJ;EACA;EACAC,iBAAiB,GAAG;IAChB,IAAI,CAACxB,QAAQ,CAACU,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAe,QAAQ,GAAG;IACP,MAAMC,KAAK,GAAG,IAAI,CAAChC,WAAW,CAACyB,aAAa,CAACQ,SAAS,CAAC,IAAI,CAAC;IAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;IACjE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,EAAE;IACrB;IACA,OAAON,KAAK,CAACO,WAAW,EAAEC,IAAI,EAAE,IAAI,EAAE;EAC1C;EACAC,eAAe,CAACC,aAAa,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAI,CAAClC,YAAY,GAAGkC,aAAa;IACjC,IAAI,CAACtC,kBAAkB,EAAEuC,YAAY,EAAE;EAC3C;EACAC,mBAAmB,CAACC,eAAe,EAAE;IACjC;IACA,IAAI,CAACpC,gBAAgB,GAAGoC,eAAe;IACvC,IAAI,CAACzC,kBAAkB,EAAEuC,YAAY,EAAE;EAC3C;EACAG,SAAS,GAAG;IACR,OAAO,IAAI,CAAC7C,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC8C,aAAa,KAAK,IAAI,CAAChC,eAAe,EAAE;EACpF;AACJ;AACAjB,WAAW,CAACkD,IAAI;EAAA,iBAA6FlD,WAAW,EAArBpE,EAAE,mBAAqCA,EAAE,CAACuH,UAAU,GAApDvH,EAAE,mBAA+DkD,QAAQ,GAAzElD,EAAE,mBAAoFoB,EAAE,CAACoG,YAAY,GAArGxH,EAAE,mBAAgHkE,cAAc,MAAhIlE,EAAE,mBAA2JA,EAAE,CAACiB,iBAAiB;AAAA,CAA4C;AAChUmD,WAAW,CAACqD,IAAI,kBADmFzH,EAAE;EAAA,MACJoE,WAAW;EAAA;EAAA;EAAA;EAAA;IAAA;MADTpE,EAAE;QAAA,OACJ,0BAAsB;MAAA;QAAA,OAAtB,uBAAmB;MAAA;IAAA;IAAA;MADjBA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,gBACkrB;MADprBA,EAAE,6BACguB;MADluBA,EAAE,mBACyvB;MAD3vBA,EAAE,eACgwB;MADlwBA,EAAE,uBAC85B;MADh6BA,EAAE,+DACikC;IAAA;IAAA;MADnkCA,EAAE,aACs2B;MADx2BA,EAAE,mEACs2B;MADx2BA,EAAE,aACg8B;MADl8BA,EAAE,yCACg8B;IAAA;EAAA;EAAA,eAAgLiD,EAAE,CAACyE,IAAI,EAA6F9E,EAAE,CAAC+E,SAAS;EAAA;EAAA;AAAA,EAA6T;AACloD;EAAA,mDAFmG3H,EAAE,mBAELoE,WAAW,EAAc,CAAC;IAC9GwD,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEC,QAAQ,EAAE,aAAa;MAAEC,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;MAAEC,IAAI,EAAE;QAChG,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,yDAAyD;QAClE,uCAAuC,EAAE,cAAc;QACvD,2CAA2C,EAAE,kBAAkB;QAC/D,iBAAiB,EAAE,gBAAgB;QACnC,sBAAsB,EAAE,UAAU;QAClC,iBAAiB,EAAE,kBAAkB;QACrC,SAAS,EAAE,wBAAwB;QACnC,cAAc,EAAE;MACpB,CAAC;MAAEC,eAAe,EAAE/H,uBAAuB,CAACgI,MAAM;MAAEC,aAAa,EAAEhI,iBAAiB,CAACiI,IAAI;MAAEC,QAAQ,EAAE;IAAod,CAAC;EACtkB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAE5H,EAAE,CAACuH;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvFZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAAC3E,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE0E,IAAI,EAAExG,EAAE,CAACoG;IAAa,CAAC,EAAE;MAAEI,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC7DZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAAC3D,cAAc;MACzB,CAAC,EAAE;QACC0D,IAAI,EAAEtH;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsH,IAAI,EAAE5H,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0D,IAAI,EAAE,CAAC;MACnEiD,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkI,4BAA4B,GAAG;EACpC,MAAMC,KAAK,CAAE;AACjB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4B,GAAG;EACpC,MAAMD,KAAK,CAAE;AACjB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0B,GAAG;EAClC,MAAMF,KAAK,CAAE,gFAA+E,GACvF,sEAAqE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAI5I,cAAc,CAAC,gBAAgB,CAAC;AAC7D,MAAM6I,mBAAmB,CAAC;EACtBzE,WAAW,CAAC0E,SAAS,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,SAAS,EAAEC,iBAAiB,EAAE5E,SAAS,EAAEG,kBAAkB,EAAE;IACpH,IAAI,CAACqE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC5E,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAAC0E,SAAS,GAAG,IAAInH,OAAO,EAAE;EAClC;EACA;AACJ;AACA;AACA;EACIoH,MAAM,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAInG,cAAc,CAAC,IAAI,CAAC2F,SAAS,EAAE,IAAI,CAACI,iBAAiB,CAAC;IAC7E;IACA,IAAI,CAACK,MAAM,EAAE;IACb,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIpG,eAAe,CAAC,IAAI,CAACkB,SAAS,CAACmF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAACV,yBAAyB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IACzI;IACA,MAAMS,OAAO,GAAG,IAAI,CAACZ,SAAS,CAACa,UAAU,CAAC7D,aAAa;IACvD;IACA;IACA;IACA4D,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACL,OAAO,CAACM,aAAa,EAAEJ,OAAO,CAAC;IACpE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACjF,kBAAkB,EAAEuC,YAAY,EAAE;IACvC,IAAI,CAACsC,OAAO,CAACF,MAAM,CAAC,IAAI,CAACI,OAAO,EAAEH,OAAO,CAAC;IAC1C,IAAI,CAACF,SAAS,CAAC9D,IAAI,EAAE;EACzB;EACA;AACJ;AACA;AACA;EACIkE,MAAM,GAAG;IACL,IAAI,IAAI,CAACD,OAAO,CAACS,UAAU,EAAE;MACzB,IAAI,CAACT,OAAO,CAACC,MAAM,EAAE;IACzB;EACJ;EACA/D,WAAW,GAAG;IACV,IAAI,IAAI,CAACgE,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,EAAE;IAC1B;EACJ;AACJ;AACAnB,mBAAmB,CAACxB,IAAI;EAAA,iBAA6FwB,mBAAmB,EAjIrC9I,EAAE,mBAiIqDA,EAAE,CAACW,WAAW,GAjIrEX,EAAE,mBAiIgFA,EAAE,CAACkK,wBAAwB,GAjI7GlK,EAAE,mBAiIwHA,EAAE,CAACmK,cAAc,GAjI3InK,EAAE,mBAiIsJA,EAAE,CAACoK,QAAQ,GAjInKpK,EAAE,mBAiI8KA,EAAE,CAACqK,gBAAgB,GAjInMrK,EAAE,mBAiI8MkD,QAAQ,GAjIxNlD,EAAE,mBAiImOA,EAAE,CAACiB,iBAAiB;AAAA,CAA4C;AACxY6H,mBAAmB,CAACwB,IAAI,kBAlI2EtK,EAAE;EAAA,MAkII8I;AAAmB,EAAiB;AAC7I;EAAA,mDAnImG9I,EAAE,mBAmIL8I,mBAAmB,EAAc,CAAC;IACtHlB,IAAI,EAAEpH;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoH,IAAI,EAAE5H,EAAE,CAACW;IAAY,CAAC,EAAE;MAAEiH,IAAI,EAAE5H,EAAE,CAACkK;IAAyB,CAAC,EAAE;MAAEtC,IAAI,EAAE5H,EAAE,CAACmK;IAAe,CAAC,EAAE;MAAEvC,IAAI,EAAE5H,EAAE,CAACoK;IAAS,CAAC,EAAE;MAAExC,IAAI,EAAE5H,EAAE,CAACqK;IAAiB,CAAC,EAAE;MAAEzC,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClNZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAAC3E,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE0E,IAAI,EAAE5H,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;AACxD;AACA,MAAMsJ,cAAc,SAASzB,mBAAmB,CAAC;AAEjDyB,cAAc,CAACjD,IAAI;EAAA;EAAA;IAAA,sEA5IgFtH,EAAE,uBA4IWuK,cAAc,SAAdA,cAAc;EAAA;AAAA,GAAqD;AACnLA,cAAc,CAACD,IAAI,kBA7IgFtK,EAAE;EAAA,MA6IDuK,cAAc;EAAA;EAAA,WA7IfvK,EAAE,oBA6ImE,CAAC;IAAEwK,OAAO,EAAE3B,gBAAgB;IAAE4B,WAAW,EAAEF;EAAe,CAAC,CAAC,GA7IjIvK,EAAE;AAAA,EA6IuK;AAC5Q;EAAA,mDA9ImGA,EAAE,mBA8ILuK,cAAc,EAAc,CAAC;IACjH3C,IAAI,EAAEpH,SAAS;IACfqH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvC4C,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE3B,gBAAgB;QAAE4B,WAAW,EAAEF;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,GAAG;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAEtH,OAAO,CAAC,eAAe,EAAE,CACpCC,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChBqH,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHrH,UAAU,CAAC,eAAe,EAAEC,OAAO,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IAC1EqH,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJrH,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;IAAEqH,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC/E,CAAC;EACF;AACJ;AACA;AACA;EACIE,WAAW,EAAEzH,OAAO,CAAC,aAAa,EAAE;EAChC;EACA;EACAC,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IAAEqH,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACvCpH,UAAU,CAAC,WAAW,EAAE,CACpBD,KAAK,CAAC;IAAEqH,OAAO,EAAE;EAAE,CAAC,CAAC,EACrBnH,OAAO,CAAC,8CAA8C,CAAC,CAC1D,CAAC,CACL;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqH,WAAW,GAAGJ,iBAAiB,CAACI,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMH,aAAa,GAAGD,iBAAiB,CAACC,aAAa;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,wBAAwB,GAAG,IAAIhL,cAAc,CAAC,0BAA0B,EAAE;EAC5EiL,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,gCAAgC,GAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA;AACA,MAAMC,YAAY,CAAC;EACf;EACA,IAAIH,SAAS,GAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;EACA,IAAIJ,SAAS,CAACK,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjDnD,4BAA4B,EAAE;IAClC;IACA,IAAI,CAACiD,UAAU,GAAGC,KAAK;IACvB,IAAI,CAACE,kBAAkB,EAAE;EAC7B;EACA;EACA,IAAIN,SAAS,GAAG;IACZ,OAAO,IAAI,CAACO,UAAU;EAC1B;EACA,IAAIP,SAAS,CAACI,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3FjD,4BAA4B,EAAE;IAClC;IACA,IAAI,CAACmD,UAAU,GAAGH,KAAK;IACvB,IAAI,CAACE,kBAAkB,EAAE;EAC7B;EACA;EACA,IAAIR,cAAc,GAAG;IACjB,OAAO,IAAI,CAACU,eAAe;EAC/B;EACA,IAAIV,cAAc,CAACM,KAAK,EAAE;IACtB,IAAI,CAACI,eAAe,GAAGvK,qBAAqB,CAACmK,KAAK,CAAC;EACvD;EACA;EACA,IAAIK,WAAW,GAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAW,CAACL,KAAK,EAAE;IACnB,IAAI,CAACM,YAAY,GAAGzK,qBAAqB,CAACmK,KAAK,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIO,UAAU,CAACC,OAAO,EAAE;IACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IACnD,IAAID,kBAAkB,IAAIA,kBAAkB,CAACzF,MAAM,EAAE;MACjDyF,kBAAkB,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACjD,IAAI,CAACC,UAAU,CAACD,SAAS,CAAC,GAAG,KAAK;MACtC,CAAC,CAAC;IACN;IACA,IAAI,CAACH,mBAAmB,GAAGF,OAAO;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAACxF,MAAM,EAAE;MAC3BwF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACtC,IAAI,CAACC,UAAU,CAACD,SAAS,CAAC,GAAG,IAAI;MACrC,CAAC,CAAC;MACF,IAAI,CAAClI,WAAW,CAACyB,aAAa,CAACyG,SAAS,GAAG,EAAE;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIE,SAAS,GAAG;IACZ,OAAO,IAAI,CAACR,UAAU;EAC1B;EACA,IAAIQ,SAAS,CAACP,OAAO,EAAE;IACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;EAC7B;EACA9H,WAAW,CAACC,WAAW,EAAEqI,OAAO,EAAEC,eAAe;EACjD;EACAlI,kBAAkB,EAAE;IAChB,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAClI,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACgH,UAAU,GAAG,IAAI,CAACkB,eAAe,CAACtB,SAAS;IAChD,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACc,eAAe,CAACrB,SAAS;IAChD;IACA,IAAI,CAACsB,sBAAsB,GAAG,IAAIpM,SAAS,EAAE;IAC7C;IACA,IAAI,CAACgM,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACK,oBAAoB,GAAG,MAAM;IAClC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI9K,OAAO,EAAE;IACnC;IACA,IAAI,CAAC+K,iBAAiB,GAAG,IAAI,CAACJ,eAAe,CAACI,iBAAiB,IAAI,EAAE;IACrE;IACA,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACoB,eAAe,CAACpB,aAAa;IACvD,IAAI,CAACO,eAAe,GAAG,IAAI,CAACa,eAAe,CAACvB,cAAc;IAC1D,IAAI,CAACY,YAAY,GAAG,IAAI,CAACW,eAAe,CAACZ,WAAW;IACpD;IACA,IAAI,CAACiB,MAAM,GAAG,IAAIvM,YAAY,EAAE;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwM,KAAK,GAAG,IAAI,CAACD,MAAM;IACxB,IAAI,CAACE,OAAO,GAAI,kBAAiBnC,YAAY,EAAG,EAAC;EACrD;EACAoC,QAAQ,GAAG;IACP,IAAI,CAACvB,kBAAkB,EAAE;EAC7B;EACAwB,kBAAkB,GAAG;IACjB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAIlM,eAAe,CAAC,IAAI,CAACwL,sBAAsB,CAAC,CAC9DW,QAAQ,EAAE,CACVC,aAAa,EAAE,CACfC,cAAc,EAAE;IACrB,IAAI,CAACH,WAAW,CAACI,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACX,MAAM,CAACY,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE;IACA;IACA;IACA,IAAI,CAAChB,sBAAsB,CAACiB,OAAO,CAC9BC,IAAI,CAACzL,SAAS,CAAC,IAAI,CAACuK,sBAAsB,CAAC,EAAEtK,SAAS,CAACyL,KAAK,IAAI9L,KAAK,CAAC,GAAG8L,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACrJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9G+I,SAAS,CAACO,WAAW,IAAI,IAAI,CAACZ,WAAW,CAACa,gBAAgB,CAACD,WAAW,CAAC,CAAC;IAC7E,IAAI,CAACtB,sBAAsB,CAACiB,OAAO,CAACF,SAAS,CAAES,SAAS,IAAK;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,IAAI,CAACf,WAAW;MAChC,IAAI,IAAI,CAACT,oBAAoB,KAAK,OAAO,IAAIwB,OAAO,CAACC,UAAU,EAAEnH,SAAS,EAAE,EAAE;QAC1E,MAAM4G,KAAK,GAAGK,SAAS,CAACG,OAAO,EAAE;QACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACZ,KAAK,CAACrH,MAAM,GAAG,CAAC,EAAE2H,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;QACnF,IAAIb,KAAK,CAACS,KAAK,CAAC,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC,CAAC3I,QAAQ,EAAE;UACxCwI,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;QAChC,CAAC,MACI;UACDH,OAAO,CAACS,iBAAiB,EAAE;QAC/B;MACJ;IACJ,CAAC,CAAC;EACN;EACAtJ,WAAW,GAAG;IACV,IAAI,CAAC8H,WAAW,EAAEyB,OAAO,EAAE;IAC3B,IAAI,CAACnC,sBAAsB,CAACmC,OAAO,EAAE;IACrC,IAAI,CAAC/B,MAAM,CAACrH,QAAQ,EAAE;IACtB,IAAI,CAACqJ,2BAA2B,EAAEC,WAAW,EAAE;EACnD;EACA;EACAtK,QAAQ,GAAG;IACP;IACA,MAAMuK,WAAW,GAAG,IAAI,CAACtC,sBAAsB,CAACiB,OAAO;IACvD,OAAOqB,WAAW,CAACpB,IAAI,CAACzL,SAAS,CAAC,IAAI,CAACuK,sBAAsB,CAAC,EAAEtK,SAAS,CAACyL,KAAK,IAAI9L,KAAK,CAAC,GAAG8L,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACtJ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,OAAO,CAACoK,KAAK,EAAE,CAAE;EACjB;AACJ;AACA;AACA;AACA;AACA;EACIzJ,UAAU,CAACyJ,KAAK,EAAE,CAAE;EACpB;EACAC,cAAc,CAACpJ,KAAK,EAAE;IAClB,MAAMqJ,OAAO,GAAGrJ,KAAK,CAACqJ,OAAO;IAC7B,MAAMhB,OAAO,GAAG,IAAI,CAACf,WAAW;IAChC,QAAQ+B,OAAO;MACX,KAAKzN,MAAM;QACP,IAAI,CAACC,cAAc,CAACmE,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACC,cAAc,EAAE;UACtB,IAAI,CAAC+G,MAAM,CAACY,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKjM,UAAU;QACX,IAAI,IAAI,CAAC2N,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACvC,MAAM,CAACY,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKlM,WAAW;QACZ,IAAI,IAAI,CAAC4N,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACvC,MAAM,CAACY,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ;QACI,IAAIyB,OAAO,KAAK7N,QAAQ,IAAI6N,OAAO,KAAK5N,UAAU,EAAE;UAChD4M,OAAO,CAACmB,cAAc,CAAC,UAAU,CAAC;QACtC;QACAnB,OAAO,CAACoB,SAAS,CAACzJ,KAAK,CAAC;QACxB;IAAO;IAEf;IACA;IACAA,KAAK,CAACE,eAAe,EAAE;EAC3B;EACA;AACJ;AACA;AACA;EACIwJ,cAAc,CAACzK,MAAM,GAAG,SAAS,EAAE;IAC/B;IACA,IAAI,CAAC+J,2BAA2B,EAAEC,WAAW,EAAE;IAC/C,IAAI,CAACD,2BAA2B,GAAG,IAAI,CAACtC,OAAO,CAACiD,QAAQ,CAAC7B,IAAI,CAACvL,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoL,SAAS,CAAC,MAAM;MACnF,IAAIiC,SAAS,GAAG,IAAI;MACpB,IAAI,IAAI,CAAChD,sBAAsB,CAAClG,MAAM,EAAE;QACpC;QACA;QACA;QACA;QACAkJ,SAAS,GAAG,IAAI,CAAChD,sBAAsB,CAACiD,KAAK,CAACzK,eAAe,EAAE,CAAC0K,OAAO,CAAC,eAAe,CAAC;MAC5F;MACA;MACA,IAAI,CAACF,SAAS,IAAI,CAACA,SAAS,CAACG,QAAQ,CAACC,QAAQ,CAAC5I,aAAa,CAAC,EAAE;QAC3D,MAAMiH,OAAO,GAAG,IAAI,CAACf,WAAW;QAChCe,OAAO,CAACmB,cAAc,CAACvK,MAAM,CAAC,CAACgL,kBAAkB,EAAE;QACnD;QACA;QACA;QACA,IAAI,CAAC5B,OAAO,CAACC,UAAU,IAAIsB,SAAS,EAAE;UAClCA,SAAS,CAAC5K,KAAK,EAAE;QACrB;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIkL,eAAe,GAAG;IACd,IAAI,CAAC5C,WAAW,CAACuB,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIsB,YAAY,CAACC,KAAK,EAAE;IAChB;IACA;IACA,MAAMC,SAAS,GAAG5B,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC2B,cAAc,GAAGF,KAAK,EAAE,EAAE,CAAC;IAC3D,MAAMG,YAAY,GAAI,GAAE,IAAI,CAACC,gBAAiB,GAAEH,SAAU,EAAC;IAC3D,MAAMI,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnE,UAAU,CAAC,CAACoE,IAAI,CAACrE,SAAS,IAAI;MACnE,OAAOA,SAAS,CAACsE,UAAU,CAAC,IAAI,CAACL,gBAAgB,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACC,eAAe,IAAIA,eAAe,KAAK,IAAI,CAACK,kBAAkB,EAAE;MACjE,IAAI,IAAI,CAACA,kBAAkB,EAAE;QACzB,IAAI,CAACtE,UAAU,CAAC,IAAI,CAACsE,kBAAkB,CAAC,GAAG,KAAK;MACpD;MACA,IAAI,CAACtE,UAAU,CAAC+D,YAAY,CAAC,GAAG,IAAI;MACpC,IAAI,CAACO,kBAAkB,GAAGP,YAAY;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3E,kBAAkB,CAACmF,IAAI,GAAG,IAAI,CAAC1F,SAAS,EAAE2F,IAAI,GAAG,IAAI,CAAC1F,SAAS,EAAE;IAC7D,MAAMY,OAAO,GAAG,IAAI,CAACM,UAAU;IAC/BN,OAAO,CAAC,iBAAiB,CAAC,GAAG6E,IAAI,KAAK,QAAQ;IAC9C7E,OAAO,CAAC,gBAAgB,CAAC,GAAG6E,IAAI,KAAK,OAAO;IAC5C7E,OAAO,CAAC,gBAAgB,CAAC,GAAG8E,IAAI,KAAK,OAAO;IAC5C9E,OAAO,CAAC,gBAAgB,CAAC,GAAG8E,IAAI,KAAK,OAAO;IAC5C;IACA,IAAI,CAACvM,kBAAkB,EAAEuC,YAAY,EAAE;EAC3C;EACA;EACAiK,eAAe,GAAG;IACd;IACA,IAAI,CAACpE,oBAAoB,GAAG,OAAO;EACvC;EACA;EACAqE,eAAe,GAAG;IACd;IACA,IAAI,CAACrE,oBAAoB,GAAG,MAAM;EACtC;EACA;EACAsE,gBAAgB,CAACnL,KAAK,EAAE;IACpB,IAAI,CAAC8G,cAAc,CAACzH,IAAI,CAACW,KAAK,CAAC;IAC/B,IAAI,CAACoL,YAAY,GAAG,KAAK;EAC7B;EACAC,iBAAiB,CAACrL,KAAK,EAAE;IACrB,IAAI,CAACoL,YAAY,GAAG,IAAI;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIpL,KAAK,CAACsL,OAAO,KAAK,OAAO,IAAI,IAAI,CAAChE,WAAW,CAACsB,eAAe,KAAK,CAAC,EAAE;MACrE5I,KAAK,CAAC0D,OAAO,CAAC6H,SAAS,GAAG,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlE,wBAAwB,GAAG;IACvB,IAAI,CAACmE,SAAS,CAAC3D,OAAO,CACjBC,IAAI,CAACzL,SAAS,CAAC,IAAI,CAACmP,SAAS,CAAC,CAAC,CAC/B7D,SAAS,CAAEI,KAAK,IAAK;MACtB,IAAI,CAACnB,sBAAsB,CAAC6E,KAAK,CAAC1D,KAAK,CAACtL,MAAM,CAACwL,IAAI,IAAIA,IAAI,CAACzJ,WAAW,KAAK,IAAI,CAAC,CAAC;MAClF,IAAI,CAACoI,sBAAsB,CAAC8E,eAAe,EAAE;IACjD,CAAC,CAAC;EACN;AACJ;AACAlG,YAAY,CAACnE,IAAI;EAAA,iBAA6FmE,YAAY,EAhiBvBzL,EAAE,mBAgiBuCA,EAAE,CAACuH,UAAU,GAhiBtDvH,EAAE,mBAgiBiEA,EAAE,CAAC4R,MAAM,GAhiB5E5R,EAAE,mBAgiBuFiL,wBAAwB,GAhiBjHjL,EAAE,mBAgiB4HA,EAAE,CAACiB,iBAAiB;AAAA,CAA4C;AACjSwK,YAAY,CAACnB,IAAI,kBAjiBkFtK,EAAE;EAAA,MAiiBHyL,YAAY;EAAA;IAAA;MAjiBXzL,EAAE,0BAiiB2e6I,gBAAgB;MAjiB7f7I,EAAE,0BAiiB0jBoE,WAAW;MAjiBvkBpE,EAAE,0BAiiBgoBoE,WAAW;IAAA;IAAA;MAAA;MAjiB7oBpE,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;MAAFA,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,aAiiBqtBW,WAAW;IAAA;IAAA;MAAA;MAjiBluBX,EAAE,qBAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA,EAiiBuwB;AAC52B;EAAA,mDAliBmGA,EAAE,mBAkiBLyL,YAAY,EAAc,CAAC;IAC/G7D,IAAI,EAAEpH;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoH,IAAI,EAAE5H,EAAE,CAACuH;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE5H,EAAE,CAAC4R;IAAO,CAAC,EAAE;MAAEhK,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5GZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAACoD,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAErD,IAAI,EAAE5H,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEwQ,SAAS,EAAE,CAAC;MACxE7J,IAAI,EAAEhH,eAAe;MACrBiH,IAAI,EAAE,CAACzD,WAAW,EAAE;QAAEyN,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAErG,aAAa,EAAE,CAAC;MAChB5D,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuR,SAAS,EAAE,CAAC;MACZlK,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEkK,cAAc,EAAE,CAAC;MACjBnK,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEmK,eAAe,EAAE,CAAC;MAClBpK,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEyD,SAAS,EAAE,CAAC;MACZ1D,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEgL,SAAS,EAAE,CAAC;MACZ3D,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE0R,WAAW,EAAE,CAAC;MACdrK,IAAI,EAAE/G,SAAS;MACfgH,IAAI,EAAE,CAAClH,WAAW;IACtB,CAAC,CAAC;IAAEqN,KAAK,EAAE,CAAC;MACRpG,IAAI,EAAEhH,eAAe;MACrBiH,IAAI,EAAE,CAACzD,WAAW,EAAE;QAAEyN,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEK,WAAW,EAAE,CAAC;MACdtK,IAAI,EAAE9G,YAAY;MAClB+G,IAAI,EAAE,CAACgB,gBAAgB;IAC3B,CAAC,CAAC;IAAEwC,cAAc,EAAE,CAAC;MACjBzD,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEyL,WAAW,EAAE,CAAC;MACdpE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE2L,UAAU,EAAE,CAAC;MACbtE,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE6E,SAAS,EAAE,CAAC;MACZ9E,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE0M,MAAM,EAAE,CAAC;MACTrF,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAEmM,KAAK,EAAE,CAAC;MACRtF,IAAI,EAAE7G;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMoR,OAAO,SAAS1G,YAAY,CAAC;EAC/BpH,WAAW,CAACC,WAAW,EAAEqI,OAAO,EAAEC,eAAe,EAAEwF,iBAAiB,EAAE;IAClE,KAAK,CAAC9N,WAAW,EAAEqI,OAAO,EAAEC,eAAe,EAAEwF,iBAAiB,CAAC;IAC/D,IAAI,CAAC3B,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACF,cAAc,GAAG,CAAC;EAC3B;AACJ;AACA4B,OAAO,CAAC7K,IAAI;EAAA,iBAA6F6K,OAAO,EAvlBbnS,EAAE,mBAulB6BA,EAAE,CAACuH,UAAU,GAvlB5CvH,EAAE,mBAulBuDA,EAAE,CAAC4R,MAAM,GAvlBlE5R,EAAE,mBAulB6EiL,wBAAwB,GAvlBvGjL,EAAE,mBAulBkHA,EAAE,CAACiB,iBAAiB;AAAA,CAA4C;AACvRkR,OAAO,CAAC1K,IAAI,kBAxlBuFzH,EAAE;EAAA,MAwlBRmS,OAAO;EAAA;EAAA;EAAA;IAAA;MAxlBDnS,EAAE;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE,oBAwlBwJ,CAAC;IAAEwK,OAAO,EAAEtG,cAAc;IAAEuG,WAAW,EAAE0H;EAAQ,CAAC,CAAC,GAxlB7MnS,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,mEAwlBm/B;IAAA;EAAA;EAAA,eAAohGiD,EAAE,CAACoP,OAAO;EAAA;EAAA;EAAA;IAAA,WAAsE,CAAC1H,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACI,WAAW;EAAC;EAAA;AAAA,EAAiG;AAC71I;EAAA,mDAzlBmG/K,EAAE,mBAylBLmS,OAAO,EAAc,CAAC;IAC1GvK,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEI,eAAe,EAAE/H,uBAAuB,CAACgI,MAAM;MAAEC,aAAa,EAAEhI,iBAAiB,CAACiI,IAAI;MAAEN,QAAQ,EAAE,SAAS;MAAEE,IAAI,EAAE;QACtI,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE;MAC/B,CAAC;MAAEqK,UAAU,EAAE,CAAC3H,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACI,WAAW,CAAC;MAAEL,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEtG,cAAc;QAAEuG,WAAW,EAAE0H;MAAQ,CAAC,CAAC;MAAE7J,QAAQ,EAAE,muBAAmuB;MAAEiK,MAAM,EAAE,CAAC,y9FAAy9F;IAAE,CAAC;EACj3H,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3K,IAAI,EAAE5H,EAAE,CAACuH;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE5H,EAAE,CAAC4R;IAAO,CAAC,EAAE;MAAEhK,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5GZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAACoD,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAErD,IAAI,EAAE5H,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuR,wBAAwB,GAAG,IAAIvS,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA,SAASwS,gCAAgC,CAACC,OAAO,EAAE;EAC/C,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,EAAE;AACtD;AACA;AACA,MAAMC,yCAAyC,GAAG;EAC9CrI,OAAO,EAAEgI,wBAAwB;EACjCM,IAAI,EAAE,CAACjP,OAAO,CAAC;EACfkP,UAAU,EAAEN;AAChB,CAAC;AACD;AACA,MAAMO,2BAA2B,GAAGhP,+BAA+B,CAAC;EAAEiP,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;EACI,IAAIC,4BAA4B,GAAG;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,4BAA4B,CAACE,CAAC,EAAE;IAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;EACjB;EACA;EACA,IAAID,IAAI,GAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIF,IAAI,CAACA,IAAI,EAAE;IACX,IAAIA,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,sBAAsB,CAACtE,WAAW,EAAE;IACzC,IAAImE,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACI,mBAAmB,KAAK,OAAO7H,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACtFhD,0BAA0B,EAAE;MAChC;MACA,IAAI,CAAC4K,sBAAsB,GAAGH,IAAI,CAACnG,KAAK,CAACU,SAAS,CAAE8F,MAAM,IAAK;QAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QACzB;QACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACD,mBAAmB,EAAE;UACtE,IAAI,CAACA,mBAAmB,CAACxG,MAAM,CAACY,IAAI,CAAC6F,MAAM,CAAC;QAChD;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACE,iBAAiB,EAAE1M,mBAAmB,CAAC,IAAI,CAACC,eAAe,EAAE,CAAC;EACvE;EACA9C,WAAW,CAACwP,QAAQ,EAAEC,QAAQ,EAAE3K,iBAAiB,EAAE4K,cAAc,EAAExE,UAAU;EAC7E;EACA;EACAqE,iBAAiB,EAAEI,IAAI,EAAExP,aAAa,EAAEmI,OAAO,EAAE;IAC7C,IAAI,CAACkH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3K,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACyK,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACxP,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACmI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsH,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,2BAA2B,GAAGhS,YAAY,CAACiS,KAAK;IACrD,IAAI,CAACC,kBAAkB,GAAGlS,YAAY,CAACiS,KAAK;IAC5C,IAAI,CAACZ,sBAAsB,GAAGrR,YAAY,CAACiS,KAAK;IAChD,IAAI,CAAC1P,kBAAkB,GAAG1D,MAAM,CAACC,iBAAiB,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACqT,iBAAiB,GAAIrO,KAAK,IAAK;MAChC,IAAI,CAAC3E,gCAAgC,CAAC2E,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACsO,SAAS,GAAG,OAAO;MAC5B;IACJ,CAAC;IACD;IACA;IACA,IAAI,CAACA,SAAS,GAAGhM,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiM,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,UAAU,GAAG,IAAI/T,YAAY,EAAE;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACgU,UAAU,GAAG,IAAI,CAACD,UAAU;IACjC;IACA,IAAI,CAACE,UAAU,GAAG,IAAIjU,YAAY,EAAE;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACkU,WAAW,GAAG,IAAI,CAACD,UAAU;IAClC,IAAI,CAACE,eAAe,GAAGd,cAAc;IACrC,IAAI,CAACN,mBAAmB,GAAGlE,UAAU,YAAY9D,YAAY,GAAG8D,UAAU,GAAGhH,SAAS;IACtFuL,QAAQ,CAAC/N,aAAa,CAAC+O,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACR,iBAAiB,EAAEtB,2BAA2B,CAAC;EAC9G;EACA3F,kBAAkB,GAAG;IACjB,IAAI,CAAC0H,YAAY,EAAE;EACvB;EACAtP,WAAW,GAAG;IACV,IAAI,IAAI,CAACwO,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAChK,OAAO,EAAE;MAC1B,IAAI,CAACgK,WAAW,GAAG,IAAI;IAC3B;IACA,IAAI,CAACH,QAAQ,CAAC/N,aAAa,CAACiP,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACV,iBAAiB,EAAEtB,2BAA2B,CAAC;IAClH,IAAI,CAACQ,sBAAsB,CAACtE,WAAW,EAAE;IACzC,IAAI,CAACiF,2BAA2B,CAACjF,WAAW,EAAE;IAC9C,IAAI,CAACmF,kBAAkB,CAACnF,WAAW,EAAE;EACzC;EACA;EACA,IAAI+F,QAAQ,GAAG;IACX,OAAO,IAAI,CAACf,SAAS;EACzB;EACA;EACA,IAAIgB,GAAG,GAAG;IACN,OAAO,IAAI,CAAClB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrI,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAxE,eAAe,GAAG;IACd,OAAO,CAAC,EAAE,IAAI,CAACyM,iBAAiB,IAAI,IAAI,CAACH,mBAAmB,IAAI,IAAI,CAACJ,IAAI,CAAC;EAC9E;EACA;EACA8B,UAAU,GAAG;IACT,OAAO,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACkB,SAAS,EAAE,GAAG,IAAI,CAACC,QAAQ,EAAE;EAC9D;EACA;EACAA,QAAQ,GAAG;IACP,MAAMhC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAACa,SAAS,IAAI,CAACb,IAAI,EAAE;MACzB;IACJ;IACA,MAAMiC,UAAU,GAAG,IAAI,CAACC,cAAc,CAAClC,IAAI,CAAC;IAC5C,MAAMmC,aAAa,GAAGF,UAAU,CAACG,SAAS,EAAE;IAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;IACvD,IAAI,CAACC,YAAY,CAACtC,IAAI,EAAEqC,gBAAgB,CAAC;IACzCF,aAAa,CAACxJ,WAAW,GACrBqH,IAAI,CAACrH,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC7E,eAAe,EAAE,GAAGkM,IAAI,CAACrH,WAAW;IACzEsJ,UAAU,CAACjM,MAAM,CAAC,IAAI,CAACuM,UAAU,CAACvC,IAAI,CAAC,CAAC;IACxC,IAAIA,IAAI,CAACnB,WAAW,EAAE;MAClBmB,IAAI,CAACnB,WAAW,CAAC7I,MAAM,CAAC,IAAI,CAACwM,QAAQ,CAAC;IAC1C;IACA,IAAI,CAAC1B,2BAA2B,GAAG,IAAI,CAAC2B,mBAAmB,EAAE,CAAClI,SAAS,CAAC,MAAM,IAAI,CAACwH,SAAS,EAAE,CAAC;IAC/F,IAAI,CAACW,SAAS,CAAC1C,IAAI,CAAC;IACpB,IAAIA,IAAI,YAAY5H,YAAY,EAAE;MAC9B4H,IAAI,CAACnC,eAAe,EAAE;MACtBmC,IAAI,CAACxG,sBAAsB,CAACiB,OAAO,CAACC,IAAI,CAACtL,SAAS,CAAC4Q,IAAI,CAACnG,KAAK,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;QAC5E;QACA;QACA8H,gBAAgB,CAACM,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,EAAE;QAChEP,gBAAgB,CAACM,kBAAkB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ;EACA;EACAZ,SAAS,GAAG;IACR,IAAI,CAAC/B,IAAI,EAAEnG,KAAK,CAACW,IAAI,EAAE;EAC3B;EACA;AACJ;AACA;AACA;EACI5I,KAAK,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACX,aAAa,IAAIU,MAAM,EAAE;MAC9B,IAAI,CAACV,aAAa,CAACY,QAAQ,CAAC,IAAI,CAAC0O,QAAQ,EAAE5O,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAC2O,QAAQ,CAAC/N,aAAa,CAACd,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;EACI+Q,cAAc,GAAG;IACb,IAAI,CAACjC,WAAW,EAAEiC,cAAc,EAAE;EACtC;EACA;EACAvC,YAAY,CAACD,MAAM,EAAE;IACjB,IAAI,CAAC,IAAI,CAACO,WAAW,IAAI,CAAC,IAAI,CAACgB,QAAQ,EAAE;MACrC;IACJ;IACA,MAAM5B,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACc,2BAA2B,CAACjF,WAAW,EAAE;IAC9C,IAAI,CAAC+E,WAAW,CAACzK,MAAM,EAAE;IACzB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACgL,YAAY,KAAKd,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACa,SAAS,IAAI,CAAC,IAAI,CAACpN,eAAe,EAAE,CAAC,EAAE;MAC3F,IAAI,CAAClC,KAAK,CAAC,IAAI,CAACsP,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGhM,SAAS;IAC1B,IAAI8K,IAAI,YAAY5H,YAAY,EAAE;MAC9B4H,IAAI,CAAClC,eAAe,EAAE;MACtB,IAAIkC,IAAI,CAACnB,WAAW,EAAE;QAClB;QACAmB,IAAI,CAACtG,cAAc,CACdgB,IAAI,CAACrL,MAAM,CAACuD,KAAK,IAAIA,KAAK,CAACsL,OAAO,KAAK,MAAM,CAAC,EAAE/O,IAAI,CAAC,CAAC,CAAC;QAC5D;QACAC,SAAS,CAAC4Q,IAAI,CAACnB,WAAW,CAAC9I,SAAS,CAAC,CAAC,CACjCwE,SAAS,CAAC;UACXtI,IAAI,EAAE,MAAM+N,IAAI,CAACnB,WAAW,CAAC1I,MAAM,EAAE;UACrC;UACA5D,QAAQ,EAAE,MAAM,IAAI,CAACuQ,cAAc,CAAC,KAAK;QAC7C,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC9B;IACJ,CAAC,MACI;MACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC1B9C,IAAI,EAAEnB,WAAW,EAAE1I,MAAM,EAAE;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACIuM,SAAS,CAAC1C,IAAI,EAAE;IACZA,IAAI,CAAC9D,UAAU,GAAG,IAAI,CAACpI,eAAe,EAAE,GAAG,IAAI,CAACsM,mBAAmB,GAAGlL,SAAS;IAC/E8K,IAAI,CAAC7D,SAAS,GAAG,IAAI,CAAC0F,GAAG;IACzB,IAAI,CAACkB,iBAAiB,CAAC/C,IAAI,CAAC;IAC5BA,IAAI,CAAC1D,cAAc,CAAC,IAAI,CAAC4E,SAAS,IAAI,SAAS,CAAC;IAChD,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC;EAC7B;EACA;EACAC,iBAAiB,CAAC/C,IAAI,EAAE;IACpB,IAAIA,IAAI,CAACjD,YAAY,EAAE;MACnB,IAAIC,KAAK,GAAG,CAAC;MACb,IAAId,UAAU,GAAG8D,IAAI,CAAC9D,UAAU;MAChC,OAAOA,UAAU,EAAE;QACfc,KAAK,EAAE;QACPd,UAAU,GAAGA,UAAU,CAACA,UAAU;MACtC;MACA8D,IAAI,CAACjD,YAAY,CAACC,KAAK,CAAC;IAC5B;EACJ;EACA;EACA8F,cAAc,CAACE,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK,IAAI,CAACnC,SAAS,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAGmC,MAAM;MACvB,IAAI,CAACnC,SAAS,GAAG,IAAI,CAACO,UAAU,CAAC5G,IAAI,EAAE,GAAG,IAAI,CAAC8G,UAAU,CAAC9G,IAAI,EAAE;MAChE,IAAI,IAAI,CAAC1G,eAAe,EAAE,EAAE;QACxB,IAAI,CAACyM,iBAAiB,CAAC7M,eAAe,CAACsP,MAAM,CAAC;MAClD;MACA,IAAI,CAAC3R,kBAAkB,CAACuC,YAAY,EAAE;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACIsO,cAAc,CAAClC,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACY,WAAW,EAAE;MACnB,MAAMqC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAClD,IAAI,CAAC;MAC3C,IAAI,CAACmD,qBAAqB,CAACnD,IAAI,EAAEiD,MAAM,CAACZ,gBAAgB,CAAC;MACzD,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACJ,QAAQ,CAAC4C,MAAM,CAACH,MAAM,CAAC;MAC/C;MACA;MACA;MACA,IAAI,CAACrC,WAAW,CAACyC,aAAa,EAAE,CAAC9I,SAAS,EAAE;IAChD;IACA,OAAO,IAAI,CAACqG,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIsC,iBAAiB,CAAClD,IAAI,EAAE;IACpB,OAAO,IAAIvP,aAAa,CAAC;MACrB4R,gBAAgB,EAAE,IAAI,CAAC7B,QAAQ,CAC1B8C,QAAQ,EAAE,CACVC,mBAAmB,CAAC,IAAI,CAAC9C,QAAQ,CAAC,CAClCkC,kBAAkB,EAAE,CACpBa,iBAAiB,EAAE,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;MAClEtL,aAAa,EAAE6H,IAAI,CAAC7H,aAAa,IAAI,kCAAkC;MACvEU,UAAU,EAAEmH,IAAI,CAACrG,iBAAiB;MAClC+G,cAAc,EAAE,IAAI,CAACc,eAAe,EAAE;MACtCrF,SAAS,EAAE,IAAI,CAACwE;IACpB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIwC,qBAAqB,CAACnD,IAAI,EAAEsD,QAAQ,EAAE;IAClC,IAAItD,IAAI,CAACxH,kBAAkB,EAAE;MACzB8K,QAAQ,CAACI,eAAe,CAACnJ,SAAS,CAACoJ,MAAM,IAAI;QACzC,MAAMhG,IAAI,GAAGgG,MAAM,CAACC,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;QAC5E,MAAMjG,IAAI,GAAG+F,MAAM,CAACC,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;QACzE;QACA;QACA;QACA,IAAI,IAAI,CAACxK,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACyK,GAAG,CAAC,MAAM/D,IAAI,CAACxH,kBAAkB,CAACmF,IAAI,EAAEC,IAAI,CAAC,CAAC;QAC/D,CAAC,MACI;UACDoC,IAAI,CAACxH,kBAAkB,CAACmF,IAAI,EAAEC,IAAI,CAAC;QACvC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0E,YAAY,CAACtC,IAAI,EAAEqC,gBAAgB,EAAE;IACjC,IAAI,CAAC2B,OAAO,EAAEC,eAAe,CAAC,GAAGjE,IAAI,CAAC/H,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;IAClG,IAAI,CAAC6L,QAAQ,EAAEI,gBAAgB,CAAC,GAAGlE,IAAI,CAAC9H,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IACrG,IAAI,CAACiM,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACN,QAAQ,EAAEI,gBAAgB,CAAC;IAC7D,IAAI,CAACL,QAAQ,EAAEQ,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;IAC7D,IAAIK,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAACxQ,eAAe,EAAE,EAAE;MACxB;MACA;MACAuQ,gBAAgB,GAAGL,OAAO,GAAGhE,IAAI,CAAC/H,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;MAC1EgM,eAAe,GAAGJ,QAAQ,GAAGG,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;MAChE,IAAI,IAAI,CAAC5D,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAACmE,mBAAmB,IAAI,IAAI,EAAE;UAClC,MAAMC,SAAS,GAAG,IAAI,CAACpE,mBAAmB,CAACzF,KAAK,CAAC8B,KAAK;UACtD,IAAI,CAAC8H,mBAAmB,GAAGC,SAAS,GAAGA,SAAS,CAACxS,eAAe,EAAE,CAACyS,SAAS,GAAG,CAAC;QACpF;QACAH,OAAO,GAAGR,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACS,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MAC1F;IACJ,CAAC,MACI,IAAI,CAACvE,IAAI,CAAChI,cAAc,EAAE;MAC3BmM,OAAO,GAAGL,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC/CM,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;IACnE;IACA7B,gBAAgB,CAACqC,aAAa,CAAC,CAC3B;MAAEV,OAAO;MAAEG,OAAO;MAAEN,QAAQ;MAAEC,QAAQ;MAAEQ;IAAQ,CAAC,EACjD;MAAEN,OAAO,EAAEC,eAAe;MAAEE,OAAO;MAAEN,QAAQ,EAAEQ,gBAAgB;MAAEP,QAAQ;MAAEQ;IAAQ,CAAC,EACpF;MACIN,OAAO;MACPG,OAAO,EAAEC,eAAe;MACxBP,QAAQ;MACRC,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,EACD;MACIN,OAAO,EAAEC,eAAe;MACxBE,OAAO,EAAEC,eAAe;MACxBP,QAAQ,EAAEQ,gBAAgB;MAC1BP,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,CACJ,CAAC;EACN;EACA;EACA7B,mBAAmB,GAAG;IAClB,MAAMkC,QAAQ,GAAG,IAAI,CAAC/D,WAAW,CAACgE,aAAa,EAAE;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACjE,WAAW,CAACiE,WAAW,EAAE;IAClD,MAAMC,WAAW,GAAG,IAAI,CAAC1E,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACxG,MAAM,GAAG7K,EAAE,EAAE;IACrF,MAAMgW,KAAK,GAAG,IAAI,CAAC3E,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CAAC7O,QAAQ,EAAE,CAACmJ,IAAI,CAACrL,MAAM,CAAC2V,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,CAAC,EAAElR,MAAM,CAAC,MAAM,IAAI,CAACwR,SAAS,CAAC,CAAC,GAC3H9R,EAAE,EAAE;IACV,OAAOF,KAAK,CAAC8V,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;EAC3D;EACA;EACAI,gBAAgB,CAACrS,KAAK,EAAE;IACpB,IAAI,CAAC1E,+BAA+B,CAAC0E,KAAK,CAAC,EAAE;MACzC;MACA;MACA,IAAI,CAACsO,SAAS,GAAGtO,KAAK,CAACsS,MAAM,KAAK,CAAC,GAAG,OAAO,GAAGhQ,SAAS;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAACpB,eAAe,EAAE,EAAE;QACxBlB,KAAK,CAACC,cAAc,EAAE;MAC1B;IACJ;EACJ;EACA;EACAmJ,cAAc,CAACpJ,KAAK,EAAE;IAClB,MAAMqJ,OAAO,GAAGrJ,KAAK,CAACqJ,OAAO;IAC7B;IACA,IAAIA,OAAO,KAAKvN,KAAK,IAAIuN,OAAO,KAAKtN,KAAK,EAAE;MACxC,IAAI,CAACuS,SAAS,GAAG,UAAU;IAC/B;IACA,IAAI,IAAI,CAACpN,eAAe,EAAE,KACpBmI,OAAO,KAAK3N,WAAW,IAAI,IAAI,CAACuT,GAAG,KAAK,KAAK,IAC1C5F,OAAO,KAAK1N,UAAU,IAAI,IAAI,CAACsT,GAAG,KAAK,KAAM,CAAC,EAAE;MACrD,IAAI,CAACX,SAAS,GAAG,UAAU;MAC3B,IAAI,CAACc,QAAQ,EAAE;IACnB;EACJ;EACA;EACAmD,YAAY,CAACvS,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkB,eAAe,EAAE,EAAE;MACxB;MACAlB,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI,CAACkP,QAAQ,EAAE;IACnB,CAAC,MACI;MACD,IAAI,CAACF,UAAU,EAAE;IACrB;EACJ;EACA;EACAJ,YAAY,GAAG;IACX;IACA,IAAI,CAAC,IAAI,CAAC5N,eAAe,EAAE,IAAI,CAAC,IAAI,CAACsM,mBAAmB,EAAE;MACtD;IACJ;IACA,IAAI,CAACY,kBAAkB,GAAG,IAAI,CAACZ,mBAAmB,CAC7C7O,QAAQ;IACT;IACA;IACA;IAAA,CACCmJ,IAAI,CAACrL,MAAM,CAAC2V,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,IAAI,CAACyE,MAAM,CAACvS,QAAQ,CAAC,EAAEnD,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,CAAC,CACtGuL,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC2G,SAAS,GAAG,OAAO;MACxB;MACA;MACA;MACA,IAAI,IAAI,CAAClB,IAAI,YAAY5H,YAAY,IAAI,IAAI,CAAC4H,IAAI,CAAChC,YAAY,EAAE;QAC7D;QACA;QACA,IAAI,CAACgC,IAAI,CAACtG,cAAc,CACnBgB,IAAI,CAACvL,IAAI,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,EAAEI,SAAS,CAAC,IAAI,CAACgR,mBAAmB,CAAC7O,QAAQ,EAAE,CAAC,CAAC,CACtFgJ,SAAS,CAAC,MAAM,IAAI,CAACyH,QAAQ,EAAE,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,EAAE;MACnB;IACJ,CAAC,CAAC;EACN;EACA;EACAO,UAAU,CAACvC,IAAI,EAAE;IACb;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC9J,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC0I,WAAW,KAAKoB,IAAI,CAACpB,WAAW,EAAE;MAChE,IAAI,CAAC1I,OAAO,GAAG,IAAInG,cAAc,CAACiQ,IAAI,CAACpB,WAAW,EAAE,IAAI,CAAC9I,iBAAiB,CAAC;IAC/E;IACA,OAAO,IAAI,CAACI,OAAO;EACvB;AACJ;AACA4J,mBAAmB,CAAC7L,IAAI;EAAA,iBAA6F6L,mBAAmB,EApjCrCnT,EAAE,mBAojCqD4D,IAAI,CAACC,OAAO,GApjCnE7D,EAAE,mBAojC8EA,EAAE,CAACuH,UAAU,GApjC7FvH,EAAE,mBAojCwGA,EAAE,CAACqK,gBAAgB,GApjC7HrK,EAAE,mBAojCwIwS,wBAAwB,GApjClKxS,EAAE,mBAojC6KkE,cAAc,MApjC7LlE,EAAE,mBAojCwNoE,WAAW,OApjCrOpE,EAAE,mBAojC4Q2D,IAAI,CAAC8U,cAAc,MApjCjSzY,EAAE,mBAojC4ToB,EAAE,CAACoG,YAAY,GApjC7UxH,EAAE,mBAojCwVA,EAAE,CAAC4R,MAAM;AAAA,CAA4C;AAClfuB,mBAAmB,CAAC7I,IAAI,kBArjC2EtK,EAAE;EAAA,MAqjCImT,mBAAmB;EAAA;EAAA;IAAA;MArjCzBnT,EAAE;QAAA,OAqjCI,wBAAoB;MAAA;QAAA,OAApB,4BAAwB;MAAA;QAAA,OAAxB,0BAAsB;MAAA;IAAA;IAAA;MArjC5BA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA,EAqjCsrB;AAC3xB;EAAA,mDAtjCmGA,EAAE,mBAsjCLmT,mBAAmB,EAAc,CAAC;IACtHvL,IAAI,EAAEpH,SAAS;IACfqH,IAAI,EAAE,CAAC;MACCI,IAAI,EAAE;QACF,sBAAsB,EAAE,sBAAsB;QAC9C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,gCAAgC;QACxD,SAAS,EAAE,sBAAsB;QACjC,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE;MACjB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEL,IAAI,EAAEhE,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAE+D,IAAI,EAAE5H,EAAE,CAACuH;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE5H,EAAE,CAACqK;IAAiB,CAAC,EAAE;MAAEzC,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9IZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAAC2K,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAE5K,IAAI,EAAEW,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCZ,IAAI,EAAEvH,MAAM;QACZwH,IAAI,EAAE,CAAC3D,cAAc;MACzB,CAAC,EAAE;QACC0D,IAAI,EAAEtH;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsH,IAAI,EAAExD,WAAW;MAAEoE,UAAU,EAAE,CAAC;QACpCZ,IAAI,EAAEtH;MACV,CAAC,EAAE;QACCsH,IAAI,EAAE1G;MACV,CAAC;IAAE,CAAC,EAAE;MAAE0G,IAAI,EAAEjE,IAAI,CAAC8U,cAAc;MAAEjQ,UAAU,EAAE,CAAC;QAC5CZ,IAAI,EAAEtH;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsH,IAAI,EAAExG,EAAE,CAACoG;IAAa,CAAC,EAAE;MAAEI,IAAI,EAAE5H,EAAE,CAAC4R;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEwB,4BAA4B,EAAE,CAAC;MAC3GxL,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEwL,IAAI,EAAE,CAAC;MACPzL,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEgO,QAAQ,EAAE,CAAC;MACXjO,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE2M,YAAY,EAAE,CAAC;MACf5M,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE4M,UAAU,EAAE,CAAC;MACb7M,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE2T,UAAU,EAAE,CAAC;MACb9M,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE4T,UAAU,EAAE,CAAC;MACb/M,IAAI,EAAE7G;IACV,CAAC,CAAC;IAAE6T,WAAW,EAAE,CAAC;MACdhN,IAAI,EAAE7G;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM2X,cAAc,SAASvF,mBAAmB,CAAC;AAEjDuF,cAAc,CAACpR,IAAI;EAAA;EAAA;IAAA,sEAxmCgFtH,EAAE,uBAwmCW0Y,cAAc,SAAdA,cAAc;EAAA;AAAA,GAAqD;AACnLA,cAAc,CAACpO,IAAI,kBAzmCgFtK,EAAE;EAAA,MAymCD0Y,cAAc;EAAA;EAAA;EAAA;EAAA,WAzmCf1Y,EAAE;AAAA,EAymC8L;AACnS;EAAA,mDA1mCmGA,EAAE,mBA0mCL0Y,cAAc,EAAc,CAAC;IACjH9Q,IAAI,EAAEpH,SAAS;IACfqH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,6CAA4C;MACvDG,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDF,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4Q,aAAa,CAAC;AAEpBA,aAAa,CAACrR,IAAI;EAAA,iBAA6FqR,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBA/nCiF5Y,EAAE;EAAA,MA+nCW2Y;AAAa,EAKnG;AAC1BA,aAAa,CAACE,IAAI,kBAroCiF7Y,EAAE;EAAA,WAqoCqC,CAAC6S,yCAAyC,CAAC;EAAA,UAAY1P,YAAY,EAAEJ,eAAe,EAAEC,eAAe,EAAEe,aAAa,EAAEE,mBAAmB,EAC3QjB,eAAe;AAAA,EAAI;AAC3B;EAAA,mDAvoCmGhD,EAAE,mBAuoCL2Y,aAAa,EAAc,CAAC;IAChH/Q,IAAI,EAAEzG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCiR,OAAO,EAAE,CAAC3V,YAAY,EAAEJ,eAAe,EAAEC,eAAe,EAAEe,aAAa,CAAC;MACxEgV,OAAO,EAAE,CACL9U,mBAAmB,EACnBkO,OAAO,EACPnP,eAAe,EACfoB,WAAW,EACXmG,cAAc,EACdmO,cAAc,CACjB;MACDM,YAAY,EAAE,CAAC7G,OAAO,EAAE/N,WAAW,EAAEmG,cAAc,EAAEmO,cAAc,CAAC;MACpEhO,SAAS,EAAE,CAACmI,yCAAyC;IACzD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAShK,gBAAgB,EAAEoC,wBAAwB,EAAE/G,cAAc,EAAEsO,wBAAwB,EAAEK,yCAAyC,EAAEK,sBAAsB,EAAEf,OAAO,EAAE5H,cAAc,EAAEnG,WAAW,EAAEuU,aAAa,EAAED,cAAc,EAAEjN,YAAY,EAAE3C,mBAAmB,EAAEqK,mBAAmB,EAAEpI,WAAW,EAAEJ,iBAAiB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}