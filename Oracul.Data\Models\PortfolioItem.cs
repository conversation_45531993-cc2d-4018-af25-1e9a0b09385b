using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Portfolio item entity for astrology projects and case studies
    /// </summary>
    public class PortfolioItem : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? ProjectUrl { get; set; }

        [MaxLength(500)]
        public string? GithubUrl { get; set; }

        [Required]
        public DateTime CompletedAt { get; set; }

        [MaxLength(200)]
        public string? ClientName { get; set; }

        [MaxLength(100)]
        public string? Category { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual ICollection<PortfolioImage> PortfolioImages { get; set; } = new List<PortfolioImage>();
        public virtual ICollection<PortfolioTechnology> PortfolioTechnologies { get; set; } = new List<PortfolioTechnology>();
        public virtual ClientTestimonial? ClientTestimonial { get; set; }
    }
}
