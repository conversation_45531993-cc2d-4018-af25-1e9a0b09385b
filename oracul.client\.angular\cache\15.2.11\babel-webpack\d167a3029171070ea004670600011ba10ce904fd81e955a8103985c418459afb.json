{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/checkbox\";\nexport class MaterialDemoComponent {\n  constructor(snackBar) {\n    this.snackBar = snackBar;\n  }\n  showSnackbar() {\n    this.snackBar.open('Hello from Angular Material! 🎉', 'Close', {\n      duration: 3000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  static {\n    this.ɵfac = function MaterialDemoComponent_Factory(t) {\n      return new (t || MaterialDemoComponent)(i0.ɵɵdirectiveInject(i1.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MaterialDemoComponent,\n      selectors: [[\"app-material-demo\"]],\n      decls: 79,\n      vars: 0,\n      consts: [[1, \"demo-card\"], [1, \"demo-section\"], [1, \"button-row\"], [\"mat-button\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [\"mat-raised-button\", \"\", \"color\", \"warn\"], [\"mat-fab\", \"\", \"color\", \"primary\"], [\"mat-mini-fab\", \"\", \"color\", \"accent\"], [1, \"form-row\"], [\"appearance\", \"fill\"], [\"matInput\", \"\", \"placeholder\", \"Enter your name\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Enter your email\", \"type\", \"email\"], [\"value\", \"option1\"], [\"value\", \"option2\"], [\"value\", \"option3\"], [1, \"checkbox-row\"], [\"color\", \"primary\", \"checked\", \"\"], [\"color\", \"warn\"], [1, \"action-row\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\"], [\"mat-flat-button\", \"\", \"color\", \"warn\"]],\n      template: function MaterialDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-card\", 0)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"palette\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" Material Design Components Demo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n          i0.ɵɵtext(7, \" Explore various Angular Material components \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 1)(10, \"h3\");\n          i0.ɵɵtext(11, \"Buttons\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"button\", 3);\n          i0.ɵɵtext(14, \"Basic\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 4);\n          i0.ɵɵtext(16, \"Primary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 5);\n          i0.ɵɵtext(18, \"Accent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 6);\n          i0.ɵɵtext(20, \"Warn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 7)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"add\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 8)(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"edit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 1)(28, \"h3\");\n          i0.ɵɵtext(29, \"Form Fields\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"mat-form-field\", 10)(32, \"mat-label\");\n          i0.ɵɵtext(33, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 11);\n          i0.ɵɵelementStart(35, \"mat-icon\", 12);\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 13)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 14);\n          i0.ɵɵelementStart(41, \"mat-icon\", 12);\n          i0.ɵɵtext(42, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 10)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Choose an option\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-select\")(47, \"mat-option\", 15);\n          i0.ɵɵtext(48, \"Option 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"mat-option\", 16);\n          i0.ɵɵtext(50, \"Option 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-option\", 17);\n          i0.ɵɵtext(52, \"Option 3\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(53, \"div\", 1)(54, \"h3\");\n          i0.ɵɵtext(55, \"Checkboxes & Toggles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 18)(57, \"mat-checkbox\");\n          i0.ɵɵtext(58, \"Check me!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"mat-checkbox\", 19);\n          i0.ɵɵtext(60, \"Primary checkbox\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-checkbox\", 20);\n          i0.ɵɵtext(62, \"Warning checkbox\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(63, \"div\", 1)(64, \"h3\");\n          i0.ɵɵtext(65, \"Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 21)(67, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function MaterialDemoComponent_Template_button_click_67_listener() {\n            return ctx.showSnackbar();\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Show Snackbar \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"button\", 23)(72, \"mat-icon\");\n          i0.ɵɵtext(73, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(74, \" Download \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"button\", 24)(76, \"mat-icon\");\n          i0.ɵɵtext(77, \"delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(78, \" Delete \");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i2.MatButton, i2.MatMiniFabButton, i2.MatFabButton, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, i4.MatIcon, i5.MatFormField, i5.MatLabel, i5.MatSuffix, i6.MatInput, i7.MatSelect, i8.MatOption, i9.MatCheckbox],\n      styles: [\".demo-card[_ngcontent-%COMP%] {\\n      margin: 20px 0;\\n    }\\n    \\n    .demo-section[_ngcontent-%COMP%] {\\n      margin: 24px 0;\\n      padding: 16px 0;\\n      border-bottom: 1px solid #e0e0e0;\\n    }\\n    \\n    .demo-section[_ngcontent-%COMP%]:last-child {\\n      border-bottom: none;\\n    }\\n    \\n    .demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0 0 16px 0;\\n      color: #333;\\n      font-weight: 500;\\n    }\\n    \\n    .button-row[_ngcontent-%COMP%], .form-row[_ngcontent-%COMP%], .checkbox-row[_ngcontent-%COMP%], .action-row[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-wrap: wrap;\\n      gap: 16px;\\n      align-items: center;\\n    }\\n    \\n    .form-row[_ngcontent-%COMP%] {\\n      flex-direction: column;\\n      align-items: stretch;\\n    }\\n    \\n    @media (min-width: 768px) {\\n      .form-row[_ngcontent-%COMP%] {\\n        flex-direction: row;\\n        align-items: center;\\n      }\\n      \\n      .form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n        flex: 1;\\n      }\\n    }\\n    \\n    mat-card-title[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWF0ZXJpYWwtZGVtby5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsY0FBYztJQUNoQjs7SUFFQTtNQUNFLGNBQWM7TUFDZCxlQUFlO01BQ2YsZ0NBQWdDO0lBQ2xDOztJQUVBO01BQ0UsbUJBQW1CO0lBQ3JCOztJQUVBO01BQ0Usa0JBQWtCO01BQ2xCLFdBQVc7TUFDWCxnQkFBZ0I7SUFDbEI7O0lBRUE7TUFDRSxhQUFhO01BQ2IsZUFBZTtNQUNmLFNBQVM7TUFDVCxtQkFBbUI7SUFDckI7O0lBRUE7TUFDRSxzQkFBc0I7TUFDdEIsb0JBQW9CO0lBQ3RCOztJQUVBO01BQ0U7UUFDRSxtQkFBbUI7UUFDbkIsbUJBQW1CO01BQ3JCOztNQUVBO1FBQ0UsT0FBTztNQUNUO0lBQ0Y7O0lBRUE7TUFDRSxhQUFhO01BQ2IsbUJBQW1CO01BQ25CLFFBQVE7SUFDViIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5kZW1vLWNhcmQge1xuICAgICAgbWFyZ2luOiAyMHB4IDA7XG4gICAgfVxuICAgIFxuICAgIC5kZW1vLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luOiAyNHB4IDA7XG4gICAgICBwYWRkaW5nOiAxNnB4IDA7XG4gICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcbiAgICB9XG4gICAgXG4gICAgLmRlbW8tc2VjdGlvbjpsYXN0LWNoaWxkIHtcbiAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gICAgfVxuICAgIFxuICAgIC5kZW1vLXNlY3Rpb24gaDMge1xuICAgICAgbWFyZ2luOiAwIDAgMTZweCAwO1xuICAgICAgY29sb3I6ICMzMzM7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgIH1cbiAgICBcbiAgICAuYnV0dG9uLXJvdywgLmZvcm0tcm93LCAuY2hlY2tib3gtcm93LCAuYWN0aW9uLXJvdyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICB9XG4gICAgXG4gICAgLmZvcm0tcm93IHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcbiAgICB9XG4gICAgXG4gICAgQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XG4gICAgICAuZm9ybS1yb3cge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgfVxuICAgICAgXG4gICAgICAuZm9ybS1yb3cgbWF0LWZvcm0tZmllbGQge1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICBtYXQtY2FyZC10aXRsZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogOHB4O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;AAgJA,OAAM,MAAOA,qBAAqB;EAChCC,YAAoBC,QAAqB;IAArB,aAAQ,GAARA,QAAQ;EAAgB;EAE5CC,YAAY;IACV,IAAI,CAACD,QAAQ,CAACE,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;MAC7DC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;;;uBATWP,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAQ;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UA1I9BC,mCAA4B;UAGZA,uBAAO;UAAAA,iBAAW;UAC5BA,iDACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,6DACF;UAAAA,iBAAoB;UAGtBA,wCAAkB;UAGVA,wBAAO;UAAAA,iBAAK;UAChBA,+BAAwB;UACHA,sBAAK;UAAAA,iBAAS;UACjCA,kCAA0C;UAAAA,wBAAO;UAAAA,iBAAS;UAC1DA,kCAAyC;UAAAA,uBAAM;UAAAA,iBAAS;UACxDA,kCAAuC;UAAAA,qBAAI;UAAAA,iBAAS;UACpDA,kCAAgC;UACpBA,oBAAG;UAAAA,iBAAW;UAE1BA,kCAAoC;UACxBA,qBAAI;UAAAA,iBAAW;UAM/BA,+BAA0B;UACpBA,4BAAW;UAAAA,iBAAK;UACpBA,+BAAsB;UAEPA,qBAAI;UAAAA,iBAAY;UAC3BA,6BAA8C;UAC9CA,qCAAoB;UAAAA,uBAAM;UAAAA,iBAAW;UAGvCA,2CAAqC;UACxBA,sBAAK;UAAAA,iBAAY;UAC5BA,6BAA4D;UAC5DA,qCAAoB;UAAAA,sBAAK;UAAAA,iBAAW;UAGtCA,2CAAkC;UACrBA,iCAAgB;UAAAA,iBAAY;UACvCA,mCAAY;UACkBA,yBAAQ;UAAAA,iBAAa;UACjDA,uCAA4B;UAAAA,yBAAQ;UAAAA,iBAAa;UACjDA,uCAA4B;UAAAA,yBAAQ;UAAAA,iBAAa;UAOzDA,+BAA0B;UACpBA,qCAAoB;UAAAA,iBAAK;UAC7BA,gCAA0B;UACVA,0BAAS;UAAAA,iBAAe;UACtCA,yCAAsC;UAAAA,iCAAgB;UAAAA,iBAAe;UACrEA,yCAA2B;UAAAA,iCAAgB;UAAAA,iBAAe;UAK9DA,+BAA0B;UACpBA,wBAAO;UAAAA,iBAAK;UAChBA,gCAAwB;UACoBA;YAAA,OAASC,kBAAc;UAAA,EAAC;UAChED,iCAAU;UAAAA,8BAAa;UAAAA,iBAAW;UAClCA,gCACF;UAAAA,iBAAS;UACTA,mCAA0C;UAC9BA,yBAAQ;UAAAA,iBAAW;UAC7BA,2BACF;UAAAA,iBAAS;UACTA,mCAAqC;UACzBA,uBAAM;UAAAA,iBAAW;UAC3BA,yBACF;UAAAA,iBAAS", "names": ["MaterialDemoComponent", "constructor", "snackBar", "showSnackbar", "open", "duration", "horizontalPosition", "verticalPosition", "selectors", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\material-demo.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-material-demo',\r\n  template: `\r\n    <mat-card class=\"demo-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>palette</mat-icon>\r\n          Material Design Components Demo\r\n        </mat-card-title>\r\n        <mat-card-subtitle>\r\n          Explore various Angular Material components\r\n        </mat-card-subtitle>\r\n      </mat-card-header>\r\n      \r\n      <mat-card-content>\r\n        <!-- Buttons Section -->\r\n        <div class=\"demo-section\">\r\n          <h3>Buttons</h3>\r\n          <div class=\"button-row\">\r\n            <button mat-button>Basic</button>\r\n            <button mat-raised-button color=\"primary\">Primary</button>\r\n            <button mat-raised-button color=\"accent\">Accent</button>\r\n            <button mat-raised-button color=\"warn\">Warn</button>\r\n            <button mat-fab color=\"primary\">\r\n              <mat-icon>add</mat-icon>\r\n            </button>\r\n            <button mat-mini-fab color=\"accent\">\r\n              <mat-icon>edit</mat-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form Fields Section -->\r\n        <div class=\"demo-section\">\r\n          <h3>Form Fields</h3>\r\n          <div class=\"form-row\">\r\n            <mat-form-field appearance=\"fill\">\r\n              <mat-label>Name</mat-label>\r\n              <input matInput placeholder=\"Enter your name\">\r\n              <mat-icon matSuffix>person</mat-icon>\r\n            </mat-form-field>\r\n            \r\n            <mat-form-field appearance=\"outline\">\r\n              <mat-label>Email</mat-label>\r\n              <input matInput placeholder=\"Enter your email\" type=\"email\">\r\n              <mat-icon matSuffix>email</mat-icon>\r\n            </mat-form-field>\r\n            \r\n            <mat-form-field appearance=\"fill\">\r\n              <mat-label>Choose an option</mat-label>\r\n              <mat-select>\r\n                <mat-option value=\"option1\">Option 1</mat-option>\r\n                <mat-option value=\"option2\">Option 2</mat-option>\r\n                <mat-option value=\"option3\">Option 3</mat-option>\r\n              </mat-select>\r\n            </mat-form-field>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Checkboxes and Toggles -->\r\n        <div class=\"demo-section\">\r\n          <h3>Checkboxes & Toggles</h3>\r\n          <div class=\"checkbox-row\">\r\n            <mat-checkbox>Check me!</mat-checkbox>\r\n            <mat-checkbox color=\"primary\" checked>Primary checkbox</mat-checkbox>\r\n            <mat-checkbox color=\"warn\">Warning checkbox</mat-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Action Buttons -->\r\n        <div class=\"demo-section\">\r\n          <h3>Actions</h3>\r\n          <div class=\"action-row\">\r\n            <button mat-raised-button color=\"primary\" (click)=\"showSnackbar()\">\r\n              <mat-icon>notifications</mat-icon>\r\n              Show Snackbar\r\n            </button>\r\n            <button mat-stroked-button color=\"accent\">\r\n              <mat-icon>download</mat-icon>\r\n              Download\r\n            </button>\r\n            <button mat-flat-button color=\"warn\">\r\n              <mat-icon>delete</mat-icon>\r\n              Delete\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  `,\r\n  styles: [`\r\n    .demo-card {\r\n      margin: 20px 0;\r\n    }\r\n    \r\n    .demo-section {\r\n      margin: 24px 0;\r\n      padding: 16px 0;\r\n      border-bottom: 1px solid #e0e0e0;\r\n    }\r\n    \r\n    .demo-section:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .demo-section h3 {\r\n      margin: 0 0 16px 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    .button-row, .form-row, .checkbox-row, .action-row {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 16px;\r\n      align-items: center;\r\n    }\r\n    \r\n    .form-row {\r\n      flex-direction: column;\r\n      align-items: stretch;\r\n    }\r\n    \r\n    @media (min-width: 768px) {\r\n      .form-row {\r\n        flex-direction: row;\r\n        align-items: center;\r\n      }\r\n      \r\n      .form-row mat-form-field {\r\n        flex: 1;\r\n      }\r\n    }\r\n    \r\n    mat-card-title {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n    }\r\n  `]\r\n})\r\nexport class MaterialDemoComponent {\r\n  constructor(private snackBar: MatSnackBar) {}\r\n\r\n  showSnackbar() {\r\n    this.snackBar.open('Hello from Angular Material! 🎉', 'Close', {\r\n      duration: 3000,\r\n      horizontalPosition: 'center',\r\n      verticalPosition: 'bottom',\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}