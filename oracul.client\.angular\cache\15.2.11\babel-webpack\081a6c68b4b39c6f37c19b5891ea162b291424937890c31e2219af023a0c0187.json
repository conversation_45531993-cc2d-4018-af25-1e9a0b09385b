{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OracleService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/api/oracle`;\n  }\n  /**\r\n   * Get all oracle profiles\r\n   */\n  getOracles() {\n    return this.http.get(this.API_URL).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get oracle profile by ID\r\n   */\n  getOracleProfile(id) {\n    return this.http.get(`${this.API_URL}/${id}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get available oracles\r\n   */\n  getAvailableOracles() {\n    return this.http.get(`${this.API_URL}/available`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Update oracle profile\r\n   */\n  updateOracleProfile(id, request) {\n    return this.http.put(`${this.API_URL}/${id}`, request).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    console.error('Oracle service error:', error);\n    return throwError(() => error);\n  }\n  static {\n    this.ɵfac = function OracleService_Factory(t) {\n      return new (t || OracleService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OracleService,\n      factory: OracleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;;;AA0D5D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFP,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,aAAa;EAErB;EAExC;;;EAGAC,UAAU;IACR,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAA+B,IAAI,CAACC,OAAO,CAAC,CAC7DC,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAC,gBAAgB,CAACC,EAAU;IACzB,OAAO,IAAI,CAACR,IAAI,CAACG,GAAG,CAA6B,GAAG,IAAI,CAACC,OAAO,IAAII,EAAE,EAAE,CAAC,CACtEH,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAG,mBAAmB;IACjB,OAAO,IAAI,CAACT,IAAI,CAACG,GAAG,CAA+B,GAAG,IAAI,CAACC,OAAO,YAAY,CAAC,CAC5EC,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAI,mBAAmB,CAACF,EAAU,EAAEG,OAAmC;IACjE,OAAO,IAAI,CAACX,IAAI,CAACY,GAAG,CAA6B,GAAG,IAAI,CAACR,OAAO,IAAII,EAAE,EAAE,EAAEG,OAAO,CAAC,CAC/EN,IAAI,CAACT,UAAU,CAAC,IAAI,CAACU,WAAW,CAAC,CAAC;EACvC;EAEQA,WAAW,CAACO,KAAU;IAC5BC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAOlB,UAAU,CAAC,MAAMkB,KAAK,CAAC;EAChC;;;uBAxCWf,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAiB,SAAbjB,aAAa;MAAAkB,YAFZ;IAAM;EAAA", "names": ["throwError", "catchError", "environment", "OracleService", "constructor", "http", "apiUrl", "<PERSON><PERSON><PERSON><PERSON>", "get", "API_URL", "pipe", "handleError", "getOracleProfile", "id", "getAvailableOracles", "updateOracleProfile", "request", "put", "error", "console", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\services\\oracle.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface OracleProfile {\n  id: number;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phoneNumber?: string;\n  profilePictureUrl?: string;\n  about?: string;\n  hourlyRate?: number;\n  specializations: string[];\n  yearsOfExperience?: number;\n  languages: string[];\n  isAvailable: boolean;\n  services: OracleService[];\n  lastLoginAt?: Date;\n}\n\nexport interface OracleService {\n  serviceId: number;\n  name: string;\n  description?: string;\n  price: number;\n  durationMinutes: number;\n  category: string;\n  notes?: string;\n}\n\nexport interface Service {\n  id: number;\n  name: string;\n  description?: string;\n  price: number;\n  durationMinutes: number;\n  category: string;\n  isActive: boolean;\n}\n\nexport interface UpdateOracleProfileRequest {\n  about?: string;\n  hourlyRate?: number;\n  specializations: string[];\n  yearsOfExperience?: number;\n  languages: string[];\n  isAvailable: boolean;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  message: string;\n  data?: T;\n  errors: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class OracleService {\n  private readonly API_URL = `${environment.apiUrl}/api/oracle`;\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Get all oracle profiles\n   */\n  getOracles(): Observable<ApiResponse<OracleProfile[]>> {\n    return this.http.get<ApiResponse<OracleProfile[]>>(this.API_URL)\n      .pipe(catchError(this.handleError));\n  }\n\n  /**\n   * Get oracle profile by ID\n   */\n  getOracleProfile(id: number): Observable<ApiResponse<OracleProfile>> {\n    return this.http.get<ApiResponse<OracleProfile>>(`${this.API_URL}/${id}`)\n      .pipe(catchError(this.handleError));\n  }\n\n  /**\n   * Get available oracles\n   */\n  getAvailableOracles(): Observable<ApiResponse<OracleProfile[]>> {\n    return this.http.get<ApiResponse<OracleProfile[]>>(`${this.API_URL}/available`)\n      .pipe(catchError(this.handleError));\n  }\n\n  /**\n   * Update oracle profile\n   */\n  updateOracleProfile(id: number, request: UpdateOracleProfileRequest): Observable<ApiResponse<OracleProfile>> {\n    return this.http.put<ApiResponse<OracleProfile>>(`${this.API_URL}/${id}`, request)\n      .pipe(catchError(this.handleError));\n  }\n\n  private handleError(error: any): Observable<never> {\n    console.error('Oracle service error:', error);\n    return throwError(() => error);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}