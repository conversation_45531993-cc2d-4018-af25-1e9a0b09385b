{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProfileService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = '/api/profiles';\n    this.currentProfileSubject = new BehaviorSubject(null);\n    this.currentProfile$ = this.currentProfileSubject.asObservable();\n  }\n  // Profile CRUD Operations\n  getProfile(identifier) {\n    return this.http.get(`${this.API_URL}/${identifier}`).pipe(tap(profile => {\n      if (profile) {\n        this.currentProfileSubject.next(profile);\n      }\n    }), catchError(this.handleError));\n  }\n  getCurrentUserProfile() {\n    return this.http.get(`${this.API_URL}/me`).pipe(tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  updateProfile(updates) {\n    return this.http.put(`${this.API_URL}/me`, updates).pipe(tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  createProfile(profileData) {\n    return this.http.post(`${this.API_URL}`, profileData).pipe(tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n  }\n  deleteProfile() {\n    return this.http.delete(`${this.API_URL}/me`).pipe(tap(() => this.currentProfileSubject.next(null)), catchError(this.handleError));\n  }\n  // Profile Search\n  searchProfiles(filters, page = 1, limit = 20) {\n    let params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n    if (filters.location) params = params.set('location', filters.location);\n    if (filters.skills?.length) params = params.set('skills', filters.skills.join(','));\n    if (filters.experience) params = params.set('experience', filters.experience);\n    if (filters.availability !== undefined) params = params.set('availability', filters.availability.toString());\n    if (filters.sortBy) params = params.set('sortBy', filters.sortBy);\n    return this.http.get(`${this.API_URL}/search`, {\n      params\n    }).pipe(catchError(this.handleError));\n  }\n  // Skills Management\n  addSkill(skill) {\n    return this.http.post(`${this.API_URL}/me/skills`, skill).pipe(catchError(this.handleError));\n  }\n  updateSkill(skillId, updates) {\n    return this.http.put(`${this.API_URL}/me/skills/${skillId}`, updates).pipe(catchError(this.handleError));\n  }\n  deleteSkill(skillId) {\n    return this.http.delete(`${this.API_URL}/me/skills/${skillId}`).pipe(catchError(this.handleError));\n  }\n  endorseSkill(request) {\n    return this.http.post(`${this.API_URL}/skills/endorse`, request).pipe(catchError(this.handleError));\n  }\n  // Experience Management\n  addExperience(experience) {\n    return this.http.post(`${this.API_URL}/me/experiences`, experience).pipe(catchError(this.handleError));\n  }\n  updateExperience(experienceId, updates) {\n    return this.http.put(`${this.API_URL}/me/experiences/${experienceId}`, updates).pipe(catchError(this.handleError));\n  }\n  deleteExperience(experienceId) {\n    return this.http.delete(`${this.API_URL}/me/experiences/${experienceId}`).pipe(catchError(this.handleError));\n  }\n  // Portfolio Management\n  addPortfolioItem(item) {\n    return this.http.post(`${this.API_URL}/me/portfolio`, item).pipe(catchError(this.handleError));\n  }\n  updatePortfolioItem(itemId, updates) {\n    return this.http.put(`${this.API_URL}/me/portfolio/${itemId}`, updates).pipe(catchError(this.handleError));\n  }\n  deletePortfolioItem(itemId) {\n    return this.http.delete(`${this.API_URL}/me/portfolio/${itemId}`).pipe(catchError(this.handleError));\n  }\n  // Achievements & Certifications\n  addAchievement(achievement) {\n    return this.http.post(`${this.API_URL}/me/achievements`, achievement).pipe(catchError(this.handleError));\n  }\n  addCertification(certification) {\n    return this.http.post(`${this.API_URL}/me/certifications`, certification).pipe(catchError(this.handleError));\n  }\n  // Blog Posts\n  getBlogPosts(profileId) {\n    return this.http.get(`${this.API_URL}/${profileId}/blog-posts`).pipe(catchError(this.handleError));\n  }\n  // Analytics\n  getProfileAnalytics() {\n    return this.http.get(`${this.API_URL}/me/analytics`).pipe(catchError(this.handleError));\n  }\n  recordProfileView(request) {\n    return this.http.post(`${this.API_URL}/views`, request).pipe(catchError(this.handleError));\n  }\n  // File Upload\n  uploadProfilePhoto(file) {\n    const formData = new FormData();\n    formData.append('photo', file);\n    return this.http.post(`${this.API_URL}/me/photo`, formData).pipe(catchError(this.handleError));\n  }\n  uploadCoverPhoto(file) {\n    const formData = new FormData();\n    formData.append('cover', file);\n    return this.http.post(`${this.API_URL}/me/cover`, formData).pipe(catchError(this.handleError));\n  }\n  // Utility Methods\n  generateProfileSlug(firstName, lastName) {\n    return this.http.post(`${this.API_URL}/generate-slug`, {\n      firstName,\n      lastName\n    }).pipe(catchError(this.handleError));\n  }\n  checkSlugAvailability(slug) {\n    return this.http.get(`${this.API_URL}/check-slug/${slug}`).pipe(catchError(this.handleError));\n  }\n  // Social Sharing\n  getProfileShareData(profileId) {\n    return this.http.get(`${this.API_URL}/${profileId}/share-data`).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    console.error('ProfileService error:', error);\n    throw error;\n  }\n  static {\n    this.ɵfac = function ProfileService_Factory(t) {\n      return new (t || ProfileService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileService,\n      factory: ProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,QAAQ,MAAM;AAClD,SAAcC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAoBrD,OAAM,MAAOC,cAAc;EAMzBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IALP,YAAO,GAAG,eAAe;IAElC,0BAAqB,GAAG,IAAIL,eAAe,CAAqB,IAAI,CAAC;IACtE,oBAAe,GAAG,IAAI,CAACM,qBAAqB,CAACC,YAAY,EAAE;EAE3B;EAEvC;EACAC,UAAU,CAACC,UAAkB;IAC3B,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAc,GAAG,IAAI,CAACC,OAAO,IAAIF,UAAU,EAAE,CAAC,CAC/DG,IAAI,CACHX,GAAG,CAACY,OAAO,IAAG;MACZ,IAAIA,OAAO,EAAE;QACX,IAAI,CAACP,qBAAqB,CAACQ,IAAI,CAACD,OAAO,CAAC;;IAE5C,CAAC,CAAC,EACFX,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACL;EAEAC,qBAAqB;IACnB,OAAO,IAAI,CAACX,IAAI,CAACK,GAAG,CAAc,GAAG,IAAI,CAACC,OAAO,KAAK,CAAC,CACpDC,IAAI,CACHX,GAAG,CAACY,OAAO,IAAI,IAAI,CAACP,qBAAqB,CAACQ,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDX,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACL;EAEAE,aAAa,CAACC,OAA6B;IACzC,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAc,GAAG,IAAI,CAACR,OAAO,KAAK,EAAEO,OAAO,CAAC,CAC7DN,IAAI,CACHX,GAAG,CAACY,OAAO,IAAI,IAAI,CAACP,qBAAqB,CAACQ,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDX,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACL;EAEAK,aAAa,CAACC,WAAiC;IAC7C,OAAO,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAc,GAAG,IAAI,CAACX,OAAO,EAAE,EAAEU,WAAW,CAAC,CAC/DT,IAAI,CACHX,GAAG,CAACY,OAAO,IAAI,IAAI,CAACP,qBAAqB,CAACQ,IAAI,CAACD,OAAO,CAAC,CAAC,EACxDX,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACL;EAEAQ,aAAa;IACX,OAAO,IAAI,CAAClB,IAAI,CAACmB,MAAM,CAAO,GAAG,IAAI,CAACb,OAAO,KAAK,CAAC,CAChDC,IAAI,CACHX,GAAG,CAAC,MAAM,IAAI,CAACK,qBAAqB,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC,EAChDZ,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAC7B;EACL;EAEA;EACAU,cAAc,CAACC,OAA6B,EAAEC,OAAe,CAAC,EAAEC,QAAgB,EAAE;IAChF,IAAIC,MAAM,GAAG,IAAI9B,UAAU,EAAE,CAC1B+B,GAAG,CAAC,MAAM,EAAEH,IAAI,CAACI,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAEjC,IAAIL,OAAO,CAACM,QAAQ,EAAEH,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,UAAU,EAAEJ,OAAO,CAACM,QAAQ,CAAC;IACvE,IAAIN,OAAO,CAACO,MAAM,EAAEC,MAAM,EAAEL,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEJ,OAAO,CAACO,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;IACnF,IAAIT,OAAO,CAACU,UAAU,EAAEP,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,YAAY,EAAEJ,OAAO,CAACU,UAAU,CAAC;IAC7E,IAAIV,OAAO,CAACW,YAAY,KAAKC,SAAS,EAAET,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACW,YAAY,CAACN,QAAQ,EAAE,CAAC;IAC5G,IAAIL,OAAO,CAACa,MAAM,EAAEV,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEJ,OAAO,CAACa,MAAM,CAAC;IAEjE,OAAO,IAAI,CAAClC,IAAI,CAACK,GAAG,CAAsB,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE;MAAEkB;IAAM,CAAE,CAAC,CAC5EjB,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAyB,QAAQ,CAACC,KAA4E;IACnF,OAAO,IAAI,CAACpC,IAAI,CAACiB,IAAI,CAAe,GAAG,IAAI,CAACX,OAAO,YAAY,EAAE8B,KAAK,CAAC,CACpE7B,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA2B,WAAW,CAACC,OAAe,EAAEzB,OAA8B;IACzD,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAe,GAAG,IAAI,CAACR,OAAO,cAAcgC,OAAO,EAAE,EAAEzB,OAAO,CAAC,CAChFN,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA6B,WAAW,CAACD,OAAe;IACzB,OAAO,IAAI,CAACtC,IAAI,CAACmB,MAAM,CAAO,GAAG,IAAI,CAACb,OAAO,cAAcgC,OAAO,EAAE,CAAC,CAClE/B,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA8B,YAAY,CAACC,OAAgC;IAC3C,OAAO,IAAI,CAACzC,IAAI,CAACiB,IAAI,CAAO,GAAG,IAAI,CAACX,OAAO,iBAAiB,EAAEmC,OAAO,CAAC,CACnElC,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAgC,aAAa,CAACX,UAAsC;IAClD,OAAO,IAAI,CAAC/B,IAAI,CAACiB,IAAI,CAAiB,GAAG,IAAI,CAACX,OAAO,iBAAiB,EAAEyB,UAAU,CAAC,CAChFxB,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAiC,gBAAgB,CAACC,YAAoB,EAAE/B,OAAgC;IACrE,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAiB,GAAG,IAAI,CAACR,OAAO,mBAAmBsC,YAAY,EAAE,EAAE/B,OAAO,CAAC,CAC5FN,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAmC,gBAAgB,CAACD,YAAoB;IACnC,OAAO,IAAI,CAAC5C,IAAI,CAACmB,MAAM,CAAO,GAAG,IAAI,CAACb,OAAO,mBAAmBsC,YAAY,EAAE,CAAC,CAC5ErC,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAoC,gBAAgB,CAACC,IAA+B;IAC9C,OAAO,IAAI,CAAC/C,IAAI,CAACiB,IAAI,CAAgB,GAAG,IAAI,CAACX,OAAO,eAAe,EAAEyC,IAAI,CAAC,CACvExC,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAsC,mBAAmB,CAACC,MAAc,EAAEpC,OAA+B;IACjE,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAgB,GAAG,IAAI,CAACR,OAAO,iBAAiB2C,MAAM,EAAE,EAAEpC,OAAO,CAAC,CACnFN,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAwC,mBAAmB,CAACD,MAAc;IAChC,OAAO,IAAI,CAACjD,IAAI,CAACmB,MAAM,CAAO,GAAG,IAAI,CAACb,OAAO,iBAAiB2C,MAAM,EAAE,CAAC,CACpE1C,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAyC,cAAc,CAACC,WAAoC;IACjD,OAAO,IAAI,CAACpD,IAAI,CAACiB,IAAI,CAAc,GAAG,IAAI,CAACX,OAAO,kBAAkB,EAAE8C,WAAW,CAAC,CAC/E7C,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA2C,gBAAgB,CAACC,aAAwC;IACvD,OAAO,IAAI,CAACtD,IAAI,CAACiB,IAAI,CAAgB,GAAG,IAAI,CAACX,OAAO,oBAAoB,EAAEgD,aAAa,CAAC,CACrF/C,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACA6C,YAAY,CAACC,SAAiB;IAC5B,OAAO,IAAI,CAACxD,IAAI,CAACK,GAAG,CAAa,GAAG,IAAI,CAACC,OAAO,IAAIkD,SAAS,aAAa,CAAC,CACxEjD,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACA+C,mBAAmB;IACjB,OAAO,IAAI,CAACzD,IAAI,CAACK,GAAG,CAAmB,GAAG,IAAI,CAACC,OAAO,eAAe,CAAC,CACnEC,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAgD,iBAAiB,CAACjB,OAA2B;IAC3C,OAAO,IAAI,CAACzC,IAAI,CAACiB,IAAI,CAAO,GAAG,IAAI,CAACX,OAAO,QAAQ,EAAEmC,OAAO,CAAC,CAC1DlC,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAiD,kBAAkB,CAACC,IAAU;IAC3B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAO,IAAI,CAAC5D,IAAI,CAACiB,IAAI,CAAkB,GAAG,IAAI,CAACX,OAAO,WAAW,EAAEuD,QAAQ,CAAC,CACzEtD,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEAsD,gBAAgB,CAACJ,IAAU;IACzB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAO,IAAI,CAAC5D,IAAI,CAACiB,IAAI,CAAkB,GAAG,IAAI,CAACX,OAAO,WAAW,EAAEuD,QAAQ,CAAC,CACzEtD,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACAuD,mBAAmB,CAACC,SAAiB,EAAEC,QAAgB;IACrD,OAAO,IAAI,CAACnE,IAAI,CAACiB,IAAI,CAAmB,GAAG,IAAI,CAACX,OAAO,gBAAgB,EAAE;MAAE4D,SAAS;MAAEC;IAAQ,CAAE,CAAC,CAC9F5D,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA0D,qBAAqB,CAACC,IAAY;IAChC,OAAO,IAAI,CAACrE,IAAI,CAACK,GAAG,CAAyB,GAAG,IAAI,CAACC,OAAO,eAAe+D,IAAI,EAAE,CAAC,CAC/E9D,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEA;EACA4D,mBAAmB,CAACd,SAAiB;IACnC,OAAO,IAAI,CAACxD,IAAI,CAACK,GAAG,CAAwE,GAAG,IAAI,CAACC,OAAO,IAAIkD,SAAS,aAAa,CAAC,CACnIjD,IAAI,CAACV,UAAU,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC;EACvC;EAEQA,WAAW,CAAC6D,KAAU;IAC5BC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,MAAMA,KAAK;EACb;;;uBA3LWzE,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAA2E,SAAd3E,cAAc;MAAA4E,YAFb;IAAM;EAAA", "names": ["HttpParams", "BehaviorSubject", "tap", "catchError", "ProfileService", "constructor", "http", "currentProfileSubject", "asObservable", "getProfile", "identifier", "get", "API_URL", "pipe", "profile", "next", "handleError", "getCurrentUserProfile", "updateProfile", "updates", "put", "createProfile", "profileData", "post", "deleteProfile", "delete", "searchProfiles", "filters", "page", "limit", "params", "set", "toString", "location", "skills", "length", "join", "experience", "availability", "undefined", "sortBy", "addSkill", "skill", "updateSkill", "skillId", "deleteSkill", "endorseSkill", "request", "addExperience", "updateExperience", "experienceId", "deleteExperience", "addPortfolioItem", "item", "updatePortfolioItem", "itemId", "deletePortfolioItem", "addAchievement", "achievement", "addCertification", "certification", "getBlogPosts", "profileId", "getProfileAnalytics", "recordProfileView", "uploadProfilePhoto", "file", "formData", "FormData", "append", "uploadCoverPhoto", "generateProfileSlug", "firstName", "lastName", "checkSlugAvailability", "slug", "getProfileShareData", "error", "console", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\services\\profile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject } from 'rxjs';\r\nimport { map, tap, catchError } from 'rxjs/operators';\r\nimport {\r\n  UserProfile,\r\n  ProfileUpdateRequest,\r\n  SkillEndorsementRequest,\r\n  ProfileViewRequest,\r\n  ProfileAnalytics,\r\n  ProfileSearchFilters,\r\n  ProfileSearchResult,\r\n  BlogPost,\r\n  Achievement,\r\n  Certification,\r\n  WorkExperience,\r\n  PortfolioItem,\r\n  ProfileSkill\r\n} from '../models/profile.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProfileService {\r\n  private readonly API_URL = '/api/profiles';\r\n  \r\n  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);\r\n  public currentProfile$ = this.currentProfileSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Profile CRUD Operations\r\n  getProfile(identifier: string): Observable<UserProfile> {\r\n    return this.http.get<UserProfile>(`${this.API_URL}/${identifier}`)\r\n      .pipe(\r\n        tap(profile => {\r\n          if (profile) {\r\n            this.currentProfileSubject.next(profile);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  getCurrentUserProfile(): Observable<UserProfile> {\r\n    return this.http.get<UserProfile>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {\r\n    return this.http.put<UserProfile>(`${this.API_URL}/me`, updates)\r\n      .pipe(\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {\r\n    return this.http.post<UserProfile>(`${this.API_URL}`, profileData)\r\n      .pipe(\r\n        tap(profile => this.currentProfileSubject.next(profile)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  deleteProfile(): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(() => this.currentProfileSubject.next(null)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Profile Search\r\n  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {\r\n    let params = new HttpParams()\r\n      .set('page', page.toString())\r\n      .set('limit', limit.toString());\r\n\r\n    if (filters.location) params = params.set('location', filters.location);\r\n    if (filters.skills?.length) params = params.set('skills', filters.skills.join(','));\r\n    if (filters.experience) params = params.set('experience', filters.experience);\r\n    if (filters.availability !== undefined) params = params.set('availability', filters.availability.toString());\r\n    if (filters.sortBy) params = params.set('sortBy', filters.sortBy);\r\n\r\n    return this.http.get<ProfileSearchResult>(`${this.API_URL}/search`, { params })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Skills Management\r\n  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {\r\n    return this.http.post<ProfileSkill>(`${this.API_URL}/me/skills`, skill)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {\r\n    return this.http.put<ProfileSkill>(`${this.API_URL}/me/skills/${skillId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deleteSkill(skillId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/skills/${skillId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  endorseSkill(request: SkillEndorsementRequest): Observable<void> {\r\n    return this.http.post<void>(`${this.API_URL}/skills/endorse`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Experience Management\r\n  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {\r\n    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {\r\n    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deleteExperience(experienceId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Portfolio Management\r\n  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {\r\n    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {\r\n    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  deletePortfolioItem(itemId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Achievements & Certifications\r\n  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {\r\n    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {\r\n    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Blog Posts\r\n  getBlogPosts(profileId: number): Observable<BlogPost[]> {\r\n    return this.http.get<BlogPost[]>(`${this.API_URL}/${profileId}/blog-posts`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Analytics\r\n  getProfileAnalytics(): Observable<ProfileAnalytics> {\r\n    return this.http.get<ProfileAnalytics>(`${this.API_URL}/me/analytics`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  recordProfileView(request: ProfileViewRequest): Observable<void> {\r\n    return this.http.post<void>(`${this.API_URL}/views`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // File Upload\r\n  uploadProfilePhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('photo', file);\r\n    \r\n    return this.http.post<{ url: string }>(`${this.API_URL}/me/photo`, formData)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  uploadCoverPhoto(file: File): Observable<{ url: string }> {\r\n    const formData = new FormData();\r\n    formData.append('cover', file);\r\n    \r\n    return this.http.post<{ url: string }>(`${this.API_URL}/me/cover`, formData)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Utility Methods\r\n  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {\r\n    return this.http.post<{ slug: string }>(`${this.API_URL}/generate-slug`, { firstName, lastName })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {\r\n    return this.http.get<{ available: boolean }>(`${this.API_URL}/check-slug/${slug}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Social Sharing\r\n  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {\r\n    return this.http.get<{ title: string; description: string; imageUrl: string; url: string }>(`${this.API_URL}/${profileId}/share-data`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  private handleError(error: any): Observable<never> {\r\n    console.error('ProfileService error:', error);\r\n    throw error;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}