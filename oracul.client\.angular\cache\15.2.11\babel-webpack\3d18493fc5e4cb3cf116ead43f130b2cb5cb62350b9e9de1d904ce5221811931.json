{"ast": null, "code": "import { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./token.service\";\nexport class AuthService {\n  constructor(http, router, tokenService) {\n    this.http = http;\n    this.router = router;\n    this.tokenService = tokenService;\n    this.API_URL = '/api/auth';\n    this.TOKEN_KEY = 'access_token';\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    this.USER_KEY = 'user_info';\n    this.REMEMBER_ME_KEY = 'remember_me';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.initializeAuth();\n  }\n  initializeAuth() {\n    const token = this.tokenService.getToken();\n    const user = this.tokenService.getStoredUser();\n    if (token && user) {\n      // Check if token is expired\n      if (this.tokenService.isTokenExpired(token)) {\n        // Try to refresh the token\n        this.refreshToken().subscribe({\n          next: () => {\n            this.currentUserSubject.next(user);\n            this.isAuthenticatedSubject.next(true);\n            this.startTokenExpirationCheck();\n          },\n          error: () => {\n            // Refresh failed, clear auth data\n            this.clearAuthData();\n          }\n        });\n      } else {\n        this.currentUserSubject.next(user);\n        this.isAuthenticatedSubject.next(true);\n        this.startTokenExpirationCheck();\n      }\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        // Store remember me preference\n        const rememberMe = credentials.rememberMe || false;\n        this.tokenService.setRememberMe(rememberMe);\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        this.tokenService.setUser(response.user, rememberMe);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    // Determine endpoint based on data type\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\n    return this.http.post(`${this.API_URL}/${endpoint}`, userData).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        this.setToken(response.accessToken);\n        if (response.refreshToken) {\n          this.setRefreshToken(response.refreshToken);\n        }\n        this.setUser(response.user);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  isOracleRegistration(userData) {\n    return 'professionalTitle' in userData;\n  }\n  logout() {\n    return this.http.post(`${this.API_URL}/logout`, {}).pipe(tap(() => {\n      this.clearAuthData();\n    }), catchError(() => {\n      // Even if logout fails on server, clear local data\n      this.clearAuthData();\n      return throwError('Logout failed');\n    }));\n  }\n  refreshToken() {\n    const refreshToken = this.tokenService.getRefreshToken();\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n    return this.http.post(`${this.API_URL}/refresh-token`, {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.accessToken) {\n        const rememberMe = this.tokenService.getRememberMe();\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        if (response.user) {\n          this.tokenService.setUser(response.user, rememberMe);\n          this.currentUserSubject.next(response.user);\n        }\n      }\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(error);\n    }));\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/me`).pipe(tap(user => {\n      const rememberMe = this.tokenService.getRememberMe();\n      this.tokenService.setUser(user, rememberMe);\n      this.currentUserSubject.next(user);\n    }), catchError(this.handleError));\n  }\n  changePassword(request) {\n    return this.http.post(`${this.API_URL}/change-password`, request).pipe(catchError(this.handleError));\n  }\n  forgotPassword(request) {\n    return this.http.post(`${this.API_URL}/forgot-password`, request).pipe(catchError(this.handleError));\n  }\n  resetPassword(request) {\n    return this.http.post(`${this.API_URL}/reset-password`, request).pipe(catchError(this.handleError));\n  }\n  loginWithOAuth(request) {\n    return this.http.post(`${this.API_URL}/oauth-login`, request).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        // For OAuth, default to remember me = true for better UX\n        const rememberMe = true;\n        this.tokenService.setRememberMe(rememberMe);\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        this.tokenService.setUser(response.user, rememberMe);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  // Token management - delegated to TokenService\n  getToken() {\n    return this.tokenService.getToken();\n  }\n  checkTokenExpiration() {\n    const token = this.getToken();\n    if (token && this.isTokenExpired(token)) {\n      // Try to refresh the token\n      this.refreshToken().subscribe({\n        next: () => {\n          // Token refreshed successfully\n        },\n        error: () => {\n          // Refresh failed, logout user\n          this.clearAuthData();\n        }\n      });\n    }\n  }\n  startTokenExpirationCheck() {\n    // Check token expiration every 5 minutes\n    setInterval(() => {\n      this.checkTokenExpiration();\n    }, 5 * 60 * 1000);\n  }\n  clearAuthData() {\n    // Clear from both localStorage and sessionStorage\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\n    sessionStorage.removeItem(this.TOKEN_KEY);\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\n    sessionStorage.removeItem(this.USER_KEY);\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    return !!token && !this.isTokenExpired(token);\n  }\n  hasRole(role) {\n    const user = this.currentUserSubject.value;\n    return user?.roles.includes(role) || false;\n  }\n  hasPermission(permission) {\n    const user = this.currentUserSubject.value;\n    return user?.permissions.includes(permission) || false;\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(errorMessage);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.TokenService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;;AAmBrD,OAAM,MAAOC,WAAW;EAatBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,YAA0B;IAF1B,SAAI,GAAJF,IAAI;IACJ,WAAM,GAANC,MAAM;IACN,iBAAY,GAAZC,YAAY;IAfL,YAAO,GAAG,WAAW;IACrB,cAAS,GAAG,cAAc;IAC1B,sBAAiB,GAAG,eAAe;IACnC,aAAQ,GAAG,WAAW;IACtB,oBAAe,GAAG,aAAa;IAExC,uBAAkB,GAAG,IAAIR,eAAe,CAAkB,IAAI,CAAC;IAChE,iBAAY,GAAG,IAAI,CAACS,kBAAkB,CAACC,YAAY,EAAE;IAEpD,2BAAsB,GAAG,IAAIV,eAAe,CAAU,KAAK,CAAC;IAC7D,qBAAgB,GAAG,IAAI,CAACW,sBAAsB,CAACD,YAAY,EAAE;IAOlE,IAAI,CAACE,cAAc,EAAE;EACvB;EAEQA,cAAc;IACpB,MAAMC,KAAK,GAAG,IAAI,CAACL,YAAY,CAACM,QAAQ,EAAE;IAC1C,MAAMC,IAAI,GAAG,IAAI,CAACP,YAAY,CAACQ,aAAa,EAAE;IAE9C,IAAIH,KAAK,IAAIE,IAAI,EAAE;MACjB;MACA,IAAI,IAAI,CAACP,YAAY,CAACS,cAAc,CAACJ,KAAK,CAAC,EAAE;QAC3C;QACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;UAC5BC,IAAI,EAAE,MAAK;YACT,IAAI,CAACX,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;YAClC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;YACtC,IAAI,CAACC,yBAAyB,EAAE;UAClC,CAAC;UACDC,KAAK,EAAE,MAAK;YACV;YACA,IAAI,CAACC,aAAa,EAAE;UACtB;SACD,CAAC;OACH,MAAM;QACL,IAAI,CAACd,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;QAClC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAACC,yBAAyB,EAAE;;;EAGtC;EAEAG,KAAK,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,QAAQ,EAAEF,WAAW,CAAC,CACtEG,IAAI,CACHzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D;QACA,MAAMiB,UAAU,GAAGP,WAAW,CAACO,UAAU,IAAI,KAAK;QAClD,IAAI,CAACxB,YAAY,CAACyB,aAAa,CAACD,UAAU,CAAC;QAE3C,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAI,CAACxB,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFlB,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQ,CAACC,QAAiD;IACxD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAAC,GAAG,iBAAiB,GAAG,UAAU;IAErF,OAAO,IAAI,CAACjC,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,IAAIa,QAAQ,EAAE,EAAED,QAAQ,CAAC,CACzEX,IAAI,CACHzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D,IAAI,CAACmB,QAAQ,CAACL,QAAQ,CAACE,WAAW,CAAC;QACnC,IAAIF,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACiB,eAAe,CAACN,QAAQ,CAACX,YAAY,CAAC;;QAE7C,IAAI,CAACkB,OAAO,CAACP,QAAQ,CAACd,IAAI,CAAC;QAC3B,IAAI,CAACN,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFlB,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAC7B;EACL;EAEQI,oBAAoB,CAACF,QAAiD;IAC5E,OAAO,mBAAmB,IAAIA,QAAQ;EACxC;EAEAG,MAAM;IACJ,OAAO,IAAI,CAACpC,IAAI,CAACoB,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE,EAAE,CAAC,CAChDC,IAAI,CACHzB,GAAG,CAAC,MAAK;MACP,IAAI,CAACoB,aAAa,EAAE;IACtB,CAAC,CAAC,EACFrB,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACqB,aAAa,EAAE;MACpB,OAAOtB,UAAU,CAAC,eAAe,CAAC;IACpC,CAAC,CAAC,CACH;EACL;EAEAiB,YAAY;IACV,MAAMA,YAAY,GAAG,IAAI,CAACV,YAAY,CAACmC,eAAe,EAAE;IACxD,IAAI,CAACzB,YAAY,EAAE;MACjB,OAAOjB,UAAU,CAAC,4BAA4B,CAAC;;IAGjD,OAAO,IAAI,CAACK,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,gBAAgB,EAAE;MAAET;IAAY,CAAE,CAAC,CACnFU,IAAI,CACHzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;QAC5C,MAAMC,UAAU,GAAG,IAAI,CAACxB,YAAY,CAACoC,aAAa,EAAE;QACpD,IAAI,CAACpC,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAIH,QAAQ,CAACd,IAAI,EAAE;UACjB,IAAI,CAACP,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;UACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;;;IAGjD,CAAC,CAAC,EACFb,UAAU,CAACoB,KAAK,IAAG;MACjB,IAAI,CAACC,aAAa,EAAE;MACpB,OAAOtB,UAAU,CAACqB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEAuB,cAAc;IACZ,OAAO,IAAI,CAACvC,IAAI,CAACwC,GAAG,CAAW,GAAG,IAAI,CAACnB,OAAO,KAAK,CAAC,CACjDC,IAAI,CACHzB,GAAG,CAACY,IAAI,IAAG;MACT,MAAMiB,UAAU,GAAG,IAAI,CAACxB,YAAY,CAACoC,aAAa,EAAE;MACpD,IAAI,CAACpC,YAAY,CAAC4B,OAAO,CAACrB,IAAI,EAAEiB,UAAU,CAAC;MAC3C,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;IACpC,CAAC,CAAC,EACFb,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAC7B;EACL;EAEAU,cAAc,CAACC,OAA8B;IAC3C,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC1B,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAAC;EACvC;EAEAY,cAAc,CAACD,OAA8B;IAC3C,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC1B,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAAC;EACvC;EAEAa,aAAa,CAACF,OAA6B;IACzC,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,iBAAiB,EAAEqB,OAAO,CAAC,CAC/EpB,IAAI,CAAC1B,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAAC;EACvC;EAEAc,cAAc,CAACH,OAA0B;IACvC,OAAO,IAAI,CAAC1C,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,cAAc,EAAEqB,OAAO,CAAC,CACxEpB,IAAI,CACHzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D;QACA,MAAMiB,UAAU,GAAG,IAAI;QACvB,IAAI,CAACxB,YAAY,CAACyB,aAAa,CAACD,UAAU,CAAC;QAE3C,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACV,YAAY,CAAC2B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAI,CAACxB,YAAY,CAAC4B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFlB,UAAU,CAAC,IAAI,CAACmC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAvB,QAAQ;IACN,OAAO,IAAI,CAACN,YAAY,CAACM,QAAQ,EAAE;EACrC;EAEAsC,oBAAoB;IAClB,MAAMvC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,IAAI,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC,EAAE;MACvC;MACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;QAC5BC,IAAI,EAAE,MAAK;UACT;QAAA,CACD;QACDE,KAAK,EAAE,MAAK;UACV;UACA,IAAI,CAACC,aAAa,EAAE;QACtB;OACD,CAAC;;EAEN;EAEAF,yBAAyB;IACvB;IACAgC,WAAW,CAAC,MAAK;MACf,IAAI,CAACD,oBAAoB,EAAE;IAC7B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;EACnB;EAEQ7B,aAAa;IACnB;IACA+B,YAAY,CAACC,UAAU,CAAC,IAAI,CAACC,SAAS,CAAC;IACvCF,YAAY,CAACC,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IAC/CH,YAAY,CAACC,UAAU,CAAC,IAAI,CAACG,QAAQ,CAAC;IACtCJ,YAAY,CAACC,UAAU,CAAC,IAAI,CAACI,eAAe,CAAC;IAE7CC,cAAc,CAACL,UAAU,CAAC,IAAI,CAACC,SAAS,CAAC;IACzCI,cAAc,CAACL,UAAU,CAAC,IAAI,CAACE,iBAAiB,CAAC;IACjDG,cAAc,CAACL,UAAU,CAAC,IAAI,CAACG,QAAQ,CAAC;IAExC,IAAI,CAACjD,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACb,MAAM,CAACsD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAe;IACb,MAAMjD,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,OAAO,CAAC,CAACD,KAAK,IAAI,CAAC,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC;EAC/C;EAIAkD,OAAO,CAACC,IAAY;IAClB,MAAMjD,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACwD,KAAK;IAC1C,OAAOlD,IAAI,EAAEmD,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAI,KAAK;EAC5C;EAEAI,aAAa,CAACC,UAAkB;IAC9B,MAAMtD,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACwD,KAAK;IAC1C,OAAOlD,IAAI,EAAEuD,WAAW,CAACH,QAAQ,CAACE,UAAU,CAAC,IAAI,KAAK;EACxD;EAEQhC,WAAW,CAACf,KAAU;IAC5B,IAAIiD,YAAY,GAAG,mBAAmB;IAEtC,IAAIjD,KAAK,CAACA,KAAK,EAAEkD,OAAO,EAAE;MACxBD,YAAY,GAAGjD,KAAK,CAACA,KAAK,CAACkD,OAAO;KACnC,MAAM,IAAIlD,KAAK,CAACkD,OAAO,EAAE;MACxBD,YAAY,GAAGjD,KAAK,CAACkD,OAAO;;IAG9B,OAAOvE,UAAU,CAACsE,YAAY,CAAC;EACjC;;;uBAjQWnE,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAqE,SAAXrE,WAAW;MAAAsE,YAFV;IAAM;EAAA", "names": ["BehaviorSubject", "throwError", "catchError", "tap", "AuthService", "constructor", "http", "router", "tokenService", "currentUserSubject", "asObservable", "isAuthenticatedSubject", "initializeAuth", "token", "getToken", "user", "getStoredUser", "isTokenExpired", "refreshToken", "subscribe", "next", "startTokenExpirationCheck", "error", "clearAuthData", "login", "credentials", "post", "API_URL", "pipe", "response", "success", "accessToken", "rememberMe", "setRememberMe", "setToken", "setRefreshToken", "setUser", "handleError", "register", "userData", "endpoint", "isOracleRegistration", "logout", "getRefreshToken", "getRememberMe", "getCurrentUser", "get", "changePassword", "request", "forgotPassword", "resetPassword", "loginWithOAuth", "checkTokenExpiration", "setInterval", "localStorage", "removeItem", "TOKEN_KEY", "REFRESH_TOKEN_KEY", "USER_KEY", "REMEMBER_ME_KEY", "sessionStorage", "navigate", "isAuthenticated", "hasRole", "role", "value", "roles", "includes", "hasPermission", "permission", "permissions", "errorMessage", "message", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { map, catchError, tap } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport { TokenService } from './token.service';\r\nimport {\r\n  LoginRequest,\r\n  RegisterRequest,\r\n  OracleRegisterRequest,\r\n  AuthResponse,\r\n  UserInfo,\r\n  ChangePasswordRequest,\r\n  ForgotPasswordRequest,\r\n  ResetPasswordRequest,\r\n  ApiResponse,\r\n  OAuthLoginRequest\r\n} from '../models/auth.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private readonly API_URL = '/api/auth';\r\n  private readonly TOKEN_KEY = 'access_token';\r\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\r\n  private readonly USER_KEY = 'user_info';\r\n  private readonly REMEMBER_ME_KEY = 'remember_me';\r\n\r\n  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\r\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private router: Router,\r\n    private tokenService: TokenService\r\n  ) {\r\n    this.initializeAuth();\r\n  }\r\n\r\n  private initializeAuth(): void {\r\n    const token = this.tokenService.getToken();\r\n    const user = this.tokenService.getStoredUser();\r\n\r\n    if (token && user) {\r\n      // Check if token is expired\r\n      if (this.tokenService.isTokenExpired(token)) {\r\n        // Try to refresh the token\r\n        this.refreshToken().subscribe({\r\n          next: () => {\r\n            this.currentUserSubject.next(user);\r\n            this.isAuthenticatedSubject.next(true);\r\n            this.startTokenExpirationCheck();\r\n          },\r\n          error: () => {\r\n            // Refresh failed, clear auth data\r\n            this.clearAuthData();\r\n          }\r\n        });\r\n      } else {\r\n        this.currentUserSubject.next(user);\r\n        this.isAuthenticatedSubject.next(true);\r\n        this.startTokenExpirationCheck();\r\n      }\r\n    }\r\n  }\r\n\r\n  login(credentials: LoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // Store remember me preference\r\n            const rememberMe = credentials.rememberMe || false;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {\r\n    // Determine endpoint based on data type\r\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            this.setToken(response.accessToken);\r\n            if (response.refreshToken) {\r\n              this.setRefreshToken(response.refreshToken);\r\n            }\r\n            this.setUser(response.user);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {\r\n    return 'professionalTitle' in userData;\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(`${this.API_URL}/logout`, {})\r\n      .pipe(\r\n        tap(() => {\r\n          this.clearAuthData();\r\n        }),\r\n        catchError(() => {\r\n          // Even if logout fails on server, clear local data\r\n          this.clearAuthData();\r\n          return throwError('Logout failed');\r\n        })\r\n      );\r\n  }\r\n\r\n  refreshToken(): Observable<AuthResponse> {\r\n    const refreshToken = this.tokenService.getRefreshToken();\r\n    if (!refreshToken) {\r\n      return throwError('No refresh token available');\r\n    }\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken) {\r\n            const rememberMe = this.tokenService.getRememberMe();\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            if (response.user) {\r\n              this.tokenService.setUser(response.user, rememberMe);\r\n              this.currentUserSubject.next(response.user);\r\n            }\r\n          }\r\n        }),\r\n        catchError(error => {\r\n          this.clearAuthData();\r\n          return throwError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  getCurrentUser(): Observable<UserInfo> {\r\n    return this.http.get<UserInfo>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(user => {\r\n          const rememberMe = this.tokenService.getRememberMe();\r\n          this.tokenService.setUser(user, rememberMe);\r\n          this.currentUserSubject.next(user);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // For OAuth, default to remember me = true for better UX\r\n            const rememberMe = true;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Token management - delegated to TokenService\r\n  getToken(): string | null {\r\n    return this.tokenService.getToken();\r\n  }\r\n\r\n  checkTokenExpiration(): void {\r\n    const token = this.getToken();\r\n    if (token && this.isTokenExpired(token)) {\r\n      // Try to refresh the token\r\n      this.refreshToken().subscribe({\r\n        next: () => {\r\n          // Token refreshed successfully\r\n        },\r\n        error: () => {\r\n          // Refresh failed, logout user\r\n          this.clearAuthData();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  startTokenExpirationCheck(): void {\r\n    // Check token expiration every 5 minutes\r\n    setInterval(() => {\r\n      this.checkTokenExpiration();\r\n    }, 5 * 60 * 1000);\r\n  }\r\n\r\n  private clearAuthData(): void {\r\n    // Clear from both localStorage and sessionStorage\r\n    localStorage.removeItem(this.TOKEN_KEY);\r\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\r\n    localStorage.removeItem(this.USER_KEY);\r\n    localStorage.removeItem(this.REMEMBER_ME_KEY);\r\n\r\n    sessionStorage.removeItem(this.TOKEN_KEY);\r\n    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);\r\n    sessionStorage.removeItem(this.USER_KEY);\r\n\r\n    this.currentUserSubject.next(null);\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    const token = this.getToken();\r\n    return !!token && !this.isTokenExpired(token);\r\n  }\r\n\r\n\r\n\r\n  hasRole(role: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.roles.includes(role) || false;\r\n  }\r\n\r\n  hasPermission(permission: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.permissions.includes(permission) || false;\r\n  }\r\n\r\n  private handleError(error: any): Observable<never> {\r\n    let errorMessage = 'An error occurred';\r\n\r\n    if (error.error?.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.message) {\r\n      errorMessage = error.message;\r\n    }\r\n\r\n    return throwError(errorMessage);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}