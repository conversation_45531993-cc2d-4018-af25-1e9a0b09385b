import { Component, OnInit } from '@angular/core';
import { ProfileService } from './profile/services/profile.service';
import { UserProfile } from './profile/models/profile.models';

@Component({
  selector: 'app-test-api-connection',
  template: `
    <div style="padding: 20px; margin: 20px; border: 2px solid #ccc; border-radius: 8px;">
      <h2>API Connection Test</h2>
      
      <div style="margin: 10px 0;">
        <button (click)="testGetProfile()" style="margin-right: 10px; padding: 8px 16px;">
          Test Get Profile (luna-starweaver)
        </button>
        <button (click)="testGetPublicProfiles()" style="margin-right: 10px; padding: 8px 16px;">
          Test Get Public Profiles
        </button>
        <button (click)="testSearchProfiles()" style="padding: 8px 16px;">
          Test Search Profiles
        </button>
      </div>

      <div style="margin-top: 20px;">
        <h3>Results:</h3>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">
          <pre>{{ results | json }}</pre>
        </div>
      </div>

      <div style="margin-top: 20px;" *ngIf="error">
        <h3 style="color: red;">Error:</h3>
        <div style="background: #ffe6e6; padding: 10px; border-radius: 4px; color: red;">
          {{ error }}
        </div>
      </div>
    </div>
  `
})
export class TestApiConnectionComponent implements OnInit {
  results: any = null;
  error: string = '';

  constructor(private profileService: ProfileService) {}

  ngOnInit() {
    console.log('TestApiConnectionComponent initialized');
  }

  testGetProfile() {
    this.error = '';
    this.results = 'Loading...';
    
    this.profileService.getProfile('luna-starweaver').subscribe({
      next: (profile: UserProfile) => {
        this.results = {
          success: true,
          message: 'Profile loaded successfully!',
          data: {
            id: profile.id,
            username: profile.username,
            firstName: profile.firstName,
            lastName: profile.lastName,
            professionalTitle: profile.professionalTitle,
            headline: profile.headline,
            profileViews: profile.profileViews,
            skillsCount: profile.skills?.length || 0,
            blogPostsCount: profile.blogPosts?.length || 0,
            certificationsCount: profile.certifications?.length || 0
          }
        };
        console.log('Profile loaded:', profile);
      },
      error: (err) => {
        this.error = err.message || 'Unknown error occurred';
        this.results = null;
        console.error('Error loading profile:', err);
      }
    });
  }

  testGetPublicProfiles() {
    this.error = '';
    this.results = 'Loading...';
    
    this.profileService.getPublicProfiles(1, 5).subscribe({
      next: (result) => {
        this.results = {
          success: true,
          message: 'Public profiles loaded successfully!',
          data: {
            totalCount: result.totalCount,
            currentPage: result.currentPage,
            profilesCount: result.profiles.length,
            profiles: result.profiles.map(p => ({
              id: p.id,
              username: p.username,
              firstName: p.firstName,
              lastName: p.lastName,
              professionalTitle: p.professionalTitle
            }))
          }
        };
        console.log('Public profiles loaded:', result);
      },
      error: (err) => {
        this.error = err.message || 'Unknown error occurred';
        this.results = null;
        console.error('Error loading public profiles:', err);
      }
    });
  }

  testSearchProfiles() {
    this.error = '';
    this.results = 'Loading...';
    
    this.profileService.searchProfilesByTerm('astrology', 1, 5).subscribe({
      next: (result) => {
        this.results = {
          success: true,
          message: 'Search completed successfully!',
          data: {
            searchTerm: 'astrology',
            totalCount: result.totalCount,
            currentPage: result.currentPage,
            profilesCount: result.profiles.length,
            profiles: result.profiles.map(p => ({
              id: p.id,
              username: p.username,
              firstName: p.firstName,
              lastName: p.lastName,
              professionalTitle: p.professionalTitle
            }))
          }
        };
        console.log('Search results:', result);
      },
      error: (err) => {
        this.error = err.message || 'Unknown error occurred';
        this.results = null;
        console.error('Error searching profiles:', err);
      }
    });
  }
}
