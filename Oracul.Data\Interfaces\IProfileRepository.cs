using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Repository interface for profile-specific operations
    /// </summary>
    public interface IProfileRepository : IRepository<UserProfile>
    {
        // Profile retrieval methods
        Task<UserProfile?> GetByUserIdAsync(int userId);
        Task<UserProfile?> GetBySlugAsync(string slug);
        Task<UserProfile?> GetByUsernameAsync(string username);
        Task<UserProfile?> GetCompleteProfileAsync(int profileId);
        Task<UserProfile?> GetCompleteProfileBySlugAsync(string slug);
        Task<UserProfile?> GetCompleteProfileByUserIdAsync(int userId);

        // Profile search and filtering
        Task<IEnumerable<UserProfile>> GetPublicProfilesAsync(int page = 1, int pageSize = 20);
        Task<IEnumerable<UserProfile>> SearchProfilesAsync(string searchTerm, int page = 1, int pageSize = 20);
        Task<IEnumerable<UserProfile>> GetProfilesBySkillAsync(string skillName, int page = 1, int pageSize = 20);
        Task<IEnumerable<UserProfile>> GetProfilesByLocationAsync(string location, int page = 1, int pageSize = 20);

        // Profile analytics
        Task IncrementProfileViewAsync(int profileId);
        Task<int> GetProfileViewCountAsync(int profileId);

        // Profile validation
        Task<bool> IsSlugAvailableAsync(string slug, int? excludeProfileId = null);
        Task<bool> IsUsernameAvailableAsync(string username, int? excludeProfileId = null);

        // Profile completion
        Task<int> CalculateProfileCompletionPercentageAsync(int profileId);
        Task UpdateProfileCompletionPercentageAsync(int profileId);

        // Bulk operations
        Task<IEnumerable<UserProfile>> GetProfilesByUserIdsAsync(IEnumerable<int> userIds);
    }
}
