{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/oauth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"../../core/i18n/translation.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/divider\";\nfunction LoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getEmailErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction LoginComponent_mat_icon_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_icon_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"login\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, oauthService, router, route, snackBar, t) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.t = t;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.returnUrl = '/';\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n    // Redirect if already logged in\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      if (isAuth) {\n        this.router.navigate([this.returnUrl]);\n      }\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const loginRequest = {\n        email: this.loginForm.value.email,\n        password: this.loginForm.value.password,\n        rememberMe: this.loginForm.value.rememberMe\n      };\n      this.authService.login(loginRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open(this.t.auth.loginSuccess, this.t.common.close, {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate([this.returnUrl]);\n          } else {\n            this.snackBar.open(response.message || this.t.auth.loginError, this.t.common.close, {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = this.getErrorMessage(error);\n          this.snackBar.open(errorMessage, this.t.common.close, {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getEmailErrorMessage() {\n    const emailControl = this.loginForm.get('email');\n    if (emailControl?.hasError('required')) {\n      return this.t.auth.emailRequired;\n    }\n    if (emailControl?.hasError('email')) {\n      return this.t.auth.invalidEmail;\n    }\n    return '';\n  }\n  getPasswordErrorMessage() {\n    const passwordControl = this.loginForm.get('password');\n    if (passwordControl?.hasError('required')) {\n      return this.t.auth.passwordRequired;\n    }\n    if (passwordControl?.hasError('minlength')) {\n      return this.t.auth.passwordTooShort;\n    }\n    return '';\n  }\n  getErrorMessage(error) {\n    // Handle different types of errors\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (error?.error?.message) {\n      const message = error.error.message.toLowerCase();\n      // Account locked\n      if (message.includes('locked') || message.includes('temporarily locked')) {\n        return 'Акаунтът е временно заключен поради множество неуспешни опити за вход. Моля, опитайте отново след 30 минути.';\n      }\n      // Invalid credentials\n      if (message.includes('invalid') || message.includes('password') || message.includes('email')) {\n        return 'Невалиден имейл или парола. Моля, проверете данните си и опитайте отново.';\n      }\n      // Account deactivated\n      if (message.includes('deactivated') || message.includes('inactive')) {\n        return 'Акаунтът е деактивиран. Моля, свържете се с поддръжката.';\n      }\n      return error.error.message;\n    }\n    // Network errors\n    if (error?.status === 0 || error?.name === 'HttpErrorResponse') {\n      return 'Проблем с мрежовата връзка. Моля, проверете интернет връзката си и опитайте отново.';\n    }\n    // Server errors\n    if (error?.status >= 500) {\n      return 'Възникна проблем със сървъра. Моля, опитайте отново след малко.';\n    }\n    // Default error message\n    return this.t.auth.loginError;\n  }\n  navigateToRegister() {\n    this.router.navigate(['/register'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  navigateToForgotPassword() {\n    this.router.navigate(['/forgot-password']);\n  }\n  signInWithGoogle() {\n    this.isLoading = true;\n    this.oauthService.signInWithGooglePopup().subscribe({\n      next: oauthUser => {\n        this.handleOAuthLogin(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Неуспешен вход с Google. Моля, опитайте отново.', this.t.common.close, {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  signInWithFacebook() {\n    this.isLoading = true;\n    this.oauthService.signInWithFacebook().subscribe({\n      next: oauthUser => {\n        this.handleOAuthLogin(oauthUser);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open('Неуспешен вход с Facebook. Моля, опитайте отново.', this.t.common.close, {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  handleOAuthLogin(oauthUser) {\n    const oauthRequest = {\n      provider: oauthUser.provider,\n      accessToken: oauthUser.accessToken,\n      email: oauthUser.email,\n      firstName: oauthUser.firstName,\n      lastName: oauthUser.lastName,\n      profilePictureUrl: oauthUser.profilePictureUrl\n    };\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          this.snackBar.open(`Добре дошли! Влязохте с ${oauthUser.provider}`, this.t.common.close, {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate([this.returnUrl]);\n        } else {\n          this.snackBar.open(response.message || 'Неуспешен OAuth вход', this.t.common.close, {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'Неуспешен OAuth вход. Моля, опитайте отново.', this.t.common.close, {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 58,\n      vars: 23,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"login-icon\"], [1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\", 3, \"placeholder\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\", \"placeholder\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"remember-me-container\"], [\"formControlName\", \"rememberMe\", \"color\", \"primary\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [1, \"divider-container\"], [1, \"divider-text\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"google-button\", 3, \"disabled\", \"click\"], [\"src\", \"https://developers.google.com/identity/images/g-logo.png\", \"alt\", \"Google\", 1, \"oauth-icon\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"facebook-button\", 3, \"disabled\", \"click\"], [1, \"oauth-icon\", \"facebook-icon\"], [1, \"login-actions\"], [1, \"action-links\"], [\"mat-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 3, \"click\"], [1, \"register-section\"], [1, \"register-text\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"full-width\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\")(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" \\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0434\\u0430\\u043D\\u043D\\u0438\\u0442\\u0435 \\u0441\\u0438 \\u0437\\u0430 \\u0434\\u043E\\u0441\\u0442\\u044A\\u043F \\u0434\\u043E \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\\u0430 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 5)(12, \"mat-label\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 6);\n          i0.ɵɵelementStart(15, \"mat-icon\", 7);\n          i0.ɵɵtext(16, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, LoginComponent_mat_error_17_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 5)(19, \"mat-label\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 9);\n          i0.ɵɵelementStart(22, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_22_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 1, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 11)(27, \"mat-checkbox\", 12);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"button\", 13);\n          i0.ɵɵtemplate(30, LoginComponent_mat_icon_30_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtemplate(31, LoginComponent_mat_icon_31_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 14);\n          i0.ɵɵelement(34, \"mat-divider\");\n          i0.ɵɵelementStart(35, \"span\", 15);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"mat-divider\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 16)(39, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_39_listener() {\n            return ctx.signInWithGoogle();\n          });\n          i0.ɵɵelement(40, \"img\", 18);\n          i0.ɵɵtext(41, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0441 Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_42_listener() {\n            return ctx.signInWithFacebook();\n          });\n          i0.ɵɵelementStart(43, \"mat-icon\", 20);\n          i0.ɵɵtext(44, \"facebook\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0441 Facebook \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(46, \"mat-card-actions\", 21)(47, \"div\", 22)(48, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_48_listener() {\n            return ctx.navigateToForgotPassword();\n          });\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(50, \"mat-divider\");\n          i0.ɵɵelementStart(51, \"div\", 24)(52, \"p\", 25);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_54_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(55, \"mat-icon\");\n          i0.ɵɵtext(56, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_11_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.loginTitle, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.t.auth.email);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u044F \\u0438\\u043C\\u0435\\u0439\\u043B\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.t.auth.password);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.rememberMe, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"\\u0412\\u043B\\u0438\\u0437\\u0430\\u043D\\u0435...\" : ctx.t.auth.signIn, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.auth.orContinueWith);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.forgotPassword, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.auth.dontHaveAccount);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.auth.createAccount, \" \");\n        }\n      },\n      dependencies: [i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatProgressSpinner, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, i13.MatInput, i14.MatCheckbox, i15.MatDivider],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  min-height: 100vh;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-gradient-auth);\\r\\n}\\r\\n\\r\\n.login-card[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  max-width: 400px;\\r\\n  padding: 0;\\r\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-auth);\\r\\n  color: white;\\r\\n  padding: 24px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 8px;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 24px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.login-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 28px;\\r\\n  width: 28px;\\r\\n  height: 28px;\\r\\n}\\r\\n\\r\\n.login-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 14px;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.login-form[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.login-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 16px;\\r\\n  font-weight: 500;\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.login-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n.remember-me-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\n.remember-me-container[_ngcontent-%COMP%]   .mat-checkbox[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.login-actions[_ngcontent-%COMP%] {\\r\\n  padding: 0 24px 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.action-links[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.register-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.register-text[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.mat-form-field[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n\\r\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.divider-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  margin: 24px 0 16px;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.divider-text[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n  white-space: nowrap;\\r\\n}\\r\\n\\r\\n.oauth-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.oauth-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 14px;\\r\\n  font-weight: 500;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n.oauth-icon[_ngcontent-%COMP%] {\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%] {\\r\\n  border-color: #dadce0;\\r\\n  color: #3c4043;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f8f9fa;\\r\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%] {\\r\\n  border-color: #1877f2;\\r\\n  color: #1877f2;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f0f2f5;\\r\\n}\\r\\n\\r\\n.facebook-icon[_ngcontent-%COMP%] {\\r\\n  color: #1877f2;\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .login-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .login-card[_ngcontent-%COMP%] {\\r\\n    max-width: 100%;\\r\\n  }\\r\\n\\r\\n  .login-form[_ngcontent-%COMP%] {\\r\\n    padding: 20px;\\r\\n  }\\r\\n\\r\\n  .login-actions[_ngcontent-%COMP%] {\\r\\n    padding: 0 20px 20px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n  .success-snackbar {\\r\\n  background-color: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n  .error-snackbar {\\r\\n  background-color: #f44336;\\r\\n  color: white;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;ICuBzDC,iCAAsF;IACpFA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,8DACF;;;;;IAqBAA,iCAA4F;IAC1FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,iEACF;;;;;IAiBAA,gCAA4B;IAC1BA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA6B;IAAAA,qBAAK;IAAAA,iBAAW;;;ADvDvD,OAAM,MAAOC,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB,EACtBC,CAAqB;IANpB,gBAAW,GAAXN,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IACN,UAAK,GAALC,KAAK;IACL,aAAQ,GAARC,QAAQ;IACT,MAAC,GAADC,CAAC;IAXV,cAAS,GAAG,KAAK;IACjB,iBAAY,GAAG,IAAI;IACnB,cAAS,GAAG,GAAG;IAWb,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACa,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACf,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG;IAEpE;IACA,IAAI,CAAChB,WAAW,CAACiB,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAIA,MAAM,EAAE;QACV,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;;IAE1C,CAAC,CAAC;EACJ;EAEAO,QAAQ;IACN,IAAI,IAAI,CAACf,SAAS,CAACgB,KAAK,EAAE;MACxB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMC,YAAY,GAAiB;QACjChB,KAAK,EAAE,IAAI,CAACF,SAAS,CAACmB,KAAK,CAACjB,KAAK;QACjCE,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACmB,KAAK,CAACf,QAAQ;QACvCE,UAAU,EAAE,IAAI,CAACN,SAAS,CAACmB,KAAK,CAACb;OAClC;MAED,IAAI,CAACZ,WAAW,CAAC0B,KAAK,CAACF,YAAY,CAAC,CAACN,SAAS,CAAC;QAC7CS,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACL,SAAS,GAAG,KAAK;UACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAAC,IAAI,CAACzB,CAAC,CAAC0B,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC3B,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;cAChEC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,kBAAkB;aAChC,CAAC;YACF,IAAI,CAAClC,MAAM,CAACkB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;WACvC,MAAM;YACL,IAAI,CAACV,QAAQ,CAAC0B,IAAI,CAACF,QAAQ,CAACS,OAAO,IAAI,IAAI,CAAChC,CAAC,CAAC0B,IAAI,CAACO,UAAU,EAAE,IAAI,CAACjC,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;cAClFC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,gBAAgB;aAC9B,CAAC;;QAEN,CAAC;QACDG,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChB,SAAS,GAAG,KAAK;UACtB,MAAMiB,YAAY,GAAG,IAAI,CAACC,eAAe,CAACF,KAAK,CAAC;UAChD,IAAI,CAACnC,QAAQ,CAAC0B,IAAI,CAACU,YAAY,EAAE,IAAI,CAACnC,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;YACpDC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACM,oBAAoB,EAAE;;EAE/B;EAEQA,oBAAoB;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtC,SAAS,CAACuC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAAC1C,SAAS,CAAC2C,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,oBAAoB;IAClB,MAAMC,YAAY,GAAG,IAAI,CAAC9C,SAAS,CAAC2C,GAAG,CAAC,OAAO,CAAC;IAChD,IAAIG,YAAY,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACtC,OAAO,IAAI,CAAChD,CAAC,CAAC0B,IAAI,CAACuB,aAAa;;IAElC,IAAIF,YAAY,EAAEC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACnC,OAAO,IAAI,CAAChD,CAAC,CAAC0B,IAAI,CAACwB,YAAY;;IAEjC,OAAO,EAAE;EACX;EAEAC,uBAAuB;IACrB,MAAMC,eAAe,GAAG,IAAI,CAACnD,SAAS,CAAC2C,GAAG,CAAC,UAAU,CAAC;IACtD,IAAIQ,eAAe,EAAEJ,QAAQ,CAAC,UAAU,CAAC,EAAE;MACzC,OAAO,IAAI,CAAChD,CAAC,CAAC0B,IAAI,CAAC2B,gBAAgB;;IAErC,IAAID,eAAe,EAAEJ,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC1C,OAAO,IAAI,CAAChD,CAAC,CAAC0B,IAAI,CAAC4B,gBAAgB;;IAErC,OAAO,EAAE;EACX;EAEQlB,eAAe,CAACF,KAAU;IAChC;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;;IAGd,IAAIA,KAAK,EAAEA,KAAK,EAAEF,OAAO,EAAE;MACzB,MAAMA,OAAO,GAAGE,KAAK,CAACA,KAAK,CAACF,OAAO,CAACuB,WAAW,EAAE;MAEjD;MACA,IAAIvB,OAAO,CAACwB,QAAQ,CAAC,QAAQ,CAAC,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACxE,OAAO,8GAA8G;;MAGvH;MACA,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,SAAS,CAAC,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,UAAU,CAAC,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC5F,OAAO,2EAA2E;;MAGpF;MACA,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,aAAa,CAAC,IAAIxB,OAAO,CAACwB,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,OAAO,0DAA0D;;MAGnE,OAAOtB,KAAK,CAACA,KAAK,CAACF,OAAO;;IAG5B;IACA,IAAIE,KAAK,EAAEuB,MAAM,KAAK,CAAC,IAAIvB,KAAK,EAAEwB,IAAI,KAAK,mBAAmB,EAAE;MAC9D,OAAO,qFAAqF;;IAG9F;IACA,IAAIxB,KAAK,EAAEuB,MAAM,IAAI,GAAG,EAAE;MACxB,OAAO,iEAAiE;;IAG1E;IACA,OAAO,IAAI,CAACzD,CAAC,CAAC0B,IAAI,CAACO,UAAU;EAC/B;EAEA0B,kBAAkB;IAChB,IAAI,CAAC9D,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAAEJ,WAAW,EAAE;QAAEF,SAAS,EAAE,IAAI,CAACA;MAAS;IAAE,CAAE,CAAC;EACrF;EAEAmD,wBAAwB;IACtB,IAAI,CAAC/D,MAAM,CAACkB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEA8C,gBAAgB;IACd,IAAI,CAAC3C,SAAS,GAAG,IAAI;IAErB,IAAI,CAACtB,YAAY,CAACkE,qBAAqB,EAAE,CAACjD,SAAS,CAAC;MAClDS,IAAI,EAAGyC,SAAoB,IAAI;QAC7B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;MAClC,CAAC;MACD7B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACnB,QAAQ,CAAC0B,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAACzB,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;UACzFC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEAkC,kBAAkB;IAChB,IAAI,CAAC/C,SAAS,GAAG,IAAI;IAErB,IAAI,CAACtB,YAAY,CAACqE,kBAAkB,EAAE,CAACpD,SAAS,CAAC;MAC/CS,IAAI,EAAGyC,SAAoB,IAAI;QAC7B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;MAClC,CAAC;MACD7B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACnB,QAAQ,CAAC0B,IAAI,CAAC,mDAAmD,EAAE,IAAI,CAACzB,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;UAC3FC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQiC,gBAAgB,CAACD,SAAoB;IAC3C,MAAMG,YAAY,GAAsB;MACtCC,QAAQ,EAAEJ,SAAS,CAACI,QAAQ;MAC5BC,WAAW,EAAEL,SAAS,CAACK,WAAW;MAClCjE,KAAK,EAAE4D,SAAS,CAAC5D,KAAK;MACtBkE,SAAS,EAAEN,SAAS,CAACM,SAAS;MAC9BC,QAAQ,EAAEP,SAAS,CAACO,QAAQ;MAC5BC,iBAAiB,EAAER,SAAS,CAACQ;KAC9B;IAED,IAAI,CAAC5E,WAAW,CAAC6E,cAAc,CAACN,YAAY,CAAC,CAACrD,SAAS,CAAC;MACtDS,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAIK,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAAC,2BAA2BsC,SAAS,CAACI,QAAQ,EAAE,EAAE,IAAI,CAACnE,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;YACvFC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UACF,IAAI,CAAClC,MAAM,CAACkB,QAAQ,CAAC,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;SACvC,MAAM;UACL,IAAI,CAACV,QAAQ,CAAC0B,IAAI,CAACF,QAAQ,CAACS,OAAO,IAAI,sBAAsB,EAAE,IAAI,CAAChC,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;YAClFC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACnB,QAAQ,CAAC0B,IAAI,CAACS,KAAK,IAAI,8CAA8C,EAAE,IAAI,CAAClC,CAAC,CAAC4B,MAAM,CAACC,KAAK,EAAE;UAC/FC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;;;uBA5NWvC,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAiF;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCd3BtF,8BAA6B;UAIQA,oBAAI;UAAAA,iBAAW;UAC5CA,YACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,oOACF;UAAAA,iBAAoB;UAGtBA,wCAAkB;UACcA;YAAA,OAAYuF,cAAU;UAAA,EAAC;UAEnDvF,0CAAwD;UAC3CA,aAAkB;UAAAA,iBAAY;UACzCA,4BAKuB;UACvBA,oCAAoB;UAAAA,sBAAK;UAAAA,iBAAW;UACpCA,6EAEY;UACdA,iBAAiB;UAGjBA,0CAAwD;UAC3CA,aAAqB;UAAAA,iBAAY;UAC5CA,4BAKkC;UAClCA,mCAMqC;UAFnCA;YAAA;UAAA,EAAsC;UAGtCA,iCAAU;UAAAA,aAAoD;UAAAA,iBAAW;UAE3EA,6EAEY;UACdA,iBAAiB;UAGjBA,gCAAmC;UAE/BA,aACF;UAAAA,iBAAe;UAIjBA,mCAKyB;UACvBA,2EAEW;UACXA,2EAA6C;UAC7CA,aACF;UAAAA,iBAAS;UAGTA,gCAA+B;UAC7BA,+BAA2B;UAC3BA,iCAA2B;UAAAA,aAA2B;UAAAA,iBAAO;UAC7DA,+BAA2B;UAC7BA,iBAAM;UAGNA,gCAA2B;UAOvBA;YAAA,OAASuF,sBAAkB;UAAA,EAAC;UAC5BvF,2BAAoG;UACpGA,iFACF;UAAAA,iBAAS;UAGTA,mCAKiC;UAA/BA;YAAA,OAASuF,wBAAoB;UAAA,EAAC;UAC9BvF,qCAA2C;UAAAA,yBAAQ;UAAAA,iBAAW;UAC9DA,mFACF;UAAAA,iBAAS;UAKfA,6CAAwC;UAMlCA;YAAA,OAASuF,8BAA0B;UAAA,EAAC;UACpCvF,aACF;UAAAA,iBAAS;UAGXA,+BAA2B;UAE3BA,gCAA8B;UACHA,aAA4B;UAAAA,iBAAI;UACzDA,mCAKqB;UADnBA;YAAA,OAASuF,wBAAoB;UAAA,EAAC;UAE9BvF,iCAAU;UAAAA,2BAAU;UAAAA,iBAAW;UAC/BA,aACF;UAAAA,iBAAS;;;;;UA7HTA,eACF;UADEA,sDACF;UAOMA,eAAuB;UAAvBA,yCAAuB;UAGdA,eAAkB;UAAlBA,sCAAkB;UAK3BA,eAAsC;UAAtCA,8IAAsC;UAG5BA,eAAwE;UAAxEA,6KAAwE;UAOzEA,eAAqB;UAArBA,yCAAqB;UAG9BA,eAA2C;UAA3CA,6DAA2C;UAS3CA,eAAkC;UAAlCA,mGAAkC;UAExBA,eAAoD;UAApDA,wEAAoD;UAEpDA,eAA8E;UAA9EA,uLAA8E;UAQxFA,eACF;UADEA,sDACF;UASAA,eAAsB;UAAtBA,wCAAsB;UACXA,eAAe;UAAfA,oCAAe;UAGfA,eAAgB;UAAhBA,qCAAgB;UAC3BA,eACF;UADEA,oHACF;UAK6BA,eAA2B;UAA3BA,+CAA2B;UAWpDA,eAAsB;UAAtBA,wCAAsB;UAWtBA,eAAsB;UAAtBA,wCAAsB;UAgBxBA,eACF;UADEA,0DACF;UAMyBA,eAA4B;UAA5BA,gDAA4B;UAQnDA,eACF;UADEA,yDACF", "names": ["Validators", "i0", "LoginComponent", "constructor", "formBuilder", "authService", "oauthService", "router", "route", "snackBar", "t", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "rememberMe", "ngOnInit", "returnUrl", "snapshot", "queryParams", "isAuthenticated$", "subscribe", "isAuth", "navigate", "onSubmit", "valid", "isLoading", "loginRequest", "value", "login", "next", "response", "success", "open", "auth", "loginSuccess", "common", "close", "duration", "panelClass", "message", "loginError", "error", "errorMessage", "getErrorMessage", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getEmailErrorMessage", "emailControl", "<PERSON><PERSON><PERSON><PERSON>", "emailRequired", "invalidEmail", "getPasswordErrorMessage", "passwordControl", "passwordRequired", "passwordTooShort", "toLowerCase", "includes", "status", "name", "navigateToRegister", "navigateToForgotPassword", "signInWithGoogle", "signInWithGooglePopup", "<PERSON><PERSON><PERSON><PERSON>ser", "handleOAuthLogin", "signInWithFacebook", "oauthRequest", "provider", "accessToken", "firstName", "lastName", "profilePictureUrl", "loginWithOAuth", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\login\\login.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { OAuthService, OAuthUser } from '../services/oauth.service';\r\nimport { LoginRequest, OAuthLoginRequest } from '../models/auth.models';\r\nimport { TranslationService } from '../../core/i18n/translation.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.css']\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginForm: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  returnUrl = '/';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private oauthService: OAuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar,\r\n    public t: TranslationService\r\n  ) {\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      rememberMe: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\r\n\r\n    // Redirect if already logged in\r\n    this.authService.isAuthenticated$.subscribe(isAuth => {\r\n      if (isAuth) {\r\n        this.router.navigate([this.returnUrl]);\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading = true;\r\n\r\n      const loginRequest: LoginRequest = {\r\n        email: this.loginForm.value.email,\r\n        password: this.loginForm.value.password,\r\n        rememberMe: this.loginForm.value.rememberMe\r\n      };\r\n\r\n      this.authService.login(loginRequest).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          if (response.success) {\r\n            this.snackBar.open(this.t.auth.loginSuccess, this.t.common.close, {\r\n              duration: 3000,\r\n              panelClass: ['success-snackbar']\r\n            });\r\n            this.router.navigate([this.returnUrl]);\r\n          } else {\r\n            this.snackBar.open(response.message || this.t.auth.loginError, this.t.common.close, {\r\n              duration: 5000,\r\n              panelClass: ['error-snackbar']\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          const errorMessage = this.getErrorMessage(error);\r\n          this.snackBar.open(errorMessage, this.t.common.close, {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.loginForm.controls).forEach(key => {\r\n      const control = this.loginForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getEmailErrorMessage(): string {\r\n    const emailControl = this.loginForm.get('email');\r\n    if (emailControl?.hasError('required')) {\r\n      return this.t.auth.emailRequired;\r\n    }\r\n    if (emailControl?.hasError('email')) {\r\n      return this.t.auth.invalidEmail;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getPasswordErrorMessage(): string {\r\n    const passwordControl = this.loginForm.get('password');\r\n    if (passwordControl?.hasError('required')) {\r\n      return this.t.auth.passwordRequired;\r\n    }\r\n    if (passwordControl?.hasError('minlength')) {\r\n      return this.t.auth.passwordTooShort;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  private getErrorMessage(error: any): string {\r\n    // Handle different types of errors\r\n    if (typeof error === 'string') {\r\n      return error;\r\n    }\r\n\r\n    if (error?.error?.message) {\r\n      const message = error.error.message.toLowerCase();\r\n\r\n      // Account locked\r\n      if (message.includes('locked') || message.includes('temporarily locked')) {\r\n        return 'Акаунтът е временно заключен поради множество неуспешни опити за вход. Моля, опитайте отново след 30 минути.';\r\n      }\r\n\r\n      // Invalid credentials\r\n      if (message.includes('invalid') || message.includes('password') || message.includes('email')) {\r\n        return 'Невалиден имейл или парола. Моля, проверете данните си и опитайте отново.';\r\n      }\r\n\r\n      // Account deactivated\r\n      if (message.includes('deactivated') || message.includes('inactive')) {\r\n        return 'Акаунтът е деактивиран. Моля, свържете се с поддръжката.';\r\n      }\r\n\r\n      return error.error.message;\r\n    }\r\n\r\n    // Network errors\r\n    if (error?.status === 0 || error?.name === 'HttpErrorResponse') {\r\n      return 'Проблем с мрежовата връзка. Моля, проверете интернет връзката си и опитайте отново.';\r\n    }\r\n\r\n    // Server errors\r\n    if (error?.status >= 500) {\r\n      return 'Възникна проблем със сървъра. Моля, опитайте отново след малко.';\r\n    }\r\n\r\n    // Default error message\r\n    return this.t.auth.loginError;\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    this.router.navigate(['/register'], { queryParams: { returnUrl: this.returnUrl } });\r\n  }\r\n\r\n  navigateToForgotPassword(): void {\r\n    this.router.navigate(['/forgot-password']);\r\n  }\r\n\r\n  signInWithGoogle(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithGooglePopup().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthLogin(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Неуспешен вход с Google. Моля, опитайте отново.', this.t.common.close, {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  signInWithFacebook(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithFacebook().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthLogin(oauthUser);\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Неуспешен вход с Facebook. Моля, опитайте отново.', this.t.common.close, {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleOAuthLogin(oauthUser: OAuthUser): void {\r\n    const oauthRequest: OAuthLoginRequest = {\r\n      provider: oauthUser.provider,\r\n      accessToken: oauthUser.accessToken,\r\n      email: oauthUser.email,\r\n      firstName: oauthUser.firstName,\r\n      lastName: oauthUser.lastName,\r\n      profilePictureUrl: oauthUser.profilePictureUrl\r\n    };\r\n\r\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          this.snackBar.open(`Добре дошли! Влязохте с ${oauthUser.provider}`, this.t.common.close, {\r\n            duration: 3000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n          this.router.navigate([this.returnUrl]);\r\n        } else {\r\n          this.snackBar.open(response.message || 'Неуспешен OAuth вход', this.t.common.close, {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'Неуспешен OAuth вход. Моля, опитайте отново.', this.t.common.close, {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"login-container\">\r\n  <mat-card class=\"login-card\">\r\n    <mat-card-header class=\"login-header\">\r\n      <mat-card-title>\r\n        <mat-icon class=\"login-icon\">lock</mat-icon>\r\n        {{ t.auth.loginTitle }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Въведете данните си за достъп до акаунта\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\r\n        <!-- Email Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>{{ t.auth.email }}</mat-label>\r\n          <input\r\n            matInput\r\n            type=\"email\"\r\n            formControlName=\"email\"\r\n            [placeholder]=\"'Въведете вашия имейл'\"\r\n            autocomplete=\"email\">\r\n          <mat-icon matSuffix>email</mat-icon>\r\n          <mat-error *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\r\n            {{ getEmailErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Password Field -->\r\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n          <mat-label>{{ t.auth.password }}</mat-label>\r\n          <input\r\n            matInput\r\n            [type]=\"hidePassword ? 'password' : 'text'\"\r\n            formControlName=\"password\"\r\n            [placeholder]=\"'Въведете вашата парола'\"\r\n            autocomplete=\"current-password\">\r\n          <button\r\n            mat-icon-button\r\n            matSuffix\r\n            type=\"button\"\r\n            (click)=\"hidePassword = !hidePassword\"\r\n            [attr.aria-label]=\"'Скрий парола'\"\r\n            [attr.aria-pressed]=\"hidePassword\">\r\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n          </button>\r\n          <mat-error *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\r\n            {{ getPasswordErrorMessage() }}\r\n          </mat-error>\r\n        </mat-form-field>\r\n\r\n        <!-- Remember Me Checkbox -->\r\n        <div class=\"remember-me-container\">\r\n          <mat-checkbox formControlName=\"rememberMe\" color=\"primary\">\r\n            {{ t.auth.rememberMe }}\r\n          </mat-checkbox>\r\n        </div>\r\n\r\n        <!-- Login Button -->\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          type=\"submit\"\r\n          class=\"full-width login-button\"\r\n          [disabled]=\"isLoading\">\r\n          <mat-icon *ngIf=\"isLoading\">\r\n            <mat-spinner diameter=\"20\"></mat-spinner>\r\n          </mat-icon>\r\n          <mat-icon *ngIf=\"!isLoading\">login</mat-icon>\r\n          {{ isLoading ? 'Влизане...' : t.auth.signIn }}\r\n        </button>\r\n\r\n        <!-- Divider -->\r\n        <div class=\"divider-container\">\r\n          <mat-divider></mat-divider>\r\n          <span class=\"divider-text\">{{ t.auth.orContinueWith }}</span>\r\n          <mat-divider></mat-divider>\r\n        </div>\r\n\r\n        <!-- OAuth Buttons -->\r\n        <div class=\"oauth-buttons\">\r\n          <!-- Google Sign-In Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button google-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signInWithGoogle()\">\r\n            <img src=\"https://developers.google.com/identity/images/g-logo.png\" alt=\"Google\" class=\"oauth-icon\">\r\n            Продължи с Google\r\n          </button>\r\n\r\n          <!-- Facebook Sign-In Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button facebook-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signInWithFacebook()\">\r\n            <mat-icon class=\"oauth-icon facebook-icon\">facebook</mat-icon>\r\n            Продължи с Facebook\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions class=\"login-actions\">\r\n      <div class=\"action-links\">\r\n        <button\r\n          mat-button\r\n          color=\"accent\"\r\n          type=\"button\"\r\n          (click)=\"navigateToForgotPassword()\">\r\n          {{ t.auth.forgotPassword }}\r\n        </button>\r\n      </div>\r\n\r\n      <mat-divider></mat-divider>\r\n\r\n      <div class=\"register-section\">\r\n        <p class=\"register-text\">{{ t.auth.dontHaveAccount }}</p>\r\n        <button\r\n          mat-stroked-button\r\n          color=\"primary\"\r\n          type=\"button\"\r\n          (click)=\"navigateToRegister()\"\r\n          class=\"full-width\">\r\n          <mat-icon>person_add</mat-icon>\r\n          {{ t.auth.createAccount }}\r\n        </button>\r\n      </div>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}