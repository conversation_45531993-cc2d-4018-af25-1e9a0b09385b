{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PortfolioManagementComponent {\n  static {\n    this.ɵfac = function PortfolioManagementComponent_Factory(t) {\n      return new (t || PortfolioManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PortfolioManagementComponent,\n      selectors: [[\"app-portfolio-management\"]],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"portfolio-management\"]],\n      template: function PortfolioManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"work_outline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Manage Portfolio \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\");\n          i0.ɵɵtext(9, \"Portfolio management component - Coming soon!\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".portfolio-management[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHJvZmlsZS9jb21wb25lbnRzL3BvcnRmb2xpby1tYW5hZ2VtZW50L3BvcnRmb2xpby1tYW5hZ2VtZW50LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0ksd0JBQXdCLGFBQWEsRUFBRSIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5wb3J0Zm9saW8tbWFuYWdlbWVudCB7IHBhZGRpbmc6IDIwcHg7IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAuBA,OAAM,MAAOA,4BAA4B;;;uBAA5BA,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UAlBrCC,8BAAkC;UAIhBA,4BAAY;UAAAA,iBAAW;UACjCA,kCACF;UAAAA,iBAAiB;UAEnBA,wCAAkB;UACbA,6DAA6C;UAAAA,iBAAI", "names": ["PortfolioManagementComponent", "selectors", "decls", "vars", "consts", "template", "i0"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\profile\\components\\portfolio-management\\portfolio-management.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-portfolio-management',\r\n  template: `\r\n    <div class=\"portfolio-management\">\r\n      <mat-card>\r\n        <mat-card-header>\r\n          <mat-card-title>\r\n            <mat-icon>work_outline</mat-icon>\r\n            Manage Portfolio\r\n          </mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n          <p>Portfolio management component - Coming soon!</p>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .portfolio-management { padding: 20px; }\r\n  `]\r\n})\r\nexport class PortfolioManagementComponent {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}