{"ConnectionStrings": {"DefaultConnection": "Server=localhost,1433;Database=Oracle2DB;User Id=sa;Password=**********;TrustServerCertificate=True;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity123456789", "Issuer": "OraculApp", "Audience": "OraculUsers", "AccessTokenExpirationMinutes": 15, "RefreshTokenExpirationDays": 7}, "EmailSettings": {"SmtpHost": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "Oracul App"}, "Authentication": {"Google": {"ClientId": "your-google-client-id", "ClientSecret": "your-google-client-secret"}, "Facebook": {"AppId": "your-facebook-app-id", "AppSecret": "your-facebook-app-secret"}}, "AppSettings": {"ClientUrl": "http://localhost:4200"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}