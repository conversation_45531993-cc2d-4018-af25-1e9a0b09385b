using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Work experience entity for astrology and spiritual career history
    /// </summary>
    public class WorkExperience : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Company { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Position { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool IsCurrent { get; set; } = false;

        [Required]
        public string Description { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? Location { get; set; }

        [MaxLength(500)]
        public string? CompanyLogoUrl { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual ICollection<WorkAchievement> WorkAchievements { get; set; } = new List<WorkAchievement>();
    }
}
